2025-07-12 00:49:22,468 INFO    StreamThr :2859589 [internal.py:wandb_internal():89] W&B internal server running at pid: 2859589, started at: 2025-07-12 00:49:22.468013
2025-07-12 00:49:22,472 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: status
2025-07-12 00:49:22,483 INFO    WriterThread:2859589 [datastore.py:open_for_write():85] open: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-b0tcsftu/run-b0tcsftu.wandb
2025-07-12 00:49:22,484 DEBUG   SenderThread:2859589 [sender.py:send():369] send: header
2025-07-12 00:49:22,499 DEBUG   SenderThread:2859589 [sender.py:send():369] send: run
2025-07-12 00:49:22,872 INFO    SenderThread:2859589 [dir_watcher.py:__init__():211] watching files in: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-b0tcsftu/files
2025-07-12 00:49:22,872 INFO    SenderThread:2859589 [sender.py:_start_run_threads():1103] run started: b0tcsftu with start time 1752295762.473998
2025-07-12 00:49:22,872 DEBUG   SenderThread:2859589 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:49:22,873 INFO    SenderThread:2859589 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:49:22,881 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: check_version
2025-07-12 00:49:22,882 DEBUG   SenderThread:2859589 [sender.py:send_request():396] send_request: check_version
2025-07-12 00:49:22,950 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: run_start
2025-07-12 00:49:22,955 DEBUG   HandlerThread:2859589 [system_info.py:__init__():31] System info init
2025-07-12 00:49:22,955 DEBUG   HandlerThread:2859589 [system_info.py:__init__():46] System info init done
2025-07-12 00:49:22,955 INFO    HandlerThread:2859589 [system_monitor.py:start():181] Starting system monitor
2025-07-12 00:49:22,955 INFO    SystemMonitor:2859589 [system_monitor.py:_start():145] Starting system asset monitoring threads
2025-07-12 00:49:22,955 INFO    HandlerThread:2859589 [system_monitor.py:probe():201] Collecting system info
2025-07-12 00:49:22,956 INFO    SystemMonitor:2859589 [interfaces.py:start():190] Started cpu monitoring
2025-07-12 00:49:22,956 INFO    SystemMonitor:2859589 [interfaces.py:start():190] Started disk monitoring
2025-07-12 00:49:22,957 INFO    SystemMonitor:2859589 [interfaces.py:start():190] Started gpu monitoring
2025-07-12 00:49:22,958 INFO    SystemMonitor:2859589 [interfaces.py:start():190] Started memory monitoring
2025-07-12 00:49:22,958 INFO    SystemMonitor:2859589 [interfaces.py:start():190] Started network monitoring
2025-07-12 00:49:22,977 DEBUG   HandlerThread:2859589 [system_info.py:probe():195] Probing system
2025-07-12 00:49:22,983 DEBUG   HandlerThread:2859589 [system_info.py:_probe_git():180] Probing git
2025-07-12 00:49:22,999 DEBUG   HandlerThread:2859589 [system_info.py:_probe_git():188] Probing git done
2025-07-12 00:49:22,999 DEBUG   HandlerThread:2859589 [system_info.py:probe():240] Probing system done
2025-07-12 00:49:23,000 DEBUG   HandlerThread:2859589 [system_monitor.py:probe():210] {'os': 'Linux-5.4.0-77-generic-x86_64-with-debian-bullseye-sid', 'python': '3.7.16', 'heartbeatAt': '2025-07-12T04:49:22.977653', 'startedAt': '2025-07-12T04:49:22.450498', 'docker': None, 'cuda': None, 'args': ('--local_rank=2', '--cfg', 'config/base_train.yaml', '--output', '/home-local2/akath.extra.nobkp/sereact', '--data-path', '/home-local2/akath.extra.nobkp/dl_challenge', '--batch-size', '1'), 'state': 'running', 'program': 'main.py', 'codePath': 'main.py', 'git': {'remote': 'https://github.com/Shrinidhibhat87/codingchallenge_sereact.git', 'commit': 'a3ff60e13dfced588b500c8a1de00f36fdf22d49'}, 'email': '<EMAIL>', 'root': '/home-local/akath.nobkp/codingchallenge_sereact', 'host': 'lv3-32190', 'username': 'akath', 'executable': '/gel/usr/akath/.conda/envs/swin/bin/python', 'cpu_count': 6, 'cpu_count_logical': 12, 'cpu_freq': {'current': 2.4611666666666667, 'min': 1200.0, 'max': 4000.0}, 'cpu_freq_per_core': [{'current': 2.068, 'min': 1200.0, 'max': 4000.0}, {'current': 2.22, 'min': 1200.0, 'max': 4000.0}, {'current': 1.835, 'min': 1200.0, 'max': 4000.0}, {'current': 2.716, 'min': 1200.0, 'max': 4000.0}, {'current': 1.603, 'min': 1200.0, 'max': 4000.0}, {'current': 3.597, 'min': 1200.0, 'max': 4000.0}, {'current': 2.416, 'min': 1200.0, 'max': 4000.0}, {'current': 1.874, 'min': 1200.0, 'max': 4000.0}, {'current': 1.934, 'min': 1200.0, 'max': 4000.0}, {'current': 2.361, 'min': 1200.0, 'max': 4000.0}, {'current': 3.313, 'min': 1200.0, 'max': 4000.0}, {'current': 3.597, 'min': 1200.0, 'max': 4000.0}], 'disk': {'total': 111.2200813293457, 'used': 53.24767303466797}, 'gpu': 'TITAN Xp', 'gpu_count': 3, 'gpu_devices': [{'name': 'TITAN Xp', 'memory_total': 12787122176}, {'name': 'TITAN Xp', 'memory_total': 12788498432}, {'name': 'TITAN Xp', 'memory_total': 12788498432}], 'memory': {'total': 62.72977066040039}}
2025-07-12 00:49:23,000 INFO    HandlerThread:2859589 [system_monitor.py:probe():211] Finished collecting system info
2025-07-12 00:49:23,000 INFO    HandlerThread:2859589 [system_monitor.py:probe():214] Publishing system info
2025-07-12 00:49:23,000 DEBUG   HandlerThread:2859589 [system_info.py:_save_pip():52] Saving list of pip packages installed into the current environment
2025-07-12 00:49:23,000 DEBUG   HandlerThread:2859589 [system_info.py:_save_pip():67] Saving pip packages done
2025-07-12 00:49:23,000 DEBUG   HandlerThread:2859589 [system_info.py:_save_conda():75] Saving list of conda packages installed into the current environment
2025-07-12 00:49:23,874 INFO    Thread-12 :2859589 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-b0tcsftu/files/wandb-summary.json
2025-07-12 00:49:23,875 INFO    Thread-12 :2859589 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-b0tcsftu/files/requirements.txt
2025-07-12 00:49:23,875 INFO    Thread-12 :2859589 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-b0tcsftu/files/conda-environment.yaml
2025-07-12 00:49:25,564 DEBUG   HandlerThread:2859589 [system_info.py:_save_conda():86] Saving conda packages done
2025-07-12 00:49:25,565 INFO    HandlerThread:2859589 [system_monitor.py:probe():216] Finished publishing system info
2025-07-12 00:49:25,574 DEBUG   SenderThread:2859589 [sender.py:send():369] send: files
2025-07-12 00:49:25,575 INFO    SenderThread:2859589 [sender.py:_save_file():1354] saving file wandb-metadata.json with policy now
2025-07-12 00:49:25,581 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: stop_status
2025-07-12 00:49:25,583 DEBUG   SenderThread:2859589 [sender.py:send_request():396] send_request: stop_status
2025-07-12 00:49:25,758 DEBUG   SenderThread:2859589 [sender.py:send():369] send: telemetry
2025-07-12 00:49:25,876 INFO    Thread-12 :2859589 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-b0tcsftu/files/conda-environment.yaml
2025-07-12 00:49:25,876 INFO    Thread-12 :2859589 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-b0tcsftu/files/output.log
2025-07-12 00:49:25,877 INFO    Thread-12 :2859589 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-b0tcsftu/files/wandb-metadata.json
2025-07-12 00:49:25,931 INFO    wandb-upload_0:2859589 [upload_job.py:push():133] Uploaded file /tmp/tmpe4c1wb_kwandb/7nljy0es-wandb-metadata.json
2025-07-12 00:49:27,591 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: status_report
2025-07-12 00:49:27,877 INFO    Thread-12 :2859589 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-b0tcsftu/files/output.log
2025-07-12 00:49:29,878 INFO    Thread-12 :2859589 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-b0tcsftu/files/output.log
2025-07-12 00:49:31,599 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:49:31,603 DEBUG   SenderThread:2859589 [sender.py:send():369] send: history
2025-07-12 00:49:31,603 DEBUG   SenderThread:2859589 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:49:31,603 INFO    SenderThread:2859589 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:49:31,878 INFO    Thread-12 :2859589 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-b0tcsftu/files/wandb-summary.json
2025-07-12 00:49:32,604 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: status_report
2025-07-12 00:49:34,213 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:49:34,214 DEBUG   SenderThread:2859589 [sender.py:send():369] send: history
2025-07-12 00:49:34,215 DEBUG   SenderThread:2859589 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:49:34,215 INFO    SenderThread:2859589 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:49:34,879 INFO    Thread-12 :2859589 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-b0tcsftu/files/wandb-summary.json
2025-07-12 00:49:36,007 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:49:36,008 DEBUG   SenderThread:2859589 [sender.py:send():369] send: history
2025-07-12 00:49:36,008 DEBUG   SenderThread:2859589 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:49:36,010 INFO    SenderThread:2859589 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:49:36,880 INFO    Thread-12 :2859589 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-b0tcsftu/files/wandb-summary.json
2025-07-12 00:49:38,011 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: status_report
2025-07-12 00:49:38,143 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:49:38,145 DEBUG   SenderThread:2859589 [sender.py:send():369] send: history
2025-07-12 00:49:38,145 DEBUG   SenderThread:2859589 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:49:38,145 INFO    SenderThread:2859589 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:49:38,881 INFO    Thread-12 :2859589 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-b0tcsftu/files/wandb-summary.json
2025-07-12 00:49:40,268 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:49:40,270 DEBUG   SenderThread:2859589 [sender.py:send():369] send: history
2025-07-12 00:49:40,270 DEBUG   SenderThread:2859589 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:49:40,271 INFO    SenderThread:2859589 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:49:40,581 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: stop_status
2025-07-12 00:49:40,582 DEBUG   SenderThread:2859589 [sender.py:send_request():396] send_request: stop_status
2025-07-12 00:49:40,881 INFO    Thread-12 :2859589 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-b0tcsftu/files/wandb-summary.json
2025-07-12 00:49:42,391 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:49:42,392 DEBUG   SenderThread:2859589 [sender.py:send():369] send: history
2025-07-12 00:49:42,393 DEBUG   SenderThread:2859589 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:49:42,394 INFO    SenderThread:2859589 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:49:42,882 INFO    Thread-12 :2859589 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-b0tcsftu/files/wandb-summary.json
2025-07-12 00:49:43,395 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: status_report
2025-07-12 00:49:44,101 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:49:44,102 DEBUG   SenderThread:2859589 [sender.py:send():369] send: history
2025-07-12 00:49:44,102 DEBUG   SenderThread:2859589 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:49:44,102 INFO    SenderThread:2859589 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:49:44,883 INFO    Thread-12 :2859589 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-b0tcsftu/files/wandb-summary.json
2025-07-12 00:49:46,090 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:49:46,091 DEBUG   SenderThread:2859589 [sender.py:send():369] send: history
2025-07-12 00:49:46,091 DEBUG   SenderThread:2859589 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:49:46,094 INFO    SenderThread:2859589 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:49:46,884 INFO    Thread-12 :2859589 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-b0tcsftu/files/wandb-summary.json
2025-07-12 00:49:48,151 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:49:48,153 DEBUG   SenderThread:2859589 [sender.py:send():369] send: history
2025-07-12 00:49:48,153 DEBUG   SenderThread:2859589 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:49:48,157 INFO    SenderThread:2859589 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:49:48,884 INFO    Thread-12 :2859589 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-b0tcsftu/files/wandb-summary.json
2025-07-12 00:49:49,157 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: status_report
2025-07-12 00:49:50,139 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:49:50,140 DEBUG   SenderThread:2859589 [sender.py:send():369] send: history
2025-07-12 00:49:50,140 DEBUG   SenderThread:2859589 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:49:50,141 INFO    SenderThread:2859589 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:49:50,885 INFO    Thread-12 :2859589 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-b0tcsftu/files/wandb-summary.json
2025-07-12 00:49:52,601 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:49:52,602 DEBUG   SenderThread:2859589 [sender.py:send():369] send: history
2025-07-12 00:49:52,602 DEBUG   SenderThread:2859589 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:49:52,603 INFO    SenderThread:2859589 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:49:52,886 INFO    Thread-12 :2859589 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-b0tcsftu/files/wandb-summary.json
2025-07-12 00:49:54,473 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:49:54,474 DEBUG   SenderThread:2859589 [sender.py:send():369] send: history
2025-07-12 00:49:54,483 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: status_report
2025-07-12 00:49:54,599 DEBUG   SenderThread:2859589 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:49:54,600 INFO    SenderThread:2859589 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:49:54,886 INFO    Thread-12 :2859589 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-b0tcsftu/files/config.yaml
2025-07-12 00:49:54,887 INFO    Thread-12 :2859589 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-b0tcsftu/files/wandb-summary.json
2025-07-12 00:49:55,581 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: stop_status
2025-07-12 00:49:55,581 DEBUG   SenderThread:2859589 [sender.py:send_request():396] send_request: stop_status
2025-07-12 00:49:56,633 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:49:56,634 DEBUG   SenderThread:2859589 [sender.py:send():369] send: history
2025-07-12 00:49:56,634 DEBUG   SenderThread:2859589 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:49:56,635 INFO    SenderThread:2859589 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:49:56,887 INFO    Thread-12 :2859589 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-b0tcsftu/files/wandb-summary.json
2025-07-12 00:49:58,267 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:49:58,268 DEBUG   SenderThread:2859589 [sender.py:send():369] send: history
2025-07-12 00:49:58,268 DEBUG   SenderThread:2859589 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:49:58,269 INFO    SenderThread:2859589 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:49:58,888 INFO    Thread-12 :2859589 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-b0tcsftu/files/wandb-summary.json
2025-07-12 00:49:59,910 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:49:59,911 DEBUG   SenderThread:2859589 [sender.py:send():369] send: history
2025-07-12 00:49:59,912 DEBUG   SenderThread:2859589 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:49:59,913 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: status_report
2025-07-12 00:49:59,914 INFO    SenderThread:2859589 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:00,663 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:00,664 DEBUG   SenderThread:2859589 [sender.py:send():369] send: history
2025-07-12 00:50:00,665 DEBUG   SenderThread:2859589 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:00,665 INFO    SenderThread:2859589 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:00,889 INFO    Thread-12 :2859589 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-b0tcsftu/files/wandb-summary.json
2025-07-12 00:50:02,767 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:02,768 DEBUG   SenderThread:2859589 [sender.py:send():369] send: history
2025-07-12 00:50:02,769 DEBUG   SenderThread:2859589 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:02,769 INFO    SenderThread:2859589 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:02,889 INFO    Thread-12 :2859589 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-b0tcsftu/files/wandb-summary.json
2025-07-12 00:50:04,394 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:04,395 DEBUG   SenderThread:2859589 [sender.py:send():369] send: history
2025-07-12 00:50:04,395 DEBUG   SenderThread:2859589 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:04,395 INFO    SenderThread:2859589 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:04,890 INFO    Thread-12 :2859589 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-b0tcsftu/files/wandb-summary.json
2025-07-12 00:50:05,396 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: status_report
2025-07-12 00:50:06,733 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:06,735 DEBUG   SenderThread:2859589 [sender.py:send():369] send: history
2025-07-12 00:50:06,735 DEBUG   SenderThread:2859589 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:06,736 INFO    SenderThread:2859589 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:06,891 INFO    Thread-12 :2859589 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-b0tcsftu/files/wandb-summary.json
2025-07-12 00:50:08,687 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:08,688 DEBUG   SenderThread:2859589 [sender.py:send():369] send: history
2025-07-12 00:50:08,688 DEBUG   SenderThread:2859589 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:08,691 INFO    SenderThread:2859589 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:08,891 INFO    Thread-12 :2859589 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-b0tcsftu/files/wandb-summary.json
2025-07-12 00:50:10,581 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: stop_status
2025-07-12 00:50:10,582 DEBUG   SenderThread:2859589 [sender.py:send_request():396] send_request: stop_status
2025-07-12 00:50:10,690 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: status_report
2025-07-12 00:50:10,907 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:10,908 DEBUG   SenderThread:2859589 [sender.py:send():369] send: history
2025-07-12 00:50:10,909 DEBUG   SenderThread:2859589 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:10,911 INFO    SenderThread:2859589 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:11,893 INFO    Thread-12 :2859589 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-b0tcsftu/files/wandb-summary.json
2025-07-12 00:50:12,850 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:12,851 DEBUG   SenderThread:2859589 [sender.py:send():369] send: history
2025-07-12 00:50:12,852 DEBUG   SenderThread:2859589 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:12,852 INFO    SenderThread:2859589 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:12,893 INFO    Thread-12 :2859589 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-b0tcsftu/files/wandb-summary.json
2025-07-12 00:50:13,961 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:13,962 DEBUG   SenderThread:2859589 [sender.py:send():369] send: history
2025-07-12 00:50:13,962 DEBUG   SenderThread:2859589 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:13,964 INFO    SenderThread:2859589 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:14,894 INFO    Thread-12 :2859589 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-b0tcsftu/files/wandb-summary.json
2025-07-12 00:50:15,621 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:15,622 DEBUG   SenderThread:2859589 [sender.py:send():369] send: history
2025-07-12 00:50:15,622 DEBUG   SenderThread:2859589 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:15,623 INFO    SenderThread:2859589 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:15,894 INFO    Thread-12 :2859589 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-b0tcsftu/files/wandb-summary.json
2025-07-12 00:50:16,623 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: status_report
2025-07-12 00:50:17,560 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:17,561 DEBUG   SenderThread:2859589 [sender.py:send():369] send: history
2025-07-12 00:50:17,561 DEBUG   SenderThread:2859589 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:17,564 INFO    SenderThread:2859589 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:17,895 INFO    Thread-12 :2859589 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-b0tcsftu/files/wandb-summary.json
2025-07-12 00:50:19,529 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:19,530 DEBUG   SenderThread:2859589 [sender.py:send():369] send: history
2025-07-12 00:50:19,530 DEBUG   SenderThread:2859589 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:19,531 INFO    SenderThread:2859589 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:19,895 INFO    Thread-12 :2859589 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-b0tcsftu/files/wandb-summary.json
2025-07-12 00:50:20,648 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:20,649 DEBUG   SenderThread:2859589 [sender.py:send():369] send: history
2025-07-12 00:50:20,650 DEBUG   SenderThread:2859589 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:20,654 INFO    SenderThread:2859589 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:20,896 INFO    Thread-12 :2859589 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-b0tcsftu/files/wandb-summary.json
2025-07-12 00:50:21,654 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: status_report
2025-07-12 00:50:22,753 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:22,754 DEBUG   SenderThread:2859589 [sender.py:send():369] send: history
2025-07-12 00:50:22,755 DEBUG   SenderThread:2859589 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:22,755 INFO    SenderThread:2859589 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:22,897 INFO    Thread-12 :2859589 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-b0tcsftu/files/wandb-summary.json
2025-07-12 00:50:22,958 DEBUG   SystemMonitor:2859589 [system_monitor.py:_start():159] Starting system metrics aggregation loop
2025-07-12 00:50:22,960 DEBUG   SenderThread:2859589 [sender.py:send():369] send: stats
2025-07-12 00:50:25,056 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:25,057 DEBUG   SenderThread:2859589 [sender.py:send():369] send: history
2025-07-12 00:50:25,057 DEBUG   SenderThread:2859589 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:25,058 INFO    SenderThread:2859589 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:25,581 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: stop_status
2025-07-12 00:50:25,581 DEBUG   SenderThread:2859589 [sender.py:send_request():396] send_request: stop_status
2025-07-12 00:50:25,897 INFO    Thread-12 :2859589 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-b0tcsftu/files/wandb-summary.json
2025-07-12 00:50:26,198 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:26,199 DEBUG   SenderThread:2859589 [sender.py:send():369] send: history
2025-07-12 00:50:26,200 DEBUG   SenderThread:2859589 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:26,200 INFO    SenderThread:2859589 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:26,898 INFO    Thread-12 :2859589 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-b0tcsftu/files/wandb-summary.json
2025-07-12 00:50:27,201 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: status_report
2025-07-12 00:50:27,230 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:27,231 DEBUG   SenderThread:2859589 [sender.py:send():369] send: history
2025-07-12 00:50:27,232 DEBUG   SenderThread:2859589 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:27,232 INFO    SenderThread:2859589 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:27,898 INFO    Thread-12 :2859589 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-b0tcsftu/files/wandb-summary.json
2025-07-12 00:50:29,383 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:29,384 DEBUG   SenderThread:2859589 [sender.py:send():369] send: history
2025-07-12 00:50:29,384 DEBUG   SenderThread:2859589 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:29,385 INFO    SenderThread:2859589 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:29,899 INFO    Thread-12 :2859589 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-b0tcsftu/files/wandb-summary.json
2025-07-12 00:50:31,017 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:31,017 DEBUG   SenderThread:2859589 [sender.py:send():369] send: history
2025-07-12 00:50:31,018 DEBUG   SenderThread:2859589 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:31,020 INFO    SenderThread:2859589 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:31,900 INFO    Thread-12 :2859589 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-b0tcsftu/files/wandb-summary.json
2025-07-12 00:50:33,021 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: status_report
2025-07-12 00:50:33,104 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:33,105 DEBUG   SenderThread:2859589 [sender.py:send():369] send: history
2025-07-12 00:50:33,106 DEBUG   SenderThread:2859589 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:33,106 INFO    SenderThread:2859589 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:33,901 INFO    Thread-12 :2859589 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-b0tcsftu/files/wandb-summary.json
2025-07-12 00:50:35,063 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:35,064 DEBUG   SenderThread:2859589 [sender.py:send():369] send: history
2025-07-12 00:50:35,064 DEBUG   SenderThread:2859589 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:35,065 INFO    SenderThread:2859589 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:35,901 INFO    Thread-12 :2859589 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-b0tcsftu/files/wandb-summary.json
2025-07-12 00:50:36,812 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:36,813 DEBUG   SenderThread:2859589 [sender.py:send():369] send: history
2025-07-12 00:50:36,813 DEBUG   SenderThread:2859589 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:36,814 INFO    SenderThread:2859589 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:36,902 INFO    Thread-12 :2859589 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-b0tcsftu/files/wandb-summary.json
2025-07-12 00:50:38,724 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:38,726 DEBUG   SenderThread:2859589 [sender.py:send():369] send: history
2025-07-12 00:50:38,726 DEBUG   SenderThread:2859589 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:38,726 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: status_report
2025-07-12 00:50:38,727 INFO    SenderThread:2859589 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:38,902 INFO    Thread-12 :2859589 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-b0tcsftu/files/wandb-summary.json
2025-07-12 00:50:40,581 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: stop_status
2025-07-12 00:50:40,581 DEBUG   SenderThread:2859589 [sender.py:send_request():396] send_request: stop_status
2025-07-12 00:50:40,645 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:40,694 DEBUG   SenderThread:2859589 [sender.py:send():369] send: history
2025-07-12 00:50:40,695 DEBUG   SenderThread:2859589 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:40,696 INFO    SenderThread:2859589 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:40,903 INFO    Thread-12 :2859589 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-b0tcsftu/files/wandb-summary.json
2025-07-12 00:50:41,751 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:41,752 DEBUG   SenderThread:2859589 [sender.py:send():369] send: history
2025-07-12 00:50:41,752 DEBUG   SenderThread:2859589 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:41,755 INFO    SenderThread:2859589 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:41,903 INFO    Thread-12 :2859589 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-b0tcsftu/files/wandb-summary.json
2025-07-12 00:50:42,842 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:42,843 DEBUG   SenderThread:2859589 [sender.py:send():369] send: history
2025-07-12 00:50:42,843 DEBUG   SenderThread:2859589 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:42,844 INFO    SenderThread:2859589 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:42,904 INFO    Thread-12 :2859589 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-b0tcsftu/files/wandb-summary.json
2025-07-12 00:50:43,708 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:43,709 DEBUG   SenderThread:2859589 [sender.py:send():369] send: history
2025-07-12 00:50:43,709 DEBUG   SenderThread:2859589 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:43,710 INFO    SenderThread:2859589 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:43,904 INFO    Thread-12 :2859589 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-b0tcsftu/files/wandb-summary.json
2025-07-12 00:50:44,710 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: status_report
2025-07-12 00:50:45,653 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:45,654 DEBUG   SenderThread:2859589 [sender.py:send():369] send: history
2025-07-12 00:50:45,655 DEBUG   SenderThread:2859589 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:45,655 INFO    SenderThread:2859589 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:45,905 INFO    Thread-12 :2859589 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-b0tcsftu/files/wandb-summary.json
2025-07-12 00:50:46,864 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:46,866 DEBUG   SenderThread:2859589 [sender.py:send():369] send: history
2025-07-12 00:50:46,866 DEBUG   SenderThread:2859589 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:46,867 INFO    SenderThread:2859589 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:46,905 INFO    Thread-12 :2859589 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-b0tcsftu/files/wandb-summary.json
2025-07-12 00:50:48,958 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:48,959 DEBUG   SenderThread:2859589 [sender.py:send():369] send: history
2025-07-12 00:50:48,959 DEBUG   SenderThread:2859589 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:48,960 INFO    SenderThread:2859589 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:49,835 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:49,836 DEBUG   SenderThread:2859589 [sender.py:send():369] send: history
2025-07-12 00:50:49,837 DEBUG   SenderThread:2859589 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:49,837 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: status_report
2025-07-12 00:50:49,838 INFO    SenderThread:2859589 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:49,906 INFO    Thread-12 :2859589 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-b0tcsftu/files/wandb-summary.json
2025-07-12 00:50:50,805 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:50,806 DEBUG   SenderThread:2859589 [sender.py:send():369] send: history
2025-07-12 00:50:50,806 DEBUG   SenderThread:2859589 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:50,806 INFO    SenderThread:2859589 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:50,907 INFO    Thread-12 :2859589 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-b0tcsftu/files/wandb-summary.json
2025-07-12 00:50:52,659 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:52,660 DEBUG   SenderThread:2859589 [sender.py:send():369] send: history
2025-07-12 00:50:52,660 DEBUG   SenderThread:2859589 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:52,663 INFO    SenderThread:2859589 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:52,907 INFO    Thread-12 :2859589 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-b0tcsftu/files/wandb-summary.json
2025-07-12 00:50:52,961 DEBUG   SenderThread:2859589 [sender.py:send():369] send: stats
2025-07-12 00:50:53,571 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:53,572 DEBUG   SenderThread:2859589 [sender.py:send():369] send: history
2025-07-12 00:50:53,572 DEBUG   SenderThread:2859589 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:53,573 INFO    SenderThread:2859589 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:53,908 INFO    Thread-12 :2859589 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-b0tcsftu/files/wandb-summary.json
2025-07-12 00:50:54,649 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:54,649 DEBUG   SenderThread:2859589 [sender.py:send():369] send: history
2025-07-12 00:50:54,650 DEBUG   SenderThread:2859589 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:54,650 INFO    SenderThread:2859589 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:54,908 INFO    Thread-12 :2859589 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-b0tcsftu/files/wandb-summary.json
2025-07-12 00:50:55,581 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: stop_status
2025-07-12 00:50:55,581 DEBUG   SenderThread:2859589 [sender.py:send_request():396] send_request: stop_status
2025-07-12 00:50:55,689 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: status_report
2025-07-12 00:50:56,007 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:56,008 DEBUG   SenderThread:2859589 [sender.py:send():369] send: history
2025-07-12 00:50:56,008 DEBUG   SenderThread:2859589 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:56,009 INFO    SenderThread:2859589 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:56,729 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:56,730 DEBUG   SenderThread:2859589 [sender.py:send():369] send: history
2025-07-12 00:50:56,730 DEBUG   SenderThread:2859589 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:56,731 INFO    SenderThread:2859589 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:56,909 INFO    Thread-12 :2859589 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-b0tcsftu/files/wandb-summary.json
2025-07-12 00:50:57,991 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:57,991 DEBUG   SenderThread:2859589 [sender.py:send():369] send: history
2025-07-12 00:50:57,992 DEBUG   SenderThread:2859589 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:57,992 INFO    SenderThread:2859589 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:58,782 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:58,783 DEBUG   SenderThread:2859589 [sender.py:send():369] send: history
2025-07-12 00:50:58,783 DEBUG   SenderThread:2859589 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:58,784 INFO    SenderThread:2859589 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:58,909 INFO    Thread-12 :2859589 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-b0tcsftu/files/wandb-summary.json
2025-07-12 00:51:00,492 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:51:00,493 DEBUG   SenderThread:2859589 [sender.py:send():369] send: history
2025-07-12 00:51:00,493 DEBUG   SenderThread:2859589 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:51:00,494 INFO    SenderThread:2859589 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:51:00,914 INFO    Thread-12 :2859589 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-b0tcsftu/files/wandb-summary.json
2025-07-12 00:51:01,494 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: status_report
2025-07-12 00:51:06,495 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: status_report
2025-07-12 00:51:10,993 INFO    memory    :2859589 [interfaces.py:monitor():140] Process proc.memory.rssMB has exited.
2025-07-12 00:51:10,993 DEBUG   SystemMonitor:2859589 [system_monitor.py:_start():166] Finished system metrics aggregation loop
2025-07-12 00:51:10,994 DEBUG   SystemMonitor:2859589 [system_monitor.py:_start():170] Publishing last batch of metrics
2025-07-12 00:51:10,996 DEBUG   SenderThread:2859589 [sender.py:send():369] send: stats
2025-07-12 00:51:11,969 INFO    MainThread:2859589 [internal.py:handle_exit():76] Internal process exited
2025-07-12 00:51:11,998 DEBUG   HandlerThread:2859589 [handler.py:handle_request():144] handle_request: status_report
