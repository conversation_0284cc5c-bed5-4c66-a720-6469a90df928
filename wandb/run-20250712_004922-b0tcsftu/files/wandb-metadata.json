{"os": "Linux-5.4.0-77-generic-x86_64-with-debian-bullseye-sid", "python": "3.7.16", "heartbeatAt": "2025-07-12T04:49:22.977653", "startedAt": "2025-07-12T04:49:22.450498", "docker": null, "cuda": null, "args": ["--local_rank=2", "--cfg", "config/base_train.yaml", "--output", "/home-local2/akath.extra.nobkp/sereact", "--data-path", "/home-local2/akath.extra.nobkp/dl_challenge", "--batch-size", "1"], "state": "running", "program": "main.py", "codePath": "main.py", "git": {"remote": "https://github.com/Shrinidhibhat87/codingchallenge_sereact.git", "commit": "a3ff60e13dfced588b500c8a1de00f36fdf22d49"}, "email": "<EMAIL>", "root": "/home-local/akath.nobkp/codingchallenge_sereact", "host": "lv3-32190", "username": "akath", "executable": "/gel/usr/akath/.conda/envs/swin/bin/python", "cpu_count": 6, "cpu_count_logical": 12, "cpu_freq": {"current": 2.4611666666666667, "min": 1200.0, "max": 4000.0}, "cpu_freq_per_core": [{"current": 2.068, "min": 1200.0, "max": 4000.0}, {"current": 2.22, "min": 1200.0, "max": 4000.0}, {"current": 1.835, "min": 1200.0, "max": 4000.0}, {"current": 2.716, "min": 1200.0, "max": 4000.0}, {"current": 1.603, "min": 1200.0, "max": 4000.0}, {"current": 3.597, "min": 1200.0, "max": 4000.0}, {"current": 2.416, "min": 1200.0, "max": 4000.0}, {"current": 1.874, "min": 1200.0, "max": 4000.0}, {"current": 1.934, "min": 1200.0, "max": 4000.0}, {"current": 2.361, "min": 1200.0, "max": 4000.0}, {"current": 3.313, "min": 1200.0, "max": 4000.0}, {"current": 3.597, "min": 1200.0, "max": 4000.0}], "disk": {"total": 111.2200813293457, "used": 53.24767303466797}, "gpu": "TITAN Xp", "gpu_count": 3, "gpu_devices": [{"name": "TITAN Xp", "memory_total": 12787122176}, {"name": "TITAN Xp", "memory_total": 12788498432}, {"name": "TITAN Xp", "memory_total": 12788498432}], "memory": {"total": 62.72977066040039}}