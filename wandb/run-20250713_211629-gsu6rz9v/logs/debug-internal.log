2025-07-13 21:16:29,252 INFO    StreamThr :2965321 [internal.py:wandb_internal():89] W&B internal server running at pid: 2965321, started at: 2025-07-13 21:16:29.252178
2025-07-13 21:16:29,254 DEBUG   HandlerThread:2965321 [handler.py:handle_request():144] handle_request: status
2025-07-13 21:16:29,258 INFO    WriterThread:2965321 [datastore.py:open_for_write():85] open: /home-local/akath.nobkp/sereact/wandb/run-20250713_211629-gsu6rz9v/run-gsu6rz9v.wandb
2025-07-13 21:16:29,261 DEBUG   SenderThread:2965321 [sender.py:send():369] send: header
2025-07-13 21:16:29,279 DEBUG   SenderThread:2965321 [sender.py:send():369] send: run
2025-07-13 21:16:29,789 INFO    SenderThread:2965321 [dir_watcher.py:__init__():211] watching files in: /home-local/akath.nobkp/sereact/wandb/run-20250713_211629-gsu6rz9v/files
2025-07-13 21:16:29,790 INFO    SenderThread:2965321 [sender.py:_start_run_threads():1103] run started: gsu6rz9v with start time 1752455789.250955
2025-07-13 21:16:29,791 DEBUG   SenderThread:2965321 [sender.py:send_request():396] send_request: summary_record
2025-07-13 21:16:29,792 INFO    SenderThread:2965321 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 21:16:29,804 DEBUG   HandlerThread:2965321 [handler.py:handle_request():144] handle_request: check_version
2025-07-13 21:16:29,804 DEBUG   SenderThread:2965321 [sender.py:send_request():396] send_request: check_version
2025-07-13 21:16:29,869 DEBUG   HandlerThread:2965321 [handler.py:handle_request():144] handle_request: run_start
2025-07-13 21:16:29,872 DEBUG   HandlerThread:2965321 [system_info.py:__init__():31] System info init
2025-07-13 21:16:29,872 DEBUG   HandlerThread:2965321 [system_info.py:__init__():46] System info init done
2025-07-13 21:16:29,872 INFO    HandlerThread:2965321 [system_monitor.py:start():181] Starting system monitor
2025-07-13 21:16:29,873 INFO    SystemMonitor:2965321 [system_monitor.py:_start():145] Starting system asset monitoring threads
2025-07-13 21:16:29,873 INFO    HandlerThread:2965321 [system_monitor.py:probe():201] Collecting system info
2025-07-13 21:16:29,874 INFO    SystemMonitor:2965321 [interfaces.py:start():190] Started cpu monitoring
2025-07-13 21:16:29,874 INFO    SystemMonitor:2965321 [interfaces.py:start():190] Started disk monitoring
2025-07-13 21:16:29,875 INFO    SystemMonitor:2965321 [interfaces.py:start():190] Started gpu monitoring
2025-07-13 21:16:29,876 INFO    SystemMonitor:2965321 [interfaces.py:start():190] Started memory monitoring
2025-07-13 21:16:29,877 INFO    SystemMonitor:2965321 [interfaces.py:start():190] Started network monitoring
2025-07-13 21:16:29,909 DEBUG   HandlerThread:2965321 [system_info.py:probe():195] Probing system
2025-07-13 21:16:29,918 DEBUG   HandlerThread:2965321 [system_info.py:_probe_git():180] Probing git
2025-07-13 21:16:29,938 DEBUG   HandlerThread:2965321 [system_info.py:_probe_git():188] Probing git done
2025-07-13 21:16:29,938 DEBUG   HandlerThread:2965321 [system_info.py:probe():240] Probing system done
2025-07-13 21:16:29,938 DEBUG   HandlerThread:2965321 [system_monitor.py:probe():210] {'os': 'Linux-5.4.0-77-generic-x86_64-with-debian-bullseye-sid', 'python': '3.7.16', 'heartbeatAt': '2025-07-14T01:16:29.909210', 'startedAt': '2025-07-14T01:16:29.234627', 'docker': None, 'cuda': None, 'args': ('--local_rank=0', '--cfg', 'config/base_train.yaml', '--output', '/home-local2/akath.extra.nobkp/sereact', '--data-path', '/home-local2/akath.extra.nobkp/dl_challenge', '--batch-size', '2'), 'state': 'running', 'program': 'main.py', 'codePath': 'main.py', 'git': {'remote': 'https://github.com/Shrinidhibhat87/codingchallenge_sereact.git', 'commit': '1ad6d27318af49776c07190c53fde161bd471483'}, 'email': '<EMAIL>', 'root': '/home-local/akath.nobkp/sereact', 'host': 'lv3-32190', 'username': 'akath', 'executable': '/gel/usr/akath/.conda/envs/swin/bin/python', 'cpu_count': 6, 'cpu_count_logical': 12, 'cpu_freq': {'current': 1.4142499999999998, 'min': 1200.0, 'max': 4000.0}, 'cpu_freq_per_core': [{'current': 1.399, 'min': 1200.0, 'max': 4000.0}, {'current': 1.199, 'min': 1200.0, 'max': 4000.0}, {'current': 1.199, 'min': 1200.0, 'max': 4000.0}, {'current': 1.199, 'min': 1200.0, 'max': 4000.0}, {'current': 1.443, 'min': 1200.0, 'max': 4000.0}, {'current': 2.1, 'min': 1200.0, 'max': 4000.0}, {'current': 1.399, 'min': 1200.0, 'max': 4000.0}, {'current': 1.206, 'min': 1200.0, 'max': 4000.0}, {'current': 1.199, 'min': 1200.0, 'max': 4000.0}, {'current': 1.199, 'min': 1200.0, 'max': 4000.0}, {'current': 1.386, 'min': 1200.0, 'max': 4000.0}, {'current': 2.043, 'min': 1200.0, 'max': 4000.0}], 'disk': {'total': 111.2200813293457, 'used': 53.12038040161133}, 'gpu': 'TITAN Xp', 'gpu_count': 3, 'gpu_devices': [{'name': 'TITAN Xp', 'memory_total': 12787122176}, {'name': 'TITAN Xp', 'memory_total': 12788498432}, {'name': 'TITAN Xp', 'memory_total': 12788498432}], 'memory': {'total': 62.72977066040039}}
2025-07-13 21:16:29,938 INFO    HandlerThread:2965321 [system_monitor.py:probe():211] Finished collecting system info
2025-07-13 21:16:29,939 INFO    HandlerThread:2965321 [system_monitor.py:probe():214] Publishing system info
2025-07-13 21:16:29,939 DEBUG   HandlerThread:2965321 [system_info.py:_save_pip():52] Saving list of pip packages installed into the current environment
2025-07-13 21:16:29,940 DEBUG   HandlerThread:2965321 [system_info.py:_save_pip():67] Saving pip packages done
2025-07-13 21:16:29,940 DEBUG   HandlerThread:2965321 [system_info.py:_save_conda():75] Saving list of conda packages installed into the current environment
2025-07-13 21:16:30,792 INFO    Thread-12 :2965321 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/sereact/wandb/run-20250713_211629-gsu6rz9v/files/conda-environment.yaml
2025-07-13 21:16:30,793 INFO    Thread-12 :2965321 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/sereact/wandb/run-20250713_211629-gsu6rz9v/files/requirements.txt
2025-07-13 21:16:30,793 INFO    Thread-12 :2965321 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/sereact/wandb/run-20250713_211629-gsu6rz9v/files/wandb-summary.json
2025-07-13 21:16:31,880 INFO    cpu       :2965321 [interfaces.py:monitor():140] Process cpu has exited.
2025-07-13 21:16:31,880 INFO    memory    :2965321 [interfaces.py:monitor():140] Process proc.memory.rssMB has exited.
2025-07-13 21:16:31,881 DEBUG   SystemMonitor:2965321 [system_monitor.py:_start():159] Starting system metrics aggregation loop
2025-07-13 21:16:31,881 DEBUG   SystemMonitor:2965321 [system_monitor.py:_start():166] Finished system metrics aggregation loop
2025-07-13 21:16:31,881 DEBUG   SystemMonitor:2965321 [system_monitor.py:_start():170] Publishing last batch of metrics
2025-07-13 21:16:32,869 INFO    MainThread:2965321 [internal.py:handle_exit():76] Internal process exited
