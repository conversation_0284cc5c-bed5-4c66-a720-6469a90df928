name: swin
channels:
  - conda-forge
  - pytorch
  - anaconda
  - defaults
dependencies:
  - _libgcc_mutex=0.1=main
  - _openmp_mutex=5.1=1_gnu
  - backcall=0.2.0=pyhd3eb1b0_0
  - blas=1.0=mkl
  - bzip2=1.0.8=h7b6447c_0
  - ca-certificates=2025.2.25=h06a4308_0
  - certifi=2022.12.7=py37h06a4308_0
  - cudatoolkit=10.2.89=hfd86e86_1
  - debugpy=1.5.1=py37h295c915_0
  - decorator=5.1.1=pyhd3eb1b0_0
  - entrypoints=0.4=py37h06a4308_0
  - ffmpeg=4.3=hf484d3e_0
  - freetype=2.12.1=h4a9f257_0
  - geos=3.11.0=h27087fc_0
  - giflib=5.2.1=h5eee18b_3
  - gmp=6.2.1=h295c915_3
  - gmpy2=2.1.2=py37heeb90bb_0
  - gnutls=3.6.15=he1e5248_0
  - intel-openmp=2021.4.0=h06a4308_3561
  - ipykernel=6.15.2=py37h06a4308_0
  - ipython=7.31.1=py37h06a4308_1
  - jedi=0.18.1=py37h06a4308_1
  - jpeg=9b=h024ee3a_2
  - jupyter_client=7.4.8=py37h06a4308_0
  - jupyter_core=4.11.1=py37h06a4308_0
  - lame=3.100=h7b6447c_0
  - lcms2=2.12=h3be6417_0
  - ld_impl_linux-64=2.38=h1181459_1
  - libffi=3.4.4=h6a678d5_0
  - libgcc=15.1.0=h767d61c_3
  - libgcc-ng=15.1.0=h69a702a_3
  - libgomp=15.1.0=h767d61c_3
  - libiconv=1.16=h7f8727e_2
  - libidn2=2.3.4=h5eee18b_0
  - libpng=1.6.39=h5eee18b_0
  - libprotobuf=3.20.3=he621ea3_0
  - libsodium=1.0.18=h7b6447c_0
  - libstdcxx=15.1.0=h8f9b012_3
  - libstdcxx-ng=15.1.0=h4852527_3
  - libtasn1=4.19.0=h5eee18b_0
  - libtiff=4.1.0=h2733197_1
  - libunistring=0.9.10=h27cfd23_0
  - libuv=1.44.2=h5eee18b_0
  - libwebp=1.2.0=h89dd481_0
  - lz4-c=1.9.4=h6a678d5_0
  - matplotlib-inline=0.1.6=py37h06a4308_0
  - mkl=2021.4.0=h06a4308_640
  - mkl-service=2.4.0=py37h7f8727e_0
  - mkl_fft=1.3.1=py37hd3c417c_0
  - mkl_random=1.2.2=py37h51133e4_0
  - mpc=1.1.0=h10f8cd9_1
  - mpfr=4.0.2=hb69a4c5_1
  - mpmath=1.2.1=py37h06a4308_0
  - ncurses=6.4=h6a678d5_0
  - nest-asyncio=1.5.6=py37h06a4308_0
  - nettle=3.7.3=hbbd107a_1
  - ninja=1.10.2=h06a4308_5
  - ninja-base=1.10.2=hd09550d_5
  - numpy=1.21.5=py37h6c91a56_3
  - numpy-base=1.21.5=py37ha15fc14_3
  - openh264=2.1.1=h4ff587b_0
  - openssl=1.1.1w=h7f8727e_0
  - packaging=22.0=py37h06a4308_0
  - parso=0.8.3=pyhd3eb1b0_0
  - pexpect=4.8.0=pyhd3eb1b0_3
  - pickleshare=0.7.5=pyhd3eb1b0_1003
  - pillow=9.3.0=py37hace64e9_1
  - prompt-toolkit=3.0.36=py37h06a4308_0
  - protobuf=3.20.3=py37h6a678d5_0
  - ptyprocess=0.7.0=pyhd3eb1b0_2
  - pygments=2.11.2=pyhd3eb1b0_0
  - python=3.7.16=h7a1cb2a_0
  - python-dateutil=2.8.2=pyhd3eb1b0_0
  - python_abi=3.7=2_cp37m
  - pytorch=1.8.0=py3.7_cuda10.2_cudnn7.6.5_0
  - pyzmq=23.2.0=py37h6a678d5_0
  - readline=8.2=h5eee18b_0
  - setuptools=65.6.3=py37h06a4308_0
  - shapely=1.8.5=py37ha4e3bd1_0
  - six=1.16.0=pyhd3eb1b0_1
  - sqlite=3.41.2=h5eee18b_0
  - sympy=1.10.1=py37h06a4308_0
  - tensorboardx=2.2=pyhd3eb1b0_0
  - tk=8.6.12=h1ccaba5_0
  - torchaudio=0.8.0=py37
  - torchmetrics=0.10.3=pyhd8ed1ab_0
  - torchvision=0.9.0=py37_cu102
  - tornado=6.2=py37h5eee18b_0
  - traitlets=5.7.1=py37h06a4308_0
  - typing_extensions=4.3.0=py37h06a4308_0
  - wcwidth=0.2.5=pyhd3eb1b0_0
  - wheel=0.38.4=py37h06a4308_0
  - xz=5.4.2=h5eee18b_0
  - zeromq=4.3.4=h2531618_0
  - zlib=1.2.13=h5eee18b_0
  - zstd=1.4.9=haebb681_0
  - pip:
    - absl-py==1.4.0
    - addict==2.4.0
    - antlr4-python3-runtime==4.9.3
    - appdirs==1.4.4
    - astropy==4.3.1
    - attrs==24.2.0
    - black==23.3.0
    - cachetools==5.3.3
    - charset-normalizer==3.1.0
    - click==8.1.3
    - cloudpickle==2.2.1
    - comm==0.1.4
    - configargparse==1.7.1
    - contextlib2==21.6.0
    - cupy-cuda111==11.6.0
    - cycler==0.11.0
    - cython==3.0.10
    - dash==2.15.0
    - dash-core-components==2.0.0
    - dash-html-components==2.0.0
    - dash-table==5.0.0
    - detectron2==0.6
    - docker-pycreds==0.4.0
    - einops==0.6.1
    - equilib==0.0.1
    - faiss-gpu==1.7.2
    - fastjsonschema==2.21.1
    - fastrlock==0.8.2
    - flask==2.2.5
    - fonttools==4.38.0
    - fvcore==0.1.5.post20221221
    - gitdb==4.0.10
    - gitpython==3.1.31
    - google-auth==2.28.2
    - google-auth-oauthlib==0.4.6
    - grpcio==1.62.1
    - h5py==3.8.0
    - healpy==1.16.2
    - hydra-core==1.3.2
    - idna==3.4
    - imageio==2.31.2
    - importlib-metadata==6.6.0
    - importlib-resources==5.12.0
    - iopath==0.1.9
    - ipywidgets==8.1.7
    - itsdangerous==2.1.2
    - jinja2==3.1.6
    - joblib==1.3.2
    - jsonschema==4.17.3
    - jupyterlab-widgets==3.0.15
    - keopscore==2.1.2
    - kiwisolver==1.4.4
    - lpips==0.1.4
    - markdown==3.4.4
    - markupsafe==2.1.5
    - matplotlib==3.5.3
    - medpy==0.4.0
    - ml-collections==0.1.1
    - mypy-extensions==1.0.0
    - nbformat==5.7.0
    - networkx==2.6.3
    - nmslib==2.1.1
    - oauthlib==3.2.2
    - omegaconf==2.3.0
    - open3d==0.17.0
    - opencv-python==********
    - openexr==3.2.3
    - pandas==1.3.5
    - pathspec==0.11.2
    - pathtools==0.1.2
    - pip==24.0
    - pkgutil-resolve-name==1.3.10
    - platformdirs==4.0.0
    - plotly==5.18.0
    - portalocker==2.7.0
    - prettytable==3.7.0
    - psutil==5.9.5
    - py360convert==0.1.0
    - pyasn1==0.5.1
    - pyasn1-modules==0.3.0
    - pybind11==2.6.1
    - pycocotools==2.0.7
    - pyequilib==0.3.0
    - pyerfa==*******
    - pyinstrument==4.5.0
    - pykeops==2.1.2
    - pyparsing==3.0.9
    - pyquaternion==0.9.9
    - pyrsistent==0.19.3
    - pysolar==0.11
    - pytz==2024.1
    - pywavelets==1.3.0
    - pyyaml==6.0
    - requests==2.31.0
    - requests-oauthlib==1.4.0
    - retrying==1.4.0
    - rsa==4.9
    - scikit-image==0.19.3
    - scikit-learn==1.0.2
    - scipy==1.7.3
    - seaborn==0.12.2
    - sentry-sdk==1.25.1
    - setproctitle==1.3.2
    - simpleitk==2.2.1
    - skylibs==0.7.4
    - smmap==5.0.0
    - sphericaldistortion==0.1
    - tabulate==0.9.0
    - tenacity==8.2.3
    - tensorboard==2.11.2
    - tensorboard-data-server==0.6.1
    - tensorboard-plugin-wit==1.8.1
    - termcolor==1.1.0
    - threadpoolctl==3.1.0
    - tifffile==2021.11.2
    - timm==0.4.12
    - tomli==2.0.1
    - tqdm==4.65.0
    - typed-ast==1.5.5
    - typing-extensions==4.7.1
    - urllib3==2.0.3
    - wandb==0.15.4
    - werkzeug==2.2.3
    - widgetsnbextension==4.0.14
    - yacs==0.1.8
    - zipp==3.15.0
prefix: /gel/usr/akath/.conda/envs/swin
