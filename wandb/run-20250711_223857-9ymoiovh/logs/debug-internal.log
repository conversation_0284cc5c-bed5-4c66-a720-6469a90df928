2025-07-11 22:38:57,604 INFO    StreamThr :2842105 [internal.py:wandb_internal():89] W&B internal server running at pid: 2842105, started at: 2025-07-11 22:38:57.603601
2025-07-11 22:38:57,606 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status
2025-07-11 22:38:57,610 INFO    WriterThread:2842105 [datastore.py:open_for_write():85] open: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/run-9ymoiovh.wandb
2025-07-11 22:38:57,611 DEBUG   SenderThread:2842105 [sender.py:send():369] send: header
2025-07-11 22:38:57,627 DEBUG   SenderThread:2842105 [sender.py:send():369] send: run
2025-07-11 22:38:57,982 INFO    SenderThread:2842105 [dir_watcher.py:__init__():211] watching files in: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files
2025-07-11 22:38:57,983 INFO    SenderThread:2842105 [sender.py:_start_run_threads():1103] run started: 9ymoiovh with start time 1752287937.60372
2025-07-11 22:38:57,986 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:38:57,986 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:38:57,993 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: check_version
2025-07-11 22:38:57,993 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: check_version
2025-07-11 22:38:58,066 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: run_start
2025-07-11 22:38:58,073 DEBUG   HandlerThread:2842105 [system_info.py:__init__():31] System info init
2025-07-11 22:38:58,073 DEBUG   HandlerThread:2842105 [system_info.py:__init__():46] System info init done
2025-07-11 22:38:58,073 INFO    HandlerThread:2842105 [system_monitor.py:start():181] Starting system monitor
2025-07-11 22:38:58,073 INFO    SystemMonitor:2842105 [system_monitor.py:_start():145] Starting system asset monitoring threads
2025-07-11 22:38:58,074 INFO    HandlerThread:2842105 [system_monitor.py:probe():201] Collecting system info
2025-07-11 22:38:58,074 INFO    SystemMonitor:2842105 [interfaces.py:start():190] Started cpu monitoring
2025-07-11 22:38:58,075 INFO    SystemMonitor:2842105 [interfaces.py:start():190] Started disk monitoring
2025-07-11 22:38:58,076 INFO    SystemMonitor:2842105 [interfaces.py:start():190] Started gpu monitoring
2025-07-11 22:38:58,076 INFO    SystemMonitor:2842105 [interfaces.py:start():190] Started memory monitoring
2025-07-11 22:38:58,077 INFO    SystemMonitor:2842105 [interfaces.py:start():190] Started network monitoring
2025-07-11 22:38:58,097 DEBUG   HandlerThread:2842105 [system_info.py:probe():195] Probing system
2025-07-11 22:38:58,104 DEBUG   HandlerThread:2842105 [system_info.py:_probe_git():180] Probing git
2025-07-11 22:38:58,120 DEBUG   HandlerThread:2842105 [system_info.py:_probe_git():188] Probing git done
2025-07-11 22:38:58,120 DEBUG   HandlerThread:2842105 [system_info.py:probe():240] Probing system done
2025-07-11 22:38:58,120 DEBUG   HandlerThread:2842105 [system_monitor.py:probe():210] {'os': 'Linux-5.4.0-77-generic-x86_64-with-debian-bullseye-sid', 'python': '3.7.16', 'heartbeatAt': '2025-07-12T02:38:58.097479', 'startedAt': '2025-07-12T02:38:57.585849', 'docker': None, 'cuda': None, 'args': ('--local_rank=1', '--cfg', 'config/base_train.yaml', '--output', '/home-local2/akath.extra.nobkp/sereact', '--data-path', '/home-local2/akath.extra.nobkp/dl_challenge', '--batch-size', '2'), 'state': 'running', 'program': 'main.py', 'codePath': 'main.py', 'git': {'remote': 'https://github.com/Shrinidhibhat87/codingchallenge_sereact.git', 'commit': 'a3ff60e13dfced588b500c8a1de00f36fdf22d49'}, 'email': '<EMAIL>', 'root': '/home-local/akath.nobkp/codingchallenge_sereact', 'host': 'lv3-32190', 'username': 'akath', 'executable': '/gel/usr/akath/.conda/envs/swin/bin/python', 'cpu_count': 6, 'cpu_count_logical': 12, 'cpu_freq': {'current': 2.3565, 'min': 1200.0, 'max': 4000.0}, 'cpu_freq_per_core': [{'current': 1.703, 'min': 1200.0, 'max': 4000.0}, {'current': 1.422, 'min': 1200.0, 'max': 4000.0}, {'current': 1.811, 'min': 1200.0, 'max': 4000.0}, {'current': 3.27, 'min': 1200.0, 'max': 4000.0}, {'current': 2.205, 'min': 1200.0, 'max': 4000.0}, {'current': 3.589, 'min': 1200.0, 'max': 4000.0}, {'current': 1.908, 'min': 1200.0, 'max': 4000.0}, {'current': 1.301, 'min': 1200.0, 'max': 4000.0}, {'current': 1.964, 'min': 1200.0, 'max': 4000.0}, {'current': 3.013, 'min': 1200.0, 'max': 4000.0}, {'current': 2.525, 'min': 1200.0, 'max': 4000.0}, {'current': 3.567, 'min': 1200.0, 'max': 4000.0}], 'disk': {'total': 111.2200813293457, 'used': 53.244991302490234}, 'gpu': 'TITAN Xp', 'gpu_count': 3, 'gpu_devices': [{'name': 'TITAN Xp', 'memory_total': 12787122176}, {'name': 'TITAN Xp', 'memory_total': 12788498432}, {'name': 'TITAN Xp', 'memory_total': 12788498432}], 'memory': {'total': 62.72977066040039}}
2025-07-11 22:38:58,120 INFO    HandlerThread:2842105 [system_monitor.py:probe():211] Finished collecting system info
2025-07-11 22:38:58,120 INFO    HandlerThread:2842105 [system_monitor.py:probe():214] Publishing system info
2025-07-11 22:38:58,120 DEBUG   HandlerThread:2842105 [system_info.py:_save_pip():52] Saving list of pip packages installed into the current environment
2025-07-11 22:38:58,121 DEBUG   HandlerThread:2842105 [system_info.py:_save_pip():67] Saving pip packages done
2025-07-11 22:38:58,121 DEBUG   HandlerThread:2842105 [system_info.py:_save_conda():75] Saving list of conda packages installed into the current environment
2025-07-11 22:38:58,987 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/requirements.txt
2025-07-11 22:38:58,988 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/conda-environment.yaml
2025-07-11 22:38:58,988 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:39:00,727 DEBUG   HandlerThread:2842105 [system_info.py:_save_conda():86] Saving conda packages done
2025-07-11 22:39:00,729 INFO    HandlerThread:2842105 [system_monitor.py:probe():216] Finished publishing system info
2025-07-11 22:39:00,734 DEBUG   SenderThread:2842105 [sender.py:send():369] send: files
2025-07-11 22:39:00,735 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-metadata.json with policy now
2025-07-11 22:39:00,745 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:39:00,746 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:39:00,871 DEBUG   SenderThread:2842105 [sender.py:send():369] send: telemetry
2025-07-11 22:39:00,984 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/conda-environment.yaml
2025-07-11 22:39:00,984 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/output.log
2025-07-11 22:39:00,985 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-metadata.json
2025-07-11 22:39:01,079 INFO    wandb-upload_0:2842105 [upload_job.py:push():133] Uploaded file /tmp/tmpvvxn4npzwandb/xlhjgl8a-wandb-metadata.json
2025-07-11 22:39:02,874 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:39:02,986 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/output.log
2025-07-11 22:39:04,987 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/output.log
2025-07-11 22:39:08,691 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:39:08,821 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:39:08,823 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:39:08,823 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:39:08,831 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:39:08,988 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:39:11,613 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:39:11,614 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:39:11,614 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:39:11,615 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:39:11,989 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:39:14,456 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:39:14,457 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:39:14,458 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:39:14,458 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:39:14,458 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:39:14,991 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:39:15,744 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:39:15,745 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:39:17,120 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:39:17,121 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:39:17,122 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:39:17,122 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:39:17,992 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:39:19,581 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:39:19,583 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:39:19,583 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:39:19,583 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:39:19,584 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:39:19,993 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:39:22,585 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:39:22,585 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:39:22,586 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:39:22,586 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:39:22,994 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:39:24,588 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:39:25,008 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:39:25,010 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:39:25,010 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:39:25,010 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:39:25,995 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:39:27,614 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:39:27,615 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:39:27,615 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:39:27,616 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:39:27,996 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:39:29,622 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:39:29,996 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/config.yaml
2025-07-11 22:39:30,246 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:39:30,247 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:39:30,247 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:39:30,248 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:39:30,744 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:39:30,745 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:39:30,997 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:39:33,206 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:39:33,208 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:39:33,208 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:39:33,209 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:39:33,998 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:39:35,211 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:39:36,105 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:39:36,106 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:39:36,106 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:39:36,107 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:39:36,999 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:39:38,807 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:39:38,808 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:39:38,809 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:39:38,809 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:39:39,000 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:39:40,420 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:39:40,420 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:39:40,421 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:39:40,423 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:39:40,424 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:39:41,000 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:39:43,006 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:39:43,007 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:39:43,007 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:39:43,008 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:39:44,001 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:39:45,744 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:39:45,745 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:39:45,856 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:39:45,891 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:39:45,892 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:39:45,893 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:39:45,893 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:39:46,002 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:39:48,301 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:39:48,302 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:39:48,302 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:39:48,303 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:39:49,003 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:39:50,855 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:39:50,856 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:39:50,856 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:39:50,856 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:39:50,857 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:39:51,004 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:39:53,239 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:39:53,240 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:39:53,240 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:39:53,241 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:39:54,005 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:39:55,837 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:39:55,838 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:39:55,838 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:39:55,838 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:39:56,006 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:39:56,839 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:39:57,841 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:39:57,842 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:39:57,842 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:39:57,843 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:39:58,007 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:39:58,078 DEBUG   SystemMonitor:2842105 [system_monitor.py:_start():159] Starting system metrics aggregation loop
2025-07-11 22:39:58,080 DEBUG   SenderThread:2842105 [sender.py:send():369] send: stats
2025-07-11 22:40:00,324 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:00,324 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:40:00,325 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:00,325 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:00,744 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:40:00,745 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:40:01,008 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:40:01,882 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:40:03,089 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:03,090 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:40:03,090 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:03,091 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:04,009 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:40:05,660 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:05,661 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:40:05,661 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:05,664 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:06,010 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:40:07,440 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:07,441 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:40:07,441 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:07,442 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:40:07,442 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:08,010 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:40:09,797 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:09,798 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:40:09,798 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:09,798 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:10,011 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:40:11,187 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:11,188 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:40:11,188 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:11,189 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:12,012 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:40:12,599 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:12,599 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:40:12,600 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:12,602 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:40:12,602 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:13,012 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:40:15,746 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:40:15,747 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:40:17,853 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:40:22,853 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:40:25,559 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:25,560 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:40:25,561 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:25,561 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:26,016 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:40:27,017 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/output.log
2025-07-11 22:40:28,081 DEBUG   SenderThread:2842105 [sender.py:send():369] send: stats
2025-07-11 22:40:28,083 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:40:28,691 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:28,692 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:40:28,692 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:28,693 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:29,017 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:40:30,476 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:30,477 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:40:30,477 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:30,478 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:30,745 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:40:30,745 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:40:31,018 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:40:32,817 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:32,819 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:40:32,819 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:32,823 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:33,019 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:40:33,823 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:40:35,644 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:35,645 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:40:35,645 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:35,645 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:36,020 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:40:37,144 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:37,146 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:40:37,146 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:37,147 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:38,021 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:40:39,148 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:40:39,739 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:39,740 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:40:39,741 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:39,741 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:40,022 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:40:41,479 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:41,480 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:40:41,480 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:41,480 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:42,022 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:40:43,151 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:43,152 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:40:43,152 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:43,155 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:44,023 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:40:44,155 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:40:44,724 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:44,725 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:40:44,726 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:44,726 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:45,023 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:40:45,745 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:40:45,745 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:40:47,814 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:47,816 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:40:47,816 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:47,817 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:48,024 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:40:49,818 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:40:49,883 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:49,884 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:40:49,885 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:49,885 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:50,025 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:40:52,502 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:52,502 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:40:52,503 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:52,504 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:53,026 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:40:55,102 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:55,103 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:40:55,103 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:55,105 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:40:55,106 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:56,027 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:40:56,926 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:56,927 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:40:56,927 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:56,928 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:57,027 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:40:58,083 DEBUG   SenderThread:2842105 [sender.py:send():369] send: stats
2025-07-11 22:40:58,255 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:58,256 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:40:58,256 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:58,256 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:59,028 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:41:00,046 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:00,047 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:41:00,048 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:00,049 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:00,745 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:41:00,745 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:41:00,846 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:41:01,029 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:41:01,136 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:01,137 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:41:01,137 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:01,138 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:02,029 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:41:02,313 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:02,313 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:41:02,314 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:02,314 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:03,030 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:41:04,195 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:04,196 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:41:04,196 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:04,196 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:05,030 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:41:05,482 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:05,483 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:41:05,483 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:05,486 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:06,031 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:41:06,487 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:41:07,492 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:07,493 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:41:07,493 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:07,494 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:08,031 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:41:09,750 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:09,751 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:41:09,751 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:09,752 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:10,032 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:41:11,674 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:11,675 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:41:11,675 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:11,675 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:41:11,676 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:12,032 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:41:13,459 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:13,460 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:41:13,460 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:13,460 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:14,033 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:41:15,592 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:15,593 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:41:15,593 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:15,595 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:15,745 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:41:15,745 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:41:16,033 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:41:16,824 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:41:17,059 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:17,061 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:41:17,061 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:17,061 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:18,034 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:41:19,668 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:19,669 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:41:19,670 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:19,671 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:20,035 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:41:22,672 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:41:27,672 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:41:28,084 DEBUG   SenderThread:2842105 [sender.py:send():369] send: stats
2025-07-11 22:41:30,361 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:30,361 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:41:30,362 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:30,362 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:30,745 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:41:30,746 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:41:31,039 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/output.log
2025-07-11 22:41:31,039 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:41:32,904 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:41:33,480 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:33,482 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:41:33,482 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:33,482 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:34,040 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:41:34,681 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:34,682 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:41:34,682 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:34,682 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:35,041 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:41:36,255 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:36,256 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:41:36,256 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:36,257 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:37,042 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:41:38,257 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:41:38,267 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:38,268 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:41:38,268 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:38,270 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:39,042 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:41:41,068 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:41,069 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:41:41,070 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:41,070 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:42,043 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:41:42,632 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:42,633 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:41:42,634 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:42,634 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:43,044 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:41:43,635 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:41:45,087 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:45,088 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:41:45,088 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:45,089 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:45,746 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:41:45,747 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:41:46,045 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:41:46,681 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:46,682 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:41:46,683 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:46,683 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:47,045 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:41:48,106 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:48,107 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:41:48,107 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:48,108 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:49,046 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:41:49,108 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:41:49,569 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:49,570 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:41:49,570 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:49,575 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:50,046 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:41:51,002 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:51,003 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:41:51,003 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:51,003 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:51,047 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:41:52,806 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:52,806 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:41:52,807 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:52,807 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:53,047 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:41:54,041 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:54,042 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:41:54,042 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:54,043 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:54,048 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:41:55,043 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:41:55,843 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:55,844 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:41:55,844 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:55,844 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:56,049 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:41:57,719 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:57,720 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:41:57,720 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:57,720 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:58,049 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:41:58,086 DEBUG   SenderThread:2842105 [sender.py:send():369] send: stats
2025-07-11 22:41:59,725 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:59,726 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:41:59,726 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:59,728 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:00,050 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:42:00,729 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:42:00,746 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:42:00,747 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:42:00,900 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:00,902 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:42:00,903 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:00,904 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:01,050 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:42:02,106 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:02,107 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:42:02,107 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:02,108 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:03,051 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:42:03,335 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:03,336 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:42:03,336 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:03,337 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:04,052 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:42:05,807 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:05,807 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:42:05,808 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:05,808 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:42:05,809 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:06,052 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:42:07,943 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:07,944 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:42:07,944 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:07,944 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:08,053 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:42:09,557 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:09,558 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:42:09,558 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:09,559 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:10,054 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:42:11,559 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:11,560 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:42:11,560 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:42:11,561 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:11,563 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:12,055 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:42:13,671 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:13,672 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:42:13,672 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:13,673 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:14,055 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:42:15,233 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:15,234 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:42:15,234 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:15,235 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:15,745 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:42:15,746 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:42:16,056 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:42:16,711 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:16,712 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:42:16,713 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:16,713 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:42:16,713 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:17,056 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:42:18,548 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:18,549 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:42:18,549 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:18,549 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:19,057 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:42:22,550 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:42:27,551 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:42:28,087 DEBUG   SenderThread:2842105 [sender.py:send():369] send: stats
2025-07-11 22:42:28,947 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:28,949 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:42:28,949 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:28,949 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:29,061 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:42:30,746 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:42:30,747 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:42:31,750 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:31,751 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:42:31,752 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:31,752 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:32,061 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:42:32,753 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:42:33,125 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:33,127 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:42:33,127 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:33,128 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:34,062 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:42:34,806 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:34,807 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:42:34,807 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:34,808 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:35,063 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:42:36,729 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:36,730 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:42:36,730 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:36,731 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:37,063 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:42:38,337 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:38,337 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:42:38,338 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:38,340 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:42:38,340 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:39,064 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:42:39,690 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:39,691 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:42:39,692 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:39,692 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:40,064 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:42:41,642 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:41,643 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:42:41,643 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:41,644 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:42,065 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:42:43,474 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:43,475 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:42:43,475 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:43,475 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:42:43,476 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:44,065 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:42:45,064 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:45,065 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:42:45,065 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:45,066 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:45,066 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:42:45,746 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:42:45,746 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:42:47,919 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:47,920 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:42:47,920 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:47,920 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:48,067 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:42:48,920 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:42:49,335 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:49,336 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:42:49,336 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:49,338 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:50,068 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:42:51,165 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:51,166 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:42:51,166 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:51,167 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:52,069 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:42:52,740 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:52,741 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:42:52,741 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:52,741 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:53,069 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:42:54,259 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:54,260 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:42:54,261 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:54,261 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:42:54,262 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:55,069 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:42:55,693 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:55,694 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:42:55,694 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:55,694 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:56,070 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:42:56,915 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:56,917 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:42:56,917 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:56,918 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:57,070 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:42:58,089 DEBUG   SenderThread:2842105 [sender.py:send():369] send: stats
2025-07-11 22:42:58,887 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:58,888 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:42:58,888 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:58,889 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:59,071 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:42:59,890 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:43:00,746 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:43:00,746 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:43:01,386 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:01,387 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:43:01,387 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:01,391 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:02,072 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:43:02,859 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:02,860 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:43:02,860 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:02,860 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:03,072 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:43:04,794 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:04,795 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:43:04,795 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:04,795 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:05,073 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:43:05,796 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:43:06,323 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:06,324 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:43:06,325 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:06,325 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:07,073 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:43:07,935 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:07,936 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:43:07,937 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:07,937 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:08,073 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:43:09,320 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:09,321 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:43:09,321 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:09,321 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:10,074 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:43:10,840 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:10,841 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:43:10,841 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:10,841 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:43:10,842 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:11,074 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:43:12,138 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:12,147 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:43:12,148 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:12,150 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:13,075 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:43:13,591 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:13,592 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:43:13,592 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:13,593 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:14,075 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:43:15,672 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:15,673 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:43:15,673 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:15,673 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:15,800 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:43:15,800 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:43:15,918 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:43:16,076 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:43:20,918 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:43:25,919 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:43:26,052 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:26,053 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:43:26,053 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:26,053 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:26,078 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:43:28,090 DEBUG   SenderThread:2842105 [sender.py:send():369] send: stats
2025-07-11 22:43:28,406 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:28,407 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:43:28,407 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:28,408 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:29,079 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:43:30,212 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:30,213 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:43:30,214 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:30,214 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:30,772 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:43:30,772 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:43:31,080 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:43:31,682 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:31,683 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:43:31,684 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:31,684 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:43:31,685 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:32,080 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:43:33,288 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:33,289 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:43:33,289 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:33,290 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:34,081 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:43:35,423 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:35,424 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:43:35,424 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:35,425 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:36,081 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:43:37,336 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:37,337 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:43:37,337 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:37,339 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:43:37,340 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:38,082 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:43:39,047 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:39,048 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:43:39,048 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:39,049 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:39,082 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:43:40,679 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:40,680 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:43:40,680 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:40,681 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:41,083 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:43:42,231 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:42,232 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:43:42,232 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:42,233 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:43,084 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:43:43,233 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:43:43,807 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:43,808 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:43:43,808 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:43,809 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:44,084 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:43:45,590 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:45,591 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:43:45,591 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:45,591 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:45,772 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:43:45,772 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:43:46,085 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:43:47,244 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:47,245 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:43:47,245 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:47,246 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:48,085 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:43:48,246 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:43:48,724 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:48,725 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:43:48,725 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:48,728 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:49,086 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:43:50,379 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:50,380 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:43:50,380 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:50,381 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:51,087 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:43:52,335 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:52,336 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:43:52,336 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:52,337 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:53,087 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:43:53,337 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:43:54,214 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:54,215 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:43:54,216 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:54,216 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:55,088 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:43:55,717 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:55,718 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:43:55,719 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:55,719 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:56,088 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:43:57,290 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:57,291 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:43:57,291 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:57,292 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:58,089 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:43:58,092 DEBUG   SenderThread:2842105 [sender.py:send():369] send: stats
2025-07-11 22:43:58,968 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:58,969 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:43:58,969 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:58,970 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:43:58,970 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:59,090 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:44:00,772 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:44:00,773 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:44:00,824 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:00,897 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:44:00,897 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:00,898 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:01,090 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:44:01,978 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:01,979 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:44:01,979 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:01,979 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:02,091 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:44:03,665 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:03,666 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:44:03,666 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:03,666 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:04,092 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:44:04,667 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:44:05,409 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:05,411 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:44:05,411 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:05,411 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:06,092 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:44:06,944 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:06,945 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:44:06,945 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:06,946 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:07,093 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:44:09,041 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:09,041 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:44:09,041 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:09,042 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:09,094 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:44:10,042 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:44:10,654 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:10,655 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:44:10,655 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:10,656 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:11,094 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:44:13,199 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:13,200 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:44:13,200 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:13,201 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:14,095 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:44:15,202 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:44:15,773 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:44:15,774 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:44:20,850 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:44:23,520 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:23,522 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:44:23,523 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:23,528 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:24,099 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:44:25,769 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:25,770 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:44:25,770 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:25,771 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:26,099 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:44:26,772 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:44:27,336 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:27,337 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:44:27,337 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:27,338 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:28,093 DEBUG   SenderThread:2842105 [sender.py:send():369] send: stats
2025-07-11 22:44:28,100 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:44:29,213 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:29,214 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:44:29,214 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:29,215 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:30,101 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:44:30,772 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:44:30,773 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:44:30,943 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:30,944 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:44:30,945 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:30,945 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:31,101 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:44:31,946 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:44:32,335 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:32,336 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:44:32,337 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:32,337 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:33,102 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:44:33,877 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:33,879 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:44:33,879 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:33,879 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:34,102 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:44:35,876 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:35,877 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:44:35,877 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:35,878 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:36,103 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:44:37,113 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:37,114 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:44:37,115 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:37,115 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:44:37,116 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:38,103 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:44:38,708 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:38,709 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:44:38,710 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:38,711 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:39,104 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:44:40,236 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:40,238 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:44:40,239 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:40,239 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:41,104 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:44:41,627 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:41,628 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:44:41,628 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:41,629 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:42,105 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:44:42,629 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:44:43,384 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:43,385 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:44:43,385 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:43,386 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:44,105 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:44:44,863 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:44,864 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:44:44,865 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:44,865 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:45,106 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:44:45,772 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:44:45,773 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:44:46,719 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:46,720 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:44:46,720 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:46,721 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:47,106 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:44:47,722 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:44:47,895 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:47,896 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:44:47,896 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:47,897 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:48,107 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:44:49,489 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:49,490 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:44:49,490 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:49,491 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:50,107 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:44:50,992 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:50,993 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:44:50,993 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:50,994 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:51,108 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:44:52,570 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:52,571 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:44:52,572 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:52,573 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:53,109 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:44:53,574 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:44:54,220 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:54,221 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:44:54,222 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:54,224 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:55,110 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:44:56,246 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:56,247 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:44:56,247 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:56,248 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:57,110 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:44:57,791 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:57,792 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:44:57,793 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:57,793 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:58,094 DEBUG   SenderThread:2842105 [sender.py:send():369] send: stats
2025-07-11 22:44:58,111 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:44:59,095 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:44:59,243 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:59,244 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:44:59,244 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:59,245 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:00,111 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:45:00,772 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:45:00,773 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:45:00,907 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:00,929 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:45:00,929 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:00,930 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:01,112 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:45:02,303 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:02,303 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:45:02,304 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:02,304 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:03,112 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:45:03,611 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:03,612 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:45:03,612 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:03,612 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:04,113 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:45:04,613 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:45:05,756 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:05,758 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:45:05,758 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:05,758 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:06,113 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:45:07,216 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:07,216 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:45:07,217 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:07,217 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:08,114 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:45:10,218 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:45:15,219 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:45:15,773 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:45:15,774 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:45:17,505 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:17,506 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:45:17,506 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:17,507 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:18,117 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:45:19,700 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:19,701 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:45:19,701 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:19,702 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:20,118 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/output.log
2025-07-11 22:45:20,118 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:45:20,702 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:45:21,252 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:21,253 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:45:21,253 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:21,254 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:22,118 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:45:23,190 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:23,191 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:45:23,191 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:23,192 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:24,119 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:45:24,827 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:24,828 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:45:24,828 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:24,842 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:25,119 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:45:25,843 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:45:26,461 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:26,462 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:45:26,462 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:26,462 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:27,120 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:45:27,635 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:27,636 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:45:27,636 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:27,636 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:28,096 DEBUG   SenderThread:2842105 [sender.py:send():369] send: stats
2025-07-11 22:45:28,120 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:45:29,475 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:29,476 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:45:29,476 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:29,477 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:30,121 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:45:30,773 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:45:30,774 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:45:30,863 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:45:30,937 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:30,938 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:45:30,938 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:30,939 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:31,121 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:45:32,220 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:32,221 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:45:32,221 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:32,222 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:33,122 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:45:33,890 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:33,891 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:45:33,892 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:33,892 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:34,122 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:45:35,558 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:35,559 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:45:35,560 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:35,560 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:36,122 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:45:36,561 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:45:37,197 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:37,198 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:45:37,198 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:37,199 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:38,123 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:45:38,806 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:38,807 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:45:38,807 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:38,808 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:39,123 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:45:40,791 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:40,792 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:45:40,792 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:40,793 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:41,124 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:45:41,793 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:45:42,021 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:42,022 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:45:42,022 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:42,023 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:42,124 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:45:43,376 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:43,378 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:45:43,378 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:43,378 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:44,125 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:45:45,096 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:45,097 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:45:45,097 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:45,098 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:45,125 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:45:45,773 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:45:45,773 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:45:46,627 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:46,629 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:45:46,629 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:46,630 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:47,126 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:45:47,631 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:45:48,187 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:48,189 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:45:48,189 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:48,190 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:49,127 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:45:49,712 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:49,713 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:45:49,713 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:49,714 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:50,127 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:45:51,782 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:51,782 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:45:51,783 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:51,783 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:52,128 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:45:52,784 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:45:53,255 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:53,256 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:45:53,256 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:53,257 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:54,128 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:45:54,707 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:54,708 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:45:54,708 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:54,709 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:55,129 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:45:55,884 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:55,885 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:45:55,885 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:55,887 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:56,129 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:45:57,656 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:57,657 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:45:57,657 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:57,658 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:58,097 DEBUG   SenderThread:2842105 [sender.py:send():369] send: stats
2025-07-11 22:45:58,099 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:45:58,130 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:45:59,134 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:59,135 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:45:59,135 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:59,136 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:00,131 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:46:00,773 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:46:00,774 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:46:00,779 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:00,926 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:46:00,927 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:00,927 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:01,131 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:46:03,928 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:46:08,928 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:46:11,071 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:11,072 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:46:11,072 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:11,072 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:11,135 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:46:12,716 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:12,717 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:46:12,717 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:12,718 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:13,135 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:46:14,555 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:14,555 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:46:14,556 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:14,556 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:46:14,556 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:15,136 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:46:15,773 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:46:15,774 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:46:16,598 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:16,599 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:46:16,600 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:16,600 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:17,136 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:46:18,226 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:18,227 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:46:18,227 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:18,227 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:19,137 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:46:19,564 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:19,565 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:46:19,565 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:19,566 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:46:19,566 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:20,137 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:46:21,003 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:21,004 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:46:21,004 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:21,005 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:21,137 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:46:22,808 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:22,809 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:46:22,809 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:22,810 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:23,138 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:46:24,180 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:24,181 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:46:24,181 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:24,182 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:25,139 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:46:25,182 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:46:26,131 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:26,132 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:46:26,132 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:26,134 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:26,139 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:46:27,476 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:27,477 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:46:27,477 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:27,478 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:28,099 DEBUG   SenderThread:2842105 [sender.py:send():369] send: stats
2025-07-11 22:46:28,140 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:46:28,767 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:28,768 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:46:28,768 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:28,769 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:29,140 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:46:30,356 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:30,357 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:46:30,358 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:30,358 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:46:30,359 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:30,773 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:46:30,773 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:46:31,141 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:46:31,935 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:31,936 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:46:31,936 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:31,937 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:32,141 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:46:33,462 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:33,463 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:46:33,464 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:33,464 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:34,142 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:46:35,320 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:35,321 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:46:35,321 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:35,322 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:36,143 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:46:36,322 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:46:36,902 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:36,903 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:46:36,903 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:36,904 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:37,143 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:46:39,023 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:39,024 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:46:39,024 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:39,025 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:39,144 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:46:40,339 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:40,340 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:46:40,340 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:40,340 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:41,145 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:46:41,341 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:46:42,305 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:42,306 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:46:42,306 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:42,306 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:43,145 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:46:44,275 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:44,276 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:46:44,276 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:44,277 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:45,146 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:46:45,773 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:46:45,774 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:46:45,968 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:45,969 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:46:45,969 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:45,970 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:46,146 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:46:46,970 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:46:48,124 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:48,125 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:46:48,125 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:48,125 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:48,147 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:46:49,415 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:49,416 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:46:49,417 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:49,417 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:50,148 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:46:50,987 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:50,988 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:46:50,988 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:50,988 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:51,148 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:46:51,989 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:46:52,830 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:52,831 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:46:52,831 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:52,831 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:53,149 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:46:54,187 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:54,188 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:46:54,188 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:54,188 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:55,150 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:46:55,515 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:55,520 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:46:55,520 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:55,521 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:56,150 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:46:57,521 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:46:58,101 DEBUG   SenderThread:2842105 [sender.py:send():369] send: stats
2025-07-11 22:47:00,774 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:47:00,774 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:47:02,931 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:47:05,831 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:47:05,832 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:47:05,832 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:47:05,833 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:47:06,152 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:47:07,658 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:47:07,659 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:47:07,659 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:47:07,660 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:47:08,153 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:47:08,660 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:47:09,511 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:47:09,512 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:47:09,512 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:47:09,513 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:47:10,154 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:47:11,072 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:47:11,073 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:47:11,074 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:47:11,074 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:47:11,154 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:47:12,916 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:47:12,917 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:47:12,918 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:47:12,918 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:47:13,154 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:47:13,919 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:47:14,977 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:47:14,978 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:47:14,978 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:47:14,978 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:47:15,155 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:47:15,773 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:47:15,774 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:47:17,144 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:47:17,145 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:47:17,145 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:47:17,146 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:47:17,156 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:47:18,604 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:47:18,605 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:47:18,605 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:47:18,606 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:47:19,157 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:47:19,607 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:47:20,168 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:47:20,168 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:47:20,169 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:47:20,169 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:47:21,157 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:47:21,734 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:47:21,735 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:47:21,735 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:47:21,736 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:47:22,157 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:47:23,295 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:47:23,296 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:47:23,296 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:47:23,297 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:47:24,158 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:47:24,993 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:47:24,994 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:47:24,994 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:47:24,995 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:47:24,995 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:47:25,159 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:47:26,486 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:47:26,487 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:47:26,487 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:47:26,487 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:47:27,160 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:47:27,859 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:47:27,860 DEBUG   SenderThread:2842105 [sender.py:send():369] send: history
2025-07-11 22:47:27,860 DEBUG   SenderThread:2842105 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:47:27,861 INFO    SenderThread:2842105 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:47:28,102 DEBUG   SenderThread:2842105 [sender.py:send():369] send: stats
2025-07-11 22:47:28,160 INFO    Thread-12 :2842105 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/files/wandb-summary.json
2025-07-11 22:47:30,103 DEBUG   HandlerThread:2842105 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:47:30,239 INFO    memory    :2842105 [interfaces.py:monitor():140] Process proc.memory.rssMB has exited.
2025-07-11 22:47:30,239 DEBUG   SystemMonitor:2842105 [system_monitor.py:_start():166] Finished system metrics aggregation loop
2025-07-11 22:47:30,240 DEBUG   SystemMonitor:2842105 [system_monitor.py:_start():170] Publishing last batch of metrics
2025-07-11 22:47:30,241 DEBUG   SenderThread:2842105 [sender.py:send():369] send: stats
2025-07-11 22:47:31,127 WARNING StreamThr :2842105 [internal.py:is_dead():415] Internal process exiting, parent pid 2842065 disappeared
2025-07-11 22:47:31,127 ERROR   StreamThr :2842105 [internal.py:wandb_internal():152] Internal process shutdown.
2025-07-11 22:47:31,145 INFO    MainThread:2842105 [internal.py:handle_exit():76] Internal process exited
2025-07-11 22:47:31,241 INFO    HandlerThread:2842105 [handler.py:finish():854] shutting down handler
2025-07-11 22:47:31,241 INFO    HandlerThread:2842105 [system_monitor.py:finish():190] Stopping system monitor
2025-07-11 22:47:31,241 INFO    HandlerThread:2842105 [interfaces.py:finish():202] Joined cpu monitor
2025-07-11 22:47:31,241 INFO    WriterThread:2842105 [datastore.py:close():298] close: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-9ymoiovh/run-9ymoiovh.wandb
2025-07-11 22:47:31,241 INFO    HandlerThread:2842105 [interfaces.py:finish():202] Joined disk monitor
2025-07-11 22:47:31,242 INFO    SenderThread:2842105 [sender.py:finish():1526] shutting down sender
