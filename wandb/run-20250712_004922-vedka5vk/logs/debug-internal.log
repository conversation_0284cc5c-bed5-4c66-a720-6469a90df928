2025-07-12 00:49:22,473 INFO    StreamThr :2859590 [internal.py:wandb_internal():89] W&B internal server running at pid: 2859590, started at: 2025-07-12 00:49:22.472751
2025-07-12 00:49:22,474 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: status
2025-07-12 00:49:22,479 INFO    WriterThread:2859590 [datastore.py:open_for_write():85] open: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/run-vedka5vk.wandb
2025-07-12 00:49:22,479 DEBUG   SenderThread:2859590 [sender.py:send():369] send: header
2025-07-12 00:49:22,498 DEBUG   SenderThread:2859590 [sender.py:send():369] send: run
2025-07-12 00:49:22,697 INFO    SenderThread:2859590 [dir_watcher.py:__init__():211] watching files in: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files
2025-07-12 00:49:22,697 INFO    SenderThread:2859590 [sender.py:_start_run_threads():1103] run started: vedka5vk with start time 1752295762.474489
2025-07-12 00:49:22,699 DEBUG   SenderThread:2859590 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:49:22,699 INFO    SenderThread:2859590 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:49:22,708 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: check_version
2025-07-12 00:49:22,709 DEBUG   SenderThread:2859590 [sender.py:send_request():396] send_request: check_version
2025-07-12 00:49:22,810 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: run_start
2025-07-12 00:49:22,814 DEBUG   HandlerThread:2859590 [system_info.py:__init__():31] System info init
2025-07-12 00:49:22,814 DEBUG   HandlerThread:2859590 [system_info.py:__init__():46] System info init done
2025-07-12 00:49:22,814 INFO    HandlerThread:2859590 [system_monitor.py:start():181] Starting system monitor
2025-07-12 00:49:22,815 INFO    SystemMonitor:2859590 [system_monitor.py:_start():145] Starting system asset monitoring threads
2025-07-12 00:49:22,815 INFO    HandlerThread:2859590 [system_monitor.py:probe():201] Collecting system info
2025-07-12 00:49:22,816 INFO    SystemMonitor:2859590 [interfaces.py:start():190] Started cpu monitoring
2025-07-12 00:49:22,816 INFO    SystemMonitor:2859590 [interfaces.py:start():190] Started disk monitoring
2025-07-12 00:49:22,817 INFO    SystemMonitor:2859590 [interfaces.py:start():190] Started gpu monitoring
2025-07-12 00:49:22,818 INFO    SystemMonitor:2859590 [interfaces.py:start():190] Started memory monitoring
2025-07-12 00:49:22,819 INFO    SystemMonitor:2859590 [interfaces.py:start():190] Started network monitoring
2025-07-12 00:49:22,848 DEBUG   HandlerThread:2859590 [system_info.py:probe():195] Probing system
2025-07-12 00:49:22,855 DEBUG   HandlerThread:2859590 [system_info.py:_probe_git():180] Probing git
2025-07-12 00:49:22,869 DEBUG   HandlerThread:2859590 [system_info.py:_probe_git():188] Probing git done
2025-07-12 00:49:22,869 DEBUG   HandlerThread:2859590 [system_info.py:probe():240] Probing system done
2025-07-12 00:49:22,869 DEBUG   HandlerThread:2859590 [system_monitor.py:probe():210] {'os': 'Linux-5.4.0-77-generic-x86_64-with-debian-bullseye-sid', 'python': '3.7.16', 'heartbeatAt': '2025-07-12T04:49:22.848580', 'startedAt': '2025-07-12T04:49:22.461173', 'docker': None, 'cuda': None, 'args': ('--local_rank=0', '--cfg', 'config/base_train.yaml', '--output', '/home-local2/akath.extra.nobkp/sereact', '--data-path', '/home-local2/akath.extra.nobkp/dl_challenge', '--batch-size', '1'), 'state': 'running', 'program': 'main.py', 'codePath': 'main.py', 'git': {'remote': 'https://github.com/Shrinidhibhat87/codingchallenge_sereact.git', 'commit': 'a3ff60e13dfced588b500c8a1de00f36fdf22d49'}, 'email': '<EMAIL>', 'root': '/home-local/akath.nobkp/codingchallenge_sereact', 'host': 'lv3-32190', 'username': 'akath', 'executable': '/gel/usr/akath/.conda/envs/swin/bin/python', 'cpu_count': 6, 'cpu_count_logical': 12, 'cpu_freq': {'current': 1.8120833333333335, 'min': 1200.0, 'max': 4000.0}, 'cpu_freq_per_core': [{'current': 1.568, 'min': 1200.0, 'max': 4000.0}, {'current': 2.355, 'min': 1200.0, 'max': 4000.0}, {'current': 1.202, 'min': 1200.0, 'max': 4000.0}, {'current': 2.301, 'min': 1200.0, 'max': 4000.0}, {'current': 1.199, 'min': 1200.0, 'max': 4000.0}, {'current': 2.266, 'min': 1200.0, 'max': 4000.0}, {'current': 1.507, 'min': 1200.0, 'max': 4000.0}, {'current': 2.046, 'min': 1200.0, 'max': 4000.0}, {'current': 1.199, 'min': 1200.0, 'max': 4000.0}, {'current': 2.682, 'min': 1200.0, 'max': 4000.0}, {'current': 1.199, 'min': 1200.0, 'max': 4000.0}, {'current': 2.221, 'min': 1200.0, 'max': 4000.0}], 'disk': {'total': 111.2200813293457, 'used': 53.24767303466797}, 'gpu': 'TITAN Xp', 'gpu_count': 3, 'gpu_devices': [{'name': 'TITAN Xp', 'memory_total': 12787122176}, {'name': 'TITAN Xp', 'memory_total': 12788498432}, {'name': 'TITAN Xp', 'memory_total': 12788498432}], 'memory': {'total': 62.72977066040039}}
2025-07-12 00:49:22,870 INFO    HandlerThread:2859590 [system_monitor.py:probe():211] Finished collecting system info
2025-07-12 00:49:22,870 INFO    HandlerThread:2859590 [system_monitor.py:probe():214] Publishing system info
2025-07-12 00:49:22,870 DEBUG   HandlerThread:2859590 [system_info.py:_save_pip():52] Saving list of pip packages installed into the current environment
2025-07-12 00:49:22,871 DEBUG   HandlerThread:2859590 [system_info.py:_save_pip():67] Saving pip packages done
2025-07-12 00:49:22,871 DEBUG   HandlerThread:2859590 [system_info.py:_save_conda():75] Saving list of conda packages installed into the current environment
2025-07-12 00:49:23,700 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/wandb-summary.json
2025-07-12 00:49:23,700 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/requirements.txt
2025-07-12 00:49:23,701 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/conda-environment.yaml
2025-07-12 00:49:25,386 DEBUG   HandlerThread:2859590 [system_info.py:_save_conda():86] Saving conda packages done
2025-07-12 00:49:25,388 INFO    HandlerThread:2859590 [system_monitor.py:probe():216] Finished publishing system info
2025-07-12 00:49:25,400 DEBUG   SenderThread:2859590 [sender.py:send():369] send: files
2025-07-12 00:49:25,400 INFO    SenderThread:2859590 [sender.py:_save_file():1354] saving file wandb-metadata.json with policy now
2025-07-12 00:49:25,406 DEBUG   SenderThread:2859590 [sender.py:send():369] send: telemetry
2025-07-12 00:49:25,418 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: stop_status
2025-07-12 00:49:25,418 DEBUG   SenderThread:2859590 [sender.py:send_request():396] send_request: stop_status
2025-07-12 00:49:25,699 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/conda-environment.yaml
2025-07-12 00:49:25,699 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/output.log
2025-07-12 00:49:25,699 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/wandb-metadata.json
2025-07-12 00:49:25,777 INFO    wandb-upload_0:2859590 [upload_job.py:push():133] Uploaded file /tmp/tmpu8yjl0c0wandb/26wq0r67-wandb-metadata.json
2025-07-12 00:49:27,556 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: status_report
2025-07-12 00:49:27,699 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/output.log
2025-07-12 00:49:29,700 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/output.log
2025-07-12 00:49:31,598 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:49:31,600 DEBUG   SenderThread:2859590 [sender.py:send():369] send: history
2025-07-12 00:49:31,601 DEBUG   SenderThread:2859590 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:49:31,603 INFO    SenderThread:2859590 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:49:31,701 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/wandb-summary.json
2025-07-12 00:49:32,604 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: status_report
2025-07-12 00:49:33,701 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/output.log
2025-07-12 00:49:34,213 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:49:34,214 DEBUG   SenderThread:2859590 [sender.py:send():369] send: history
2025-07-12 00:49:34,215 DEBUG   SenderThread:2859590 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:49:34,219 INFO    SenderThread:2859590 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:49:34,702 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/wandb-summary.json
2025-07-12 00:49:36,005 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:49:36,006 DEBUG   SenderThread:2859590 [sender.py:send():369] send: history
2025-07-12 00:49:36,007 DEBUG   SenderThread:2859590 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:49:36,007 INFO    SenderThread:2859590 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:49:36,702 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/wandb-summary.json
2025-07-12 00:49:38,010 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: status_report
2025-07-12 00:49:38,141 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:49:38,143 DEBUG   SenderThread:2859590 [sender.py:send():369] send: history
2025-07-12 00:49:38,143 DEBUG   SenderThread:2859590 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:49:38,144 INFO    SenderThread:2859590 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:49:38,703 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/wandb-summary.json
2025-07-12 00:49:40,268 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:49:40,269 DEBUG   SenderThread:2859590 [sender.py:send():369] send: history
2025-07-12 00:49:40,270 DEBUG   SenderThread:2859590 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:49:40,271 INFO    SenderThread:2859590 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:49:40,407 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: stop_status
2025-07-12 00:49:40,407 DEBUG   SenderThread:2859590 [sender.py:send_request():396] send_request: stop_status
2025-07-12 00:49:40,704 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/wandb-summary.json
2025-07-12 00:49:42,391 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:49:42,392 DEBUG   SenderThread:2859590 [sender.py:send():369] send: history
2025-07-12 00:49:42,392 DEBUG   SenderThread:2859590 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:49:42,392 INFO    SenderThread:2859590 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:49:42,705 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/wandb-summary.json
2025-07-12 00:49:43,393 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: status_report
2025-07-12 00:49:44,106 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:49:44,107 DEBUG   SenderThread:2859590 [sender.py:send():369] send: history
2025-07-12 00:49:44,108 DEBUG   SenderThread:2859590 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:49:44,109 INFO    SenderThread:2859590 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:49:44,706 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/wandb-summary.json
2025-07-12 00:49:46,092 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:49:46,093 DEBUG   SenderThread:2859590 [sender.py:send():369] send: history
2025-07-12 00:49:46,093 DEBUG   SenderThread:2859590 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:49:46,096 INFO    SenderThread:2859590 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:49:46,706 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/wandb-summary.json
2025-07-12 00:49:48,162 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:49:48,163 DEBUG   SenderThread:2859590 [sender.py:send():369] send: history
2025-07-12 00:49:48,163 DEBUG   SenderThread:2859590 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:49:48,167 INFO    SenderThread:2859590 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:49:48,707 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/wandb-summary.json
2025-07-12 00:49:49,168 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: status_report
2025-07-12 00:49:50,142 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:49:50,143 DEBUG   SenderThread:2859590 [sender.py:send():369] send: history
2025-07-12 00:49:50,143 DEBUG   SenderThread:2859590 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:49:50,143 INFO    SenderThread:2859590 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:49:50,708 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/wandb-summary.json
2025-07-12 00:49:52,610 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:49:52,611 DEBUG   SenderThread:2859590 [sender.py:send():369] send: history
2025-07-12 00:49:52,611 DEBUG   SenderThread:2859590 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:49:52,612 INFO    SenderThread:2859590 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:49:52,708 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/wandb-summary.json
2025-07-12 00:49:53,709 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/output.log
2025-07-12 00:49:54,488 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:49:54,489 DEBUG   SenderThread:2859590 [sender.py:send():369] send: history
2025-07-12 00:49:54,495 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: status_report
2025-07-12 00:49:54,570 DEBUG   SenderThread:2859590 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:49:54,571 INFO    SenderThread:2859590 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:49:54,709 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/wandb-summary.json
2025-07-12 00:49:54,709 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/config.yaml
2025-07-12 00:49:55,406 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: stop_status
2025-07-12 00:49:55,406 DEBUG   SenderThread:2859590 [sender.py:send_request():396] send_request: stop_status
2025-07-12 00:49:56,635 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:49:56,636 DEBUG   SenderThread:2859590 [sender.py:send():369] send: history
2025-07-12 00:49:56,636 DEBUG   SenderThread:2859590 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:49:56,636 INFO    SenderThread:2859590 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:49:56,710 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/wandb-summary.json
2025-07-12 00:49:58,274 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:49:58,275 DEBUG   SenderThread:2859590 [sender.py:send():369] send: history
2025-07-12 00:49:58,275 DEBUG   SenderThread:2859590 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:49:58,276 INFO    SenderThread:2859590 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:49:58,710 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/wandb-summary.json
2025-07-12 00:49:59,918 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:49:59,919 DEBUG   SenderThread:2859590 [sender.py:send():369] send: history
2025-07-12 00:49:59,920 DEBUG   SenderThread:2859590 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:49:59,922 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: status_report
2025-07-12 00:49:59,923 INFO    SenderThread:2859590 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:00,663 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:00,664 DEBUG   SenderThread:2859590 [sender.py:send():369] send: history
2025-07-12 00:50:00,665 DEBUG   SenderThread:2859590 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:00,665 INFO    SenderThread:2859590 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:00,711 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/wandb-summary.json
2025-07-12 00:50:02,755 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:02,757 DEBUG   SenderThread:2859590 [sender.py:send():369] send: history
2025-07-12 00:50:02,757 DEBUG   SenderThread:2859590 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:02,757 INFO    SenderThread:2859590 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:03,712 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/wandb-summary.json
2025-07-12 00:50:04,397 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:04,398 DEBUG   SenderThread:2859590 [sender.py:send():369] send: history
2025-07-12 00:50:04,398 DEBUG   SenderThread:2859590 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:04,398 INFO    SenderThread:2859590 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:04,712 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/wandb-summary.json
2025-07-12 00:50:05,399 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: status_report
2025-07-12 00:50:06,733 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:06,735 DEBUG   SenderThread:2859590 [sender.py:send():369] send: history
2025-07-12 00:50:06,735 DEBUG   SenderThread:2859590 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:06,736 INFO    SenderThread:2859590 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:07,713 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/wandb-summary.json
2025-07-12 00:50:08,700 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:08,701 DEBUG   SenderThread:2859590 [sender.py:send():369] send: history
2025-07-12 00:50:08,701 DEBUG   SenderThread:2859590 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:08,704 INFO    SenderThread:2859590 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:08,714 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/wandb-summary.json
2025-07-12 00:50:10,406 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: stop_status
2025-07-12 00:50:10,406 DEBUG   SenderThread:2859590 [sender.py:send_request():396] send_request: stop_status
2025-07-12 00:50:10,471 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: status_report
2025-07-12 00:50:10,915 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:10,917 DEBUG   SenderThread:2859590 [sender.py:send():369] send: history
2025-07-12 00:50:10,917 DEBUG   SenderThread:2859590 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:10,919 INFO    SenderThread:2859590 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:11,715 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/wandb-summary.json
2025-07-12 00:50:11,715 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/output.log
2025-07-12 00:50:12,853 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:12,854 DEBUG   SenderThread:2859590 [sender.py:send():369] send: history
2025-07-12 00:50:12,854 DEBUG   SenderThread:2859590 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:12,855 INFO    SenderThread:2859590 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:13,716 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/wandb-summary.json
2025-07-12 00:50:13,960 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:13,962 DEBUG   SenderThread:2859590 [sender.py:send():369] send: history
2025-07-12 00:50:13,962 DEBUG   SenderThread:2859590 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:13,965 INFO    SenderThread:2859590 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:14,716 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/wandb-summary.json
2025-07-12 00:50:15,626 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:15,627 DEBUG   SenderThread:2859590 [sender.py:send():369] send: history
2025-07-12 00:50:15,627 DEBUG   SenderThread:2859590 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:15,627 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: status_report
2025-07-12 00:50:15,627 INFO    SenderThread:2859590 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:15,716 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/wandb-summary.json
2025-07-12 00:50:17,566 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:17,567 DEBUG   SenderThread:2859590 [sender.py:send():369] send: history
2025-07-12 00:50:17,567 DEBUG   SenderThread:2859590 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:17,570 INFO    SenderThread:2859590 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:17,717 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/wandb-summary.json
2025-07-12 00:50:19,537 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:19,539 DEBUG   SenderThread:2859590 [sender.py:send():369] send: history
2025-07-12 00:50:19,539 DEBUG   SenderThread:2859590 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:19,539 INFO    SenderThread:2859590 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:19,717 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/wandb-summary.json
2025-07-12 00:50:20,641 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:20,642 DEBUG   SenderThread:2859590 [sender.py:send():369] send: history
2025-07-12 00:50:20,643 DEBUG   SenderThread:2859590 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:20,644 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: status_report
2025-07-12 00:50:20,646 INFO    SenderThread:2859590 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:20,718 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/wandb-summary.json
2025-07-12 00:50:22,753 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:22,754 DEBUG   SenderThread:2859590 [sender.py:send():369] send: history
2025-07-12 00:50:22,755 DEBUG   SenderThread:2859590 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:22,755 INFO    SenderThread:2859590 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:22,819 DEBUG   SystemMonitor:2859590 [system_monitor.py:_start():159] Starting system metrics aggregation loop
2025-07-12 00:50:22,821 DEBUG   SenderThread:2859590 [sender.py:send():369] send: stats
2025-07-12 00:50:23,719 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/wandb-summary.json
2025-07-12 00:50:25,060 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:25,062 DEBUG   SenderThread:2859590 [sender.py:send():369] send: history
2025-07-12 00:50:25,062 DEBUG   SenderThread:2859590 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:25,063 INFO    SenderThread:2859590 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:25,408 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: stop_status
2025-07-12 00:50:25,409 DEBUG   SenderThread:2859590 [sender.py:send_request():396] send_request: stop_status
2025-07-12 00:50:25,719 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/wandb-summary.json
2025-07-12 00:50:26,192 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:26,193 DEBUG   SenderThread:2859590 [sender.py:send():369] send: history
2025-07-12 00:50:26,193 DEBUG   SenderThread:2859590 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:26,193 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: status_report
2025-07-12 00:50:26,194 INFO    SenderThread:2859590 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:26,720 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/wandb-summary.json
2025-07-12 00:50:27,237 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:27,239 DEBUG   SenderThread:2859590 [sender.py:send():369] send: history
2025-07-12 00:50:27,239 DEBUG   SenderThread:2859590 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:27,240 INFO    SenderThread:2859590 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:27,720 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/wandb-summary.json
2025-07-12 00:50:29,378 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:29,379 DEBUG   SenderThread:2859590 [sender.py:send():369] send: history
2025-07-12 00:50:29,379 DEBUG   SenderThread:2859590 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:29,380 INFO    SenderThread:2859590 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:29,721 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/wandb-summary.json
2025-07-12 00:50:29,721 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/output.log
2025-07-12 00:50:31,013 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:31,015 DEBUG   SenderThread:2859590 [sender.py:send():369] send: history
2025-07-12 00:50:31,015 DEBUG   SenderThread:2859590 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:31,019 INFO    SenderThread:2859590 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:31,721 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/wandb-summary.json
2025-07-12 00:50:32,020 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: status_report
2025-07-12 00:50:33,092 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:33,093 DEBUG   SenderThread:2859590 [sender.py:send():369] send: history
2025-07-12 00:50:33,093 DEBUG   SenderThread:2859590 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:33,094 INFO    SenderThread:2859590 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:33,722 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/wandb-summary.json
2025-07-12 00:50:35,061 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:35,062 DEBUG   SenderThread:2859590 [sender.py:send():369] send: history
2025-07-12 00:50:35,062 DEBUG   SenderThread:2859590 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:35,063 INFO    SenderThread:2859590 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:35,723 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/wandb-summary.json
2025-07-12 00:50:36,809 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:36,810 DEBUG   SenderThread:2859590 [sender.py:send():369] send: history
2025-07-12 00:50:36,811 DEBUG   SenderThread:2859590 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:36,812 INFO    SenderThread:2859590 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:37,723 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/wandb-summary.json
2025-07-12 00:50:37,813 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: status_report
2025-07-12 00:50:38,732 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:38,732 DEBUG   SenderThread:2859590 [sender.py:send():369] send: history
2025-07-12 00:50:38,733 DEBUG   SenderThread:2859590 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:38,733 INFO    SenderThread:2859590 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:39,724 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/wandb-summary.json
2025-07-12 00:50:40,406 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: stop_status
2025-07-12 00:50:40,407 DEBUG   SenderThread:2859590 [sender.py:send_request():396] send_request: stop_status
2025-07-12 00:50:40,653 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:40,654 DEBUG   SenderThread:2859590 [sender.py:send():369] send: history
2025-07-12 00:50:40,655 DEBUG   SenderThread:2859590 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:40,655 INFO    SenderThread:2859590 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:40,724 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/wandb-summary.json
2025-07-12 00:50:41,759 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:41,760 DEBUG   SenderThread:2859590 [sender.py:send():369] send: history
2025-07-12 00:50:41,760 DEBUG   SenderThread:2859590 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:41,763 INFO    SenderThread:2859590 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:42,725 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/wandb-summary.json
2025-07-12 00:50:42,839 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:42,841 DEBUG   SenderThread:2859590 [sender.py:send():369] send: history
2025-07-12 00:50:42,841 DEBUG   SenderThread:2859590 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:42,841 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: status_report
2025-07-12 00:50:42,842 INFO    SenderThread:2859590 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:43,704 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:43,708 DEBUG   SenderThread:2859590 [sender.py:send():369] send: history
2025-07-12 00:50:43,708 DEBUG   SenderThread:2859590 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:43,709 INFO    SenderThread:2859590 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:43,725 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/wandb-summary.json
2025-07-12 00:50:45,652 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:45,653 DEBUG   SenderThread:2859590 [sender.py:send():369] send: history
2025-07-12 00:50:45,653 DEBUG   SenderThread:2859590 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:45,654 INFO    SenderThread:2859590 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:45,726 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/wandb-summary.json
2025-07-12 00:50:45,726 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/output.log
2025-07-12 00:50:46,866 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:46,868 DEBUG   SenderThread:2859590 [sender.py:send():369] send: history
2025-07-12 00:50:46,868 DEBUG   SenderThread:2859590 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:46,869 INFO    SenderThread:2859590 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:47,727 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/wandb-summary.json
2025-07-12 00:50:47,869 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: status_report
2025-07-12 00:50:48,950 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:48,951 DEBUG   SenderThread:2859590 [sender.py:send():369] send: history
2025-07-12 00:50:48,951 DEBUG   SenderThread:2859590 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:48,952 INFO    SenderThread:2859590 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:49,728 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/wandb-summary.json
2025-07-12 00:50:49,827 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:49,828 DEBUG   SenderThread:2859590 [sender.py:send():369] send: history
2025-07-12 00:50:49,828 DEBUG   SenderThread:2859590 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:49,829 INFO    SenderThread:2859590 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:50,728 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/wandb-summary.json
2025-07-12 00:50:50,797 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:50,798 DEBUG   SenderThread:2859590 [sender.py:send():369] send: history
2025-07-12 00:50:50,798 DEBUG   SenderThread:2859590 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:50,799 INFO    SenderThread:2859590 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:51,728 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/wandb-summary.json
2025-07-12 00:50:52,670 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:52,671 DEBUG   SenderThread:2859590 [sender.py:send():369] send: history
2025-07-12 00:50:52,671 DEBUG   SenderThread:2859590 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:52,675 INFO    SenderThread:2859590 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:52,729 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/wandb-summary.json
2025-07-12 00:50:52,822 DEBUG   SenderThread:2859590 [sender.py:send():369] send: stats
2025-07-12 00:50:53,576 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:53,577 DEBUG   SenderThread:2859590 [sender.py:send():369] send: history
2025-07-12 00:50:53,577 DEBUG   SenderThread:2859590 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:53,578 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: status_report
2025-07-12 00:50:53,578 INFO    SenderThread:2859590 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:53,729 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/wandb-summary.json
2025-07-12 00:50:54,652 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:54,653 DEBUG   SenderThread:2859590 [sender.py:send():369] send: history
2025-07-12 00:50:54,653 DEBUG   SenderThread:2859590 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:54,654 INFO    SenderThread:2859590 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:54,729 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/wandb-summary.json
2025-07-12 00:50:55,406 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: stop_status
2025-07-12 00:50:55,407 DEBUG   SenderThread:2859590 [sender.py:send_request():396] send_request: stop_status
2025-07-12 00:50:56,012 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:56,013 DEBUG   SenderThread:2859590 [sender.py:send():369] send: history
2025-07-12 00:50:56,013 DEBUG   SenderThread:2859590 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:56,014 INFO    SenderThread:2859590 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:56,723 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:56,724 DEBUG   SenderThread:2859590 [sender.py:send():369] send: history
2025-07-12 00:50:56,724 DEBUG   SenderThread:2859590 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:56,724 INFO    SenderThread:2859590 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:56,730 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/wandb-summary.json
2025-07-12 00:50:57,732 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/output.log
2025-07-12 00:50:57,992 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:57,993 DEBUG   SenderThread:2859590 [sender.py:send():369] send: history
2025-07-12 00:50:57,994 DEBUG   SenderThread:2859590 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:57,994 INFO    SenderThread:2859590 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:58,733 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/wandb-summary.json
2025-07-12 00:50:58,785 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:58,786 DEBUG   SenderThread:2859590 [sender.py:send():369] send: history
2025-07-12 00:50:58,786 DEBUG   SenderThread:2859590 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:58,786 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: status_report
2025-07-12 00:50:58,787 INFO    SenderThread:2859590 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:59,733 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/wandb-summary.json
2025-07-12 00:51:00,492 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:51:00,493 DEBUG   SenderThread:2859590 [sender.py:send():369] send: history
2025-07-12 00:51:00,493 DEBUG   SenderThread:2859590 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:51:00,493 INFO    SenderThread:2859590 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:51:00,733 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/wandb-summary.json
2025-07-12 00:51:02,734 INFO    Thread-12 :2859590 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-vedka5vk/files/output.log
2025-07-12 00:51:04,547 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: status_report
2025-07-12 00:51:09,547 DEBUG   HandlerThread:2859590 [handler.py:handle_request():144] handle_request: status_report
2025-07-12 00:51:10,861 INFO    memory    :2859590 [interfaces.py:monitor():140] Process proc.memory.rssMB has exited.
2025-07-12 00:51:10,861 DEBUG   SystemMonitor:2859590 [system_monitor.py:_start():166] Finished system metrics aggregation loop
2025-07-12 00:51:10,862 DEBUG   SystemMonitor:2859590 [system_monitor.py:_start():170] Publishing last batch of metrics
2025-07-12 00:51:10,865 DEBUG   SenderThread:2859590 [sender.py:send():369] send: stats
2025-07-12 00:51:11,789 INFO    MainThread:2859590 [internal.py:handle_exit():76] Internal process exited
