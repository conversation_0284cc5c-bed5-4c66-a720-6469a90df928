=> merge config from config/base_train.yaml
RANK and WORLD_SIZE in environ: 0/3
[32m[2025-07-11 22:48:19 3DDETR.yaml][33m(main.py 549)[39m: INFO Full config saved to /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/config.json
[32m[2025-07-11 22:48:19 3DDETR.yaml][33m(main.py 552)[39m: INFO AMP_ENABLE: true
TAG: default
amp_opt_level: ''
base:
- ''
data:
  augment: false
  batch_size: 2
  cache_mode: part
  data_path: /home-local2/akath.extra.nobkp/dl_challenge
  dataset: Sereact_dataset
  debug: false
  num_workers: 4
  pin_memory: true
  transform: null
  zip_mode: false
eval_mode: false
local_rank: 0
loss:
  matcher_costs:
    cost_box_corners: 1.0
    giou: 5.0
    l1: 2.0
  weights:
    box_corners: 1.0
    giou: 1.0
    size: 1.0
    size_reg: 1.0
model:
  decoder:
    dim: 256
    dropout: 0.1
    ffn_dim: 256
    nhead: 4
    num_layers: 3
  encoder:
    activation: relu
    dim: 256
    dropout: 0.1
    ffn_dim: 128
    nheads: 4
    num_layers: 3
    preencoder_npoints: 2048
    type: vanilla
    use_color: false
  export_model: false
  mlp_dropout: 0.3
  name: 3DDETR.yaml
  num_angular_bins: 12
  num_queries: 256
  position_embedding: fourier
  pretrained: null
  pretrained_weights_path: /home/<USER>/Coding/Pre_trained_Weights/3detr/scannet_ep1080.pth
  resume: ''
  training: true
  unit_test: false
output: /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123
print_freq: 10
save_freq: 1
seed: 40
tag: '123'
train:
  accumulation_steps: 1
  auto_resume: false
  base_lr: 0.0001
  clip_grad: 0.1
  filter_biases_wd: true
  final_lr: 1.0e-06
  lr_scheduler: cosine
  max_epoch: 200
  start_epoch: 0
  unit_test_epoch: 100
  use_checkpoint: false
  warm_lr: 5.0e-06
  warm_lr_epochs: 9
  weight_decay: 0.01
unit_test: false
[32m[2025-07-11 22:48:19 3DDETR.yaml][33m(main.py 553)[39m: INFO {"cfg": "config/base_train.yaml", "opts": null, "batch_size": 2, "data_path": "/home-local2/akath.extra.nobkp/dl_challenge", "pretrained": null, "resume": null, "accumulation_steps": null, "use_checkpoint": false, "disable_amp": false, "amp_opt_level": null, "output": "/home-local2/akath.extra.nobkp/sereact", "tag": null, "eval": false, "unit_test": false, "base_lr": null, "local_rank": 0}
local rank 0 / global rank 0 successfully build train dataset
local rank 0 / global rank 0 successfully build val dataset
[32m[2025-07-11 22:48:19 3DDETR.yaml][33m(main.py 102)[39m: INFO Model3DDETR(
  (pre_encoder): PointnetSAModuleVotes(
    (grouper): QueryAndGroup()
    (mlp_module): SharedMLP(
      (layer0): Conv2d(
        (conv): Conv2d(3, 64, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn): BatchNorm2d(
          (bn): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        )
        (activation): ReLU(inplace=True)
      )
      (layer1): Conv2d(
        (conv): Conv2d(64, 128, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn): BatchNorm2d(
          (bn): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        )
        (activation): ReLU(inplace=True)
      )
      (layer2): Conv2d(
        (conv): Conv2d(128, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn): BatchNorm2d(
          (bn): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        )
        (activation): ReLU(inplace=True)
      )
    )
  )
  (encoder): TransformerEncoder(
    (layers): ModuleList(
      (0): TransformerEncoderLayer(
        attn_dr=0.1
        (self_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (linear1): Linear(in_features=256, out_features=128, bias=True)
        (dropout): Dropout(p=0.1, inplace=False)
        (linear2): Linear(in_features=128, out_features=256, bias=True)
        (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout2): Dropout(p=0.1, inplace=False)
        (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout1): Dropout(p=0.1, inplace=False)
        (activation): ReLU()
      )
      (1): TransformerEncoderLayer(
        attn_dr=0.1
        (self_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (linear1): Linear(in_features=256, out_features=128, bias=True)
        (dropout): Dropout(p=0.1, inplace=False)
        (linear2): Linear(in_features=128, out_features=256, bias=True)
        (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout2): Dropout(p=0.1, inplace=False)
        (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout1): Dropout(p=0.1, inplace=False)
        (activation): ReLU()
      )
      (2): TransformerEncoderLayer(
        attn_dr=0.1
        (self_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (linear1): Linear(in_features=256, out_features=128, bias=True)
        (dropout): Dropout(p=0.1, inplace=False)
        (linear2): Linear(in_features=128, out_features=256, bias=True)
        (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout2): Dropout(p=0.1, inplace=False)
        (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout1): Dropout(p=0.1, inplace=False)
        (activation): ReLU()
      )
    )
  )
  (encoder_decoder_projection): GenericMLP(
    (layers): Sequential(
      (0): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
      (1): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (2): ReLU()
      (3): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
      (4): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (5): ReLU()
    )
  )
  (positional_embedding): PositionEmbeddingCoordsSine(type=fourier, scale=6.283185307179586, normalize=True, gaussB_shape=torch.Size([3, 128]), gaussB_sum=-17.944507598876953)
  (query_projection): GenericMLP(
    (layers): Sequential(
      (0): Conv1d(256, 256, kernel_size=(1,), stride=(1,))
      (1): ReLU()
      (2): Conv1d(256, 256, kernel_size=(1,), stride=(1,))
      (3): ReLU()
    )
  )
  (decoder): TransformerDecoder(
    (layers): ModuleList(
      (0): TransformerDecoderLayer(
        (self_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (multihead_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm3): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout1): Dropout(p=0.1, inplace=False)
        (dropout2): Dropout(p=0.1, inplace=False)
        (dropout3): Dropout(p=0.1, inplace=False)
        (linear1): Linear(in_features=256, out_features=256, bias=True)
        (dropout): Dropout(p=0.1, inplace=False)
        (linear2): Linear(in_features=256, out_features=256, bias=True)
        (activation): ReLU()
      )
      (1): TransformerDecoderLayer(
        (self_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (multihead_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm3): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout1): Dropout(p=0.1, inplace=False)
        (dropout2): Dropout(p=0.1, inplace=False)
        (dropout3): Dropout(p=0.1, inplace=False)
        (linear1): Linear(in_features=256, out_features=256, bias=True)
        (dropout): Dropout(p=0.1, inplace=False)
        (linear2): Linear(in_features=256, out_features=256, bias=True)
        (activation): ReLU()
      )
      (2): TransformerDecoderLayer(
        (self_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (multihead_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm3): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout1): Dropout(p=0.1, inplace=False)
        (dropout2): Dropout(p=0.1, inplace=False)
        (dropout3): Dropout(p=0.1, inplace=False)
        (linear1): Linear(in_features=256, out_features=256, bias=True)
        (dropout): Dropout(p=0.1, inplace=False)
        (linear2): Linear(in_features=256, out_features=256, bias=True)
        (activation): ReLU()
      )
    )
    (norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (mlp_heads): ModuleDict(
    (center_head): GenericMLP(
      (layers): Sequential(
        (0): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
        (1): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU()
        (3): Dropout(p=0.3, inplace=False)
        (4): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
        (5): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (6): ReLU()
        (7): Dropout(p=0.3, inplace=False)
        (8): Conv1d(256, 3, kernel_size=(1,), stride=(1,))
      )
    )
    (size_head): GenericMLP(
      (layers): Sequential(
        (0): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
        (1): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU()
        (3): Dropout(p=0.3, inplace=False)
        (4): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
        (5): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (6): ReLU()
        (7): Dropout(p=0.3, inplace=False)
        (8): Conv1d(256, 3, kernel_size=(1,), stride=(1,))
      )
    )
    (angle_cls_head): GenericMLP(
      (layers): Sequential(
        (0): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
        (1): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU()
        (3): Dropout(p=0.3, inplace=False)
        (4): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
        (5): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (6): ReLU()
        (7): Dropout(p=0.3, inplace=False)
        (8): Conv1d(256, 12, kernel_size=(1,), stride=(1,))
      )
    )
    (angle_residual_head): GenericMLP(
      (layers): Sequential(
        (0): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
        (1): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU()
        (3): Dropout(p=0.3, inplace=False)
        (4): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
        (5): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (6): ReLU()
        (7): Dropout(p=0.3, inplace=False)
        (8): Conv1d(256, 12, kernel_size=(1,), stride=(1,))
      )
    )
  )
)
[32m[2025-07-11 22:48:19 3DDETR.yaml][33m(main.py 104)[39m: INFO number of params: 3811038
[32m[2025-07-11 22:48:19 3DDETR.yaml][33m(main.py 153)[39m: INFO Start training
[32m[2025-07-11 22:48:24 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [0/200][0/27]	eta 0:02:17 lr 0.000100	time 5.1074 (5.1074)	loss 19.7473 (19.7473)	miou 0.1350	grad_norm 142.4439 (142.4439)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 22:48:51 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [0/200][10/27]	eta 0:00:50 lr 0.000099	time 2.8742 (2.9414)	loss 13.9520 (17.9870)	miou 0.1909	grad_norm 57.7440 (108.1210)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 22:49:15 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [0/200][20/27]	eta 0:00:18 lr 0.000097	time 2.4993 (2.6944)	loss 9.4408 (16.3835)	miou 0.1901	grad_norm 37.4478 (80.8683)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 22:49:27 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 0 training takes 0:01:08
[32m[2025-07-11 22:49:28 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
0
[32m[2025-07-11 22:49:39 3DDETR.yaml][33m(utils_help.py 202)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saving......
[32m[2025-07-11 22:49:40 3DDETR.yaml][33m(utils_help.py 204)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saved !!!
[32m[2025-07-11 22:49:40 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.0676%
[32m[2025-07-11 22:49:40 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.0676%
[32m[2025-07-11 22:49:44 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [1/200][0/27]	eta 0:01:40 lr 0.000095	time 3.7376 (3.7376)	loss 14.6092 (14.6092)	miou 0.0682	grad_norm 119.9638 (119.9638)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 22:50:05 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [1/200][10/27]	eta 0:00:38 lr 0.000091	time 2.0751 (2.2693)	loss 9.8620 (12.5737)	miou 0.1078	grad_norm 59.3548 (55.3008)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 22:50:22 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [1/200][20/27]	eta 0:00:14 lr 0.000086	time 2.0047 (2.0279)	loss 14.8838 (12.7448)	miou 0.1270	grad_norm 58.7684 (49.5945)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 22:50:35 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 1 training takes 0:00:54
[32m[2025-07-11 22:50:35 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
1
[32m[2025-07-11 22:50:45 3DDETR.yaml][33m(utils_help.py 202)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saving......
[32m[2025-07-11 22:50:45 3DDETR.yaml][33m(utils_help.py 204)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saved !!!
[32m[2025-07-11 22:50:45 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1144%
[32m[2025-07-11 22:50:45 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.1144%
[32m[2025-07-11 22:50:48 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [2/200][0/27]	eta 0:01:21 lr 0.000082	time 3.0265 (3.0265)	loss 11.8751 (11.8751)	miou 0.1120	grad_norm 27.8654 (27.8654)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 22:51:06 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [2/200][10/27]	eta 0:00:31 lr 0.000076	time 1.4264 (1.8676)	loss 9.8015 (9.8847)	miou 0.1396	grad_norm 30.0424 (31.5103)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 22:51:23 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [2/200][20/27]	eta 0:00:12 lr 0.000069	time 2.1143 (1.7834)	loss 18.1846 (11.5171)	miou 0.1473	grad_norm 46.8845 (38.2067)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 22:51:34 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 2 training takes 0:00:48
[32m[2025-07-11 22:51:34 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
2
[32m[2025-07-11 22:51:44 3DDETR.yaml][33m(utils_help.py 202)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saving......
[32m[2025-07-11 22:51:44 3DDETR.yaml][33m(utils_help.py 204)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saved !!!
[32m[2025-07-11 22:51:44 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1546%
[32m[2025-07-11 22:51:44 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.1546%
[32m[2025-07-11 22:51:47 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [3/200][0/27]	eta 0:01:10 lr 0.000064	time 2.5966 (2.5966)	loss 8.4443 (8.4443)	miou 0.1563	grad_norm 37.8212 (37.8212)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 22:52:04 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [3/200][10/27]	eta 0:00:31 lr 0.000056	time 1.3904 (1.8266)	loss 9.0787 (11.2563)	miou 0.1655	grad_norm 36.4897 (34.7239)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 22:52:21 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [3/200][20/27]	eta 0:00:12 lr 0.000048	time 1.5287 (1.7703)	loss 14.5943 (11.0847)	miou 0.1752	grad_norm 53.4640 (34.6960)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 22:52:31 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 3 training takes 0:00:46
[32m[2025-07-11 22:52:31 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
3
[32m[2025-07-11 22:52:41 3DDETR.yaml][33m(utils_help.py 202)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saving......
[32m[2025-07-11 22:52:41 3DDETR.yaml][33m(utils_help.py 204)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saved !!!
[32m[2025-07-11 22:52:41 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1620%
[32m[2025-07-11 22:52:41 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.1620%
[32m[2025-07-11 22:52:44 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [4/200][0/27]	eta 0:01:10 lr 0.000043	time 2.6272 (2.6272)	loss 11.2463 (11.2463)	miou 0.1604	grad_norm 30.6972 (30.6972)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 22:53:01 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [4/200][10/27]	eta 0:00:30 lr 0.000035	time 1.7586 (1.7937)	loss 10.3656 (9.6044)	miou 0.1857	grad_norm 42.6122 (33.4427)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 22:53:17 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [4/200][20/27]	eta 0:00:12 lr 0.000028	time 1.1531 (1.7172)	loss 10.9948 (10.2496)	miou 0.1907	grad_norm 22.6750 (33.7137)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 22:53:28 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 4 training takes 0:00:47
[32m[2025-07-11 22:53:28 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 22:53:38 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1359%
[32m[2025-07-11 22:53:38 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.1620%
[32m[2025-07-11 22:53:41 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [5/200][0/27]	eta 0:01:13 lr 0.000023	time 2.7047 (2.7047)	loss 7.3837 (7.3837)	miou 0.1370	grad_norm 88.7931 (88.7931)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 22:53:57 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [5/200][10/27]	eta 0:00:28 lr 0.000017	time 1.4000 (1.6933)	loss 9.8432 (11.1613)	miou 0.1555	grad_norm 31.1154 (41.0319)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 22:54:13 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [5/200][20/27]	eta 0:00:11 lr 0.000011	time 1.5593 (1.6491)	loss 13.4619 (10.5946)	miou 0.1684	grad_norm 28.1654 (39.7771)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 22:54:22 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 5 training takes 0:00:44
[32m[2025-07-11 22:54:22 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 22:54:32 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1538%
[32m[2025-07-11 22:54:32 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.1620%
[32m[2025-07-11 22:54:34 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [6/200][0/27]	eta 0:00:52 lr 0.000008	time 1.9574 (1.9574)	loss 11.0874 (11.0874)	miou 0.1587	grad_norm 45.5395 (45.5395)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 22:54:50 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [6/200][10/27]	eta 0:00:27 lr 0.000004	time 1.6894 (1.6183)	loss 11.5376 (10.5218)	miou 0.1753	grad_norm 20.1476 (35.1389)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 22:55:06 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [6/200][20/27]	eta 0:00:11 lr 0.000002	time 1.9499 (1.6140)	loss 8.1992 (10.4473)	miou 0.1831	grad_norm 22.9651 (36.8848)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 22:55:15 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 6 training takes 0:00:43
[32m[2025-07-11 22:55:15 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
6
[32m[2025-07-11 22:55:25 3DDETR.yaml][33m(utils_help.py 202)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saving......
[32m[2025-07-11 22:55:26 3DDETR.yaml][33m(utils_help.py 204)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saved !!!
[32m[2025-07-11 22:55:26 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1694%
[32m[2025-07-11 22:55:26 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.1694%
[32m[2025-07-11 22:55:27 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [7/200][0/27]	eta 0:00:42 lr 0.000001	time 1.5793 (1.5793)	loss 10.8663 (10.8663)	miou 0.1744	grad_norm 24.0222 (24.0222)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 22:55:43 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [7/200][10/27]	eta 0:00:27 lr 0.000000	time 1.2885 (1.5883)	loss 11.0290 (10.8272)	miou 0.1784	grad_norm 28.6806 (29.3462)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 22:56:00 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [7/200][20/27]	eta 0:00:11 lr 0.000001	time 1.6845 (1.6544)	loss 9.8367 (10.5746)	miou 0.1782	grad_norm 26.8513 (32.7962)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 22:56:10 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 7 training takes 0:00:44
[32m[2025-07-11 22:56:10 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 22:56:20 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1446%
[32m[2025-07-11 22:56:20 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.1694%
[32m[2025-07-11 22:56:22 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [8/200][0/27]	eta 0:00:53 lr 0.000002	time 1.9776 (1.9776)	loss 8.4051 (8.4051)	miou 0.1459	grad_norm 48.2847 (48.2847)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 22:56:39 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [8/200][10/27]	eta 0:00:29 lr 0.000004	time 1.7286 (1.7551)	loss 8.3095 (10.0332)	miou 0.1508	grad_norm 39.4573 (35.9139)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 22:56:54 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [8/200][20/27]	eta 0:00:11 lr 0.000008	time 1.4126 (1.6317)	loss 9.6301 (10.2901)	miou 0.1508	grad_norm 18.5648 (35.8596)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 22:57:04 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 8 training takes 0:00:43
[32m[2025-07-11 22:57:04 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 22:57:14 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1631%
[32m[2025-07-11 22:57:14 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.1694%
[32m[2025-07-11 22:57:16 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [9/200][0/27]	eta 0:00:53 lr 0.000011	time 1.9844 (1.9844)	loss 11.2724 (11.2724)	miou 0.1664	grad_norm 22.8809 (22.8809)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 22:57:33 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [9/200][10/27]	eta 0:00:29 lr 0.000017	time 1.7063 (1.7221)	loss 7.4829 (10.7684)	miou 0.1746	grad_norm 28.1768 (30.1904)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 22:57:50 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [9/200][20/27]	eta 0:00:12 lr 0.000023	time 2.0167 (1.7158)	loss 10.3035 (10.2247)	miou 0.1753	grad_norm 35.1419 (30.4191)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 22:57:58 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 9 training takes 0:00:44
[32m[2025-07-11 22:57:58 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 22:58:09 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1639%
[32m[2025-07-11 22:58:09 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.1694%
[32m[2025-07-11 22:58:11 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [10/200][0/27]	eta 0:01:04 lr 0.000028	time 2.3913 (2.3913)	loss 7.9509 (7.9509)	miou 0.1825	grad_norm 17.8256 (17.8256)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 22:58:28 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [10/200][10/27]	eta 0:00:30 lr 0.000035	time 1.4885 (1.7763)	loss 12.7787 (10.2683)	miou 0.1862	grad_norm 18.0480 (27.3320)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 22:58:44 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [10/200][20/27]	eta 0:00:11 lr 0.000043	time 1.8295 (1.7097)	loss 11.5031 (10.2903)	miou 0.1841	grad_norm 42.5300 (35.5128)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 22:58:54 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 10 training takes 0:00:45
[32m[2025-07-11 22:58:54 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
10
[32m[2025-07-11 22:59:04 3DDETR.yaml][33m(utils_help.py 202)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saving......
[32m[2025-07-11 22:59:05 3DDETR.yaml][33m(utils_help.py 204)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saved !!!
[32m[2025-07-11 22:59:05 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1748%
[32m[2025-07-11 22:59:05 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.1748%
[32m[2025-07-11 22:59:07 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [11/200][0/27]	eta 0:00:53 lr 0.000048	time 1.9755 (1.9755)	loss 10.6541 (10.6541)	miou 0.1738	grad_norm 30.3235 (30.3235)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 22:59:21 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [11/200][10/27]	eta 0:00:25 lr 0.000056	time 1.8348 (1.5164)	loss 9.4777 (10.4642)	miou 0.1725	grad_norm 29.3224 (37.5427)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 22:59:37 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [11/200][20/27]	eta 0:00:10 lr 0.000064	time 1.1525 (1.5344)	loss 10.2808 (10.0009)	miou 0.1778	grad_norm 30.1735 (32.8522)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 22:59:47 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 11 training takes 0:00:42
[32m[2025-07-11 22:59:47 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
11
[32m[2025-07-11 22:59:57 3DDETR.yaml][33m(utils_help.py 202)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saving......
[32m[2025-07-11 22:59:57 3DDETR.yaml][33m(utils_help.py 204)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saved !!!
[32m[2025-07-11 22:59:57 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1880%
[32m[2025-07-11 22:59:57 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.1880%
[32m[2025-07-11 22:59:59 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [12/200][0/27]	eta 0:01:09 lr 0.000069	time 2.5757 (2.5757)	loss 9.4079 (9.4079)	miou 0.1928	grad_norm 34.9038 (34.9038)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:00:15 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [12/200][10/27]	eta 0:00:27 lr 0.000076	time 1.1505 (1.6465)	loss 7.2470 (9.8570)	miou 0.1787	grad_norm 27.3124 (27.6160)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:00:32 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [12/200][20/27]	eta 0:00:11 lr 0.000082	time 2.0697 (1.6758)	loss 7.4016 (10.0819)	miou 0.1887	grad_norm 31.9429 (32.2251)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:00:41 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 12 training takes 0:00:44
[32m[2025-07-11 23:00:41 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 23:00:51 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1278%
[32m[2025-07-11 23:00:51 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.1880%
[32m[2025-07-11 23:00:54 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [13/200][0/27]	eta 0:01:04 lr 0.000086	time 2.4017 (2.4017)	loss 10.4831 (10.4831)	miou 0.1349	grad_norm 20.6008 (20.6008)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:01:10 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [13/200][10/27]	eta 0:00:28 lr 0.000091	time 1.3387 (1.6649)	loss 9.4448 (9.5515)	miou 0.1747	grad_norm 22.7365 (30.9558)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:01:26 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [13/200][20/27]	eta 0:00:11 lr 0.000095	time 1.2943 (1.6628)	loss 7.8327 (9.0281)	miou 0.1850	grad_norm 26.4975 (30.2266)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:01:36 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 13 training takes 0:00:44
[32m[2025-07-11 23:01:36 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 23:01:46 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1798%
[32m[2025-07-11 23:01:46 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.1880%
[32m[2025-07-11 23:01:48 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [14/200][0/27]	eta 0:01:02 lr 0.000097	time 2.3237 (2.3237)	loss 10.5572 (10.5572)	miou 0.1688	grad_norm 23.4378 (23.4378)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:02:04 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [14/200][10/27]	eta 0:00:27 lr 0.000099	time 1.7603 (1.6355)	loss 11.2950 (9.4184)	miou 0.1767	grad_norm 21.4281 (27.2440)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:02:20 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [14/200][20/27]	eta 0:00:11 lr 0.000100	time 1.4940 (1.6385)	loss 8.8826 (9.7559)	miou 0.1826	grad_norm 15.6326 (27.4039)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:02:30 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 14 training takes 0:00:44
[32m[2025-07-11 23:02:30 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 23:02:40 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1848%
[32m[2025-07-11 23:02:40 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.1880%
[32m[2025-07-11 23:02:43 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [15/200][0/27]	eta 0:01:03 lr 0.000100	time 2.3548 (2.3548)	loss 10.2081 (10.2081)	miou 0.1799	grad_norm 27.2926 (27.2926)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:02:59 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [15/200][10/27]	eta 0:00:28 lr 0.000098	time 1.4473 (1.6982)	loss 10.1304 (9.4924)	miou 0.1836	grad_norm 22.8147 (33.6269)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:03:16 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [15/200][20/27]	eta 0:00:11 lr 0.000096	time 1.6825 (1.6755)	loss 7.0410 (8.9370)	miou 0.1906	grad_norm 32.2386 (31.2462)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:03:25 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 15 training takes 0:00:44
[32m[2025-07-11 23:03:25 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 23:03:35 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1171%
[32m[2025-07-11 23:03:35 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.1880%
[32m[2025-07-11 23:03:37 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [16/200][0/27]	eta 0:00:55 lr 0.000093	time 2.0638 (2.0638)	loss 12.2097 (12.2097)	miou 0.1219	grad_norm 42.9576 (42.9576)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:03:53 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [16/200][10/27]	eta 0:00:27 lr 0.000089	time 1.2748 (1.6451)	loss 10.0561 (9.3860)	miou 0.1643	grad_norm 22.7406 (26.8695)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:04:09 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [16/200][20/27]	eta 0:00:11 lr 0.000084	time 1.8143 (1.6249)	loss 9.0975 (9.2755)	miou 0.1819	grad_norm 14.2271 (27.4025)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:04:18 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 16 training takes 0:00:43
[32m[2025-07-11 23:04:18 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 23:04:28 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1313%
[32m[2025-07-11 23:04:28 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.1880%
[32m[2025-07-11 23:04:30 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [17/200][0/27]	eta 0:00:44 lr 0.000079	time 1.6471 (1.6471)	loss 13.0984 (13.0984)	miou 0.1305	grad_norm 25.3399 (25.3399)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:04:47 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [17/200][10/27]	eta 0:00:28 lr 0.000073	time 1.6472 (1.6638)	loss 12.0018 (9.0355)	miou 0.1540	grad_norm 19.8112 (27.0398)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:05:04 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [17/200][20/27]	eta 0:00:11 lr 0.000065	time 1.9709 (1.6794)	loss 10.0669 (9.6154)	miou 0.1621	grad_norm 20.6502 (26.4446)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:05:13 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 17 training takes 0:00:44
[32m[2025-07-11 23:05:13 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
17
[32m[2025-07-11 23:05:23 3DDETR.yaml][33m(utils_help.py 202)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saving......
[32m[2025-07-11 23:05:23 3DDETR.yaml][33m(utils_help.py 204)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saved !!!
[32m[2025-07-11 23:05:23 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1926%
[32m[2025-07-11 23:05:23 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.1926%
[32m[2025-07-11 23:05:26 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [18/200][0/27]	eta 0:01:01 lr 0.000060	time 2.2739 (2.2739)	loss 6.1705 (6.1705)	miou 0.1984	grad_norm 33.1398 (33.1398)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:05:43 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [18/200][10/27]	eta 0:00:29 lr 0.000052	time 1.5907 (1.7387)	loss 11.3590 (8.2640)	miou 0.2109	grad_norm 17.2959 (24.0714)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:05:57 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [18/200][20/27]	eta 0:00:11 lr 0.000045	time 1.4248 (1.6104)	loss 10.5529 (8.6345)	miou 0.2206	grad_norm 26.3790 (25.6930)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:06:08 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 18 training takes 0:00:44
[32m[2025-07-11 23:06:08 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
18
[32m[2025-07-11 23:06:18 3DDETR.yaml][33m(utils_help.py 202)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saving......
[32m[2025-07-11 23:06:19 3DDETR.yaml][33m(utils_help.py 204)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saved !!!
[32m[2025-07-11 23:06:19 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2086%
[32m[2025-07-11 23:06:19 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2086%
[32m[2025-07-11 23:06:20 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [19/200][0/27]	eta 0:00:51 lr 0.000039	time 1.9185 (1.9185)	loss 10.0072 (10.0072)	miou 0.2110	grad_norm 15.2666 (15.2666)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:06:37 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [19/200][10/27]	eta 0:00:27 lr 0.000032	time 1.3744 (1.6328)	loss 12.0454 (9.3711)	miou 0.1951	grad_norm 30.2927 (23.9599)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:06:53 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [19/200][20/27]	eta 0:00:11 lr 0.000025	time 1.3467 (1.6315)	loss 10.5582 (9.3592)	miou 0.1940	grad_norm 24.6115 (23.3616)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:07:03 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 19 training takes 0:00:44
[32m[2025-07-11 23:07:03 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 23:07:13 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1533%
[32m[2025-07-11 23:07:13 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2086%
[32m[2025-07-11 23:07:15 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [20/200][0/27]	eta 0:01:00 lr 0.000020	time 2.2461 (2.2461)	loss 8.2217 (8.2217)	miou 0.1553	grad_norm 19.5765 (19.5765)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:07:32 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [20/200][10/27]	eta 0:00:29 lr 0.000014	time 1.6337 (1.7341)	loss 8.0707 (8.9050)	miou 0.1875	grad_norm 33.6869 (30.4753)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:07:48 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [20/200][20/27]	eta 0:00:11 lr 0.000009	time 1.7861 (1.6603)	loss 6.4693 (8.6792)	miou 0.1938	grad_norm 29.2687 (29.3512)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:07:57 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 20 training takes 0:00:44
[32m[2025-07-11 23:07:57 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 23:08:07 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1643%
[32m[2025-07-11 23:08:07 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2086%
[32m[2025-07-11 23:08:10 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [21/200][0/27]	eta 0:01:04 lr 0.000006	time 2.3936 (2.3936)	loss 7.8717 (7.8717)	miou 0.1758	grad_norm 20.7326 (20.7326)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:08:27 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [21/200][10/27]	eta 0:00:29 lr 0.000003	time 1.7218 (1.7516)	loss 9.7240 (9.6799)	miou 0.1978	grad_norm 26.1199 (29.6132)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:08:44 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [21/200][20/27]	eta 0:00:12 lr 0.000001	time 1.7728 (1.7199)	loss 7.4302 (9.2593)	miou 0.2088	grad_norm 15.4315 (26.4293)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:08:53 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 21 training takes 0:00:45
[32m[2025-07-11 23:08:53 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 23:09:03 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1695%
[32m[2025-07-11 23:09:03 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2086%
[32m[2025-07-11 23:09:06 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [22/200][0/27]	eta 0:01:01 lr 0.000000	time 2.2915 (2.2915)	loss 6.8433 (6.8433)	miou 0.1887	grad_norm 21.9080 (21.9080)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:09:22 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [22/200][10/27]	eta 0:00:29 lr 0.000000	time 1.4913 (1.7162)	loss 8.3219 (8.4045)	miou 0.1963	grad_norm 24.5996 (24.0158)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:09:39 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [22/200][20/27]	eta 0:00:11 lr 0.000001	time 1.5683 (1.6924)	loss 8.2833 (8.7738)	miou 0.2125	grad_norm 18.1016 (23.8706)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:09:49 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 22 training takes 0:00:45
[32m[2025-07-11 23:09:49 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 23:09:59 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1532%
[32m[2025-07-11 23:09:59 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2086%
[32m[2025-07-11 23:10:02 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [23/200][0/27]	eta 0:01:12 lr 0.000003	time 2.6793 (2.6793)	loss 9.6103 (9.6103)	miou 0.1548	grad_norm 44.8343 (44.8343)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:10:18 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [23/200][10/27]	eta 0:00:28 lr 0.000006	time 1.5924 (1.6979)	loss 9.0692 (9.5963)	miou 0.1601	grad_norm 18.5588 (22.5442)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:10:34 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [23/200][20/27]	eta 0:00:11 lr 0.000010	time 1.9030 (1.6653)	loss 10.3373 (9.2016)	miou 0.1899	grad_norm 17.6367 (23.1869)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:10:45 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 23 training takes 0:00:45
[32m[2025-07-11 23:10:45 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 23:10:55 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1743%
[32m[2025-07-11 23:10:55 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2086%
[32m[2025-07-11 23:10:56 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [24/200][0/27]	eta 0:00:49 lr 0.000014	time 1.8160 (1.8160)	loss 9.3789 (9.3789)	miou 0.1817	grad_norm 20.2145 (20.2145)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:11:12 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [24/200][10/27]	eta 0:00:27 lr 0.000020	time 1.5326 (1.6187)	loss 10.5320 (9.5832)	miou 0.1963	grad_norm 26.5865 (20.8309)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:11:27 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [24/200][20/27]	eta 0:00:10 lr 0.000027	time 1.4619 (1.5506)	loss 8.8422 (9.2516)	miou 0.2049	grad_norm 19.6272 (22.9296)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:11:37 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 24 training takes 0:00:42
[32m[2025-07-11 23:11:37 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 23:11:47 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1609%
[32m[2025-07-11 23:11:47 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2086%
[32m[2025-07-11 23:11:50 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [25/200][0/27]	eta 0:01:08 lr 0.000032	time 2.5309 (2.5309)	loss 6.8955 (6.8955)	miou 0.1694	grad_norm 14.8803 (14.8803)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:12:05 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [25/200][10/27]	eta 0:00:27 lr 0.000039	time 1.5082 (1.6376)	loss 14.1197 (10.0832)	miou 0.1900	grad_norm 28.5867 (21.5428)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:12:22 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [25/200][20/27]	eta 0:00:11 lr 0.000047	time 1.4514 (1.6515)	loss 8.5066 (9.7632)	miou 0.2072	grad_norm 31.4267 (25.2374)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:12:32 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 25 training takes 0:00:44
[32m[2025-07-11 23:12:32 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 23:12:42 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1920%
[32m[2025-07-11 23:12:42 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2086%
[32m[2025-07-11 23:12:44 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [26/200][0/27]	eta 0:01:05 lr 0.000052	time 2.4128 (2.4128)	loss 9.9239 (9.9239)	miou 0.2027	grad_norm 20.3895 (20.3895)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:13:00 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [26/200][10/27]	eta 0:00:28 lr 0.000060	time 1.6960 (1.6673)	loss 6.9561 (9.4692)	miou 0.2124	grad_norm 47.2776 (30.6339)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:13:15 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [26/200][20/27]	eta 0:00:11 lr 0.000068	time 1.2918 (1.5822)	loss 7.9666 (9.3027)	miou 0.2126	grad_norm 21.6885 (27.5428)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:13:25 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 26 training takes 0:00:42
[32m[2025-07-11 23:13:25 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 23:13:35 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1350%
[32m[2025-07-11 23:13:35 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2086%
[32m[2025-07-11 23:13:38 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [27/200][0/27]	eta 0:01:18 lr 0.000073	time 2.9239 (2.9239)	loss 7.5406 (7.5406)	miou 0.1373	grad_norm 34.3385 (34.3385)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:13:55 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [27/200][10/27]	eta 0:00:31 lr 0.000079	time 2.5210 (1.8564)	loss 10.5780 (7.9803)	miou 0.2056	grad_norm 21.4044 (29.0809)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:14:10 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [27/200][20/27]	eta 0:00:11 lr 0.000085	time 1.5187 (1.6659)	loss 6.2162 (8.2945)	miou 0.2057	grad_norm 54.6774 (27.3675)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:14:19 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 27 training takes 0:00:44
[32m[2025-07-11 23:14:19 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 23:14:29 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1446%
[32m[2025-07-11 23:14:29 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2086%
[32m[2025-07-11 23:14:32 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [28/200][0/27]	eta 0:01:16 lr 0.000089	time 2.8387 (2.8387)	loss 8.1111 (8.1111)	miou 0.1590	grad_norm 21.6170 (21.6170)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:14:48 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [28/200][10/27]	eta 0:00:28 lr 0.000093	time 1.6106 (1.7026)	loss 8.2412 (9.3154)	miou 0.1934	grad_norm 14.9838 (23.9157)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:15:04 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [28/200][20/27]	eta 0:00:11 lr 0.000097	time 1.4609 (1.6598)	loss 7.8745 (9.1056)	miou 0.2139	grad_norm 19.6664 (25.8657)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:15:13 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 28 training takes 0:00:44
[32m[2025-07-11 23:15:13 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 23:15:23 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1993%
[32m[2025-07-11 23:15:23 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2086%
[32m[2025-07-11 23:15:25 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [29/200][0/27]	eta 0:00:50 lr 0.000098	time 1.8655 (1.8655)	loss 7.2592 (7.2592)	miou 0.2021	grad_norm 16.9656 (16.9656)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:15:42 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [29/200][10/27]	eta 0:00:28 lr 0.000100	time 1.7734 (1.6950)	loss 7.8893 (8.9627)	miou 0.2164	grad_norm 16.1566 (21.1172)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:15:57 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [29/200][20/27]	eta 0:00:11 lr 0.000100	time 1.4877 (1.6259)	loss 10.2897 (8.4934)	miou 0.2284	grad_norm 19.8634 (23.9255)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:16:08 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 29 training takes 0:00:44
[32m[2025-07-11 23:16:08 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
29
[32m[2025-07-11 23:16:18 3DDETR.yaml][33m(utils_help.py 202)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saving......
[32m[2025-07-11 23:16:18 3DDETR.yaml][33m(utils_help.py 204)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saved !!!
[32m[2025-07-11 23:16:18 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2505%
[32m[2025-07-11 23:16:18 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-11 23:16:20 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [30/200][0/27]	eta 0:00:47 lr 0.000099	time 1.7718 (1.7718)	loss 13.1484 (13.1484)	miou 0.2454	grad_norm 18.2291 (18.2291)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:16:36 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [30/200][10/27]	eta 0:00:27 lr 0.000097	time 1.1050 (1.5998)	loss 6.9281 (8.9340)	miou 0.2422	grad_norm 16.5996 (22.8245)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:16:52 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [30/200][20/27]	eta 0:00:11 lr 0.000094	time 1.4037 (1.6288)	loss 9.4693 (8.8582)	miou 0.2307	grad_norm 31.1146 (25.9065)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:17:03 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 30 training takes 0:00:44
[32m[2025-07-11 23:17:03 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 23:17:13 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1755%
[32m[2025-07-11 23:17:13 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-11 23:17:16 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [31/200][0/27]	eta 0:01:14 lr 0.000091	time 2.7441 (2.7441)	loss 7.1295 (7.1295)	miou 0.1753	grad_norm 55.2451 (55.2451)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:17:32 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [31/200][10/27]	eta 0:00:29 lr 0.000086	time 1.1655 (1.7215)	loss 5.8679 (7.3944)	miou 0.1974	grad_norm 19.7510 (27.4089)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:17:47 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [31/200][20/27]	eta 0:00:11 lr 0.000081	time 1.2374 (1.6116)	loss 10.3940 (8.2932)	miou 0.2186	grad_norm 12.5789 (25.2656)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:17:57 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 31 training takes 0:00:43
[32m[2025-07-11 23:17:57 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 23:18:07 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1251%
[32m[2025-07-11 23:18:07 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-11 23:18:10 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [32/200][0/27]	eta 0:01:13 lr 0.000076	time 2.7225 (2.7225)	loss 6.7855 (6.7855)	miou 0.1420	grad_norm 47.2095 (47.2095)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:18:25 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [32/200][10/27]	eta 0:00:28 lr 0.000069	time 1.5086 (1.6804)	loss 10.9296 (8.4108)	miou 0.1853	grad_norm 27.5155 (29.5907)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:18:42 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [32/200][20/27]	eta 0:00:11 lr 0.000062	time 1.6833 (1.6486)	loss 7.6676 (8.3023)	miou 0.1947	grad_norm 17.3257 (25.2123)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:18:52 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 32 training takes 0:00:44
[32m[2025-07-11 23:18:52 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 23:19:02 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1283%
[32m[2025-07-11 23:19:02 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-11 23:19:04 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [33/200][0/27]	eta 0:01:01 lr 0.000056	time 2.2638 (2.2638)	loss 8.7657 (8.7657)	miou 0.1454	grad_norm 10.9866 (10.9866)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:19:21 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [33/200][10/27]	eta 0:00:29 lr 0.000048	time 1.6182 (1.7567)	loss 9.1037 (8.9723)	miou 0.1664	grad_norm 22.1887 (20.0123)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:19:38 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [33/200][20/27]	eta 0:00:12 lr 0.000041	time 1.1482 (1.7160)	loss 7.0654 (8.7531)	miou 0.1876	grad_norm 21.3393 (22.9614)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:19:46 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 33 training takes 0:00:44
[32m[2025-07-11 23:19:46 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 23:19:56 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1420%
[32m[2025-07-11 23:19:56 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-11 23:19:58 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [34/200][0/27]	eta 0:00:45 lr 0.000035	time 1.6868 (1.6868)	loss 8.7011 (8.7011)	miou 0.1529	grad_norm 14.8196 (14.8196)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:20:15 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [34/200][10/27]	eta 0:00:28 lr 0.000028	time 1.5762 (1.6707)	loss 11.4786 (9.0118)	miou 0.1934	grad_norm 13.0321 (22.7776)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:20:30 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [34/200][20/27]	eta 0:00:11 lr 0.000021	time 1.6773 (1.6200)	loss 7.8656 (8.8546)	miou 0.2138	grad_norm 13.4997 (21.6298)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:20:40 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 34 training takes 0:00:43
[32m[2025-07-11 23:20:40 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 23:20:50 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1428%
[32m[2025-07-11 23:20:50 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-11 23:20:52 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [35/200][0/27]	eta 0:00:59 lr 0.000017	time 2.2139 (2.2139)	loss 8.8970 (8.8970)	miou 0.1661	grad_norm 21.1560 (21.1560)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:21:10 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [35/200][10/27]	eta 0:00:31 lr 0.000011	time 1.4510 (1.8274)	loss 6.2629 (7.6757)	miou 0.2078	grad_norm 12.4800 (23.2400)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:21:25 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [35/200][20/27]	eta 0:00:11 lr 0.000007	time 1.7464 (1.6701)	loss 7.8359 (8.3534)	miou 0.2243	grad_norm 17.1388 (25.4251)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:21:35 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 35 training takes 0:00:44
[32m[2025-07-11 23:21:35 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 23:21:45 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1263%
[32m[2025-07-11 23:21:45 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-11 23:21:47 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [36/200][0/27]	eta 0:01:01 lr 0.000004	time 2.2823 (2.2823)	loss 11.0816 (11.0816)	miou 0.1256	grad_norm 24.3960 (24.3960)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:22:03 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [36/200][10/27]	eta 0:00:28 lr 0.000002	time 1.3953 (1.6540)	loss 6.0589 (8.4217)	miou 0.1925	grad_norm 23.4069 (21.2150)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:22:19 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [36/200][20/27]	eta 0:00:11 lr 0.000000	time 1.6611 (1.6401)	loss 6.8877 (8.3626)	miou 0.2128	grad_norm 20.5673 (20.0826)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:22:28 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 36 training takes 0:00:43
[32m[2025-07-11 23:22:28 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 23:22:38 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1368%
[32m[2025-07-11 23:22:38 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-11 23:22:41 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [37/200][0/27]	eta 0:01:06 lr 0.000000	time 2.4475 (2.4475)	loss 8.6874 (8.6874)	miou 0.1473	grad_norm 25.5699 (25.5699)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:22:57 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [37/200][10/27]	eta 0:00:28 lr 0.000001	time 1.6025 (1.6879)	loss 13.1751 (8.4533)	miou 0.2124	grad_norm 33.1608 (23.6723)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:23:14 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [37/200][20/27]	eta 0:00:11 lr 0.000002	time 1.4719 (1.6945)	loss 6.8405 (8.4973)	miou 0.2269	grad_norm 12.2640 (25.4164)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:23:22 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 37 training takes 0:00:44
[32m[2025-07-11 23:23:22 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 23:23:32 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1353%
[32m[2025-07-11 23:23:32 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-11 23:23:35 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [38/200][0/27]	eta 0:01:04 lr 0.000004	time 2.3982 (2.3982)	loss 7.3465 (7.3465)	miou 0.1441	grad_norm 16.5410 (16.5410)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:23:52 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [38/200][10/27]	eta 0:00:30 lr 0.000008	time 1.8125 (1.8195)	loss 8.8303 (8.4719)	miou 0.1966	grad_norm 20.9212 (23.0022)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:24:08 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [38/200][20/27]	eta 0:00:11 lr 0.000013	time 1.1326 (1.6872)	loss 10.3391 (8.3509)	miou 0.1962	grad_norm 14.3603 (24.9947)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:24:17 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 38 training takes 0:00:44
[32m[2025-07-11 23:24:17 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 23:24:27 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1343%
[32m[2025-07-11 23:24:27 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-11 23:24:29 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [39/200][0/27]	eta 0:00:53 lr 0.000017	time 1.9749 (1.9749)	loss 7.5504 (7.5504)	miou 0.1372	grad_norm 24.7370 (24.7370)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:24:44 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [39/200][10/27]	eta 0:00:26 lr 0.000023	time 1.7525 (1.5533)	loss 7.0621 (9.2234)	miou 0.1770	grad_norm 14.0465 (22.5503)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:25:00 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [39/200][20/27]	eta 0:00:11 lr 0.000030	time 1.4487 (1.5783)	loss 8.7872 (8.8971)	miou 0.2059	grad_norm 14.8367 (22.7096)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:25:10 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 39 training takes 0:00:42
[32m[2025-07-11 23:25:10 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 23:25:20 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1447%
[32m[2025-07-11 23:25:20 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-11 23:25:22 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [40/200][0/27]	eta 0:00:54 lr 0.000035	time 2.0262 (2.0262)	loss 8.9272 (8.9272)	miou 0.1495	grad_norm 16.2691 (16.2691)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:25:38 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [40/200][10/27]	eta 0:00:28 lr 0.000043	time 1.2834 (1.6655)	loss 9.1137 (8.3293)	miou 0.1885	grad_norm 23.8825 (22.3869)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:25:55 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [40/200][20/27]	eta 0:00:11 lr 0.000051	time 1.5113 (1.6607)	loss 6.7282 (8.0068)	miou 0.2269	grad_norm 15.5032 (21.4759)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:26:04 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 40 training takes 0:00:43
[32m[2025-07-11 23:26:04 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 23:26:14 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1312%
[32m[2025-07-11 23:26:14 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-11 23:26:16 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [41/200][0/27]	eta 0:00:52 lr 0.000056	time 1.9517 (1.9517)	loss 5.5985 (5.5985)	miou 0.1522	grad_norm 32.3034 (32.3034)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:26:32 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [41/200][10/27]	eta 0:00:28 lr 0.000064	time 2.0209 (1.6746)	loss 11.7503 (8.6862)	miou 0.1860	grad_norm 26.6962 (26.2283)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:26:48 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [41/200][20/27]	eta 0:00:11 lr 0.000071	time 1.5880 (1.6355)	loss 7.1859 (8.7459)	miou 0.1998	grad_norm 22.7924 (25.3283)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:26:58 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 41 training takes 0:00:44
[32m[2025-07-11 23:26:58 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 23:27:08 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1434%
[32m[2025-07-11 23:27:08 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-11 23:27:10 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [42/200][0/27]	eta 0:00:51 lr 0.000076	time 1.9034 (1.9034)	loss 7.9868 (7.9868)	miou 0.1585	grad_norm 20.2551 (20.2551)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:27:25 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [42/200][10/27]	eta 0:00:25 lr 0.000082	time 1.6558 (1.5168)	loss 10.5963 (8.2603)	miou 0.1905	grad_norm 12.1821 (21.1477)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:27:42 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [42/200][20/27]	eta 0:00:11 lr 0.000088	time 1.3632 (1.5853)	loss 7.5115 (8.5145)	miou 0.2073	grad_norm 20.7485 (25.7603)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:27:52 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 42 training takes 0:00:43
[32m[2025-07-11 23:27:52 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 23:28:02 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1546%
[32m[2025-07-11 23:28:02 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-11 23:28:04 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [43/200][0/27]	eta 0:01:05 lr 0.000091	time 2.4160 (2.4160)	loss 6.7263 (6.7263)	miou 0.1566	grad_norm 14.0981 (14.0981)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:28:21 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [43/200][10/27]	eta 0:00:29 lr 0.000095	time 1.9263 (1.7075)	loss 10.2923 (9.0717)	miou 0.1767	grad_norm 19.4893 (23.6543)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:28:37 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [43/200][20/27]	eta 0:00:11 lr 0.000098	time 1.8979 (1.6708)	loss 7.3125 (8.9482)	miou 0.1965	grad_norm 20.4496 (23.1599)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:28:47 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 43 training takes 0:00:44
[32m[2025-07-11 23:28:47 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 23:28:57 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1245%
[32m[2025-07-11 23:28:57 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-11 23:28:59 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [44/200][0/27]	eta 0:01:07 lr 0.000099	time 2.5081 (2.5081)	loss 7.9953 (7.9953)	miou 0.1268	grad_norm 20.1804 (20.1804)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:29:15 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [44/200][10/27]	eta 0:00:28 lr 0.000100	time 1.6669 (1.6498)	loss 10.4187 (8.8073)	miou 0.1758	grad_norm 27.5483 (27.6400)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:29:30 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [44/200][20/27]	eta 0:00:10 lr 0.000100	time 1.5577 (1.5653)	loss 8.9218 (8.6179)	miou 0.1870	grad_norm 23.6514 (24.6680)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:29:39 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 44 training takes 0:00:42
[32m[2025-07-11 23:29:39 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 23:29:49 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1120%
[32m[2025-07-11 23:29:49 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-11 23:29:52 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [45/200][0/27]	eta 0:01:05 lr 0.000098	time 2.4140 (2.4140)	loss 6.2837 (6.2837)	miou 0.1426	grad_norm 13.9986 (13.9986)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:30:09 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [45/200][10/27]	eta 0:00:29 lr 0.000096	time 1.4340 (1.7533)	loss 10.0092 (7.6731)	miou 0.1867	grad_norm 18.3540 (22.9042)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:30:24 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [45/200][20/27]	eta 0:00:11 lr 0.000092	time 2.0676 (1.6327)	loss 6.5438 (7.3908)	miou 0.2245	grad_norm 23.4171 (23.4999)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:30:33 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 45 training takes 0:00:43
[32m[2025-07-11 23:30:33 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 23:30:43 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1458%
[32m[2025-07-11 23:30:43 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-11 23:30:45 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [46/200][0/27]	eta 0:01:02 lr 0.000089	time 2.2993 (2.2993)	loss 6.1389 (6.1389)	miou 0.1545	grad_norm 23.7113 (23.7113)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:31:00 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [46/200][10/27]	eta 0:00:26 lr 0.000084	time 1.3806 (1.5727)	loss 12.8436 (8.2701)	miou 0.1901	grad_norm 14.1833 (23.0187)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:31:17 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [46/200][20/27]	eta 0:00:11 lr 0.000077	time 1.4133 (1.6004)	loss 11.6762 (8.2085)	miou 0.2119	grad_norm 29.5920 (27.6217)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:31:26 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 46 training takes 0:00:42
[32m[2025-07-11 23:31:26 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 23:31:36 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.0813%
[32m[2025-07-11 23:31:36 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-11 23:31:38 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [47/200][0/27]	eta 0:00:50 lr 0.000073	time 1.8546 (1.8546)	loss 10.4611 (10.4611)	miou 0.0939	grad_norm 14.1535 (14.1535)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:31:53 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [47/200][10/27]	eta 0:00:26 lr 0.000065	time 1.1021 (1.5859)	loss 10.3984 (8.6025)	miou 0.1637	grad_norm 16.6541 (20.7648)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:32:09 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [47/200][20/27]	eta 0:00:11 lr 0.000058	time 1.6767 (1.5876)	loss 8.1070 (8.1794)	miou 0.2025	grad_norm 23.2988 (21.4944)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:32:18 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 47 training takes 0:00:41
[32m[2025-07-11 23:32:18 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 23:32:28 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.0811%
[32m[2025-07-11 23:32:28 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-11 23:32:30 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [48/200][0/27]	eta 0:00:52 lr 0.000052	time 1.9611 (1.9611)	loss 10.8839 (10.8839)	miou 0.0883	grad_norm 46.7380 (46.7380)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:32:46 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [48/200][10/27]	eta 0:00:27 lr 0.000045	time 1.9087 (1.6137)	loss 8.5462 (8.5663)	miou 0.1749	grad_norm 24.8778 (28.3689)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:33:02 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [48/200][20/27]	eta 0:00:11 lr 0.000037	time 1.4637 (1.6220)	loss 11.7143 (8.4629)	miou 0.1938	grad_norm 22.8316 (24.5761)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:33:12 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 48 training takes 0:00:43
[32m[2025-07-11 23:33:12 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 23:33:22 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1143%
[32m[2025-07-11 23:33:22 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-11 23:33:24 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [49/200][0/27]	eta 0:00:56 lr 0.000032	time 2.1046 (2.1046)	loss 9.1581 (9.1581)	miou 0.1407	grad_norm 17.9719 (17.9719)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:33:41 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [49/200][10/27]	eta 0:00:28 lr 0.000025	time 1.4006 (1.6963)	loss 6.9623 (8.3268)	miou 0.1994	grad_norm 18.6124 (21.5625)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:33:56 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [49/200][20/27]	eta 0:00:11 lr 0.000018	time 1.4513 (1.6375)	loss 7.0865 (8.0788)	miou 0.2206	grad_norm 39.9331 (22.1505)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:34:06 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 49 training takes 0:00:44
[32m[2025-07-11 23:34:06 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 23:34:16 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1262%
[32m[2025-07-11 23:34:16 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-11 23:34:18 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [50/200][0/27]	eta 0:00:49 lr 0.000014	time 1.8370 (1.8370)	loss 7.1222 (7.1222)	miou 0.1424	grad_norm 26.7325 (26.7325)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:34:34 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [50/200][10/27]	eta 0:00:27 lr 0.000009	time 1.4112 (1.6262)	loss 8.3348 (7.7878)	miou 0.2103	grad_norm 17.4162 (18.7367)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:34:51 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [50/200][20/27]	eta 0:00:11 lr 0.000005	time 1.7521 (1.6654)	loss 7.7572 (7.6834)	miou 0.2291	grad_norm 19.6159 (17.8373)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:35:01 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 50 training takes 0:00:44
[32m[2025-07-11 23:35:01 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 23:35:11 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1231%
[32m[2025-07-11 23:35:11 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-11 23:35:13 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [51/200][0/27]	eta 0:00:54 lr 0.000003	time 2.0129 (2.0129)	loss 9.7601 (9.7601)	miou 0.1439	grad_norm 26.6416 (26.6416)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:35:29 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [51/200][10/27]	eta 0:00:28 lr 0.000001	time 1.6864 (1.6522)	loss 8.7716 (9.9231)	miou 0.1850	grad_norm 17.0961 (19.4265)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:35:45 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [51/200][20/27]	eta 0:00:11 lr 0.000000	time 1.4369 (1.6191)	loss 9.3832 (9.2935)	miou 0.2040	grad_norm 17.1091 (20.0181)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:35:55 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 51 training takes 0:00:43
[32m[2025-07-11 23:35:55 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 23:36:05 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1205%
[32m[2025-07-11 23:36:05 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-11 23:36:08 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [52/200][0/27]	eta 0:01:30 lr 0.000000	time 3.3693 (3.3693)	loss 10.2444 (10.2444)	miou 0.1380	grad_norm 20.8814 (20.8814)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:36:24 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [52/200][10/27]	eta 0:00:30 lr 0.000001	time 1.2036 (1.7696)	loss 5.9966 (7.7492)	miou 0.2061	grad_norm 22.8309 (19.3756)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:36:40 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [52/200][20/27]	eta 0:00:11 lr 0.000004	time 1.5511 (1.6714)	loss 6.6940 (8.3415)	miou 0.2216	grad_norm 19.0887 (18.1750)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:36:49 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 52 training takes 0:00:44
[32m[2025-07-11 23:36:49 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 23:36:59 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1076%
[32m[2025-07-11 23:36:59 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-11 23:37:02 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [53/200][0/27]	eta 0:01:00 lr 0.000006	time 2.2478 (2.2478)	loss 9.9665 (9.9665)	miou 0.1160	grad_norm 28.9082 (28.9082)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:37:19 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [53/200][10/27]	eta 0:00:29 lr 0.000010	time 1.3813 (1.7581)	loss 5.7916 (8.5683)	miou 0.1937	grad_norm 30.9640 (22.2970)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:37:35 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [53/200][20/27]	eta 0:00:11 lr 0.000016	time 1.6465 (1.7027)	loss 9.1067 (8.0294)	miou 0.2278	grad_norm 15.7378 (19.3989)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:37:44 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 53 training takes 0:00:44
[32m[2025-07-11 23:37:44 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 23:37:54 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1100%
[32m[2025-07-11 23:37:54 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-11 23:37:57 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [54/200][0/27]	eta 0:01:15 lr 0.000020	time 2.7911 (2.7911)	loss 7.2986 (7.2986)	miou 0.1167	grad_norm 28.7646 (28.7646)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:38:14 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [54/200][10/27]	eta 0:00:30 lr 0.000027	time 1.5913 (1.7939)	loss 10.8723 (9.3748)	miou 0.1812	grad_norm 22.2270 (22.9046)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:38:30 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [54/200][20/27]	eta 0:00:11 lr 0.000034	time 1.5983 (1.7001)	loss 8.7172 (9.5130)	miou 0.2084	grad_norm 12.7423 (23.1719)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:38:39 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 54 training takes 0:00:44
[32m[2025-07-11 23:38:39 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 23:38:49 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1061%
[32m[2025-07-11 23:38:49 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-11 23:38:51 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [55/200][0/27]	eta 0:01:07 lr 0.000039	time 2.4901 (2.4901)	loss 7.1741 (7.1741)	miou 0.1283	grad_norm 16.5501 (16.5501)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:39:08 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [55/200][10/27]	eta 0:00:29 lr 0.000047	time 1.5378 (1.7532)	loss 9.4070 (8.6387)	miou 0.1850	grad_norm 38.1675 (20.7094)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:39:24 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [55/200][20/27]	eta 0:00:11 lr 0.000055	time 1.6093 (1.6560)	loss 9.0543 (8.8695)	miou 0.1829	grad_norm 19.4254 (21.3629)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:39:34 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 55 training takes 0:00:44
[32m[2025-07-11 23:39:34 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 23:39:44 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1157%
[32m[2025-07-11 23:39:44 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-11 23:39:47 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [56/200][0/27]	eta 0:01:21 lr 0.000060	time 3.0034 (3.0034)	loss 7.3915 (7.3915)	miou 0.1299	grad_norm 31.8118 (31.8118)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:40:03 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [56/200][10/27]	eta 0:00:29 lr 0.000068	time 1.3720 (1.7563)	loss 6.2720 (9.5398)	miou 0.1603	grad_norm 17.0565 (20.9657)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:40:20 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [56/200][20/27]	eta 0:00:12 lr 0.000075	time 1.9882 (1.7155)	loss 8.6915 (8.8215)	miou 0.1923	grad_norm 16.2505 (20.5126)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:40:30 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 56 training takes 0:00:45
[32m[2025-07-11 23:40:30 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 23:40:40 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1160%
[32m[2025-07-11 23:40:40 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-11 23:40:42 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [57/200][0/27]	eta 0:01:05 lr 0.000079	time 2.4389 (2.4389)	loss 8.2766 (8.2766)	miou 0.1368	grad_norm 27.0310 (27.0310)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:40:58 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [57/200][10/27]	eta 0:00:29 lr 0.000085	time 1.4869 (1.7140)	loss 9.3570 (7.8828)	miou 0.1972	grad_norm 22.3500 (19.1713)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:41:15 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [57/200][20/27]	eta 0:00:11 lr 0.000090	time 1.2830 (1.6702)	loss 6.6087 (7.9635)	miou 0.2103	grad_norm 25.6563 (21.8436)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:41:24 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 57 training takes 0:00:43
[32m[2025-07-11 23:41:24 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 23:41:34 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.0773%
[32m[2025-07-11 23:41:34 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-11 23:41:36 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [58/200][0/27]	eta 0:01:01 lr 0.000093	time 2.2922 (2.2922)	loss 8.8644 (8.8644)	miou 0.0985	grad_norm 9.7739 (9.7739)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:41:52 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [58/200][10/27]	eta 0:00:28 lr 0.000097	time 1.5561 (1.6627)	loss 5.8472 (8.7388)	miou 0.1749	grad_norm 15.9038 (20.0474)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:42:08 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [58/200][20/27]	eta 0:00:11 lr 0.000099	time 1.9002 (1.6503)	loss 8.0809 (8.6945)	miou 0.2014	grad_norm 21.7907 (21.5716)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:42:19 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 58 training takes 0:00:45
[32m[2025-07-11 23:42:19 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 23:42:29 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.0965%
[32m[2025-07-11 23:42:29 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-11 23:42:31 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [59/200][0/27]	eta 0:00:53 lr 0.000100	time 1.9835 (1.9835)	loss 6.4999 (6.4999)	miou 0.1086	grad_norm 16.5511 (16.5511)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:42:48 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [59/200][10/27]	eta 0:00:29 lr 0.000100	time 1.8332 (1.7293)	loss 9.7196 (8.8330)	miou 0.1705	grad_norm 25.4948 (18.6034)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:43:04 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [59/200][20/27]	eta 0:00:11 lr 0.000099	time 1.8373 (1.6542)	loss 11.5190 (8.8518)	miou 0.2054	grad_norm 22.8441 (18.8926)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:43:14 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 59 training takes 0:00:44
[32m[2025-07-11 23:43:14 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 23:43:24 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1276%
[32m[2025-07-11 23:43:24 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-11 23:43:27 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [60/200][0/27]	eta 0:01:19 lr 0.000097	time 2.9380 (2.9380)	loss 7.1706 (7.1706)	miou 0.1590	grad_norm 31.9952 (31.9952)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:43:44 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [60/200][10/27]	eta 0:00:31 lr 0.000094	time 2.0485 (1.8597)	loss 6.1455 (7.8874)	miou 0.2065	grad_norm 36.6352 (21.5990)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:44:00 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [60/200][20/27]	eta 0:00:11 lr 0.000090	time 1.6090 (1.7086)	loss 7.3820 (8.0851)	miou 0.2039	grad_norm 26.1256 (22.7202)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:44:09 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 60 training takes 0:00:45
[32m[2025-07-11 23:44:09 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 23:44:19 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1345%
[32m[2025-07-11 23:44:19 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-11 23:44:22 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [61/200][0/27]	eta 0:01:12 lr 0.000086	time 2.6800 (2.6800)	loss 7.3339 (7.3339)	miou 0.1358	grad_norm 23.9110 (23.9110)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:44:38 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [61/200][10/27]	eta 0:00:28 lr 0.000081	time 2.1221 (1.6929)	loss 8.4980 (8.1637)	miou 0.1862	grad_norm 29.1022 (22.7778)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:44:54 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [61/200][20/27]	eta 0:00:11 lr 0.000074	time 1.2345 (1.6513)	loss 8.4105 (8.3720)	miou 0.2148	grad_norm 13.4208 (22.1682)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:45:03 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 61 training takes 0:00:44
[32m[2025-07-11 23:45:03 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 23:45:13 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.0548%
[32m[2025-07-11 23:45:13 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-11 23:45:15 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [62/200][0/27]	eta 0:00:54 lr 0.000069	time 2.0008 (2.0008)	loss 9.1730 (9.1730)	miou 0.0628	grad_norm 43.9075 (43.9075)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:45:32 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [62/200][10/27]	eta 0:00:28 lr 0.000062	time 1.9174 (1.7022)	loss 6.1838 (8.6926)	miou 0.1721	grad_norm 21.2140 (23.0633)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:45:47 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [62/200][20/27]	eta 0:00:11 lr 0.000054	time 1.5646 (1.5933)	loss 7.4594 (8.5620)	miou 0.2055	grad_norm 16.1138 (21.3168)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:45:56 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 62 training takes 0:00:42
[32m[2025-07-11 23:45:56 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 23:46:06 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.0271%
[32m[2025-07-11 23:46:06 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-11 23:46:09 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [63/200][0/27]	eta 0:01:08 lr 0.000048	time 2.5418 (2.5418)	loss 7.0665 (7.0665)	miou 0.0511	grad_norm 17.6076 (17.6076)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:46:24 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [63/200][10/27]	eta 0:00:27 lr 0.000041	time 1.2828 (1.6357)	loss 8.8564 (8.6693)	miou 0.1227	grad_norm 18.3827 (24.5658)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:46:40 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [63/200][20/27]	eta 0:00:11 lr 0.000033	time 1.7577 (1.6153)	loss 7.8689 (8.4798)	miou 0.1774	grad_norm 17.8696 (22.1570)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:46:50 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 63 training takes 0:00:43
[32m[2025-07-11 23:46:50 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 23:47:00 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.0779%
[32m[2025-07-11 23:47:00 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-11 23:47:02 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [64/200][0/27]	eta 0:00:55 lr 0.000028	time 2.0473 (2.0473)	loss 7.2485 (7.2485)	miou 0.1031	grad_norm 27.4125 (27.4125)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:47:19 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [64/200][10/27]	eta 0:00:30 lr 0.000021	time 1.7600 (1.7731)	loss 6.9783 (9.2417)	miou 0.1730	grad_norm 20.1219 (20.0383)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:47:35 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [64/200][20/27]	eta 0:00:11 lr 0.000015	time 1.4088 (1.6880)	loss 10.4025 (9.4010)	miou 0.2008	grad_norm 16.3306 (18.5687)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:47:45 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 64 training takes 0:00:44
[32m[2025-07-11 23:47:45 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 23:47:55 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.0680%
[32m[2025-07-11 23:47:55 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-11 23:47:58 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [65/200][0/27]	eta 0:01:18 lr 0.000011	time 2.9139 (2.9139)	loss 9.0237 (9.0237)	miou 0.0842	grad_norm 18.3813 (18.3813)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:48:15 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [65/200][10/27]	eta 0:00:31 lr 0.000007	time 2.0391 (1.8431)	loss 8.4481 (7.4424)	miou 0.1802	grad_norm 66.6761 (30.7508)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:48:30 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [65/200][20/27]	eta 0:00:11 lr 0.000004	time 1.3671 (1.7063)	loss 7.9464 (7.8509)	miou 0.1969	grad_norm 28.9224 (25.5836)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:48:41 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 65 training takes 0:00:46
[32m[2025-07-11 23:48:41 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 23:48:51 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.0712%
[32m[2025-07-11 23:48:51 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-11 23:48:53 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [66/200][0/27]	eta 0:00:51 lr 0.000002	time 1.9076 (1.9076)	loss 9.1797 (9.1797)	miou 0.0720	grad_norm 20.2938 (20.2938)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:49:10 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [66/200][10/27]	eta 0:00:29 lr 0.000000	time 2.0279 (1.7216)	loss 13.7378 (8.3589)	miou 0.1482	grad_norm 18.1307 (20.2052)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:49:27 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [66/200][20/27]	eta 0:00:11 lr 0.000000	time 1.4785 (1.6914)	loss 8.2472 (8.5300)	miou 0.1769	grad_norm 18.3945 (20.5029)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:49:36 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 66 training takes 0:00:45
[32m[2025-07-11 23:49:36 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 23:49:46 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.0598%
[32m[2025-07-11 23:49:46 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-11 23:49:49 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [67/200][0/27]	eta 0:01:02 lr 0.000001	time 2.3192 (2.3192)	loss 7.0735 (7.0735)	miou 0.0611	grad_norm 26.3797 (26.3797)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:50:03 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [67/200][10/27]	eta 0:00:25 lr 0.000002	time 1.5573 (1.5151)	loss 8.3818 (7.8446)	miou 0.1635	grad_norm 20.8430 (22.3820)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:50:20 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [67/200][20/27]	eta 0:00:11 lr 0.000005	time 1.6365 (1.6008)	loss 6.4162 (8.2656)	miou 0.1943	grad_norm 19.0855 (22.9279)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:50:30 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 67 training takes 0:00:43
[32m[2025-07-11 23:50:30 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 23:50:40 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1038%
[32m[2025-07-11 23:50:40 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-11 23:50:42 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [68/200][0/27]	eta 0:01:05 lr 0.000008	time 2.4082 (2.4082)	loss 7.8142 (7.8142)	miou 0.1167	grad_norm 12.1127 (12.1127)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:50:58 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [68/200][10/27]	eta 0:00:27 lr 0.000013	time 1.2440 (1.6406)	loss 14.4823 (8.8167)	miou 0.1836	grad_norm 12.1134 (19.0680)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:51:15 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [68/200][20/27]	eta 0:00:11 lr 0.000019	time 1.3335 (1.6663)	loss 6.0988 (8.3254)	miou 0.2122	grad_norm 23.2990 (21.0055)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:51:24 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 68 training takes 0:00:44
[32m[2025-07-11 23:51:24 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 23:51:34 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.0742%
[32m[2025-07-11 23:51:34 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-11 23:51:37 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [69/200][0/27]	eta 0:01:11 lr 0.000023	time 2.6648 (2.6648)	loss 8.5539 (8.5539)	miou 0.0918	grad_norm 14.2085 (14.2085)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:51:53 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [69/200][10/27]	eta 0:00:28 lr 0.000030	time 1.8508 (1.6711)	loss 7.8779 (8.3894)	miou 0.1744	grad_norm 12.4675 (18.1595)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:52:10 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [69/200][20/27]	eta 0:00:11 lr 0.000038	time 1.5927 (1.6977)	loss 6.7677 (7.8054)	miou 0.2005	grad_norm 17.5077 (18.7753)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:52:19 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 69 training takes 0:00:45
[32m[2025-07-11 23:52:19 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 23:52:30 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.0621%
[32m[2025-07-11 23:52:30 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-11 23:52:32 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [70/200][0/27]	eta 0:01:00 lr 0.000043	time 2.2576 (2.2576)	loss 5.2583 (5.2583)	miou 0.0678	grad_norm 17.9796 (17.9796)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:52:49 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [70/200][10/27]	eta 0:00:29 lr 0.000051	time 1.6033 (1.7647)	loss 5.9130 (8.2825)	miou 0.1401	grad_norm 17.9657 (20.8022)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:53:04 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [70/200][20/27]	eta 0:00:11 lr 0.000059	time 1.7788 (1.6415)	loss 10.1724 (8.7544)	miou 0.1914	grad_norm 15.7544 (20.3950)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:53:15 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 70 training takes 0:00:45
[32m[2025-07-11 23:53:15 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 23:53:25 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1092%
[32m[2025-07-11 23:53:25 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-11 23:53:27 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [71/200][0/27]	eta 0:01:06 lr 0.000064	time 2.4450 (2.4450)	loss 6.3686 (6.3686)	miou 0.1444	grad_norm 16.5342 (16.5342)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:53:44 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [71/200][10/27]	eta 0:00:29 lr 0.000071	time 1.6352 (1.7571)	loss 8.3526 (7.6752)	miou 0.2046	grad_norm 10.9511 (29.8498)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:53:59 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [71/200][20/27]	eta 0:00:11 lr 0.000078	time 1.6979 (1.6255)	loss 8.7768 (7.9632)	miou 0.2365	grad_norm 23.0546 (23.8772)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:54:08 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 71 training takes 0:00:43
[32m[2025-07-11 23:54:08 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 23:54:18 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1691%
[32m[2025-07-11 23:54:18 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-11 23:54:20 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [72/200][0/27]	eta 0:00:56 lr 0.000082	time 2.0875 (2.0875)	loss 7.3174 (7.3174)	miou 0.1773	grad_norm 13.6339 (13.6339)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:54:37 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [72/200][10/27]	eta 0:00:29 lr 0.000088	time 1.6672 (1.7164)	loss 7.1576 (7.3550)	miou 0.2456	grad_norm 46.8747 (23.6051)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:54:53 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [72/200][20/27]	eta 0:00:11 lr 0.000093	time 1.7154 (1.6452)	loss 6.1268 (7.7074)	miou 0.2597	grad_norm 13.6273 (22.4160)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:55:03 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 72 training takes 0:00:44
[32m[2025-07-11 23:55:03 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 23:55:13 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1503%
[32m[2025-07-11 23:55:13 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-11 23:55:15 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [73/200][0/27]	eta 0:00:53 lr 0.000095	time 1.9809 (1.9809)	loss 7.2717 (7.2717)	miou 0.1709	grad_norm 20.0317 (20.0317)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:55:30 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [73/200][10/27]	eta 0:00:26 lr 0.000098	time 1.3885 (1.5816)	loss 8.5612 (7.9690)	miou 0.2083	grad_norm 33.2876 (19.0049)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:55:48 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [73/200][20/27]	eta 0:00:11 lr 0.000100	time 1.5012 (1.6507)	loss 8.8218 (8.4489)	miou 0.2104	grad_norm 18.3754 (18.1647)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:55:57 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 73 training takes 0:00:44
[32m[2025-07-11 23:55:57 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 23:56:07 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.0247%
[32m[2025-07-11 23:56:07 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-11 23:56:10 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [74/200][0/27]	eta 0:01:16 lr 0.000100	time 2.8395 (2.8395)	loss 7.3721 (7.3721)	miou 0.0314	grad_norm 21.7563 (21.7563)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 23:56:26 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [74/200][10/27]	eta 0:00:28 lr 0.000100	time 1.4528 (1.6738)	loss 8.9421 (8.6512)	miou 0.1471	grad_norm 15.4192 (17.9251)	loss_scale 131072.0000 (125114.1818)	mem 4188MB
[32m[2025-07-11 23:56:42 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [74/200][20/27]	eta 0:00:11 lr 0.000098	time 1.6704 (1.6574)	loss 7.7938 (8.4956)	miou 0.1775	grad_norm 15.1100 (21.4211)	loss_scale 131072.0000 (127951.2381)	mem 4188MB
[32m[2025-07-11 23:56:52 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 74 training takes 0:00:44
[32m[2025-07-11 23:56:52 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 23:57:02 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1020%
[32m[2025-07-11 23:57:02 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-11 23:57:04 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [75/200][0/27]	eta 0:01:00 lr 0.000096	time 2.2248 (2.2248)	loss 8.0349 (8.0349)	miou 0.1136	grad_norm 20.4554 (20.4554)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-11 23:57:20 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [75/200][10/27]	eta 0:00:28 lr 0.000092	time 1.2800 (1.6679)	loss 8.9271 (8.6341)	miou 0.1732	grad_norm 23.8584 (19.8059)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-11 23:57:37 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [75/200][20/27]	eta 0:00:11 lr 0.000088	time 1.2869 (1.6715)	loss 8.9356 (7.9668)	miou 0.2187	grad_norm 39.5476 (23.3939)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-11 23:57:46 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 75 training takes 0:00:44
[32m[2025-07-11 23:57:46 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 23:57:56 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1249%
[32m[2025-07-11 23:57:56 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-11 23:57:59 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [76/200][0/27]	eta 0:01:06 lr 0.000084	time 2.4587 (2.4587)	loss 7.3974 (7.3974)	miou 0.1381	grad_norm 45.4285 (45.4285)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-11 23:58:16 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [76/200][10/27]	eta 0:00:30 lr 0.000077	time 1.4862 (1.7653)	loss 7.3984 (7.8002)	miou 0.2111	grad_norm 14.0536 (21.9706)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-11 23:58:31 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [76/200][20/27]	eta 0:00:11 lr 0.000071	time 1.2851 (1.6274)	loss 7.3825 (7.6125)	miou 0.2200	grad_norm 22.0425 (21.6810)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-11 23:58:40 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 76 training takes 0:00:43
[32m[2025-07-11 23:58:40 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 23:58:51 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1289%
[32m[2025-07-11 23:58:51 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-11 23:58:53 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [77/200][0/27]	eta 0:01:11 lr 0.000065	time 2.6434 (2.6434)	loss 5.6955 (5.6955)	miou 0.1653	grad_norm 15.9719 (15.9719)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-11 23:59:09 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [77/200][10/27]	eta 0:00:29 lr 0.000058	time 1.4244 (1.7244)	loss 5.1002 (7.5288)	miou 0.2096	grad_norm 22.0920 (19.7182)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-11 23:59:27 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [77/200][20/27]	eta 0:00:11 lr 0.000050	time 1.5427 (1.7133)	loss 10.6252 (7.8252)	miou 0.2283	grad_norm 31.8483 (21.6979)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-11 23:59:36 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 77 training takes 0:00:45
[32m[2025-07-11 23:59:36 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 23:59:46 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.0788%
[32m[2025-07-11 23:59:46 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-11 23:59:48 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [78/200][0/27]	eta 0:01:10 lr 0.000045	time 2.6226 (2.6226)	loss 9.6705 (9.6705)	miou 0.1249	grad_norm 9.2507 (9.2507)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:00:04 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [78/200][10/27]	eta 0:00:28 lr 0.000037	time 1.3234 (1.6794)	loss 10.5356 (8.9506)	miou 0.1687	grad_norm 13.4884 (22.0149)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:00:22 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [78/200][20/27]	eta 0:00:12 lr 0.000029	time 1.2984 (1.7190)	loss 7.6033 (8.3109)	miou 0.2165	grad_norm 15.4567 (20.6319)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:00:32 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 78 training takes 0:00:46
[32m[2025-07-12 00:00:32 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 00:00:42 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.0702%
[32m[2025-07-12 00:00:42 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-12 00:00:44 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [79/200][0/27]	eta 0:00:49 lr 0.000025	time 1.8489 (1.8489)	loss 8.4391 (8.4391)	miou 0.1009	grad_norm 17.8485 (17.8485)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:01:00 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [79/200][10/27]	eta 0:00:27 lr 0.000018	time 2.0251 (1.6422)	loss 5.9204 (7.8470)	miou 0.1664	grad_norm 11.9099 (16.9631)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:01:16 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [79/200][20/27]	eta 0:00:11 lr 0.000012	time 1.6512 (1.6306)	loss 6.3433 (8.0196)	miou 0.2146	grad_norm 28.9951 (18.8219)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:01:26 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 79 training takes 0:00:44
[32m[2025-07-12 00:01:26 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 00:01:37 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.0665%
[32m[2025-07-12 00:01:37 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-12 00:01:38 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [80/200][0/27]	eta 0:00:52 lr 0.000009	time 1.9385 (1.9385)	loss 7.8349 (7.8349)	miou 0.0915	grad_norm 10.7222 (10.7222)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:01:55 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [80/200][10/27]	eta 0:00:28 lr 0.000005	time 1.4428 (1.6995)	loss 9.6707 (7.9604)	miou 0.1909	grad_norm 12.4962 (16.5083)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:02:12 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [80/200][20/27]	eta 0:00:11 lr 0.000002	time 1.7683 (1.6771)	loss 5.2791 (8.0973)	miou 0.2210	grad_norm 16.6183 (17.4758)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:02:21 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 80 training takes 0:00:44
[32m[2025-07-12 00:02:21 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 00:02:31 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.0316%
[32m[2025-07-12 00:02:31 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-12 00:02:33 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [81/200][0/27]	eta 0:00:52 lr 0.000001	time 1.9450 (1.9450)	loss 9.8838 (9.8838)	miou 0.0350	grad_norm 20.2153 (20.2153)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:02:51 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [81/200][10/27]	eta 0:00:29 lr 0.000000	time 2.1240 (1.7559)	loss 9.3629 (8.3437)	miou 0.1336	grad_norm 25.0677 (24.9966)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:03:07 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [81/200][20/27]	eta 0:00:11 lr 0.000000	time 1.8919 (1.6916)	loss 9.2095 (8.7849)	miou 0.1753	grad_norm 15.1107 (23.4140)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:03:16 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 81 training takes 0:00:44
[32m[2025-07-12 00:03:16 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 00:03:26 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.0553%
[32m[2025-07-12 00:03:26 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-12 00:03:28 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [82/200][0/27]	eta 0:00:57 lr 0.000001	time 2.1155 (2.1155)	loss 8.6657 (8.6657)	miou 0.0616	grad_norm 15.0061 (15.0061)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:03:45 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [82/200][10/27]	eta 0:00:28 lr 0.000004	time 1.5267 (1.6844)	loss 10.5118 (7.9776)	miou 0.1942	grad_norm 14.8823 (19.3214)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:04:01 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [82/200][20/27]	eta 0:00:11 lr 0.000007	time 1.4589 (1.6466)	loss 8.2065 (7.8853)	miou 0.2189	grad_norm 30.6588 (20.0427)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:04:10 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 82 training takes 0:00:43
[32m[2025-07-12 00:04:10 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 00:04:20 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.0417%
[32m[2025-07-12 00:04:20 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-12 00:04:22 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [83/200][0/27]	eta 0:00:49 lr 0.000010	time 1.8242 (1.8242)	loss 8.7059 (8.7059)	miou 0.0435	grad_norm 24.8982 (24.8982)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:04:39 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [83/200][10/27]	eta 0:00:28 lr 0.000016	time 1.6713 (1.6802)	loss 6.3612 (8.4167)	miou 0.1707	grad_norm 39.4063 (22.6042)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:04:56 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [83/200][20/27]	eta 0:00:11 lr 0.000022	time 1.7129 (1.7105)	loss 9.8852 (9.0873)	miou 0.1816	grad_norm 17.9401 (20.9587)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:05:05 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 83 training takes 0:00:45
[32m[2025-07-12 00:05:05 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 00:05:15 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.0612%
[32m[2025-07-12 00:05:15 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-12 00:05:17 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [84/200][0/27]	eta 0:00:54 lr 0.000027	time 2.0250 (2.0250)	loss 9.2726 (9.2726)	miou 0.0710	grad_norm 32.1271 (32.1271)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:05:33 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [84/200][10/27]	eta 0:00:27 lr 0.000034	time 1.7948 (1.6293)	loss 6.6257 (8.0033)	miou 0.1598	grad_norm 10.5205 (26.6293)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:05:50 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [84/200][20/27]	eta 0:00:11 lr 0.000041	time 1.6950 (1.6382)	loss 9.5981 (8.4745)	miou 0.1887	grad_norm 18.6720 (24.0033)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:06:00 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 84 training takes 0:00:44
[32m[2025-07-12 00:06:00 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 00:06:10 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.0583%
[32m[2025-07-12 00:06:10 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-12 00:06:12 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [85/200][0/27]	eta 0:01:01 lr 0.000047	time 2.2683 (2.2683)	loss 4.5593 (4.5593)	miou 0.0623	grad_norm 50.1545 (50.1545)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:06:28 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [85/200][10/27]	eta 0:00:27 lr 0.000055	time 1.4856 (1.5933)	loss 9.4628 (7.4199)	miou 0.1700	grad_norm 23.0406 (25.5776)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:06:44 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [85/200][20/27]	eta 0:00:11 lr 0.000062	time 1.5860 (1.6051)	loss 7.9262 (8.1742)	miou 0.1919	grad_norm 21.9233 (24.1978)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:06:54 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 85 training takes 0:00:43
[32m[2025-07-12 00:06:54 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 00:07:04 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.0697%
[32m[2025-07-12 00:07:04 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-12 00:07:06 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [86/200][0/27]	eta 0:00:53 lr 0.000068	time 1.9902 (1.9902)	loss 8.4367 (8.4367)	miou 0.1014	grad_norm 18.0380 (18.0380)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:07:22 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [86/200][10/27]	eta 0:00:28 lr 0.000075	time 1.6058 (1.6510)	loss 7.2293 (7.4410)	miou 0.1911	grad_norm 22.2886 (19.6459)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:07:38 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [86/200][20/27]	eta 0:00:11 lr 0.000081	time 1.5894 (1.6109)	loss 6.8303 (8.1842)	miou 0.2133	grad_norm 22.4697 (17.8507)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:07:47 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 86 training takes 0:00:43
[32m[2025-07-12 00:07:47 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 00:07:57 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1343%
[32m[2025-07-12 00:07:57 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-12 00:07:59 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [87/200][0/27]	eta 0:00:56 lr 0.000085	time 2.0990 (2.0990)	loss 7.9651 (7.9651)	miou 0.1372	grad_norm 15.0395 (15.0395)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:08:16 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [87/200][10/27]	eta 0:00:28 lr 0.000090	time 1.4064 (1.6912)	loss 7.3484 (7.9386)	miou 0.2134	grad_norm 10.2490 (19.3221)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:08:32 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [87/200][20/27]	eta 0:00:11 lr 0.000095	time 1.5246 (1.6411)	loss 10.0268 (8.2318)	miou 0.2251	grad_norm 15.9997 (19.1647)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:08:41 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 87 training takes 0:00:43
[32m[2025-07-12 00:08:41 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 00:08:51 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.0742%
[32m[2025-07-12 00:08:51 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-12 00:08:52 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [88/200][0/27]	eta 0:00:47 lr 0.000097	time 1.7739 (1.7739)	loss 6.0729 (6.0729)	miou 0.1021	grad_norm 13.4268 (13.4268)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:09:08 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [88/200][10/27]	eta 0:00:26 lr 0.000099	time 1.6422 (1.5510)	loss 7.4028 (8.7698)	miou 0.1841	grad_norm 20.8766 (18.1732)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:09:24 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [88/200][20/27]	eta 0:00:11 lr 0.000100	time 1.4538 (1.5795)	loss 8.1585 (8.7832)	miou 0.1960	grad_norm 19.4376 (21.2098)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:09:34 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 88 training takes 0:00:43
[32m[2025-07-12 00:09:34 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 00:09:44 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.0585%
[32m[2025-07-12 00:09:44 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-12 00:09:46 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [89/200][0/27]	eta 0:00:40 lr 0.000100	time 1.4939 (1.4939)	loss 7.8270 (7.8270)	miou 0.0877	grad_norm 20.8980 (20.8980)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:10:02 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [89/200][10/27]	eta 0:00:26 lr 0.000099	time 1.8507 (1.5777)	loss 4.8792 (8.6139)	miou 0.1702	grad_norm 26.1591 (22.3179)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:10:18 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [89/200][20/27]	eta 0:00:11 lr 0.000096	time 1.5696 (1.6213)	loss 6.7613 (8.6619)	miou 0.1963	grad_norm 17.0947 (20.4009)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:10:28 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 89 training takes 0:00:43
[32m[2025-07-12 00:10:28 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 00:10:38 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.0802%
[32m[2025-07-12 00:10:38 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-12 00:10:40 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [90/200][0/27]	eta 0:01:05 lr 0.000094	time 2.4371 (2.4371)	loss 9.0413 (9.0413)	miou 0.0936	grad_norm 14.4749 (14.4749)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:10:57 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [90/200][10/27]	eta 0:00:29 lr 0.000090	time 2.1722 (1.7126)	loss 8.0033 (8.4299)	miou 0.1468	grad_norm 40.6907 (17.7878)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:11:12 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [90/200][20/27]	eta 0:00:11 lr 0.000085	time 1.2224 (1.6273)	loss 7.0427 (7.8577)	miou 0.1871	grad_norm 19.9642 (20.0111)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:11:22 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 90 training takes 0:00:44
[32m[2025-07-12 00:11:22 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 00:11:32 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1047%
[32m[2025-07-12 00:11:32 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-12 00:11:34 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [91/200][0/27]	eta 0:00:37 lr 0.000081	time 1.3870 (1.3870)	loss 11.8049 (11.8049)	miou 0.1062	grad_norm 31.2407 (31.2407)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:11:50 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [91/200][10/27]	eta 0:00:27 lr 0.000074	time 2.1319 (1.6182)	loss 6.1756 (8.0233)	miou 0.1747	grad_norm 65.3366 (21.8312)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:12:06 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [91/200][20/27]	eta 0:00:11 lr 0.000067	time 1.7754 (1.5861)	loss 7.9909 (8.2733)	miou 0.2073	grad_norm 16.7313 (20.7640)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:12:15 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 91 training takes 0:00:42
[32m[2025-07-12 00:12:15 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 00:12:25 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.0888%
[32m[2025-07-12 00:12:25 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-12 00:12:27 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [92/200][0/27]	eta 0:00:56 lr 0.000062	time 2.1029 (2.1029)	loss 10.2600 (10.2600)	miou 0.0955	grad_norm 18.9017 (18.9017)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:12:44 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [92/200][10/27]	eta 0:00:28 lr 0.000054	time 1.9821 (1.6834)	loss 6.6099 (8.4917)	miou 0.1766	grad_norm 13.7736 (16.4652)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:12:59 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [92/200][20/27]	eta 0:00:11 lr 0.000046	time 1.6214 (1.6065)	loss 6.6151 (7.8519)	miou 0.2152	grad_norm 15.0661 (18.4445)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:13:08 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 92 training takes 0:00:43
[32m[2025-07-12 00:13:08 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 00:13:19 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.0783%
[32m[2025-07-12 00:13:19 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-12 00:13:21 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [93/200][0/27]	eta 0:01:04 lr 0.000041	time 2.4006 (2.4006)	loss 11.1500 (11.1500)	miou 0.0807	grad_norm 15.7306 (15.7306)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:13:36 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [93/200][10/27]	eta 0:00:26 lr 0.000033	time 1.5939 (1.5867)	loss 8.2366 (8.4266)	miou 0.1912	grad_norm 36.0128 (19.1519)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:13:52 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [93/200][20/27]	eta 0:00:11 lr 0.000026	time 1.4359 (1.5937)	loss 7.7891 (8.5897)	miou 0.2231	grad_norm 33.9386 (19.8647)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:14:02 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 93 training takes 0:00:43
[32m[2025-07-12 00:14:02 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 00:14:12 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.0847%
[32m[2025-07-12 00:14:12 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-12 00:14:15 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [94/200][0/27]	eta 0:01:08 lr 0.000021	time 2.5201 (2.5201)	loss 9.2551 (9.2551)	miou 0.0873	grad_norm 16.0319 (16.0319)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:14:31 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [94/200][10/27]	eta 0:00:28 lr 0.000015	time 1.3938 (1.6992)	loss 7.1765 (7.6910)	miou 0.1690	grad_norm 16.7943 (25.5413)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:14:49 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [94/200][20/27]	eta 0:00:12 lr 0.000010	time 1.4861 (1.7402)	loss 7.1057 (7.5567)	miou 0.2229	grad_norm 12.9985 (21.1924)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:14:58 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 94 training takes 0:00:45
[32m[2025-07-12 00:14:58 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 00:15:08 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.0987%
[32m[2025-07-12 00:15:08 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-12 00:15:10 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [95/200][0/27]	eta 0:00:53 lr 0.000007	time 1.9765 (1.9765)	loss 8.3095 (8.3095)	miou 0.1209	grad_norm 15.2054 (15.2054)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:15:27 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [95/200][10/27]	eta 0:00:30 lr 0.000004	time 1.8454 (1.7650)	loss 7.6383 (7.9140)	miou 0.1755	grad_norm 27.8077 (22.8087)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:15:43 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [95/200][20/27]	eta 0:00:11 lr 0.000001	time 1.3035 (1.6615)	loss 8.4999 (8.1146)	miou 0.2001	grad_norm 15.1390 (19.9484)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:15:52 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 95 training takes 0:00:44
[32m[2025-07-12 00:15:52 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 00:16:02 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.0978%
[32m[2025-07-12 00:16:02 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-12 00:16:05 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [96/200][0/27]	eta 0:01:15 lr 0.000000	time 2.7828 (2.7828)	loss 8.7343 (8.7343)	miou 0.0997	grad_norm 17.0494 (17.0494)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:16:20 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [96/200][10/27]	eta 0:00:28 lr 0.000000	time 1.4031 (1.6801)	loss 7.3844 (7.8273)	miou 0.2062	grad_norm 15.7119 (18.1595)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:16:36 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [96/200][20/27]	eta 0:00:11 lr 0.000001	time 1.8726 (1.6369)	loss 7.1219 (7.9017)	miou 0.2391	grad_norm 12.8045 (17.3766)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:16:47 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 96 training takes 0:00:45
[32m[2025-07-12 00:16:47 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 00:16:57 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1081%
[32m[2025-07-12 00:16:57 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-12 00:17:00 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [97/200][0/27]	eta 0:01:09 lr 0.000002	time 2.5758 (2.5758)	loss 4.8890 (4.8890)	miou 0.1187	grad_norm 21.5426 (21.5426)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:17:16 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [97/200][10/27]	eta 0:00:28 lr 0.000005	time 1.7673 (1.7038)	loss 6.2168 (7.7055)	miou 0.1943	grad_norm 8.4886 (17.3391)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:17:32 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [97/200][20/27]	eta 0:00:11 lr 0.000010	time 1.6006 (1.6457)	loss 9.1484 (7.9840)	miou 0.2322	grad_norm 28.2741 (18.9894)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:17:40 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 97 training takes 0:00:43
[32m[2025-07-12 00:17:40 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 00:17:51 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1013%
[32m[2025-07-12 00:17:51 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-12 00:17:53 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [98/200][0/27]	eta 0:01:04 lr 0.000013	time 2.3989 (2.3989)	loss 6.0904 (6.0904)	miou 0.1376	grad_norm 13.0146 (13.0146)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:18:11 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [98/200][10/27]	eta 0:00:30 lr 0.000019	time 1.6497 (1.8218)	loss 7.2315 (8.9869)	miou 0.1853	grad_norm 16.1285 (23.5416)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:18:26 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [98/200][20/27]	eta 0:00:11 lr 0.000025	time 1.5651 (1.6857)	loss 9.2367 (8.6637)	miou 0.2009	grad_norm 43.6532 (24.7434)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:18:36 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 98 training takes 0:00:45
[32m[2025-07-12 00:18:36 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 00:18:46 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1084%
[32m[2025-07-12 00:18:46 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-12 00:18:48 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [99/200][0/27]	eta 0:00:49 lr 0.000030	time 1.8353 (1.8353)	loss 6.5382 (6.5382)	miou 0.1176	grad_norm 24.9267 (24.9267)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:19:04 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [99/200][10/27]	eta 0:00:28 lr 0.000038	time 1.5490 (1.6949)	loss 6.1752 (8.1982)	miou 0.1898	grad_norm 13.8291 (18.9199)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:19:21 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [99/200][20/27]	eta 0:00:11 lr 0.000045	time 1.4714 (1.6636)	loss 9.1965 (8.4484)	miou 0.2058	grad_norm 15.5206 (18.8563)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:19:30 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 99 training takes 0:00:44
[32m[2025-07-12 00:19:30 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 00:19:40 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.0962%
[32m[2025-07-12 00:19:40 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-12 00:19:43 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [100/200][0/27]	eta 0:01:09 lr 0.000051	time 2.5782 (2.5782)	loss 10.0423 (10.0423)	miou 0.1107	grad_norm 11.2379 (11.2379)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:19:59 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [100/200][10/27]	eta 0:00:28 lr 0.000059	time 1.4158 (1.6982)	loss 7.0905 (9.0599)	miou 0.1635	grad_norm 19.9205 (21.9147)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:20:16 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [100/200][20/27]	eta 0:00:11 lr 0.000066	time 1.7050 (1.6969)	loss 9.5719 (8.7823)	miou 0.1968	grad_norm 17.5708 (19.2684)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:20:25 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 100 training takes 0:00:44
[32m[2025-07-12 00:20:25 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 00:20:35 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1171%
[32m[2025-07-12 00:20:35 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-12 00:20:36 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [101/200][0/27]	eta 0:00:47 lr 0.000071	time 1.7568 (1.7568)	loss 8.8068 (8.8068)	miou 0.1194	grad_norm 18.2258 (18.2258)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:20:53 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [101/200][10/27]	eta 0:00:28 lr 0.000078	time 1.8062 (1.6693)	loss 5.9140 (7.4446)	miou 0.2086	grad_norm 39.8070 (20.1143)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:21:08 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [101/200][20/27]	eta 0:00:11 lr 0.000084	time 1.2189 (1.5841)	loss 6.6829 (7.8672)	miou 0.2201	grad_norm 10.6037 (18.2490)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:21:18 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 101 training takes 0:00:43
[32m[2025-07-12 00:21:18 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 00:21:28 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1473%
[32m[2025-07-12 00:21:28 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-12 00:21:30 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [102/200][0/27]	eta 0:00:50 lr 0.000088	time 1.8803 (1.8803)	loss 8.7570 (8.7570)	miou 0.1482	grad_norm 22.3518 (22.3518)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:21:45 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [102/200][10/27]	eta 0:00:26 lr 0.000093	time 1.8520 (1.5534)	loss 7.1416 (8.7586)	miou 0.2119	grad_norm 23.5006 (18.7619)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:22:01 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [102/200][20/27]	eta 0:00:11 lr 0.000096	time 2.0162 (1.5771)	loss 7.2323 (8.1459)	miou 0.2410	grad_norm 18.3973 (19.7866)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:22:11 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 102 training takes 0:00:43
[32m[2025-07-12 00:22:11 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 00:22:21 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1458%
[32m[2025-07-12 00:22:21 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-12 00:22:24 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [103/200][0/27]	eta 0:01:05 lr 0.000098	time 2.4186 (2.4186)	loss 9.3838 (9.3838)	miou 0.1500	grad_norm 16.5072 (16.5072)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:22:42 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [103/200][10/27]	eta 0:00:31 lr 0.000100	time 1.7219 (1.8679)	loss 9.5583 (8.7609)	miou 0.2034	grad_norm 23.0981 (22.9870)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:22:58 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [103/200][20/27]	eta 0:00:12 lr 0.000100	time 2.4249 (1.7341)	loss 6.1179 (8.0045)	miou 0.2478	grad_norm 12.7724 (20.0247)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:23:07 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 103 training takes 0:00:46
[32m[2025-07-12 00:23:07 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 00:23:17 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.0930%
[32m[2025-07-12 00:23:17 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-12 00:23:19 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [104/200][0/27]	eta 0:00:51 lr 0.000100	time 1.8967 (1.8967)	loss 10.8084 (10.8084)	miou 0.0941	grad_norm 38.1052 (38.1052)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:23:37 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [104/200][10/27]	eta 0:00:30 lr 0.000098	time 1.8238 (1.8186)	loss 10.6151 (8.3419)	miou 0.1669	grad_norm 37.9225 (23.6529)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:23:54 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [104/200][20/27]	eta 0:00:12 lr 0.000095	time 1.1302 (1.7388)	loss 7.6421 (8.5769)	miou 0.1998	grad_norm 22.9544 (22.3822)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:24:04 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 104 training takes 0:00:46
[32m[2025-07-12 00:24:04 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 00:24:14 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.0867%
[32m[2025-07-12 00:24:14 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-12 00:24:16 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [105/200][0/27]	eta 0:00:57 lr 0.000092	time 2.1245 (2.1245)	loss 8.1944 (8.1944)	miou 0.0887	grad_norm 32.9618 (32.9618)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:24:31 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [105/200][10/27]	eta 0:00:26 lr 0.000088	time 1.8329 (1.5794)	loss 6.6304 (9.3844)	miou 0.1803	grad_norm 16.6649 (23.1058)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:24:48 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [105/200][20/27]	eta 0:00:11 lr 0.000082	time 1.2909 (1.6159)	loss 12.2342 (9.0908)	miou 0.2177	grad_norm 16.2594 (19.9637)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:24:58 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 105 training takes 0:00:44
[32m[2025-07-12 00:24:58 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 00:25:08 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1147%
[32m[2025-07-12 00:25:08 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-12 00:25:10 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [106/200][0/27]	eta 0:00:48 lr 0.000077	time 1.7873 (1.7873)	loss 7.9643 (7.9643)	miou 0.1273	grad_norm 34.4221 (34.4221)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:25:27 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [106/200][10/27]	eta 0:00:29 lr 0.000071	time 2.2212 (1.7161)	loss 7.9261 (7.8520)	miou 0.1941	grad_norm 12.4961 (23.9974)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:25:43 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [106/200][20/27]	eta 0:00:11 lr 0.000063	time 1.9785 (1.6490)	loss 6.1207 (8.2252)	miou 0.2111	grad_norm 36.2789 (21.3578)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:25:52 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 106 training takes 0:00:43
[32m[2025-07-12 00:25:52 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 00:26:03 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1001%
[32m[2025-07-12 00:26:03 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-12 00:26:04 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [107/200][0/27]	eta 0:00:46 lr 0.000058	time 1.7109 (1.7109)	loss 7.8982 (7.8982)	miou 0.1153	grad_norm 25.2198 (25.2198)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:26:20 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [107/200][10/27]	eta 0:00:26 lr 0.000050	time 1.6736 (1.5882)	loss 6.5892 (8.7093)	miou 0.1982	grad_norm 20.1805 (22.7604)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:26:36 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [107/200][20/27]	eta 0:00:11 lr 0.000042	time 1.8744 (1.5877)	loss 9.8340 (8.4036)	miou 0.2156	grad_norm 14.2599 (21.6889)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:26:46 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 107 training takes 0:00:43
[32m[2025-07-12 00:26:46 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 00:26:56 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1038%
[32m[2025-07-12 00:26:56 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-12 00:26:58 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [108/200][0/27]	eta 0:00:59 lr 0.000037	time 2.2042 (2.2042)	loss 4.8816 (4.8816)	miou 0.1083	grad_norm 16.0126 (16.0126)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:27:15 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [108/200][10/27]	eta 0:00:29 lr 0.000029	time 1.6382 (1.7197)	loss 7.9996 (7.8096)	miou 0.1969	grad_norm 20.2236 (18.0803)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:27:31 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [108/200][20/27]	eta 0:00:11 lr 0.000023	time 1.8582 (1.6718)	loss 6.8084 (7.8063)	miou 0.2304	grad_norm 14.3059 (16.7933)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:27:41 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 108 training takes 0:00:44
[32m[2025-07-12 00:27:41 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 00:27:51 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.0963%
[32m[2025-07-12 00:27:51 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-12 00:27:53 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [109/200][0/27]	eta 0:01:08 lr 0.000018	time 2.5263 (2.5263)	loss 8.5460 (8.5460)	miou 0.1091	grad_norm 21.1589 (21.1589)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:28:09 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [109/200][10/27]	eta 0:00:28 lr 0.000012	time 1.5501 (1.6708)	loss 7.4749 (7.3980)	miou 0.2172	grad_norm 13.3634 (18.1466)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:28:25 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [109/200][20/27]	eta 0:00:11 lr 0.000008	time 1.8554 (1.6391)	loss 6.8714 (7.7608)	miou 0.2276	grad_norm 20.6122 (17.5193)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:28:35 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 109 training takes 0:00:44
[32m[2025-07-12 00:28:35 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 00:28:45 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1169%
[32m[2025-07-12 00:28:45 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-12 00:28:47 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [110/200][0/27]	eta 0:00:50 lr 0.000005	time 1.8676 (1.8676)	loss 9.2986 (9.2986)	miou 0.1185	grad_norm 29.7881 (29.7881)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:29:05 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [110/200][10/27]	eta 0:00:29 lr 0.000002	time 1.4813 (1.7589)	loss 8.1529 (7.7893)	miou 0.1994	grad_norm 8.2345 (17.7217)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:29:21 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [110/200][20/27]	eta 0:00:11 lr 0.000000	time 1.8340 (1.6683)	loss 10.0359 (7.6915)	miou 0.2162	grad_norm 20.0371 (18.6905)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:29:30 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 110 training takes 0:00:44
[32m[2025-07-12 00:29:30 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 00:29:40 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1057%
[32m[2025-07-12 00:29:40 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-12 00:29:42 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [111/200][0/27]	eta 0:00:53 lr 0.000000	time 1.9999 (1.9999)	loss 7.8320 (7.8320)	miou 0.1325	grad_norm 9.5824 (9.5824)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:29:58 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [111/200][10/27]	eta 0:00:27 lr 0.000000	time 1.3106 (1.6443)	loss 12.2049 (7.5420)	miou 0.1969	grad_norm 30.9505 (17.8188)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:30:15 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [111/200][20/27]	eta 0:00:11 lr 0.000002	time 1.8105 (1.6741)	loss 6.5245 (7.6732)	miou 0.2182	grad_norm 14.7803 (18.1225)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:30:25 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 111 training takes 0:00:44
[32m[2025-07-12 00:30:25 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 00:30:35 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.0988%
[32m[2025-07-12 00:30:35 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-12 00:30:38 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [112/200][0/27]	eta 0:01:01 lr 0.000004	time 2.2748 (2.2748)	loss 8.8650 (8.8650)	miou 0.1023	grad_norm 17.0219 (17.0219)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:30:53 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [112/200][10/27]	eta 0:00:27 lr 0.000007	time 1.4827 (1.6438)	loss 9.2567 (8.1255)	miou 0.1919	grad_norm 13.8081 (16.3453)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:31:09 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [112/200][20/27]	eta 0:00:11 lr 0.000012	time 1.3004 (1.6214)	loss 6.2284 (8.2010)	miou 0.2197	grad_norm 23.4393 (17.6641)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:31:19 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 112 training takes 0:00:43
[32m[2025-07-12 00:31:19 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 00:31:29 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1304%
[32m[2025-07-12 00:31:29 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-12 00:31:31 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [113/200][0/27]	eta 0:00:52 lr 0.000016	time 1.9268 (1.9268)	loss 6.9723 (6.9723)	miou 0.1679	grad_norm 11.7571 (11.7571)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:31:47 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [113/200][10/27]	eta 0:00:27 lr 0.000022	time 1.7408 (1.6316)	loss 9.7628 (8.7464)	miou 0.2007	grad_norm 17.4327 (19.1434)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:32:03 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [113/200][20/27]	eta 0:00:11 lr 0.000029	time 1.5474 (1.6185)	loss 7.3026 (8.1360)	miou 0.2326	grad_norm 27.3619 (18.2670)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:32:14 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 113 training takes 0:00:44
[32m[2025-07-12 00:32:14 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 00:32:24 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1230%
[32m[2025-07-12 00:32:24 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-12 00:32:27 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [114/200][0/27]	eta 0:01:18 lr 0.000034	time 2.9122 (2.9122)	loss 5.2463 (5.2463)	miou 0.1263	grad_norm 21.2626 (21.2626)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:32:43 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [114/200][10/27]	eta 0:00:28 lr 0.000041	time 1.5515 (1.6985)	loss 6.8458 (8.2272)	miou 0.1967	grad_norm 20.6538 (23.4037)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:32:58 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [114/200][20/27]	eta 0:00:11 lr 0.000049	time 1.5478 (1.6403)	loss 6.8276 (8.2742)	miou 0.2202	grad_norm 18.9512 (19.8987)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:33:09 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 114 training takes 0:00:45
[32m[2025-07-12 00:33:09 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 00:33:19 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1191%
[32m[2025-07-12 00:33:19 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-12 00:33:22 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [115/200][0/27]	eta 0:01:09 lr 0.000055	time 2.5643 (2.5643)	loss 6.6774 (6.6774)	miou 0.1547	grad_norm 19.5932 (19.5932)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:33:38 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [115/200][10/27]	eta 0:00:29 lr 0.000062	time 1.4124 (1.7296)	loss 10.0567 (8.1290)	miou 0.2113	grad_norm 10.3604 (17.3697)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:33:55 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [115/200][20/27]	eta 0:00:11 lr 0.000070	time 1.6827 (1.6830)	loss 8.4910 (8.1496)	miou 0.2297	grad_norm 13.4554 (18.7029)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:34:05 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 115 training takes 0:00:45
[32m[2025-07-12 00:34:05 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 00:34:15 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1018%
[32m[2025-07-12 00:34:15 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-12 00:34:17 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [116/200][0/27]	eta 0:01:00 lr 0.000075	time 2.2407 (2.2407)	loss 7.3552 (7.3552)	miou 0.1256	grad_norm 15.4089 (15.4089)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:34:34 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [116/200][10/27]	eta 0:00:29 lr 0.000081	time 1.6262 (1.7471)	loss 8.4673 (7.4488)	miou 0.2119	grad_norm 15.9069 (18.5315)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:34:50 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [116/200][20/27]	eta 0:00:11 lr 0.000087	time 1.2524 (1.6855)	loss 9.0693 (7.7758)	miou 0.2338	grad_norm 9.3474 (18.0672)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:35:01 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 116 training takes 0:00:45
[32m[2025-07-12 00:35:01 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 00:35:11 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.0931%
[32m[2025-07-12 00:35:11 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-12 00:35:13 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [117/200][0/27]	eta 0:00:58 lr 0.000090	time 2.1787 (2.1787)	loss 5.5576 (5.5576)	miou 0.1031	grad_norm 27.8757 (27.8757)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:35:30 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [117/200][10/27]	eta 0:00:30 lr 0.000095	time 2.3846 (1.7683)	loss 8.5208 (7.5348)	miou 0.1838	grad_norm 12.2918 (17.6562)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:35:46 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [117/200][20/27]	eta 0:00:11 lr 0.000098	time 1.2035 (1.6677)	loss 8.1750 (8.0628)	miou 0.2072	grad_norm 45.4504 (18.1516)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:35:55 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 117 training takes 0:00:44
[32m[2025-07-12 00:35:55 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 00:36:05 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.0614%
[32m[2025-07-12 00:36:05 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-12 00:36:08 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [118/200][0/27]	eta 0:01:11 lr 0.000099	time 2.6314 (2.6314)	loss 4.9368 (4.9368)	miou 0.0640	grad_norm 26.5402 (26.5402)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:36:24 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [118/200][10/27]	eta 0:00:28 lr 0.000100	time 1.6574 (1.7018)	loss 8.0008 (7.3937)	miou 0.1686	grad_norm 10.3412 (16.6513)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:36:41 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [118/200][20/27]	eta 0:00:11 lr 0.000100	time 1.5249 (1.6928)	loss 8.9344 (7.2604)	miou 0.2012	grad_norm 25.3910 (18.0813)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:36:50 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 118 training takes 0:00:44
[32m[2025-07-12 00:36:50 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 00:37:01 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.0929%
[32m[2025-07-12 00:37:01 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-12 00:37:03 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [119/200][0/27]	eta 0:01:06 lr 0.000099	time 2.4568 (2.4568)	loss 6.0587 (6.0587)	miou 0.1204	grad_norm 16.1263 (16.1263)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:37:19 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [119/200][10/27]	eta 0:00:29 lr 0.000096	time 1.5912 (1.7138)	loss 7.9607 (8.0038)	miou 0.1751	grad_norm 21.1411 (20.8635)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:37:36 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [119/200][20/27]	eta 0:00:11 lr 0.000093	time 1.6188 (1.6645)	loss 8.6445 (8.1179)	miou 0.2197	grad_norm 13.2786 (17.5626)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:37:45 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 119 training takes 0:00:44
[32m[2025-07-12 00:37:45 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 00:37:56 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.0571%
[32m[2025-07-12 00:37:56 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-12 00:37:58 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [120/200][0/27]	eta 0:01:02 lr 0.000090	time 2.3233 (2.3233)	loss 5.7015 (5.7015)	miou 0.0607	grad_norm 24.6264 (24.6264)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:38:13 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [120/200][10/27]	eta 0:00:27 lr 0.000085	time 1.4668 (1.6190)	loss 8.8923 (8.4292)	miou 0.1735	grad_norm 16.4964 (20.7737)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:38:31 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [120/200][20/27]	eta 0:00:11 lr 0.000079	time 1.3245 (1.6817)	loss 8.6252 (8.1690)	miou 0.1893	grad_norm 22.1386 (19.4864)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:38:42 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 120 training takes 0:00:46
[32m[2025-07-12 00:38:42 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 00:38:52 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.0581%
[32m[2025-07-12 00:38:52 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-12 00:38:55 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [121/200][0/27]	eta 0:01:14 lr 0.000074	time 2.7488 (2.7488)	loss 5.9625 (5.9625)	miou 0.0875	grad_norm 12.2656 (12.2656)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:39:11 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [121/200][10/27]	eta 0:00:28 lr 0.000067	time 1.6921 (1.7019)	loss 6.6063 (7.5889)	miou 0.1779	grad_norm 18.1727 (19.4458)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:39:26 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [121/200][20/27]	eta 0:00:11 lr 0.000059	time 1.7051 (1.6498)	loss 7.7843 (8.1097)	miou 0.2093	grad_norm 20.3500 (17.5083)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:39:36 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 121 training takes 0:00:44
[32m[2025-07-12 00:39:36 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 00:39:46 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.0772%
[32m[2025-07-12 00:39:46 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-12 00:39:49 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [122/200][0/27]	eta 0:00:58 lr 0.000054	time 2.1701 (2.1701)	loss 4.9826 (4.9826)	miou 0.0881	grad_norm 18.2693 (18.2693)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:40:05 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [122/200][10/27]	eta 0:00:28 lr 0.000046	time 1.3818 (1.6850)	loss 8.4105 (8.3069)	miou 0.2081	grad_norm 17.3857 (19.8427)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:40:22 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [122/200][20/27]	eta 0:00:11 lr 0.000038	time 1.5745 (1.6715)	loss 6.6338 (8.2243)	miou 0.2220	grad_norm 15.0813 (18.9483)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:40:31 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 122 training takes 0:00:44
[32m[2025-07-12 00:40:31 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 00:40:41 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.0868%
[32m[2025-07-12 00:40:41 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-12 00:40:43 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [123/200][0/27]	eta 0:00:56 lr 0.000033	time 2.0960 (2.0960)	loss 12.8415 (12.8415)	miou 0.0912	grad_norm 24.5170 (24.5170)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:41:00 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [123/200][10/27]	eta 0:00:29 lr 0.000026	time 1.5221 (1.7279)	loss 9.6295 (8.8420)	miou 0.1677	grad_norm 16.7528 (15.4444)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:41:16 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [123/200][20/27]	eta 0:00:11 lr 0.000019	time 1.6571 (1.6738)	loss 8.4114 (8.4563)	miou 0.2188	grad_norm 13.5922 (15.3157)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:41:26 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 123 training takes 0:00:45
[32m[2025-07-12 00:41:26 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 00:41:36 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.0802%
[32m[2025-07-12 00:41:36 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-12 00:41:38 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [124/200][0/27]	eta 0:00:56 lr 0.000015	time 2.0799 (2.0799)	loss 5.0602 (5.0602)	miou 0.0891	grad_norm 11.2961 (11.2961)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:41:55 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [124/200][10/27]	eta 0:00:29 lr 0.000010	time 1.5696 (1.7251)	loss 8.2083 (7.1625)	miou 0.2020	grad_norm 17.8368 (20.3718)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:42:11 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [124/200][20/27]	eta 0:00:11 lr 0.000006	time 2.1714 (1.6840)	loss 5.9804 (7.4287)	miou 0.2228	grad_norm 21.6149 (20.5372)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:42:21 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 124 training takes 0:00:44
[32m[2025-07-12 00:42:21 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 00:42:31 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.0932%
[32m[2025-07-12 00:42:31 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-12 00:42:34 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [125/200][0/27]	eta 0:01:12 lr 0.000004	time 2.7025 (2.7025)	loss 10.0461 (10.0461)	miou 0.1030	grad_norm 15.3957 (15.3957)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:42:49 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [125/200][10/27]	eta 0:00:28 lr 0.000001	time 1.8825 (1.6673)	loss 8.2324 (7.8656)	miou 0.1912	grad_norm 9.3020 (15.9088)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:43:05 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [125/200][20/27]	eta 0:00:11 lr 0.000000	time 1.6718 (1.6389)	loss 6.2691 (7.8659)	miou 0.2228	grad_norm 37.8747 (19.7637)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:43:16 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 125 training takes 0:00:45
[32m[2025-07-12 00:43:16 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 00:43:27 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.0891%
[32m[2025-07-12 00:43:27 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-12 00:43:29 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [126/200][0/27]	eta 0:01:02 lr 0.000000	time 2.3332 (2.3332)	loss 7.5382 (7.5382)	miou 0.1112	grad_norm 13.3491 (13.3491)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:43:45 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [126/200][10/27]	eta 0:00:29 lr 0.000001	time 1.5137 (1.7141)	loss 13.5043 (8.2175)	miou 0.2106	grad_norm 42.9474 (22.1202)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:44:01 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [126/200][20/27]	eta 0:00:11 lr 0.000003	time 1.5714 (1.6555)	loss 8.5067 (8.1942)	miou 0.2273	grad_norm 18.3282 (18.3925)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:44:11 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 126 training takes 0:00:44
[32m[2025-07-12 00:44:11 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 00:44:21 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.0706%
[32m[2025-07-12 00:44:21 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-12 00:44:23 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [127/200][0/27]	eta 0:00:50 lr 0.000005	time 1.8812 (1.8812)	loss 9.7918 (9.7918)	miou 0.0732	grad_norm 14.9239 (14.9239)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:44:40 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [127/200][10/27]	eta 0:00:29 lr 0.000010	time 1.8536 (1.7109)	loss 7.3739 (8.1866)	miou 0.1869	grad_norm 19.0037 (17.1172)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:44:56 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [127/200][20/27]	eta 0:00:11 lr 0.000015	time 1.4329 (1.6712)	loss 8.2887 (7.9289)	miou 0.2080	grad_norm 19.8996 (16.6487)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:45:05 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 127 training takes 0:00:43
[32m[2025-07-12 00:45:05 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 00:45:15 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.0766%
[32m[2025-07-12 00:45:15 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-12 00:45:18 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [128/200][0/27]	eta 0:00:58 lr 0.000019	time 2.1764 (2.1764)	loss 6.4874 (6.4874)	miou 0.1242	grad_norm 14.3554 (14.3554)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:45:33 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [128/200][10/27]	eta 0:00:27 lr 0.000025	time 1.7346 (1.6115)	loss 7.4795 (8.2772)	miou 0.1859	grad_norm 21.4005 (18.7746)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:45:50 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [128/200][20/27]	eta 0:00:11 lr 0.000032	time 1.5906 (1.6652)	loss 6.0397 (7.8302)	miou 0.2349	grad_norm 18.0581 (19.3993)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:46:00 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 128 training takes 0:00:44
[32m[2025-07-12 00:46:00 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 00:46:11 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.0921%
[32m[2025-07-12 00:46:11 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-12 00:46:13 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [129/200][0/27]	eta 0:01:10 lr 0.000038	time 2.6162 (2.6162)	loss 6.4739 (6.4739)	miou 0.1198	grad_norm 10.9845 (10.9845)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:46:29 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [129/200][10/27]	eta 0:00:28 lr 0.000045	time 1.8151 (1.6617)	loss 8.5313 (7.4983)	miou 0.1921	grad_norm 17.9723 (16.9916)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:46:46 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [129/200][20/27]	eta 0:00:11 lr 0.000053	time 1.9966 (1.6903)	loss 9.4639 (7.8743)	miou 0.2132	grad_norm 9.7341 (17.3678)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:46:56 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 129 training takes 0:00:45
[32m[2025-07-12 00:46:56 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 00:47:06 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1057%
[32m[2025-07-12 00:47:06 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-12 00:47:08 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [130/200][0/27]	eta 0:01:07 lr 0.000059	time 2.5155 (2.5155)	loss 7.3492 (7.3492)	miou 0.1156	grad_norm 16.6783 (16.6783)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:47:25 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [130/200][10/27]	eta 0:00:28 lr 0.000066	time 1.5972 (1.6898)	loss 8.9707 (7.4497)	miou 0.1819	grad_norm 11.8015 (18.1889)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:47:41 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [130/200][20/27]	eta 0:00:11 lr 0.000073	time 1.8216 (1.6852)	loss 6.3140 (7.4814)	miou 0.2190	grad_norm 16.5047 (17.6690)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
[32m[2025-07-12 00:47:51 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 130 training takes 0:00:44
[32m[2025-07-12 00:47:51 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 00:48:01 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.0800%
[32m[2025-07-12 00:48:01 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2505%
[32m[2025-07-12 00:48:04 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [131/200][0/27]	eta 0:01:10 lr 0.000078	time 2.6173 (2.6173)	loss 10.6183 (10.6183)	miou 0.0821	grad_norm 38.6714 (38.6714)	loss_scale 131072.0000 (131072.0000)	mem 4188MB
