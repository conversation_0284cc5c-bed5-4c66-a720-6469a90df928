{"os": "Linux-5.4.0-77-generic-x86_64-with-debian-bullseye-sid", "python": "3.7.16", "heartbeatAt": "2025-07-12T02:48:14.350369", "startedAt": "2025-07-12T02:48:13.874914", "docker": null, "cuda": null, "args": ["--local_rank=2", "--cfg", "config/base_train.yaml", "--output", "/home-local2/akath.extra.nobkp/sereact", "--data-path", "/home-local2/akath.extra.nobkp/dl_challenge", "--batch-size", "2"], "state": "running", "program": "main.py", "codePath": "main.py", "git": {"remote": "https://github.com/Shrinidhibhat87/codingchallenge_sereact.git", "commit": "a3ff60e13dfced588b500c8a1de00f36fdf22d49"}, "email": "<EMAIL>", "root": "/home-local/akath.nobkp/codingchallenge_sereact", "host": "lv3-32190", "username": "akath", "executable": "/gel/usr/akath/.conda/envs/swin/bin/python", "cpu_count": 6, "cpu_count_logical": 12, "cpu_freq": {"current": 2.2895833333333333, "min": 1200.0, "max": 4000.0}, "cpu_freq_per_core": [{"current": 2.743, "min": 1200.0, "max": 4000.0}, {"current": 1.572, "min": 1200.0, "max": 4000.0}, {"current": 1.199, "min": 1200.0, "max": 4000.0}, {"current": 3.385, "min": 1200.0, "max": 4000.0}, {"current": 2.905, "min": 1200.0, "max": 4000.0}, {"current": 2.581, "min": 1200.0, "max": 4000.0}, {"current": 3.154, "min": 1200.0, "max": 4000.0}, {"current": 1.388, "min": 1200.0, "max": 4000.0}, {"current": 1.199, "min": 1200.0, "max": 4000.0}, {"current": 2.448, "min": 1200.0, "max": 4000.0}, {"current": 2.538, "min": 1200.0, "max": 4000.0}, {"current": 2.363, "min": 1200.0, "max": 4000.0}], "disk": {"total": 111.2200813293457, "used": 53.229366302490234}, "gpu": "TITAN Xp", "gpu_count": 3, "gpu_devices": [{"name": "TITAN Xp", "memory_total": 12787122176}, {"name": "TITAN Xp", "memory_total": 12788498432}, {"name": "TITAN Xp", "memory_total": 12788498432}], "memory": {"total": 62.72977066040039}}