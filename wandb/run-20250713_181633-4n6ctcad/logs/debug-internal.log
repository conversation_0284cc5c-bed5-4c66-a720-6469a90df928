2025-07-13 18:16:33,384 INFO    StreamThr :2941650 [internal.py:wandb_internal():89] W&B internal server running at pid: 2941650, started at: 2025-07-13 18:16:33.383755
2025-07-13 18:16:33,387 DEBUG   HandlerThread:2941650 [handler.py:handle_request():144] handle_request: status
2025-07-13 18:16:33,389 INFO    WriterThread:2941650 [datastore.py:open_for_write():85] open: /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-4n6ctcad/run-4n6ctcad.wandb
2025-07-13 18:16:33,391 DEBUG   SenderThread:2941650 [sender.py:send():369] send: header
2025-07-13 18:16:33,407 DEBUG   SenderThread:2941650 [sender.py:send():369] send: run
2025-07-13 18:16:33,922 INFO    SenderThread:2941650 [dir_watcher.py:__init__():211] watching files in: /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-4n6ctcad/files
2025-07-13 18:16:33,922 INFO    SenderThread:2941650 [sender.py:_start_run_threads():1103] run started: 4n6ctcad with start time 1752444993.382976
2025-07-13 18:16:33,922 DEBUG   SenderThread:2941650 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:16:33,923 INFO    SenderThread:2941650 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:16:33,931 DEBUG   HandlerThread:2941650 [handler.py:handle_request():144] handle_request: check_version
2025-07-13 18:16:33,932 DEBUG   SenderThread:2941650 [sender.py:send_request():396] send_request: check_version
2025-07-13 18:16:33,996 DEBUG   HandlerThread:2941650 [handler.py:handle_request():144] handle_request: run_start
2025-07-13 18:16:34,001 DEBUG   HandlerThread:2941650 [system_info.py:__init__():31] System info init
2025-07-13 18:16:34,001 DEBUG   HandlerThread:2941650 [system_info.py:__init__():46] System info init done
2025-07-13 18:16:34,001 INFO    HandlerThread:2941650 [system_monitor.py:start():181] Starting system monitor
2025-07-13 18:16:34,001 INFO    SystemMonitor:2941650 [system_monitor.py:_start():145] Starting system asset monitoring threads
2025-07-13 18:16:34,002 INFO    HandlerThread:2941650 [system_monitor.py:probe():201] Collecting system info
2025-07-13 18:16:34,002 INFO    SystemMonitor:2941650 [interfaces.py:start():190] Started cpu monitoring
2025-07-13 18:16:34,003 INFO    SystemMonitor:2941650 [interfaces.py:start():190] Started disk monitoring
2025-07-13 18:16:34,004 INFO    SystemMonitor:2941650 [interfaces.py:start():190] Started gpu monitoring
2025-07-13 18:16:34,005 INFO    SystemMonitor:2941650 [interfaces.py:start():190] Started memory monitoring
2025-07-13 18:16:34,005 INFO    SystemMonitor:2941650 [interfaces.py:start():190] Started network monitoring
2025-07-13 18:16:34,024 DEBUG   HandlerThread:2941650 [system_info.py:probe():195] Probing system
2025-07-13 18:16:34,033 DEBUG   HandlerThread:2941650 [system_info.py:_probe_git():180] Probing git
2025-07-13 18:16:34,047 DEBUG   HandlerThread:2941650 [system_info.py:_probe_git():188] Probing git done
2025-07-13 18:16:34,047 DEBUG   HandlerThread:2941650 [system_info.py:probe():240] Probing system done
2025-07-13 18:16:34,047 DEBUG   HandlerThread:2941650 [system_monitor.py:probe():210] {'os': 'Linux-5.4.0-77-generic-x86_64-with-debian-bullseye-sid', 'python': '3.7.16', 'heartbeatAt': '2025-07-13T22:16:34.024506', 'startedAt': '2025-07-13T22:16:33.369392', 'docker': None, 'cuda': None, 'args': ('--local_rank=1', '--cfg', 'config/base_train.yaml', '--output', '/home-local2/akath.extra.nobkp/sereact', '--data-path', '/home-local2/akath.extra.nobkp/dl_challenge', '--batch-size', '2'), 'state': 'running', 'program': 'main.py', 'codePath': 'main.py', 'git': {'remote': 'https://github.com/Shrinidhibhat87/codingchallenge_sereact.git', 'commit': '667b7ded061f079ea281aa2193d47b61df08fec6'}, 'email': '<EMAIL>', 'root': '/home-local/akath.nobkp/sereact', 'host': 'lv3-32190', 'username': 'akath', 'executable': '/gel/usr/akath/.conda/envs/swin/bin/python', 'cpu_count': 6, 'cpu_count_logical': 12, 'cpu_freq': {'current': 2.274166666666667, 'min': 1200.0, 'max': 4000.0}, 'cpu_freq_per_core': [{'current': 3.039, 'min': 1200.0, 'max': 4000.0}, {'current': 2.153, 'min': 1200.0, 'max': 4000.0}, {'current': 2.421, 'min': 1200.0, 'max': 4000.0}, {'current': 1.31, 'min': 1200.0, 'max': 4000.0}, {'current': 1.57, 'min': 1200.0, 'max': 4000.0}, {'current': 3.697, 'min': 1200.0, 'max': 4000.0}, {'current': 1.981, 'min': 1200.0, 'max': 4000.0}, {'current': 1.777, 'min': 1200.0, 'max': 4000.0}, {'current': 2.677, 'min': 1200.0, 'max': 4000.0}, {'current': 1.232, 'min': 1200.0, 'max': 4000.0}, {'current': 1.736, 'min': 1200.0, 'max': 4000.0}, {'current': 3.697, 'min': 1200.0, 'max': 4000.0}], 'disk': {'total': 111.2200813293457, 'used': 53.132808685302734}, 'gpu': 'TITAN Xp', 'gpu_count': 3, 'gpu_devices': [{'name': 'TITAN Xp', 'memory_total': 12787122176}, {'name': 'TITAN Xp', 'memory_total': 12788498432}, {'name': 'TITAN Xp', 'memory_total': 12788498432}], 'memory': {'total': 62.72977066040039}}
2025-07-13 18:16:34,048 INFO    HandlerThread:2941650 [system_monitor.py:probe():211] Finished collecting system info
2025-07-13 18:16:34,048 INFO    HandlerThread:2941650 [system_monitor.py:probe():214] Publishing system info
2025-07-13 18:16:34,048 DEBUG   HandlerThread:2941650 [system_info.py:_save_pip():52] Saving list of pip packages installed into the current environment
2025-07-13 18:16:34,048 DEBUG   HandlerThread:2941650 [system_info.py:_save_pip():67] Saving pip packages done
2025-07-13 18:16:34,048 DEBUG   HandlerThread:2941650 [system_info.py:_save_conda():75] Saving list of conda packages installed into the current environment
2025-07-13 18:16:34,924 INFO    Thread-12 :2941650 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-4n6ctcad/files/requirements.txt
2025-07-13 18:16:34,924 INFO    Thread-12 :2941650 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-4n6ctcad/files/wandb-summary.json
2025-07-13 18:16:34,924 INFO    Thread-12 :2941650 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-4n6ctcad/files/conda-environment.yaml
2025-07-13 18:16:40,796 DEBUG   HandlerThread:2941650 [system_info.py:_save_conda():86] Saving conda packages done
2025-07-13 18:16:40,797 INFO    HandlerThread:2941650 [system_monitor.py:probe():216] Finished publishing system info
2025-07-13 18:16:40,802 DEBUG   HandlerThread:2941650 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:16:40,802 DEBUG   HandlerThread:2941650 [handler.py:handle_request():144] handle_request: keepalive
2025-07-13 18:16:40,803 DEBUG   SenderThread:2941650 [sender.py:send():369] send: files
2025-07-13 18:16:40,803 INFO    SenderThread:2941650 [sender.py:_save_file():1354] saving file wandb-metadata.json with policy now
2025-07-13 18:16:40,812 DEBUG   HandlerThread:2941650 [handler.py:handle_request():144] handle_request: stop_status
2025-07-13 18:16:40,813 DEBUG   SenderThread:2941650 [sender.py:send_request():396] send_request: stop_status
2025-07-13 18:16:40,927 INFO    Thread-12 :2941650 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-4n6ctcad/files/conda-environment.yaml
2025-07-13 18:16:40,928 INFO    Thread-12 :2941650 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-4n6ctcad/files/wandb-metadata.json
2025-07-13 18:16:40,980 DEBUG   SenderThread:2941650 [sender.py:send():369] send: telemetry
2025-07-13 18:16:41,106 INFO    wandb-upload_0:2941650 [upload_job.py:push():133] Uploaded file /tmp/tmp3m982ho3wandb/z62h9h5c-wandb-metadata.json
2025-07-13 18:16:41,928 INFO    Thread-12 :2941650 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-4n6ctcad/files/output.log
2025-07-13 18:16:43,929 INFO    Thread-12 :2941650 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-4n6ctcad/files/output.log
2025-07-13 18:16:44,018 DEBUG   HandlerThread:2941650 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:16:44,019 DEBUG   SenderThread:2941650 [sender.py:send():369] send: exit
2025-07-13 18:16:44,019 INFO    SenderThread:2941650 [sender.py:send_exit():574] handling exit code: 1
2025-07-13 18:16:44,019 INFO    SenderThread:2941650 [sender.py:send_exit():576] handling runtime: 10
2025-07-13 18:16:44,020 INFO    SenderThread:2941650 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:16:44,020 INFO    SenderThread:2941650 [sender.py:send_exit():582] send defer
2025-07-13 18:16:44,020 DEBUG   HandlerThread:2941650 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:16:44,020 INFO    HandlerThread:2941650 [handler.py:handle_request_defer():170] handle defer: 0
2025-07-13 18:16:44,021 DEBUG   SenderThread:2941650 [sender.py:send_request():396] send_request: defer
2025-07-13 18:16:44,021 INFO    SenderThread:2941650 [sender.py:send_request_defer():598] handle sender defer: 0
2025-07-13 18:16:44,021 INFO    SenderThread:2941650 [sender.py:transition_state():602] send defer: 1
2025-07-13 18:16:44,021 DEBUG   HandlerThread:2941650 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:16:44,021 INFO    HandlerThread:2941650 [handler.py:handle_request_defer():170] handle defer: 1
2025-07-13 18:16:44,021 DEBUG   SenderThread:2941650 [sender.py:send_request():396] send_request: defer
2025-07-13 18:16:44,021 INFO    SenderThread:2941650 [sender.py:send_request_defer():598] handle sender defer: 1
2025-07-13 18:16:44,021 INFO    SenderThread:2941650 [sender.py:transition_state():602] send defer: 2
2025-07-13 18:16:44,021 DEBUG   HandlerThread:2941650 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:16:44,021 INFO    HandlerThread:2941650 [handler.py:handle_request_defer():170] handle defer: 2
2025-07-13 18:16:44,021 INFO    HandlerThread:2941650 [system_monitor.py:finish():190] Stopping system monitor
2025-07-13 18:16:44,021 DEBUG   SystemMonitor:2941650 [system_monitor.py:_start():159] Starting system metrics aggregation loop
2025-07-13 18:16:44,022 INFO    HandlerThread:2941650 [interfaces.py:finish():202] Joined cpu monitor
2025-07-13 18:16:44,022 DEBUG   SystemMonitor:2941650 [system_monitor.py:_start():166] Finished system metrics aggregation loop
2025-07-13 18:16:44,022 INFO    HandlerThread:2941650 [interfaces.py:finish():202] Joined disk monitor
2025-07-13 18:16:44,022 DEBUG   SystemMonitor:2941650 [system_monitor.py:_start():170] Publishing last batch of metrics
2025-07-13 18:16:44,152 INFO    HandlerThread:2941650 [interfaces.py:finish():202] Joined gpu monitor
2025-07-13 18:16:44,153 INFO    HandlerThread:2941650 [interfaces.py:finish():202] Joined memory monitor
2025-07-13 18:16:44,153 INFO    HandlerThread:2941650 [interfaces.py:finish():202] Joined network monitor
2025-07-13 18:16:44,153 DEBUG   SenderThread:2941650 [sender.py:send_request():396] send_request: defer
2025-07-13 18:16:44,154 INFO    SenderThread:2941650 [sender.py:send_request_defer():598] handle sender defer: 2
2025-07-13 18:16:44,154 INFO    SenderThread:2941650 [sender.py:transition_state():602] send defer: 3
2025-07-13 18:16:44,154 DEBUG   SenderThread:2941650 [sender.py:send():369] send: stats
2025-07-13 18:16:44,154 DEBUG   HandlerThread:2941650 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:16:44,154 INFO    HandlerThread:2941650 [handler.py:handle_request_defer():170] handle defer: 3
2025-07-13 18:16:44,154 DEBUG   SenderThread:2941650 [sender.py:send_request():396] send_request: defer
2025-07-13 18:16:44,155 INFO    SenderThread:2941650 [sender.py:send_request_defer():598] handle sender defer: 3
2025-07-13 18:16:44,155 INFO    SenderThread:2941650 [sender.py:transition_state():602] send defer: 4
2025-07-13 18:16:44,155 DEBUG   HandlerThread:2941650 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:16:44,155 INFO    HandlerThread:2941650 [handler.py:handle_request_defer():170] handle defer: 4
2025-07-13 18:16:44,155 DEBUG   SenderThread:2941650 [sender.py:send_request():396] send_request: defer
2025-07-13 18:16:44,155 INFO    SenderThread:2941650 [sender.py:send_request_defer():598] handle sender defer: 4
2025-07-13 18:16:44,155 INFO    SenderThread:2941650 [sender.py:transition_state():602] send defer: 5
2025-07-13 18:16:44,155 DEBUG   HandlerThread:2941650 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:16:44,155 INFO    HandlerThread:2941650 [handler.py:handle_request_defer():170] handle defer: 5
2025-07-13 18:16:44,156 DEBUG   SenderThread:2941650 [sender.py:send():369] send: summary
2025-07-13 18:16:44,156 INFO    SenderThread:2941650 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:16:44,156 DEBUG   SenderThread:2941650 [sender.py:send_request():396] send_request: defer
2025-07-13 18:16:44,156 INFO    SenderThread:2941650 [sender.py:send_request_defer():598] handle sender defer: 5
2025-07-13 18:16:44,156 INFO    SenderThread:2941650 [sender.py:transition_state():602] send defer: 6
2025-07-13 18:16:44,156 DEBUG   HandlerThread:2941650 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:16:44,156 INFO    HandlerThread:2941650 [handler.py:handle_request_defer():170] handle defer: 6
2025-07-13 18:16:44,157 DEBUG   SenderThread:2941650 [sender.py:send_request():396] send_request: defer
2025-07-13 18:16:44,157 INFO    SenderThread:2941650 [sender.py:send_request_defer():598] handle sender defer: 6
2025-07-13 18:16:44,162 DEBUG   HandlerThread:2941650 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:16:44,249 INFO    SenderThread:2941650 [sender.py:transition_state():602] send defer: 7
2025-07-13 18:16:44,250 DEBUG   HandlerThread:2941650 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:16:44,250 INFO    HandlerThread:2941650 [handler.py:handle_request_defer():170] handle defer: 7
2025-07-13 18:16:44,250 DEBUG   SenderThread:2941650 [sender.py:send_request():396] send_request: defer
2025-07-13 18:16:44,250 INFO    SenderThread:2941650 [sender.py:send_request_defer():598] handle sender defer: 7
2025-07-13 18:16:44,929 INFO    Thread-12 :2941650 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-4n6ctcad/files/wandb-summary.json
2025-07-13 18:16:44,930 INFO    Thread-12 :2941650 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-4n6ctcad/files/config.yaml
2025-07-13 18:16:45,020 DEBUG   HandlerThread:2941650 [handler.py:handle_request():144] handle_request: poll_exit
2025-07-13 18:16:45,930 INFO    Thread-12 :2941650 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-4n6ctcad/files/output.log
2025-07-13 18:16:46,023 INFO    SenderThread:2941650 [sender.py:transition_state():602] send defer: 8
2025-07-13 18:16:46,023 DEBUG   SenderThread:2941650 [sender.py:send_request():396] send_request: poll_exit
2025-07-13 18:16:46,023 DEBUG   HandlerThread:2941650 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:16:46,024 INFO    HandlerThread:2941650 [handler.py:handle_request_defer():170] handle defer: 8
2025-07-13 18:16:46,024 DEBUG   SenderThread:2941650 [sender.py:send_request():396] send_request: defer
2025-07-13 18:16:46,025 INFO    SenderThread:2941650 [sender.py:send_request_defer():598] handle sender defer: 8
2025-07-13 18:16:46,025 INFO    SenderThread:2941650 [job_builder.py:build():232] Attempting to build job artifact
2025-07-13 18:16:46,025 INFO    SenderThread:2941650 [job_builder.py:build():256] is repo sourced job
2025-07-13 18:16:46,029 INFO    SenderThread:2941650 [job_builder.py:build():297] adding wandb-job metadata file
2025-07-13 18:16:46,036 INFO    SenderThread:2941650 [sender.py:transition_state():602] send defer: 9
2025-07-13 18:16:46,037 DEBUG   HandlerThread:2941650 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:16:46,037 INFO    HandlerThread:2941650 [handler.py:handle_request_defer():170] handle defer: 9
2025-07-13 18:16:46,037 DEBUG   SenderThread:2941650 [sender.py:send():369] send: artifact
2025-07-13 18:16:46,637 INFO    wandb-upload_1:2941650 [upload_job.py:push():88] Skipped uploading /gel/usr/akath/.local/share/wandb/artifacts/staging/tmppce47b1f
2025-07-13 18:16:46,787 INFO    wandb-upload_2:2941650 [upload_job.py:push():91] Uploaded file /gel/usr/akath/.local/share/wandb/artifacts/staging/tmpi_yuzxax
2025-07-13 18:16:46,930 INFO    Thread-12 :2941650 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-4n6ctcad/files/output.log
2025-07-13 18:16:47,622 INFO    SenderThread:2941650 [sender.py:send_artifact():1450] sent artifact job-https___github.com_Shrinidhibhat87_codingchallenge_sereact.git_main.py - {'id': 'QXJ0aWZhY3Q6MTg3NzkyNTU2Ng==', 'digest': '24022c04cd416a726867eb3e3cc8a3ff', 'state': 'PENDING', 'aliases': [], 'artifactSequence': {'id': 'QXJ0aWZhY3RDb2xsZWN0aW9uOjY4NDgwMzQ0Ng==', 'latestArtifact': {'id': 'QXJ0aWZhY3Q6MTg3NzkyMzY4NQ==', 'versionIndex': 8}}, 'version': 'latest'}
2025-07-13 18:16:47,622 DEBUG   SenderThread:2941650 [sender.py:send_request():396] send_request: defer
2025-07-13 18:16:47,622 INFO    SenderThread:2941650 [sender.py:send_request_defer():598] handle sender defer: 9
2025-07-13 18:16:47,622 INFO    SenderThread:2941650 [dir_watcher.py:finish():359] shutting down directory watcher
2025-07-13 18:16:47,931 INFO    SenderThread:2941650 [dir_watcher.py:finish():389] scan: /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-4n6ctcad/files
2025-07-13 18:16:47,931 INFO    SenderThread:2941650 [dir_watcher.py:finish():403] scan save: /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-4n6ctcad/files/config.yaml config.yaml
2025-07-13 18:16:47,932 INFO    SenderThread:2941650 [dir_watcher.py:finish():403] scan save: /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-4n6ctcad/files/wandb-summary.json wandb-summary.json
2025-07-13 18:16:47,936 INFO    SenderThread:2941650 [dir_watcher.py:finish():403] scan save: /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-4n6ctcad/files/requirements.txt requirements.txt
2025-07-13 18:16:47,941 INFO    SenderThread:2941650 [dir_watcher.py:finish():403] scan save: /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-4n6ctcad/files/conda-environment.yaml conda-environment.yaml
2025-07-13 18:16:47,949 INFO    SenderThread:2941650 [dir_watcher.py:finish():403] scan save: /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-4n6ctcad/files/wandb-metadata.json wandb-metadata.json
2025-07-13 18:16:47,949 INFO    SenderThread:2941650 [dir_watcher.py:finish():403] scan save: /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-4n6ctcad/files/output.log output.log
2025-07-13 18:16:47,950 INFO    SenderThread:2941650 [sender.py:transition_state():602] send defer: 10
2025-07-13 18:16:47,953 DEBUG   HandlerThread:2941650 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:16:47,953 INFO    HandlerThread:2941650 [handler.py:handle_request_defer():170] handle defer: 10
2025-07-13 18:16:47,958 DEBUG   SenderThread:2941650 [sender.py:send_request():396] send_request: defer
2025-07-13 18:16:47,958 INFO    SenderThread:2941650 [sender.py:send_request_defer():598] handle sender defer: 10
2025-07-13 18:16:47,959 INFO    SenderThread:2941650 [file_pusher.py:finish():159] shutting down file pusher
2025-07-13 18:16:48,236 INFO    wandb-upload_4:2941650 [upload_job.py:push():133] Uploaded file /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-4n6ctcad/files/wandb-summary.json
2025-07-13 18:16:48,293 INFO    wandb-upload_2:2941650 [upload_job.py:push():133] Uploaded file /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-4n6ctcad/files/conda-environment.yaml
2025-07-13 18:16:48,307 INFO    wandb-upload_3:2941650 [upload_job.py:push():133] Uploaded file /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-4n6ctcad/files/requirements.txt
2025-07-13 18:16:48,330 INFO    wandb-upload_0:2941650 [upload_job.py:push():133] Uploaded file /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-4n6ctcad/files/config.yaml
2025-07-13 18:16:48,332 INFO    wandb-upload_5:2941650 [upload_job.py:push():133] Uploaded file /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-4n6ctcad/files/output.log
2025-07-13 18:16:48,533 INFO    Thread-11 :2941650 [sender.py:transition_state():602] send defer: 11
2025-07-13 18:16:48,533 DEBUG   HandlerThread:2941650 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:16:48,533 INFO    HandlerThread:2941650 [handler.py:handle_request_defer():170] handle defer: 11
2025-07-13 18:16:48,534 DEBUG   SenderThread:2941650 [sender.py:send_request():396] send_request: defer
2025-07-13 18:16:48,534 INFO    SenderThread:2941650 [sender.py:send_request_defer():598] handle sender defer: 11
2025-07-13 18:16:48,534 INFO    SenderThread:2941650 [file_pusher.py:join():164] waiting for file pusher
2025-07-13 18:16:48,534 INFO    SenderThread:2941650 [sender.py:transition_state():602] send defer: 12
2025-07-13 18:16:48,535 DEBUG   HandlerThread:2941650 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:16:48,535 INFO    HandlerThread:2941650 [handler.py:handle_request_defer():170] handle defer: 12
2025-07-13 18:16:48,535 DEBUG   SenderThread:2941650 [sender.py:send_request():396] send_request: defer
2025-07-13 18:16:48,535 INFO    SenderThread:2941650 [sender.py:send_request_defer():598] handle sender defer: 12
2025-07-13 18:16:48,605 INFO    SenderThread:2941650 [sender.py:transition_state():602] send defer: 13
2025-07-13 18:16:48,605 DEBUG   HandlerThread:2941650 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:16:48,606 INFO    HandlerThread:2941650 [handler.py:handle_request_defer():170] handle defer: 13
2025-07-13 18:16:48,606 DEBUG   SenderThread:2941650 [sender.py:send_request():396] send_request: defer
2025-07-13 18:16:48,606 INFO    SenderThread:2941650 [sender.py:send_request_defer():598] handle sender defer: 13
2025-07-13 18:16:48,606 INFO    SenderThread:2941650 [sender.py:transition_state():602] send defer: 14
2025-07-13 18:16:48,607 DEBUG   HandlerThread:2941650 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:16:48,607 DEBUG   SenderThread:2941650 [sender.py:send():369] send: final
2025-07-13 18:16:48,607 INFO    HandlerThread:2941650 [handler.py:handle_request_defer():170] handle defer: 14
2025-07-13 18:16:48,607 DEBUG   SenderThread:2941650 [sender.py:send():369] send: footer
2025-07-13 18:16:48,608 DEBUG   SenderThread:2941650 [sender.py:send_request():396] send_request: defer
2025-07-13 18:16:48,608 INFO    SenderThread:2941650 [sender.py:send_request_defer():598] handle sender defer: 14
2025-07-13 18:16:48,609 DEBUG   HandlerThread:2941650 [handler.py:handle_request():144] handle_request: poll_exit
2025-07-13 18:16:48,609 DEBUG   SenderThread:2941650 [sender.py:send_request():396] send_request: poll_exit
2025-07-13 18:16:48,610 DEBUG   HandlerThread:2941650 [handler.py:handle_request():144] handle_request: server_info
2025-07-13 18:16:48,611 DEBUG   SenderThread:2941650 [sender.py:send_request():396] send_request: server_info
2025-07-13 18:16:48,615 DEBUG   HandlerThread:2941650 [handler.py:handle_request():144] handle_request: get_summary
2025-07-13 18:16:48,616 DEBUG   HandlerThread:2941650 [handler.py:handle_request():144] handle_request: sampled_history
2025-07-13 18:16:48,685 INFO    MainThread:2941650 [wandb_run.py:_footer_history_summary_info():3467] rendering history
2025-07-13 18:16:48,685 INFO    MainThread:2941650 [wandb_run.py:_footer_history_summary_info():3499] rendering summary
2025-07-13 18:16:48,685 INFO    MainThread:2941650 [wandb_run.py:_footer_sync_info():3426] logging synced files
2025-07-13 18:16:48,686 DEBUG   HandlerThread:2941650 [handler.py:handle_request():144] handle_request: shutdown
2025-07-13 18:16:48,686 INFO    HandlerThread:2941650 [handler.py:finish():854] shutting down handler
2025-07-13 18:16:49,611 INFO    WriterThread:2941650 [datastore.py:close():298] close: /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-4n6ctcad/run-4n6ctcad.wandb
2025-07-13 18:16:49,685 INFO    SenderThread:2941650 [sender.py:finish():1526] shutting down sender
2025-07-13 18:16:49,685 INFO    SenderThread:2941650 [file_pusher.py:finish():159] shutting down file pusher
2025-07-13 18:16:49,685 INFO    SenderThread:2941650 [file_pusher.py:join():164] waiting for file pusher
