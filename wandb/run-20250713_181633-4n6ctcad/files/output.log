=> merge config from config/base_train.yaml
RANK and WORLD_SIZE in environ: 1/3
local rank 1 / global rank 1 successfully build train dataset
local rank 1 / global rank 1 successfully build val dataset
Traceback (most recent call last):
  File "main.py", line 558, in <module>
    main(config)
  File "main.py", line 159, in main
    miou, loss = validate(config, loss_module, epoch, iou_evaluator, data_loader_val, model)
  File "/gel/usr/akath/.conda/envs/swin/lib/python3.7/site-packages/torch/autograd/grad_mode.py", line 27, in decorate_context
    return func(*args, **kwargs)
  File "main.py", line 328, in validate
    inputs_rgb = [obj.cuda() for obj in batch['rgb_tensor']]
NameError: name 'batch' is not defined