2025-07-11 22:18:47,822 INFO    StreamThr :2831849 [internal.py:wandb_internal():89] W&B internal server running at pid: 2831849, started at: 2025-07-11 22:18:47.821401
2025-07-11 22:18:47,824 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: status
2025-07-11 22:18:47,829 INFO    WriterThread:2831849 [datastore.py:open_for_write():85] open: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-77am4sza/run-77am4sza.wandb
2025-07-11 22:18:47,829 DEBUG   SenderThread:2831849 [sender.py:send():369] send: header
2025-07-11 22:18:47,847 DEBUG   SenderThread:2831849 [sender.py:send():369] send: run
2025-07-11 22:18:48,192 INFO    SenderThread:2831849 [dir_watcher.py:__init__():211] watching files in: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-77am4sza/files
2025-07-11 22:18:48,193 INFO    SenderThread:2831849 [sender.py:_start_run_threads():1103] run started: 77am4sza with start time 1752286727.823346
2025-07-11 22:18:48,193 DEBUG   SenderThread:2831849 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:18:48,193 INFO    SenderThread:2831849 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:18:48,198 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: check_version
2025-07-11 22:18:48,199 DEBUG   SenderThread:2831849 [sender.py:send_request():396] send_request: check_version
2025-07-11 22:18:48,321 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: run_start
2025-07-11 22:18:48,325 DEBUG   HandlerThread:2831849 [system_info.py:__init__():31] System info init
2025-07-11 22:18:48,325 DEBUG   HandlerThread:2831849 [system_info.py:__init__():46] System info init done
2025-07-11 22:18:48,325 INFO    HandlerThread:2831849 [system_monitor.py:start():181] Starting system monitor
2025-07-11 22:18:48,325 INFO    SystemMonitor:2831849 [system_monitor.py:_start():145] Starting system asset monitoring threads
2025-07-11 22:18:48,326 INFO    HandlerThread:2831849 [system_monitor.py:probe():201] Collecting system info
2025-07-11 22:18:48,326 INFO    SystemMonitor:2831849 [interfaces.py:start():190] Started cpu monitoring
2025-07-11 22:18:48,327 INFO    SystemMonitor:2831849 [interfaces.py:start():190] Started disk monitoring
2025-07-11 22:18:48,328 INFO    SystemMonitor:2831849 [interfaces.py:start():190] Started gpu monitoring
2025-07-11 22:18:48,329 INFO    SystemMonitor:2831849 [interfaces.py:start():190] Started memory monitoring
2025-07-11 22:18:48,330 INFO    SystemMonitor:2831849 [interfaces.py:start():190] Started network monitoring
2025-07-11 22:18:48,361 DEBUG   HandlerThread:2831849 [system_info.py:probe():195] Probing system
2025-07-11 22:18:48,369 DEBUG   HandlerThread:2831849 [system_info.py:_probe_git():180] Probing git
2025-07-11 22:18:48,385 DEBUG   HandlerThread:2831849 [system_info.py:_probe_git():188] Probing git done
2025-07-11 22:18:48,386 DEBUG   HandlerThread:2831849 [system_info.py:probe():240] Probing system done
2025-07-11 22:18:48,386 DEBUG   HandlerThread:2831849 [system_monitor.py:probe():210] {'os': 'Linux-5.4.0-77-generic-x86_64-with-debian-bullseye-sid', 'python': '3.7.16', 'heartbeatAt': '2025-07-12T02:18:48.361242', 'startedAt': '2025-07-12T02:18:47.805915', 'docker': None, 'cuda': None, 'args': ('--local_rank=1', '--cfg', 'config/base_train.yaml', '--output', '/home-local2/akath.extra.nobkp/sereact', '--data-path', '/home-local2/akath.extra.nobkp/dl_challenge', '--batch-size', '2'), 'state': 'running', 'program': 'main.py', 'codePath': 'main.py', 'git': {'remote': 'https://github.com/Shrinidhibhat87/codingchallenge_sereact.git', 'commit': 'a3ff60e13dfced588b500c8a1de00f36fdf22d49'}, 'email': '<EMAIL>', 'root': '/home-local/akath.nobkp/codingchallenge_sereact', 'host': 'lv3-32190', 'username': 'akath', 'executable': '/gel/usr/akath/.conda/envs/swin/bin/python', 'cpu_count': 6, 'cpu_count_logical': 12, 'cpu_freq': {'current': 1.5428333333333333, 'min': 1200.0, 'max': 4000.0}, 'cpu_freq_per_core': [{'current': 1.199, 'min': 1200.0, 'max': 4000.0}, {'current': 1.599, 'min': 1200.0, 'max': 4000.0}, {'current': 1.199, 'min': 1200.0, 'max': 4000.0}, {'current': 1.403, 'min': 1200.0, 'max': 4000.0}, {'current': 1.199, 'min': 1200.0, 'max': 4000.0}, {'current': 3.008, 'min': 1200.0, 'max': 4000.0}, {'current': 1.199, 'min': 1200.0, 'max': 4000.0}, {'current': 1.599, 'min': 1200.0, 'max': 4000.0}, {'current': 1.198, 'min': 1200.0, 'max': 4000.0}, {'current': 1.399, 'min': 1200.0, 'max': 4000.0}, {'current': 1.199, 'min': 1200.0, 'max': 4000.0}, {'current': 2.313, 'min': 1200.0, 'max': 4000.0}], 'disk': {'total': 111.2200813293457, 'used': 53.229248046875}, 'gpu': 'TITAN Xp', 'gpu_count': 3, 'gpu_devices': [{'name': 'TITAN Xp', 'memory_total': 12787122176}, {'name': 'TITAN Xp', 'memory_total': 12788498432}, {'name': 'TITAN Xp', 'memory_total': 12788498432}], 'memory': {'total': 62.72977066040039}}
2025-07-11 22:18:48,386 INFO    HandlerThread:2831849 [system_monitor.py:probe():211] Finished collecting system info
2025-07-11 22:18:48,386 INFO    HandlerThread:2831849 [system_monitor.py:probe():214] Publishing system info
2025-07-11 22:18:48,386 DEBUG   HandlerThread:2831849 [system_info.py:_save_pip():52] Saving list of pip packages installed into the current environment
2025-07-11 22:18:48,387 DEBUG   HandlerThread:2831849 [system_info.py:_save_pip():67] Saving pip packages done
2025-07-11 22:18:48,387 DEBUG   HandlerThread:2831849 [system_info.py:_save_conda():75] Saving list of conda packages installed into the current environment
2025-07-11 22:18:49,197 INFO    Thread-12 :2831849 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-77am4sza/files/wandb-summary.json
2025-07-11 22:18:49,198 INFO    Thread-12 :2831849 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-77am4sza/files/conda-environment.yaml
2025-07-11 22:18:49,198 INFO    Thread-12 :2831849 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-77am4sza/files/requirements.txt
2025-07-11 22:18:51,168 DEBUG   HandlerThread:2831849 [system_info.py:_save_conda():86] Saving conda packages done
2025-07-11 22:18:51,169 INFO    HandlerThread:2831849 [system_monitor.py:probe():216] Finished publishing system info
2025-07-11 22:18:51,183 DEBUG   SenderThread:2831849 [sender.py:send():369] send: files
2025-07-11 22:18:51,183 INFO    SenderThread:2831849 [sender.py:_save_file():1354] saving file wandb-metadata.json with policy now
2025-07-11 22:18:51,192 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:18:51,193 DEBUG   SenderThread:2831849 [sender.py:send():369] send: telemetry
2025-07-11 22:18:51,193 DEBUG   SenderThread:2831849 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:18:51,199 INFO    Thread-12 :2831849 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-77am4sza/files/conda-environment.yaml
2025-07-11 22:18:51,199 INFO    Thread-12 :2831849 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-77am4sza/files/wandb-metadata.json
2025-07-11 22:18:51,524 INFO    wandb-upload_0:2831849 [upload_job.py:push():133] Uploaded file /tmp/tmp95o6f1n_wandb/zdwvltf5-wandb-metadata.json
2025-07-11 22:18:52,200 INFO    Thread-12 :2831849 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-77am4sza/files/output.log
2025-07-11 22:18:53,276 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:18:54,200 INFO    Thread-12 :2831849 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-77am4sza/files/output.log
2025-07-11 22:18:56,201 INFO    Thread-12 :2831849 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-77am4sza/files/output.log
2025-07-11 22:18:59,208 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:18:59,251 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:18:59,253 DEBUG   SenderThread:2831849 [sender.py:send():369] send: history
2025-07-11 22:18:59,253 DEBUG   SenderThread:2831849 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:18:59,259 INFO    SenderThread:2831849 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:19:00,203 INFO    Thread-12 :2831849 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-77am4sza/files/wandb-summary.json
2025-07-11 22:19:02,330 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:19:02,331 DEBUG   SenderThread:2831849 [sender.py:send():369] send: history
2025-07-11 22:19:02,331 DEBUG   SenderThread:2831849 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:19:02,334 INFO    SenderThread:2831849 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:19:03,204 INFO    Thread-12 :2831849 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-77am4sza/files/wandb-summary.json
2025-07-11 22:19:04,335 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:19:05,278 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:19:05,279 DEBUG   SenderThread:2831849 [sender.py:send():369] send: history
2025-07-11 22:19:05,279 DEBUG   SenderThread:2831849 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:19:05,280 INFO    SenderThread:2831849 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:19:06,190 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:19:06,191 DEBUG   SenderThread:2831849 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:19:06,205 INFO    Thread-12 :2831849 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-77am4sza/files/wandb-summary.json
2025-07-11 22:19:08,038 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:19:08,039 DEBUG   SenderThread:2831849 [sender.py:send():369] send: history
2025-07-11 22:19:08,040 DEBUG   SenderThread:2831849 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:19:08,040 INFO    SenderThread:2831849 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:19:08,206 INFO    Thread-12 :2831849 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-77am4sza/files/wandb-summary.json
2025-07-11 22:19:10,042 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:19:10,692 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:19:10,693 DEBUG   SenderThread:2831849 [sender.py:send():369] send: history
2025-07-11 22:19:10,694 DEBUG   SenderThread:2831849 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:19:10,694 INFO    SenderThread:2831849 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:19:11,207 INFO    Thread-12 :2831849 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-77am4sza/files/wandb-summary.json
2025-07-11 22:19:14,079 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:19:14,080 DEBUG   SenderThread:2831849 [sender.py:send():369] send: history
2025-07-11 22:19:14,081 DEBUG   SenderThread:2831849 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:19:14,081 INFO    SenderThread:2831849 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:19:14,208 INFO    Thread-12 :2831849 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-77am4sza/files/wandb-summary.json
2025-07-11 22:19:15,082 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:19:16,610 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:19:16,611 DEBUG   SenderThread:2831849 [sender.py:send():369] send: history
2025-07-11 22:19:16,611 DEBUG   SenderThread:2831849 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:19:16,612 INFO    SenderThread:2831849 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:19:17,209 INFO    Thread-12 :2831849 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-77am4sza/files/wandb-summary.json
2025-07-11 22:19:19,275 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:19:19,276 DEBUG   SenderThread:2831849 [sender.py:send():369] send: history
2025-07-11 22:19:19,276 DEBUG   SenderThread:2831849 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:19:19,276 INFO    SenderThread:2831849 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:19:20,210 INFO    Thread-12 :2831849 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-77am4sza/files/wandb-summary.json
2025-07-11 22:19:20,282 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:19:21,189 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:19:21,190 DEBUG   SenderThread:2831849 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:19:21,211 INFO    Thread-12 :2831849 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-77am4sza/files/config.yaml
2025-07-11 22:19:22,630 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:19:22,631 DEBUG   SenderThread:2831849 [sender.py:send():369] send: history
2025-07-11 22:19:22,632 DEBUG   SenderThread:2831849 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:19:22,632 INFO    SenderThread:2831849 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:19:23,211 INFO    Thread-12 :2831849 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-77am4sza/files/wandb-summary.json
2025-07-11 22:19:25,601 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:19:25,602 DEBUG   SenderThread:2831849 [sender.py:send():369] send: history
2025-07-11 22:19:25,603 DEBUG   SenderThread:2831849 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:19:25,603 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:19:25,604 INFO    SenderThread:2831849 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:19:26,212 INFO    Thread-12 :2831849 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-77am4sza/files/wandb-summary.json
2025-07-11 22:19:28,695 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:19:28,696 DEBUG   SenderThread:2831849 [sender.py:send():369] send: history
2025-07-11 22:19:28,696 DEBUG   SenderThread:2831849 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:19:28,697 INFO    SenderThread:2831849 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:19:29,214 INFO    Thread-12 :2831849 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-77am4sza/files/wandb-summary.json
2025-07-11 22:19:30,699 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:19:31,061 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:19:31,062 DEBUG   SenderThread:2831849 [sender.py:send():369] send: history
2025-07-11 22:19:31,062 DEBUG   SenderThread:2831849 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:19:31,063 INFO    SenderThread:2831849 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:19:31,215 INFO    Thread-12 :2831849 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-77am4sza/files/wandb-summary.json
2025-07-11 22:19:33,341 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:19:33,342 DEBUG   SenderThread:2831849 [sender.py:send():369] send: history
2025-07-11 22:19:33,342 DEBUG   SenderThread:2831849 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:19:33,342 INFO    SenderThread:2831849 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:19:34,216 INFO    Thread-12 :2831849 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-77am4sza/files/wandb-summary.json
2025-07-11 22:19:36,189 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:19:36,190 DEBUG   SenderThread:2831849 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:19:36,280 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:19:36,313 DEBUG   SenderThread:2831849 [sender.py:send():369] send: history
2025-07-11 22:19:36,313 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:19:36,314 DEBUG   SenderThread:2831849 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:19:36,314 INFO    SenderThread:2831849 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:19:37,217 INFO    Thread-12 :2831849 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-77am4sza/files/wandb-summary.json
2025-07-11 22:19:39,798 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:19:39,799 DEBUG   SenderThread:2831849 [sender.py:send():369] send: history
2025-07-11 22:19:39,799 DEBUG   SenderThread:2831849 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:19:39,799 INFO    SenderThread:2831849 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:19:40,218 INFO    Thread-12 :2831849 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-77am4sza/files/wandb-summary.json
2025-07-11 22:19:41,802 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:19:42,583 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:19:42,583 DEBUG   SenderThread:2831849 [sender.py:send():369] send: history
2025-07-11 22:19:42,584 DEBUG   SenderThread:2831849 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:19:42,584 INFO    SenderThread:2831849 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:19:43,219 INFO    Thread-12 :2831849 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-77am4sza/files/wandb-summary.json
2025-07-11 22:19:45,171 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:19:45,172 DEBUG   SenderThread:2831849 [sender.py:send():369] send: history
2025-07-11 22:19:45,173 DEBUG   SenderThread:2831849 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:19:45,173 INFO    SenderThread:2831849 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:19:45,220 INFO    Thread-12 :2831849 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-77am4sza/files/wandb-summary.json
2025-07-11 22:19:47,175 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:19:47,837 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:19:47,838 DEBUG   SenderThread:2831849 [sender.py:send():369] send: history
2025-07-11 22:19:47,838 DEBUG   SenderThread:2831849 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:19:47,838 INFO    SenderThread:2831849 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:19:48,221 INFO    Thread-12 :2831849 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-77am4sza/files/wandb-summary.json
2025-07-11 22:19:48,330 DEBUG   SystemMonitor:2831849 [system_monitor.py:_start():159] Starting system metrics aggregation loop
2025-07-11 22:19:48,332 DEBUG   SenderThread:2831849 [sender.py:send():369] send: stats
2025-07-11 22:19:50,234 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:19:50,235 DEBUG   SenderThread:2831849 [sender.py:send():369] send: history
2025-07-11 22:19:50,235 DEBUG   SenderThread:2831849 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:19:50,235 INFO    SenderThread:2831849 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:19:51,190 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:19:51,190 DEBUG   SenderThread:2831849 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:19:51,222 INFO    Thread-12 :2831849 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-77am4sza/files/wandb-summary.json
2025-07-11 22:19:52,291 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:19:52,995 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:19:52,996 DEBUG   SenderThread:2831849 [sender.py:send():369] send: history
2025-07-11 22:19:52,996 DEBUG   SenderThread:2831849 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:19:52,997 INFO    SenderThread:2831849 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:19:53,223 INFO    Thread-12 :2831849 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-77am4sza/files/wandb-summary.json
2025-07-11 22:19:55,620 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:19:55,621 DEBUG   SenderThread:2831849 [sender.py:send():369] send: history
2025-07-11 22:19:55,621 DEBUG   SenderThread:2831849 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:19:55,621 INFO    SenderThread:2831849 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:19:56,224 INFO    Thread-12 :2831849 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-77am4sza/files/wandb-summary.json
2025-07-11 22:19:57,622 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:19:58,776 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:19:58,777 DEBUG   SenderThread:2831849 [sender.py:send():369] send: history
2025-07-11 22:19:58,777 DEBUG   SenderThread:2831849 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:19:58,779 INFO    SenderThread:2831849 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:19:59,225 INFO    Thread-12 :2831849 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-77am4sza/files/wandb-summary.json
2025-07-11 22:20:00,481 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:20:00,481 DEBUG   SenderThread:2831849 [sender.py:send():369] send: history
2025-07-11 22:20:00,482 DEBUG   SenderThread:2831849 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:20:00,482 INFO    SenderThread:2831849 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:20:01,225 INFO    Thread-12 :2831849 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-77am4sza/files/wandb-summary.json
2025-07-11 22:20:02,281 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:20:02,282 DEBUG   SenderThread:2831849 [sender.py:send():369] send: history
2025-07-11 22:20:02,282 DEBUG   SenderThread:2831849 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:20:02,283 INFO    SenderThread:2831849 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:20:03,226 INFO    Thread-12 :2831849 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-77am4sza/files/wandb-summary.json
2025-07-11 22:20:03,283 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:20:04,586 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:20:04,587 DEBUG   SenderThread:2831849 [sender.py:send():369] send: history
2025-07-11 22:20:04,587 DEBUG   SenderThread:2831849 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:20:04,587 INFO    SenderThread:2831849 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:20:05,227 INFO    Thread-12 :2831849 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-77am4sza/files/wandb-summary.json
2025-07-11 22:20:06,191 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:20:06,192 DEBUG   SenderThread:2831849 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:20:07,480 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:20:07,480 DEBUG   SenderThread:2831849 [sender.py:send():369] send: history
2025-07-11 22:20:07,481 DEBUG   SenderThread:2831849 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:20:07,481 INFO    SenderThread:2831849 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:20:08,228 INFO    Thread-12 :2831849 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-77am4sza/files/wandb-summary.json
2025-07-11 22:20:08,481 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:20:10,692 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:20:10,693 DEBUG   SenderThread:2831849 [sender.py:send():369] send: history
2025-07-11 22:20:10,693 DEBUG   SenderThread:2831849 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:20:10,697 INFO    SenderThread:2831849 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:20:10,931 DEBUG   SenderThread:2831849 [sender.py:send():369] send: exit
2025-07-11 22:20:10,931 INFO    SenderThread:2831849 [sender.py:send_exit():574] handling exit code: 1
2025-07-11 22:20:10,931 INFO    SenderThread:2831849 [sender.py:send_exit():576] handling runtime: 82
2025-07-11 22:20:10,931 INFO    SenderThread:2831849 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:20:10,932 INFO    SenderThread:2831849 [sender.py:send_exit():582] send defer
2025-07-11 22:20:10,932 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: defer
2025-07-11 22:20:10,932 INFO    HandlerThread:2831849 [handler.py:handle_request_defer():170] handle defer: 0
2025-07-11 22:20:10,932 DEBUG   SenderThread:2831849 [sender.py:send_request():396] send_request: defer
2025-07-11 22:20:10,932 INFO    SenderThread:2831849 [sender.py:send_request_defer():598] handle sender defer: 0
2025-07-11 22:20:10,932 INFO    SenderThread:2831849 [sender.py:transition_state():602] send defer: 1
2025-07-11 22:20:10,933 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: defer
2025-07-11 22:20:10,933 INFO    HandlerThread:2831849 [handler.py:handle_request_defer():170] handle defer: 1
2025-07-11 22:20:10,933 DEBUG   SenderThread:2831849 [sender.py:send_request():396] send_request: defer
2025-07-11 22:20:10,933 INFO    SenderThread:2831849 [sender.py:send_request_defer():598] handle sender defer: 1
2025-07-11 22:20:10,933 INFO    SenderThread:2831849 [sender.py:transition_state():602] send defer: 2
2025-07-11 22:20:10,933 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: defer
2025-07-11 22:20:10,933 INFO    HandlerThread:2831849 [handler.py:handle_request_defer():170] handle defer: 2
2025-07-11 22:20:10,933 INFO    HandlerThread:2831849 [system_monitor.py:finish():190] Stopping system monitor
2025-07-11 22:20:10,933 DEBUG   SystemMonitor:2831849 [system_monitor.py:_start():166] Finished system metrics aggregation loop
2025-07-11 22:20:10,934 DEBUG   SystemMonitor:2831849 [system_monitor.py:_start():170] Publishing last batch of metrics
2025-07-11 22:20:10,934 INFO    HandlerThread:2831849 [interfaces.py:finish():202] Joined cpu monitor
2025-07-11 22:20:10,935 INFO    HandlerThread:2831849 [interfaces.py:finish():202] Joined disk monitor
2025-07-11 22:20:11,067 INFO    HandlerThread:2831849 [interfaces.py:finish():202] Joined gpu monitor
2025-07-11 22:20:11,068 INFO    HandlerThread:2831849 [interfaces.py:finish():202] Joined memory monitor
2025-07-11 22:20:11,068 INFO    HandlerThread:2831849 [interfaces.py:finish():202] Joined network monitor
2025-07-11 22:20:11,069 DEBUG   SenderThread:2831849 [sender.py:send_request():396] send_request: defer
2025-07-11 22:20:11,069 INFO    SenderThread:2831849 [sender.py:send_request_defer():598] handle sender defer: 2
2025-07-11 22:20:11,069 INFO    SenderThread:2831849 [sender.py:transition_state():602] send defer: 3
2025-07-11 22:20:11,069 DEBUG   SenderThread:2831849 [sender.py:send():369] send: stats
2025-07-11 22:20:11,070 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: defer
2025-07-11 22:20:11,070 INFO    HandlerThread:2831849 [handler.py:handle_request_defer():170] handle defer: 3
2025-07-11 22:20:11,070 DEBUG   SenderThread:2831849 [sender.py:send_request():396] send_request: defer
2025-07-11 22:20:11,070 INFO    SenderThread:2831849 [sender.py:send_request_defer():598] handle sender defer: 3
2025-07-11 22:20:11,070 INFO    SenderThread:2831849 [sender.py:transition_state():602] send defer: 4
2025-07-11 22:20:11,071 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: defer
2025-07-11 22:20:11,071 INFO    HandlerThread:2831849 [handler.py:handle_request_defer():170] handle defer: 4
2025-07-11 22:20:11,071 DEBUG   SenderThread:2831849 [sender.py:send_request():396] send_request: defer
2025-07-11 22:20:11,071 INFO    SenderThread:2831849 [sender.py:send_request_defer():598] handle sender defer: 4
2025-07-11 22:20:11,071 INFO    SenderThread:2831849 [sender.py:transition_state():602] send defer: 5
2025-07-11 22:20:11,071 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: defer
2025-07-11 22:20:11,071 INFO    HandlerThread:2831849 [handler.py:handle_request_defer():170] handle defer: 5
2025-07-11 22:20:11,072 DEBUG   SenderThread:2831849 [sender.py:send():369] send: summary
2025-07-11 22:20:11,072 INFO    SenderThread:2831849 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:20:11,072 DEBUG   SenderThread:2831849 [sender.py:send_request():396] send_request: defer
2025-07-11 22:20:11,072 INFO    SenderThread:2831849 [sender.py:send_request_defer():598] handle sender defer: 5
2025-07-11 22:20:11,072 INFO    SenderThread:2831849 [sender.py:transition_state():602] send defer: 6
2025-07-11 22:20:11,072 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: defer
2025-07-11 22:20:11,072 INFO    HandlerThread:2831849 [handler.py:handle_request_defer():170] handle defer: 6
2025-07-11 22:20:11,073 DEBUG   SenderThread:2831849 [sender.py:send_request():396] send_request: defer
2025-07-11 22:20:11,073 INFO    SenderThread:2831849 [sender.py:send_request_defer():598] handle sender defer: 6
2025-07-11 22:20:11,073 INFO    SenderThread:2831849 [sender.py:transition_state():602] send defer: 7
2025-07-11 22:20:11,073 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:20:11,073 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: defer
2025-07-11 22:20:11,073 INFO    HandlerThread:2831849 [handler.py:handle_request_defer():170] handle defer: 7
2025-07-11 22:20:11,073 DEBUG   SenderThread:2831849 [sender.py:send_request():396] send_request: defer
2025-07-11 22:20:11,073 INFO    SenderThread:2831849 [sender.py:send_request_defer():598] handle sender defer: 7
2025-07-11 22:20:11,229 INFO    Thread-12 :2831849 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-77am4sza/files/wandb-summary.json
2025-07-11 22:20:11,928 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: poll_exit
2025-07-11 22:20:12,937 INFO    SenderThread:2831849 [sender.py:transition_state():602] send defer: 8
2025-07-11 22:20:12,937 DEBUG   SenderThread:2831849 [sender.py:send_request():396] send_request: poll_exit
2025-07-11 22:20:12,937 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: defer
2025-07-11 22:20:12,937 INFO    HandlerThread:2831849 [handler.py:handle_request_defer():170] handle defer: 8
2025-07-11 22:20:12,938 DEBUG   SenderThread:2831849 [sender.py:send_request():396] send_request: defer
2025-07-11 22:20:12,938 INFO    SenderThread:2831849 [sender.py:send_request_defer():598] handle sender defer: 8
2025-07-11 22:20:12,938 INFO    SenderThread:2831849 [job_builder.py:build():232] Attempting to build job artifact
2025-07-11 22:20:12,938 INFO    SenderThread:2831849 [job_builder.py:build():256] is repo sourced job
2025-07-11 22:20:12,940 INFO    SenderThread:2831849 [job_builder.py:build():297] adding wandb-job metadata file
2025-07-11 22:20:12,943 INFO    SenderThread:2831849 [sender.py:transition_state():602] send defer: 9
2025-07-11 22:20:12,944 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: defer
2025-07-11 22:20:12,944 DEBUG   SenderThread:2831849 [sender.py:send():369] send: artifact
2025-07-11 22:20:12,944 INFO    HandlerThread:2831849 [handler.py:handle_request_defer():170] handle defer: 9
2025-07-11 22:20:13,230 INFO    Thread-12 :2831849 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-77am4sza/files/output.log
2025-07-11 22:20:13,587 INFO    wandb-upload_0:2831849 [upload_job.py:push():91] Uploaded file /gel/usr/akath/.local/share/wandb/artifacts/staging/tmp593i7fk5
2025-07-11 22:20:13,598 INFO    wandb-upload_1:2831849 [upload_job.py:push():91] Uploaded file /gel/usr/akath/.local/share/wandb/artifacts/staging/tmpf3ydg9a5
2025-07-11 22:20:14,302 INFO    SenderThread:2831849 [sender.py:send_artifact():1450] sent artifact job-https___github.com_Shrinidhibhat87_codingchallenge_sereact.git_main.py - {'id': 'QXJ0aWZhY3Q6MTg3NDAzMjEwMw==', 'digest': '4f4ba9767a27b00621d65a0eba015245', 'state': 'PENDING', 'aliases': [], 'artifactSequence': {'id': 'QXJ0aWZhY3RDb2xsZWN0aW9uOjY4NDgwMzQ0Ng==', 'latestArtifact': None}, 'version': 'latest'}
2025-07-11 22:20:14,302 DEBUG   SenderThread:2831849 [sender.py:send_request():396] send_request: defer
2025-07-11 22:20:14,303 INFO    SenderThread:2831849 [sender.py:send_request_defer():598] handle sender defer: 9
2025-07-11 22:20:14,303 INFO    SenderThread:2831849 [dir_watcher.py:finish():359] shutting down directory watcher
2025-07-11 22:20:15,231 INFO    SenderThread:2831849 [dir_watcher.py:finish():389] scan: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-77am4sza/files
2025-07-11 22:20:15,231 INFO    SenderThread:2831849 [dir_watcher.py:finish():403] scan save: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-77am4sza/files/config.yaml config.yaml
2025-07-11 22:20:15,232 INFO    SenderThread:2831849 [dir_watcher.py:finish():403] scan save: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-77am4sza/files/wandb-summary.json wandb-summary.json
2025-07-11 22:20:15,237 INFO    SenderThread:2831849 [dir_watcher.py:finish():403] scan save: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-77am4sza/files/requirements.txt requirements.txt
2025-07-11 22:20:15,238 INFO    SenderThread:2831849 [dir_watcher.py:finish():403] scan save: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-77am4sza/files/conda-environment.yaml conda-environment.yaml
2025-07-11 22:20:15,241 INFO    SenderThread:2831849 [dir_watcher.py:finish():403] scan save: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-77am4sza/files/wandb-metadata.json wandb-metadata.json
2025-07-11 22:20:15,241 INFO    SenderThread:2831849 [dir_watcher.py:finish():403] scan save: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-77am4sza/files/output.log output.log
2025-07-11 22:20:15,245 INFO    SenderThread:2831849 [sender.py:transition_state():602] send defer: 10
2025-07-11 22:20:15,255 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: defer
2025-07-11 22:20:15,255 INFO    HandlerThread:2831849 [handler.py:handle_request_defer():170] handle defer: 10
2025-07-11 22:20:15,261 DEBUG   SenderThread:2831849 [sender.py:send_request():396] send_request: defer
2025-07-11 22:20:15,261 INFO    SenderThread:2831849 [sender.py:send_request_defer():598] handle sender defer: 10
2025-07-11 22:20:15,261 INFO    SenderThread:2831849 [file_pusher.py:finish():159] shutting down file pusher
2025-07-11 22:20:15,553 INFO    wandb-upload_6:2831849 [upload_job.py:push():133] Uploaded file /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-77am4sza/files/conda-environment.yaml
2025-07-11 22:20:15,588 INFO    wandb-upload_2:2831849 [upload_job.py:push():133] Uploaded file /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-77am4sza/files/config.yaml
2025-07-11 22:20:15,607 INFO    wandb-upload_4:2831849 [upload_job.py:push():133] Uploaded file /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-77am4sza/files/wandb-summary.json
2025-07-11 22:20:15,648 INFO    wandb-upload_1:2831849 [upload_job.py:push():133] Uploaded file /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-77am4sza/files/requirements.txt
2025-07-11 22:20:15,683 INFO    wandb-upload_5:2831849 [upload_job.py:push():133] Uploaded file /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-77am4sza/files/output.log
2025-07-11 22:20:15,883 INFO    Thread-11 :2831849 [sender.py:transition_state():602] send defer: 11
2025-07-11 22:20:15,884 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: defer
2025-07-11 22:20:15,884 INFO    HandlerThread:2831849 [handler.py:handle_request_defer():170] handle defer: 11
2025-07-11 22:20:15,885 DEBUG   SenderThread:2831849 [sender.py:send_request():396] send_request: defer
2025-07-11 22:20:15,885 INFO    SenderThread:2831849 [sender.py:send_request_defer():598] handle sender defer: 11
2025-07-11 22:20:15,885 INFO    SenderThread:2831849 [file_pusher.py:join():164] waiting for file pusher
2025-07-11 22:20:15,885 INFO    SenderThread:2831849 [sender.py:transition_state():602] send defer: 12
2025-07-11 22:20:15,886 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: defer
2025-07-11 22:20:15,886 INFO    HandlerThread:2831849 [handler.py:handle_request_defer():170] handle defer: 12
2025-07-11 22:20:15,886 DEBUG   SenderThread:2831849 [sender.py:send_request():396] send_request: defer
2025-07-11 22:20:15,886 INFO    SenderThread:2831849 [sender.py:send_request_defer():598] handle sender defer: 12
2025-07-11 22:20:16,193 INFO    SenderThread:2831849 [sender.py:transition_state():602] send defer: 13
2025-07-11 22:20:16,193 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: defer
2025-07-11 22:20:16,193 INFO    HandlerThread:2831849 [handler.py:handle_request_defer():170] handle defer: 13
2025-07-11 22:20:16,194 DEBUG   SenderThread:2831849 [sender.py:send_request():396] send_request: defer
2025-07-11 22:20:16,194 INFO    SenderThread:2831849 [sender.py:send_request_defer():598] handle sender defer: 13
2025-07-11 22:20:16,194 INFO    SenderThread:2831849 [sender.py:transition_state():602] send defer: 14
2025-07-11 22:20:16,195 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: defer
2025-07-11 22:20:16,195 DEBUG   SenderThread:2831849 [sender.py:send():369] send: final
2025-07-11 22:20:16,195 INFO    HandlerThread:2831849 [handler.py:handle_request_defer():170] handle defer: 14
2025-07-11 22:20:16,195 DEBUG   SenderThread:2831849 [sender.py:send():369] send: footer
2025-07-11 22:20:16,196 DEBUG   SenderThread:2831849 [sender.py:send_request():396] send_request: defer
2025-07-11 22:20:16,196 INFO    SenderThread:2831849 [sender.py:send_request_defer():598] handle sender defer: 14
2025-07-11 22:20:16,197 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: poll_exit
2025-07-11 22:20:16,198 DEBUG   SenderThread:2831849 [sender.py:send_request():396] send_request: poll_exit
2025-07-11 22:20:16,198 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: server_info
2025-07-11 22:20:16,199 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: get_summary
2025-07-11 22:20:16,200 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: sampled_history
2025-07-11 22:20:16,200 DEBUG   SenderThread:2831849 [sender.py:send_request():396] send_request: server_info
2025-07-11 22:20:16,260 INFO    MainThread:2831849 [wandb_run.py:_footer_history_summary_info():3467] rendering history
2025-07-11 22:20:16,261 INFO    MainThread:2831849 [wandb_run.py:_footer_history_summary_info():3499] rendering summary
2025-07-11 22:20:16,261 INFO    MainThread:2831849 [wandb_run.py:_footer_sync_info():3426] logging synced files
2025-07-11 22:20:16,262 DEBUG   HandlerThread:2831849 [handler.py:handle_request():144] handle_request: shutdown
2025-07-11 22:20:16,262 INFO    HandlerThread:2831849 [handler.py:finish():854] shutting down handler
2025-07-11 22:20:16,916 WARNING StreamThr :2831849 [internal.py:is_dead():415] Internal process exiting, parent pid 2831799 disappeared
2025-07-11 22:20:16,916 ERROR   StreamThr :2831849 [internal.py:wandb_internal():152] Internal process shutdown.
2025-07-11 22:20:17,200 INFO    WriterThread:2831849 [datastore.py:close():298] close: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-77am4sza/run-77am4sza.wandb
2025-07-11 22:20:17,260 INFO    SenderThread:2831849 [sender.py:finish():1526] shutting down sender
2025-07-11 22:20:17,260 INFO    SenderThread:2831849 [file_pusher.py:finish():159] shutting down file pusher
2025-07-11 22:20:17,260 INFO    SenderThread:2831849 [file_pusher.py:join():164] waiting for file pusher
