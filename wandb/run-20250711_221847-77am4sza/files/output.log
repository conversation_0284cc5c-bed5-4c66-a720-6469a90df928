=> merge config from config/base_train.yaml
RANK and WORLD_SIZE in environ: 1/3
local rank 1 / global rank 1 successfully build train dataset
local rank 1 / global rank 1 successfully build val dataset
Traceback (most recent call last):
  File "main.py", line 555, in <module>
    main(config)
  File "main.py", line 161, in main
    miou, loss = validate(config, loss_module, epoch, iou_evaluator, data_loader_val, model)
  File "/gel/usr/akath/.conda/envs/swin/lib/python3.7/site-packages/torch/autograd/grad_mode.py", line 27, in decorate_context
    return func(*args, **kwargs)
  File "main.py", line 321, in validate
    for _, batch_data in enumerate(data_loader):
  File "/gel/usr/akath/.conda/envs/swin/lib/python3.7/site-packages/torch/utils/data/dataloader.py", line 517, in __next__
    data = self._next_data()
  File "/gel/usr/akath/.conda/envs/swin/lib/python3.7/site-packages/torch/utils/data/dataloader.py", line 1199, in _next_data
    return self._process_data(data)
  File "/gel/usr/akath/.conda/envs/swin/lib/python3.7/site-packages/torch/utils/data/dataloader.py", line 1225, in _process_data
    data.reraise()
  File "/gel/usr/akath/.conda/envs/swin/lib/python3.7/site-packages/torch/_utils.py", line 429, in reraise
    raise self.exc_type(msg)
AssertionError: Caught AssertionError in DataLoader worker process 0.
Original Traceback (most recent call last):
  File "/gel/usr/akath/.conda/envs/swin/lib/python3.7/site-packages/torch/utils/data/_utils/worker.py", line 202, in _worker_loop
    data = fetcher.fetch(index)
  File "/gel/usr/akath/.conda/envs/swin/lib/python3.7/site-packages/torch/utils/data/_utils/fetch.py", line 44, in fetch
    data = [self.dataset[idx] for idx in possibly_batched_index]
  File "/gel/usr/akath/.conda/envs/swin/lib/python3.7/site-packages/torch/utils/data/_utils/fetch.py", line 44, in <listcomp>
    data = [self.dataset[idx] for idx in possibly_batched_index]
  File "/home-local/akath.nobkp/codingchallenge_sereact/dataloader/sereact_data_loader.py", line 119, in __getitem__
    pcd_normalized, centroid, max_dist = self.normalize_pointcloud(pcd) #(368836, 3)
  File "/home-local/akath.nobkp/codingchallenge_sereact/dataloader/sereact_data_loader.py", line 58, in normalize_pointcloud
    assert points.ndim == 2 and points.shape[1] == 3, f"Expected shape (N, 3), got {points.shape}"
AssertionError: Expected shape (N, 3), got (3, 542, 611)