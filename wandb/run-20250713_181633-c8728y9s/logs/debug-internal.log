2025-07-13 18:16:33,399 INFO    StreamThr :2941649 [internal.py:wandb_internal():89] W&B internal server running at pid: 2941649, started at: 2025-07-13 18:16:33.398816
2025-07-13 18:16:33,400 DEBUG   HandlerThread:2941649 [handler.py:handle_request():144] handle_request: status
2025-07-13 18:16:33,408 INFO    WriterThread:2941649 [datastore.py:open_for_write():85] open: /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-c8728y9s/run-c8728y9s.wandb
2025-07-13 18:16:33,409 DEBUG   SenderThread:2941649 [sender.py:send():369] send: header
2025-07-13 18:16:33,426 DEBUG   SenderThread:2941649 [sender.py:send():369] send: run
2025-07-13 18:16:33,672 INFO    SenderThread:2941649 [dir_watcher.py:__init__():211] watching files in: /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-c8728y9s/files
2025-07-13 18:16:33,672 INFO    SenderThread:2941649 [sender.py:_start_run_threads():1103] run started: c8728y9s with start time 1752444993.398183
2025-07-13 18:16:33,673 DEBUG   SenderThread:2941649 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:16:33,673 INFO    SenderThread:2941649 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:16:33,681 DEBUG   HandlerThread:2941649 [handler.py:handle_request():144] handle_request: check_version
2025-07-13 18:16:33,682 DEBUG   SenderThread:2941649 [sender.py:send_request():396] send_request: check_version
2025-07-13 18:16:33,756 DEBUG   HandlerThread:2941649 [handler.py:handle_request():144] handle_request: run_start
2025-07-13 18:16:33,763 DEBUG   HandlerThread:2941649 [system_info.py:__init__():31] System info init
2025-07-13 18:16:33,763 DEBUG   HandlerThread:2941649 [system_info.py:__init__():46] System info init done
2025-07-13 18:16:33,763 INFO    HandlerThread:2941649 [system_monitor.py:start():181] Starting system monitor
2025-07-13 18:16:33,763 INFO    SystemMonitor:2941649 [system_monitor.py:_start():145] Starting system asset monitoring threads
2025-07-13 18:16:33,764 INFO    HandlerThread:2941649 [system_monitor.py:probe():201] Collecting system info
2025-07-13 18:16:33,764 INFO    SystemMonitor:2941649 [interfaces.py:start():190] Started cpu monitoring
2025-07-13 18:16:33,765 INFO    SystemMonitor:2941649 [interfaces.py:start():190] Started disk monitoring
2025-07-13 18:16:33,766 INFO    SystemMonitor:2941649 [interfaces.py:start():190] Started gpu monitoring
2025-07-13 18:16:33,767 INFO    SystemMonitor:2941649 [interfaces.py:start():190] Started memory monitoring
2025-07-13 18:16:33,768 INFO    SystemMonitor:2941649 [interfaces.py:start():190] Started network monitoring
2025-07-13 18:16:33,803 DEBUG   HandlerThread:2941649 [system_info.py:probe():195] Probing system
2025-07-13 18:16:33,809 DEBUG   HandlerThread:2941649 [system_info.py:_probe_git():180] Probing git
2025-07-13 18:16:33,825 DEBUG   HandlerThread:2941649 [system_info.py:_probe_git():188] Probing git done
2025-07-13 18:16:33,825 DEBUG   HandlerThread:2941649 [system_info.py:probe():240] Probing system done
2025-07-13 18:16:33,825 DEBUG   HandlerThread:2941649 [system_monitor.py:probe():210] {'os': 'Linux-5.4.0-77-generic-x86_64-with-debian-bullseye-sid', 'python': '3.7.16', 'heartbeatAt': '2025-07-13T22:16:33.803922', 'startedAt': '2025-07-13T22:16:33.382512', 'docker': None, 'cuda': None, 'args': ('--local_rank=0', '--cfg', 'config/base_train.yaml', '--output', '/home-local2/akath.extra.nobkp/sereact', '--data-path', '/home-local2/akath.extra.nobkp/dl_challenge', '--batch-size', '2'), 'state': 'running', 'program': 'main.py', 'codePath': 'main.py', 'git': {'remote': 'https://github.com/Shrinidhibhat87/codingchallenge_sereact.git', 'commit': '667b7ded061f079ea281aa2193d47b61df08fec6'}, 'email': '<EMAIL>', 'root': '/home-local/akath.nobkp/sereact', 'host': 'lv3-32190', 'username': 'akath', 'executable': '/gel/usr/akath/.conda/envs/swin/bin/python', 'cpu_count': 6, 'cpu_count_logical': 12, 'cpu_freq': {'current': 1.40925, 'min': 1200.0, 'max': 4000.0}, 'cpu_freq_per_core': [{'current': 1.928, 'min': 1200.0, 'max': 4000.0}, {'current': 1.263, 'min': 1200.0, 'max': 4000.0}, {'current': 1.199, 'min': 1200.0, 'max': 4000.0}, {'current': 1.199, 'min': 1200.0, 'max': 4000.0}, {'current': 1.199, 'min': 1200.0, 'max': 4000.0}, {'current': 3.159, 'min': 1200.0, 'max': 4000.0}, {'current': 3.12, 'min': 1200.0, 'max': 4000.0}, {'current': 1.287, 'min': 1200.0, 'max': 4000.0}, {'current': 1.199, 'min': 1200.0, 'max': 4000.0}, {'current': 1.199, 'min': 1200.0, 'max': 4000.0}, {'current': 1.199, 'min': 1200.0, 'max': 4000.0}, {'current': 3.656, 'min': 1200.0, 'max': 4000.0}], 'disk': {'total': 111.2200813293457, 'used': 53.132808685302734}, 'gpu': 'TITAN Xp', 'gpu_count': 3, 'gpu_devices': [{'name': 'TITAN Xp', 'memory_total': 12787122176}, {'name': 'TITAN Xp', 'memory_total': 12788498432}, {'name': 'TITAN Xp', 'memory_total': 12788498432}], 'memory': {'total': 62.72977066040039}}
2025-07-13 18:16:33,825 INFO    HandlerThread:2941649 [system_monitor.py:probe():211] Finished collecting system info
2025-07-13 18:16:33,825 INFO    HandlerThread:2941649 [system_monitor.py:probe():214] Publishing system info
2025-07-13 18:16:33,825 DEBUG   HandlerThread:2941649 [system_info.py:_save_pip():52] Saving list of pip packages installed into the current environment
2025-07-13 18:16:33,826 DEBUG   HandlerThread:2941649 [system_info.py:_save_pip():67] Saving pip packages done
2025-07-13 18:16:33,826 DEBUG   HandlerThread:2941649 [system_info.py:_save_conda():75] Saving list of conda packages installed into the current environment
2025-07-13 18:16:34,675 INFO    Thread-12 :2941649 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-c8728y9s/files/wandb-summary.json
2025-07-13 18:16:34,675 INFO    Thread-12 :2941649 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-c8728y9s/files/requirements.txt
2025-07-13 18:16:34,676 INFO    Thread-12 :2941649 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-c8728y9s/files/conda-environment.yaml
2025-07-13 18:16:40,108 DEBUG   HandlerThread:2941649 [system_info.py:_save_conda():86] Saving conda packages done
2025-07-13 18:16:40,109 INFO    HandlerThread:2941649 [system_monitor.py:probe():216] Finished publishing system info
2025-07-13 18:16:40,117 DEBUG   HandlerThread:2941649 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:16:40,118 DEBUG   HandlerThread:2941649 [handler.py:handle_request():144] handle_request: keepalive
2025-07-13 18:16:40,119 DEBUG   SenderThread:2941649 [sender.py:send():369] send: files
2025-07-13 18:16:40,119 INFO    SenderThread:2941649 [sender.py:_save_file():1354] saving file wandb-metadata.json with policy now
2025-07-13 18:16:40,127 DEBUG   SenderThread:2941649 [sender.py:send():369] send: telemetry
2025-07-13 18:16:40,127 DEBUG   HandlerThread:2941649 [handler.py:handle_request():144] handle_request: stop_status
2025-07-13 18:16:40,128 DEBUG   SenderThread:2941649 [sender.py:send_request():396] send_request: stop_status
2025-07-13 18:16:40,421 INFO    wandb-upload_0:2941649 [upload_job.py:push():133] Uploaded file /tmp/tmprf_ib82ywandb/ksx67qrq-wandb-metadata.json
2025-07-13 18:16:40,675 INFO    Thread-12 :2941649 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-c8728y9s/files/conda-environment.yaml
2025-07-13 18:16:40,675 INFO    Thread-12 :2941649 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-c8728y9s/files/wandb-metadata.json
2025-07-13 18:16:40,676 INFO    Thread-12 :2941649 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-c8728y9s/files/output.log
2025-07-13 18:16:42,676 INFO    Thread-12 :2941649 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-c8728y9s/files/output.log
2025-07-13 18:16:44,110 DEBUG   HandlerThread:2941649 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:16:44,112 DEBUG   SenderThread:2941649 [sender.py:send():369] send: exit
2025-07-13 18:16:44,112 INFO    SenderThread:2941649 [sender.py:send_exit():574] handling exit code: 1
2025-07-13 18:16:44,112 INFO    SenderThread:2941649 [sender.py:send_exit():576] handling runtime: 10
2025-07-13 18:16:44,114 INFO    SenderThread:2941649 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:16:44,115 INFO    SenderThread:2941649 [sender.py:send_exit():582] send defer
2025-07-13 18:16:44,115 DEBUG   HandlerThread:2941649 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:16:44,115 INFO    HandlerThread:2941649 [handler.py:handle_request_defer():170] handle defer: 0
2025-07-13 18:16:44,115 DEBUG   SenderThread:2941649 [sender.py:send_request():396] send_request: defer
2025-07-13 18:16:44,115 INFO    SenderThread:2941649 [sender.py:send_request_defer():598] handle sender defer: 0
2025-07-13 18:16:44,115 INFO    SenderThread:2941649 [sender.py:transition_state():602] send defer: 1
2025-07-13 18:16:44,116 DEBUG   HandlerThread:2941649 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:16:44,116 INFO    HandlerThread:2941649 [handler.py:handle_request_defer():170] handle defer: 1
2025-07-13 18:16:44,116 DEBUG   SenderThread:2941649 [sender.py:send_request():396] send_request: defer
2025-07-13 18:16:44,116 INFO    SenderThread:2941649 [sender.py:send_request_defer():598] handle sender defer: 1
2025-07-13 18:16:44,116 INFO    SenderThread:2941649 [sender.py:transition_state():602] send defer: 2
2025-07-13 18:16:44,116 DEBUG   HandlerThread:2941649 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:16:44,116 INFO    HandlerThread:2941649 [handler.py:handle_request_defer():170] handle defer: 2
2025-07-13 18:16:44,116 INFO    HandlerThread:2941649 [system_monitor.py:finish():190] Stopping system monitor
2025-07-13 18:16:44,116 DEBUG   SystemMonitor:2941649 [system_monitor.py:_start():159] Starting system metrics aggregation loop
2025-07-13 18:16:44,117 INFO    HandlerThread:2941649 [interfaces.py:finish():202] Joined cpu monitor
2025-07-13 18:16:44,117 DEBUG   SystemMonitor:2941649 [system_monitor.py:_start():166] Finished system metrics aggregation loop
2025-07-13 18:16:44,117 INFO    HandlerThread:2941649 [interfaces.py:finish():202] Joined disk monitor
2025-07-13 18:16:44,117 DEBUG   SystemMonitor:2941649 [system_monitor.py:_start():170] Publishing last batch of metrics
2025-07-13 18:16:44,251 INFO    HandlerThread:2941649 [interfaces.py:finish():202] Joined gpu monitor
2025-07-13 18:16:44,251 INFO    HandlerThread:2941649 [interfaces.py:finish():202] Joined memory monitor
2025-07-13 18:16:44,251 INFO    HandlerThread:2941649 [interfaces.py:finish():202] Joined network monitor
2025-07-13 18:16:44,252 DEBUG   SenderThread:2941649 [sender.py:send_request():396] send_request: defer
2025-07-13 18:16:44,252 INFO    SenderThread:2941649 [sender.py:send_request_defer():598] handle sender defer: 2
2025-07-13 18:16:44,252 INFO    SenderThread:2941649 [sender.py:transition_state():602] send defer: 3
2025-07-13 18:16:44,253 DEBUG   SenderThread:2941649 [sender.py:send():369] send: stats
2025-07-13 18:16:44,253 DEBUG   HandlerThread:2941649 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:16:44,253 INFO    HandlerThread:2941649 [handler.py:handle_request_defer():170] handle defer: 3
2025-07-13 18:16:44,254 DEBUG   SenderThread:2941649 [sender.py:send_request():396] send_request: defer
2025-07-13 18:16:44,254 INFO    SenderThread:2941649 [sender.py:send_request_defer():598] handle sender defer: 3
2025-07-13 18:16:44,254 INFO    SenderThread:2941649 [sender.py:transition_state():602] send defer: 4
2025-07-13 18:16:44,254 DEBUG   HandlerThread:2941649 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:16:44,254 INFO    HandlerThread:2941649 [handler.py:handle_request_defer():170] handle defer: 4
2025-07-13 18:16:44,254 DEBUG   SenderThread:2941649 [sender.py:send_request():396] send_request: defer
2025-07-13 18:16:44,255 INFO    SenderThread:2941649 [sender.py:send_request_defer():598] handle sender defer: 4
2025-07-13 18:16:44,255 INFO    SenderThread:2941649 [sender.py:transition_state():602] send defer: 5
2025-07-13 18:16:44,255 DEBUG   HandlerThread:2941649 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:16:44,255 INFO    HandlerThread:2941649 [handler.py:handle_request_defer():170] handle defer: 5
2025-07-13 18:16:44,255 DEBUG   SenderThread:2941649 [sender.py:send():369] send: summary
2025-07-13 18:16:44,256 INFO    SenderThread:2941649 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:16:44,256 DEBUG   SenderThread:2941649 [sender.py:send_request():396] send_request: defer
2025-07-13 18:16:44,256 INFO    SenderThread:2941649 [sender.py:send_request_defer():598] handle sender defer: 5
2025-07-13 18:16:44,256 INFO    SenderThread:2941649 [sender.py:transition_state():602] send defer: 6
2025-07-13 18:16:44,257 DEBUG   HandlerThread:2941649 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:16:44,257 INFO    HandlerThread:2941649 [handler.py:handle_request_defer():170] handle defer: 6
2025-07-13 18:16:44,257 DEBUG   SenderThread:2941649 [sender.py:send_request():396] send_request: defer
2025-07-13 18:16:44,257 INFO    SenderThread:2941649 [sender.py:send_request_defer():598] handle sender defer: 6
2025-07-13 18:16:44,262 DEBUG   HandlerThread:2941649 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:16:44,377 INFO    SenderThread:2941649 [sender.py:transition_state():602] send defer: 7
2025-07-13 18:16:44,377 DEBUG   HandlerThread:2941649 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:16:44,377 INFO    HandlerThread:2941649 [handler.py:handle_request_defer():170] handle defer: 7
2025-07-13 18:16:44,378 DEBUG   SenderThread:2941649 [sender.py:send_request():396] send_request: defer
2025-07-13 18:16:44,378 INFO    SenderThread:2941649 [sender.py:send_request_defer():598] handle sender defer: 7
2025-07-13 18:16:44,677 INFO    Thread-12 :2941649 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-c8728y9s/files/output.log
2025-07-13 18:16:44,677 INFO    Thread-12 :2941649 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-c8728y9s/files/wandb-summary.json
2025-07-13 18:16:44,677 INFO    Thread-12 :2941649 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-c8728y9s/files/config.yaml
2025-07-13 18:16:45,113 DEBUG   HandlerThread:2941649 [handler.py:handle_request():144] handle_request: poll_exit
2025-07-13 18:16:46,678 INFO    Thread-12 :2941649 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-c8728y9s/files/output.log
2025-07-13 18:16:48,117 INFO    SenderThread:2941649 [sender.py:transition_state():602] send defer: 8
2025-07-13 18:16:48,117 DEBUG   SenderThread:2941649 [sender.py:send_request():396] send_request: poll_exit
2025-07-13 18:16:48,118 DEBUG   HandlerThread:2941649 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:16:48,118 INFO    HandlerThread:2941649 [handler.py:handle_request_defer():170] handle defer: 8
2025-07-13 18:16:48,118 DEBUG   SenderThread:2941649 [sender.py:send_request():396] send_request: defer
2025-07-13 18:16:48,118 INFO    SenderThread:2941649 [sender.py:send_request_defer():598] handle sender defer: 8
2025-07-13 18:16:48,118 INFO    SenderThread:2941649 [job_builder.py:build():232] Attempting to build job artifact
2025-07-13 18:16:48,119 INFO    SenderThread:2941649 [job_builder.py:build():256] is repo sourced job
2025-07-13 18:16:48,121 INFO    SenderThread:2941649 [job_builder.py:build():297] adding wandb-job metadata file
2025-07-13 18:16:48,134 INFO    SenderThread:2941649 [sender.py:transition_state():602] send defer: 9
2025-07-13 18:16:48,135 DEBUG   HandlerThread:2941649 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:16:48,135 DEBUG   SenderThread:2941649 [sender.py:send():369] send: artifact
2025-07-13 18:16:48,135 INFO    HandlerThread:2941649 [handler.py:handle_request_defer():170] handle defer: 9
2025-07-13 18:16:48,552 INFO    SenderThread:2941649 [sender.py:send_artifact():1450] sent artifact job-https___github.com_Shrinidhibhat87_codingchallenge_sereact.git_main.py - {'id': 'QXJ0aWZhY3Q6MTg3NzkyNTU2Ng==', 'digest': '24022c04cd416a726867eb3e3cc8a3ff', 'state': 'COMMITTED', 'aliases': [{'artifactCollectionName': 'job-https___github.com_Shrinidhibhat87_codingchallenge_sereact.git_main.py', 'alias': 'latest'}, {'artifactCollectionName': 'job-https___github.com_Shrinidhibhat87_codingchallenge_sereact.git_main.py', 'alias': 'v10'}], 'artifactSequence': {'id': 'QXJ0aWZhY3RDb2xsZWN0aW9uOjY4NDgwMzQ0Ng==', 'latestArtifact': {'id': 'QXJ0aWZhY3Q6MTg3NzkyNTU2Ng==', 'versionIndex': 10}}, 'version': 'v10'}
2025-07-13 18:16:48,552 DEBUG   SenderThread:2941649 [sender.py:send_request():396] send_request: defer
2025-07-13 18:16:48,552 INFO    SenderThread:2941649 [sender.py:send_request_defer():598] handle sender defer: 9
2025-07-13 18:16:48,552 INFO    SenderThread:2941649 [dir_watcher.py:finish():359] shutting down directory watcher
2025-07-13 18:16:48,679 INFO    Thread-12 :2941649 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-c8728y9s/files/output.log
2025-07-13 18:16:48,679 INFO    SenderThread:2941649 [dir_watcher.py:finish():389] scan: /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-c8728y9s/files
2025-07-13 18:16:48,680 INFO    SenderThread:2941649 [dir_watcher.py:finish():403] scan save: /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-c8728y9s/files/config.yaml config.yaml
2025-07-13 18:16:48,680 INFO    SenderThread:2941649 [dir_watcher.py:finish():403] scan save: /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-c8728y9s/files/wandb-summary.json wandb-summary.json
2025-07-13 18:16:48,680 INFO    SenderThread:2941649 [dir_watcher.py:finish():403] scan save: /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-c8728y9s/files/requirements.txt requirements.txt
2025-07-13 18:16:48,686 INFO    SenderThread:2941649 [dir_watcher.py:finish():403] scan save: /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-c8728y9s/files/conda-environment.yaml conda-environment.yaml
2025-07-13 18:16:48,693 INFO    SenderThread:2941649 [dir_watcher.py:finish():403] scan save: /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-c8728y9s/files/wandb-metadata.json wandb-metadata.json
2025-07-13 18:16:48,696 INFO    SenderThread:2941649 [dir_watcher.py:finish():403] scan save: /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-c8728y9s/files/output.log output.log
2025-07-13 18:16:48,698 INFO    SenderThread:2941649 [sender.py:transition_state():602] send defer: 10
2025-07-13 18:16:48,698 DEBUG   HandlerThread:2941649 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:16:48,704 INFO    HandlerThread:2941649 [handler.py:handle_request_defer():170] handle defer: 10
2025-07-13 18:16:48,710 DEBUG   SenderThread:2941649 [sender.py:send_request():396] send_request: defer
2025-07-13 18:16:48,711 INFO    SenderThread:2941649 [sender.py:send_request_defer():598] handle sender defer: 10
2025-07-13 18:16:48,711 INFO    SenderThread:2941649 [file_pusher.py:finish():159] shutting down file pusher
2025-07-13 18:16:48,951 INFO    wandb-upload_0:2941649 [upload_job.py:push():133] Uploaded file /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-c8728y9s/files/config.yaml
2025-07-13 18:16:49,012 INFO    wandb-upload_1:2941649 [upload_job.py:push():133] Uploaded file /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-c8728y9s/files/wandb-summary.json
2025-07-13 18:16:49,083 INFO    wandb-upload_2:2941649 [upload_job.py:push():133] Uploaded file /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-c8728y9s/files/requirements.txt
2025-07-13 18:16:49,089 INFO    wandb-upload_5:2941649 [upload_job.py:push():133] Uploaded file /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-c8728y9s/files/output.log
2025-07-13 18:16:49,092 INFO    wandb-upload_4:2941649 [upload_job.py:push():133] Uploaded file /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-c8728y9s/files/conda-environment.yaml
2025-07-13 18:16:49,292 INFO    Thread-11 :2941649 [sender.py:transition_state():602] send defer: 11
2025-07-13 18:16:49,293 DEBUG   HandlerThread:2941649 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:16:49,293 INFO    HandlerThread:2941649 [handler.py:handle_request_defer():170] handle defer: 11
2025-07-13 18:16:49,294 DEBUG   SenderThread:2941649 [sender.py:send_request():396] send_request: defer
2025-07-13 18:16:49,294 INFO    SenderThread:2941649 [sender.py:send_request_defer():598] handle sender defer: 11
2025-07-13 18:16:49,294 INFO    SenderThread:2941649 [file_pusher.py:join():164] waiting for file pusher
2025-07-13 18:16:49,294 INFO    SenderThread:2941649 [sender.py:transition_state():602] send defer: 12
2025-07-13 18:16:49,294 DEBUG   HandlerThread:2941649 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:16:49,294 INFO    HandlerThread:2941649 [handler.py:handle_request_defer():170] handle defer: 12
2025-07-13 18:16:49,295 DEBUG   HandlerThread:2941649 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:16:49,295 DEBUG   SenderThread:2941649 [sender.py:send_request():396] send_request: defer
2025-07-13 18:16:49,295 INFO    SenderThread:2941649 [sender.py:send_request_defer():598] handle sender defer: 12
2025-07-13 18:16:49,365 INFO    SenderThread:2941649 [sender.py:transition_state():602] send defer: 13
2025-07-13 18:16:49,366 DEBUG   HandlerThread:2941649 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:16:49,366 INFO    HandlerThread:2941649 [handler.py:handle_request_defer():170] handle defer: 13
2025-07-13 18:16:49,366 DEBUG   SenderThread:2941649 [sender.py:send_request():396] send_request: defer
2025-07-13 18:16:49,366 INFO    SenderThread:2941649 [sender.py:send_request_defer():598] handle sender defer: 13
2025-07-13 18:16:49,366 INFO    SenderThread:2941649 [sender.py:transition_state():602] send defer: 14
2025-07-13 18:16:49,367 DEBUG   HandlerThread:2941649 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:16:49,367 DEBUG   SenderThread:2941649 [sender.py:send():369] send: final
2025-07-13 18:16:49,367 INFO    HandlerThread:2941649 [handler.py:handle_request_defer():170] handle defer: 14
2025-07-13 18:16:49,367 DEBUG   SenderThread:2941649 [sender.py:send():369] send: footer
2025-07-13 18:16:49,368 DEBUG   SenderThread:2941649 [sender.py:send_request():396] send_request: defer
2025-07-13 18:16:49,368 INFO    SenderThread:2941649 [sender.py:send_request_defer():598] handle sender defer: 14
2025-07-13 18:16:49,369 DEBUG   HandlerThread:2941649 [handler.py:handle_request():144] handle_request: poll_exit
2025-07-13 18:16:49,370 DEBUG   HandlerThread:2941649 [handler.py:handle_request():144] handle_request: server_info
2025-07-13 18:16:49,370 DEBUG   HandlerThread:2941649 [handler.py:handle_request():144] handle_request: get_summary
2025-07-13 18:16:49,370 DEBUG   SenderThread:2941649 [sender.py:send_request():396] send_request: poll_exit
2025-07-13 18:16:49,370 DEBUG   HandlerThread:2941649 [handler.py:handle_request():144] handle_request: sampled_history
2025-07-13 18:16:49,371 DEBUG   SenderThread:2941649 [sender.py:send_request():396] send_request: server_info
2025-07-13 18:16:49,443 INFO    MainThread:2941649 [wandb_run.py:_footer_history_summary_info():3467] rendering history
2025-07-13 18:16:49,443 INFO    MainThread:2941649 [wandb_run.py:_footer_history_summary_info():3499] rendering summary
2025-07-13 18:16:49,443 INFO    MainThread:2941649 [wandb_run.py:_footer_sync_info():3426] logging synced files
2025-07-13 18:16:49,444 DEBUG   HandlerThread:2941649 [handler.py:handle_request():144] handle_request: shutdown
2025-07-13 18:16:49,444 INFO    HandlerThread:2941649 [handler.py:finish():854] shutting down handler
2025-07-13 18:16:50,370 INFO    WriterThread:2941649 [datastore.py:close():298] close: /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-c8728y9s/run-c8728y9s.wandb
2025-07-13 18:16:50,443 INFO    SenderThread:2941649 [sender.py:finish():1526] shutting down sender
2025-07-13 18:16:50,443 INFO    SenderThread:2941649 [file_pusher.py:finish():159] shutting down file pusher
2025-07-13 18:16:50,443 INFO    SenderThread:2941649 [file_pusher.py:join():164] waiting for file pusher
