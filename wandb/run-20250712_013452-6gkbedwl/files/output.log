=> merge config from config/base_train.yaml
RANK and WORLD_SIZE in environ: 0/3
[32m[2025-07-12 01:34:58 3DDETR.yaml][33m(main.py 549)[39m: INFO Full config saved to /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/config.json
[32m[2025-07-12 01:34:58 3DDETR.yaml][33m(main.py 552)[39m: INFO AMP_ENABLE: true
TAG: default
amp_opt_level: ''
base:
- ''
data:
  augment: false
  batch_size: 1
  cache_mode: part
  data_path: /home-local2/akath.extra.nobkp/dl_challenge
  dataset: Sereact_dataset
  debug: false
  num_workers: 4
  pin_memory: true
  transform: null
  zip_mode: false
eval_mode: false
local_rank: 0
loss:
  matcher_costs:
    cost_box_corners: 1.0
    giou: 5.0
    l1: 2.0
  weights:
    box_corners: 1.0
    giou: 1.0
    size: 1.0
    size_reg: 1.0
model:
  decoder:
    dim: 256
    dropout: 0.1
    ffn_dim: 256
    nhead: 4
    num_layers: 3
  encoder:
    activation: relu
    dim: 256
    dropout: 0.1
    ffn_dim: 128
    nheads: 4
    num_layers: 3
    preencoder_npoints: 2048
    type: vanilla
    use_color: false
  export_model: false
  mlp_dropout: 0.3
  name: 3DDETR.yaml
  num_angular_bins: 12
  num_queries: 256
  position_embedding: fourier
  pretrained: null
  pretrained_weights_path: /home/<USER>/Coding/Pre_trained_Weights/3detr/scannet_ep1080.pth
  resume: ''
  training: true
  unit_test: false
output: /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123
print_freq: 10
save_freq: 1
seed: 40
tag: '123'
train:
  accumulation_steps: 1
  auto_resume: false
  base_lr: 0.0001
  clip_grad: 0.1
  filter_biases_wd: true
  final_lr: 1.0e-06
  lr_scheduler: cosine
  max_epoch: 200
  start_epoch: 0
  unit_test_epoch: 100
  use_checkpoint: false
  warm_lr: 5.0e-06
  warm_lr_epochs: 9
  weight_decay: 0.01
unit_test: false
[32m[2025-07-12 01:34:58 3DDETR.yaml][33m(main.py 553)[39m: INFO {"cfg": "config/base_train.yaml", "opts": null, "batch_size": 1, "data_path": "/home-local2/akath.extra.nobkp/dl_challenge", "pretrained": null, "resume": null, "accumulation_steps": null, "use_checkpoint": false, "disable_amp": false, "amp_opt_level": null, "output": "/home-local2/akath.extra.nobkp/sereact", "tag": null, "eval": false, "unit_test": false, "base_lr": null, "local_rank": 0}
local rank 0 / global rank 0 successfully build train dataset
local rank 0 / global rank 0 successfully build val dataset
[32m[2025-07-12 01:34:58 3DDETR.yaml][33m(main.py 102)[39m: INFO Model3DDETR(
  (pre_encoder): PointnetSAModuleVotes(
    (grouper): QueryAndGroup()
    (mlp_module): SharedMLP(
      (layer0): Conv2d(
        (conv): Conv2d(3, 64, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn): BatchNorm2d(
          (bn): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        )
        (activation): ReLU(inplace=True)
      )
      (layer1): Conv2d(
        (conv): Conv2d(64, 128, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn): BatchNorm2d(
          (bn): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        )
        (activation): ReLU(inplace=True)
      )
      (layer2): Conv2d(
        (conv): Conv2d(128, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn): BatchNorm2d(
          (bn): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        )
        (activation): ReLU(inplace=True)
      )
    )
  )
  (encoder): TransformerEncoder(
    (layers): ModuleList(
      (0): TransformerEncoderLayer(
        attn_dr=0.1
        (self_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (linear1): Linear(in_features=256, out_features=128, bias=True)
        (dropout): Dropout(p=0.1, inplace=False)
        (linear2): Linear(in_features=128, out_features=256, bias=True)
        (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout2): Dropout(p=0.1, inplace=False)
        (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout1): Dropout(p=0.1, inplace=False)
        (activation): ReLU()
      )
      (1): TransformerEncoderLayer(
        attn_dr=0.1
        (self_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (linear1): Linear(in_features=256, out_features=128, bias=True)
        (dropout): Dropout(p=0.1, inplace=False)
        (linear2): Linear(in_features=128, out_features=256, bias=True)
        (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout2): Dropout(p=0.1, inplace=False)
        (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout1): Dropout(p=0.1, inplace=False)
        (activation): ReLU()
      )
      (2): TransformerEncoderLayer(
        attn_dr=0.1
        (self_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (linear1): Linear(in_features=256, out_features=128, bias=True)
        (dropout): Dropout(p=0.1, inplace=False)
        (linear2): Linear(in_features=128, out_features=256, bias=True)
        (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout2): Dropout(p=0.1, inplace=False)
        (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout1): Dropout(p=0.1, inplace=False)
        (activation): ReLU()
      )
    )
  )
  (encoder_decoder_projection): GenericMLP(
    (layers): Sequential(
      (0): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
      (1): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (2): ReLU()
      (3): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
      (4): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (5): ReLU()
    )
  )
  (positional_embedding): PositionEmbeddingCoordsSine(type=fourier, scale=6.283185307179586, normalize=True, gaussB_shape=torch.Size([3, 128]), gaussB_sum=-17.944507598876953)
  (query_projection): GenericMLP(
    (layers): Sequential(
      (0): Conv1d(256, 256, kernel_size=(1,), stride=(1,))
      (1): ReLU()
      (2): Conv1d(256, 256, kernel_size=(1,), stride=(1,))
      (3): ReLU()
    )
  )
  (decoder): TransformerDecoder(
    (layers): ModuleList(
      (0): TransformerDecoderLayer(
        (self_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (multihead_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm3): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout1): Dropout(p=0.1, inplace=False)
        (dropout2): Dropout(p=0.1, inplace=False)
        (dropout3): Dropout(p=0.1, inplace=False)
        (linear1): Linear(in_features=256, out_features=256, bias=True)
        (dropout): Dropout(p=0.1, inplace=False)
        (linear2): Linear(in_features=256, out_features=256, bias=True)
        (activation): ReLU()
      )
      (1): TransformerDecoderLayer(
        (self_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (multihead_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm3): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout1): Dropout(p=0.1, inplace=False)
        (dropout2): Dropout(p=0.1, inplace=False)
        (dropout3): Dropout(p=0.1, inplace=False)
        (linear1): Linear(in_features=256, out_features=256, bias=True)
        (dropout): Dropout(p=0.1, inplace=False)
        (linear2): Linear(in_features=256, out_features=256, bias=True)
        (activation): ReLU()
      )
      (2): TransformerDecoderLayer(
        (self_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (multihead_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm3): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout1): Dropout(p=0.1, inplace=False)
        (dropout2): Dropout(p=0.1, inplace=False)
        (dropout3): Dropout(p=0.1, inplace=False)
        (linear1): Linear(in_features=256, out_features=256, bias=True)
        (dropout): Dropout(p=0.1, inplace=False)
        (linear2): Linear(in_features=256, out_features=256, bias=True)
        (activation): ReLU()
      )
    )
    (norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (mlp_heads): ModuleDict(
    (center_head): GenericMLP(
      (layers): Sequential(
        (0): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
        (1): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU()
        (3): Dropout(p=0.3, inplace=False)
        (4): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
        (5): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (6): ReLU()
        (7): Dropout(p=0.3, inplace=False)
        (8): Conv1d(256, 3, kernel_size=(1,), stride=(1,))
      )
    )
    (size_head): GenericMLP(
      (layers): Sequential(
        (0): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
        (1): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU()
        (3): Dropout(p=0.3, inplace=False)
        (4): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
        (5): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (6): ReLU()
        (7): Dropout(p=0.3, inplace=False)
        (8): Conv1d(256, 3, kernel_size=(1,), stride=(1,))
      )
    )
    (angle_cls_head): GenericMLP(
      (layers): Sequential(
        (0): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
        (1): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU()
        (3): Dropout(p=0.3, inplace=False)
        (4): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
        (5): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (6): ReLU()
        (7): Dropout(p=0.3, inplace=False)
        (8): Conv1d(256, 12, kernel_size=(1,), stride=(1,))
      )
    )
    (angle_residual_head): GenericMLP(
      (layers): Sequential(
        (0): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
        (1): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU()
        (3): Dropout(p=0.3, inplace=False)
        (4): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
        (5): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (6): ReLU()
        (7): Dropout(p=0.3, inplace=False)
        (8): Conv1d(256, 12, kernel_size=(1,), stride=(1,))
      )
    )
  )
)
[32m[2025-07-12 01:34:58 3DDETR.yaml][33m(main.py 104)[39m: INFO number of params: 3811038
[32m[2025-07-12 01:34:58 3DDETR.yaml][33m(main.py 153)[39m: INFO Start training
[32m[2025-07-12 01:35:02 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [0/200][0/54]	eta 0:03:24 lr 0.000100	time 3.7957 (3.7957)	loss 37.9370 (37.9370)	miou 0.0349	grad_norm 191.2734 (191.2734)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:35:24 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [0/200][10/54]	eta 0:01:42 lr 0.000099	time 2.4213 (2.3188)	loss 12.8466 (21.1396)	miou 0.1489	grad_norm 344.7776 (136.8814)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:35:45 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [0/200][20/54]	eta 0:01:15 lr 0.000097	time 2.3826 (2.2250)	loss 24.5606 (21.0259)	miou 0.1512	grad_norm 61.8214 (112.6311)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:36:04 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [0/200][30/54]	eta 0:00:51 lr 0.000094	time 2.2072 (2.1273)	loss 23.6308 (21.0989)	miou 0.1528	grad_norm 49.1889 (101.8626)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:36:18 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [0/200][40/54]	eta 0:00:27 lr 0.000090	time 0.9967 (1.9556)	loss 13.9367 (19.8409)	miou 0.1498	grad_norm 43.8523 (89.7781)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:36:31 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [0/200][50/54]	eta 0:00:07 lr 0.000085	time 1.7627 (1.8296)	loss 11.1713 (18.8471)	miou 0.1452	grad_norm 37.3720 (89.6234)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:36:35 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 0 training takes 0:01:36
[32m[2025-07-12 01:36:35 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
0
[32m[2025-07-12 01:36:49 3DDETR.yaml][33m(utils_help.py 202)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saving......
[32m[2025-07-12 01:36:50 3DDETR.yaml][33m(utils_help.py 204)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saved !!!
[32m[2025-07-12 01:36:50 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.0999%
[32m[2025-07-12 01:36:50 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.0999%
[32m[2025-07-12 01:36:52 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [1/200][0/54]	eta 0:01:54 lr 0.000082	time 2.1127 (2.1127)	loss 13.0882 (13.0882)	miou 0.0984	grad_norm 319.2195 (319.2195)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:37:06 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [1/200][10/54]	eta 0:01:04 lr 0.000076	time 1.0495 (1.4643)	loss 11.3679 (12.4535)	miou 0.1041	grad_norm 28.0145 (97.7199)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:37:17 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [1/200][20/54]	eta 0:00:44 lr 0.000069	time 1.4256 (1.3088)	loss 9.3853 (12.9731)	miou 0.1105	grad_norm 204.9550 (121.0615)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:37:28 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [1/200][30/54]	eta 0:00:29 lr 0.000062	time 1.0303 (1.2372)	loss 8.3892 (12.7375)	miou 0.1195	grad_norm 184.5409 (116.8774)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:37:38 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [1/200][40/54]	eta 0:00:16 lr 0.000054	time 1.0518 (1.1919)	loss 16.9302 (12.1371)	miou 0.1201	grad_norm 221.2978 (116.6026)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:37:50 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [1/200][50/54]	eta 0:00:04 lr 0.000046	time 1.2550 (1.1929)	loss 11.8225 (11.8638)	miou 0.1246	grad_norm 278.3968 (123.8466)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:37:54 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 1 training takes 0:01:04
[32m[2025-07-12 01:37:54 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
1
[32m[2025-07-12 01:38:04 3DDETR.yaml][33m(utils_help.py 202)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saving......
[32m[2025-07-12 01:38:04 3DDETR.yaml][33m(utils_help.py 204)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saved !!!
[32m[2025-07-12 01:38:04 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1426%
[32m[2025-07-12 01:38:04 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.1426%
[32m[2025-07-12 01:38:06 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [2/200][0/54]	eta 0:01:01 lr 0.000043	time 1.1450 (1.1450)	loss 9.9231 (9.9231)	miou 0.1393	grad_norm 202.7117 (202.7117)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:38:17 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [2/200][10/54]	eta 0:00:49 lr 0.000035	time 1.0155 (1.1306)	loss 10.8625 (10.2453)	miou 0.1321	grad_norm 86.3085 (211.7844)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:38:29 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [2/200][20/54]	eta 0:00:39 lr 0.000028	time 1.2493 (1.1616)	loss 9.8796 (9.9800)	miou 0.1367	grad_norm 64.9583 (237.3770)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:38:40 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [2/200][30/54]	eta 0:00:27 lr 0.000021	time 1.4278 (1.1434)	loss 13.8227 (10.4136)	miou 0.1364	grad_norm 324.7897 (251.5497)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:38:50 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [2/200][40/54]	eta 0:00:15 lr 0.000015	time 0.9895 (1.1093)	loss 11.5386 (10.2914)	miou 0.1369	grad_norm 89.7952 (260.5320)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:39:01 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [2/200][50/54]	eta 0:00:04 lr 0.000010	time 1.0025 (1.1132)	loss 9.5354 (10.1982)	miou 0.1382	grad_norm 818.0443 (310.8232)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:39:04 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 2 training takes 0:00:59
[32m[2025-07-12 01:39:04 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 01:39:14 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1120%
[32m[2025-07-12 01:39:14 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.1426%
[32m[2025-07-12 01:39:16 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [3/200][0/54]	eta 0:01:36 lr 0.000008	time 1.7880 (1.7880)	loss 8.3528 (8.3528)	miou 0.1113	grad_norm 85.4598 (85.4598)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:39:27 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [3/200][10/54]	eta 0:00:52 lr 0.000004	time 0.9679 (1.2014)	loss 10.2459 (10.3838)	miou 0.1143	grad_norm 94.0776 (217.9707)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:39:38 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [3/200][20/54]	eta 0:00:38 lr 0.000002	time 1.0260 (1.1316)	loss 6.7632 (10.4748)	miou 0.1153	grad_norm 95.3877 (228.9809)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:39:49 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [3/200][30/54]	eta 0:00:26 lr 0.000000	time 1.0959 (1.1233)	loss 11.0070 (9.9364)	miou 0.1290	grad_norm 43.3594 (238.4191)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:40:01 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [3/200][40/54]	eta 0:00:15 lr 0.000000	time 1.5001 (1.1327)	loss 12.3706 (9.8624)	miou 0.1309	grad_norm 89.7860 (215.7055)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:40:12 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [3/200][50/54]	eta 0:00:04 lr 0.000001	time 0.9552 (1.1242)	loss 8.9119 (9.8617)	miou 0.1298	grad_norm 175.7455 (222.1252)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:40:15 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 3 training takes 0:01:01
[32m[2025-07-12 01:40:15 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 01:40:25 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1068%
[32m[2025-07-12 01:40:25 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.1426%
[32m[2025-07-12 01:40:27 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [4/200][0/54]	eta 0:01:21 lr 0.000002	time 1.5147 (1.5147)	loss 9.0268 (9.0268)	miou 0.1114	grad_norm 75.0955 (75.0955)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:40:37 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [4/200][10/54]	eta 0:00:48 lr 0.000004	time 1.1298 (1.0961)	loss 10.5020 (10.2733)	miou 0.1321	grad_norm 125.0711 (188.1380)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:40:48 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [4/200][20/54]	eta 0:00:36 lr 0.000008	time 0.9994 (1.0853)	loss 11.4084 (9.7327)	miou 0.1380	grad_norm 532.0654 (216.2585)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:40:59 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [4/200][30/54]	eta 0:00:26 lr 0.000013	time 1.0518 (1.0953)	loss 8.8405 (9.9356)	miou 0.1357	grad_norm 85.6700 (272.2861)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:41:10 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [4/200][40/54]	eta 0:00:15 lr 0.000019	time 1.0587 (1.0837)	loss 9.5742 (10.0750)	miou 0.1337	grad_norm 166.8726 (282.1598)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:41:22 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [4/200][50/54]	eta 0:00:04 lr 0.000025	time 1.2567 (1.1043)	loss 10.5700 (10.0161)	miou 0.1302	grad_norm 60.6207 (303.3677)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:41:25 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 4 training takes 0:01:00
[32m[2025-07-12 01:41:25 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 01:41:35 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1301%
[32m[2025-07-12 01:41:35 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.1426%
[32m[2025-07-12 01:41:37 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [5/200][0/54]	eta 0:01:05 lr 0.000028	time 1.2128 (1.2128)	loss 9.3511 (9.3511)	miou 0.1286	grad_norm 1282.7701 (1282.7701)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:41:48 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [5/200][10/54]	eta 0:00:48 lr 0.000035	time 1.0346 (1.1034)	loss 10.4864 (9.8355)	miou 0.1284	grad_norm 585.6556 (385.3019)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:41:58 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [5/200][20/54]	eta 0:00:36 lr 0.000043	time 1.2231 (1.0842)	loss 9.3204 (9.5847)	miou 0.1304	grad_norm 334.6776 (457.7025)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:42:10 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [5/200][30/54]	eta 0:00:26 lr 0.000051	time 1.2492 (1.1068)	loss 8.4854 (9.6983)	miou 0.1306	grad_norm 149.4270 (377.3869)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:42:22 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [5/200][40/54]	eta 0:00:15 lr 0.000059	time 1.0584 (1.1287)	loss 14.5584 (9.6030)	miou 0.1267	grad_norm 256.1337 (381.9553)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:42:33 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [5/200][50/54]	eta 0:00:04 lr 0.000066	time 1.0841 (1.1305)	loss 10.4649 (9.5933)	miou 0.1343	grad_norm 116.7444 (326.6857)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:42:36 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 5 training takes 0:01:00
[32m[2025-07-12 01:42:36 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 01:42:46 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1230%
[32m[2025-07-12 01:42:46 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.1426%
[32m[2025-07-12 01:42:48 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [6/200][0/54]	eta 0:01:18 lr 0.000069	time 1.4526 (1.4526)	loss 8.6002 (8.6002)	miou 0.1261	grad_norm 292.7079 (292.7079)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:42:59 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [6/200][10/54]	eta 0:00:50 lr 0.000076	time 0.8269 (1.1508)	loss 7.2062 (9.5005)	miou 0.1247	grad_norm 414.8662 (491.2227)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:43:10 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [6/200][20/54]	eta 0:00:37 lr 0.000082	time 0.9123 (1.1127)	loss 10.2338 (9.8691)	miou 0.1329	grad_norm 106.0972 (371.5688)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:43:21 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [6/200][30/54]	eta 0:00:26 lr 0.000088	time 1.0330 (1.1053)	loss 8.4031 (9.3951)	miou 0.1397	grad_norm 450.7965 (296.8246)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:43:31 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [6/200][40/54]	eta 0:00:15 lr 0.000093	time 1.2782 (1.0977)	loss 8.4025 (9.6287)	miou 0.1391	grad_norm 161.1062 (323.2978)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:43:43 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [6/200][50/54]	eta 0:00:04 lr 0.000096	time 1.0667 (1.1095)	loss 9.2514 (9.5881)	miou 0.1369	grad_norm 599.1799 (392.4917)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:43:46 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 6 training takes 0:00:59
[32m[2025-07-12 01:43:46 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
6
[32m[2025-07-12 01:43:56 3DDETR.yaml][33m(utils_help.py 202)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saving......
[32m[2025-07-12 01:43:57 3DDETR.yaml][33m(utils_help.py 204)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saved !!!
[32m[2025-07-12 01:43:57 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1904%
[32m[2025-07-12 01:43:57 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.1904%
[32m[2025-07-12 01:43:58 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [7/200][0/54]	eta 0:01:08 lr 0.000097	time 1.2667 (1.2667)	loss 8.7620 (8.7620)	miou 0.1795	grad_norm 160.3201 (160.3201)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:44:10 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [7/200][10/54]	eta 0:00:52 lr 0.000099	time 1.1295 (1.1883)	loss 7.5294 (9.2079)	miou 0.1536	grad_norm 477.8818 (227.3280)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:44:20 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [7/200][20/54]	eta 0:00:38 lr 0.000100	time 0.9577 (1.1177)	loss 8.5757 (8.8723)	miou 0.1494	grad_norm 216.3861 (254.7928)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:44:30 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [7/200][30/54]	eta 0:00:26 lr 0.000100	time 0.9368 (1.0926)	loss 9.6396 (9.0319)	miou 0.1429	grad_norm 149.8559 (339.4807)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:44:42 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [7/200][40/54]	eta 0:00:15 lr 0.000098	time 1.0715 (1.1050)	loss 7.6997 (8.7018)	miou 0.1510	grad_norm 232.5963 (317.0074)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:44:53 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [7/200][50/54]	eta 0:00:04 lr 0.000095	time 1.1410 (1.1141)	loss 6.8086 (8.8859)	miou 0.1502	grad_norm 116.1447 (293.4392)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:44:56 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 7 training takes 0:00:59
[32m[2025-07-12 01:44:56 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 01:45:06 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1564%
[32m[2025-07-12 01:45:06 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.1904%
[32m[2025-07-12 01:45:08 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [8/200][0/54]	eta 0:01:18 lr 0.000093	time 1.4579 (1.4579)	loss 7.1964 (7.1964)	miou 0.1566	grad_norm 264.6178 (264.6178)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:45:20 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [8/200][10/54]	eta 0:00:54 lr 0.000089	time 1.4643 (1.2285)	loss 10.4347 (9.1887)	miou 0.1523	grad_norm 1366.9077 (322.1049)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:45:31 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [8/200][20/54]	eta 0:00:39 lr 0.000084	time 1.2094 (1.1727)	loss 7.0185 (8.9297)	miou 0.1394	grad_norm 419.3744 (359.9111)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:45:42 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [8/200][30/54]	eta 0:00:27 lr 0.000077	time 1.5541 (1.1494)	loss 8.8313 (8.8997)	miou 0.1398	grad_norm 231.7307 (298.3856)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:45:53 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [8/200][40/54]	eta 0:00:16 lr 0.000071	time 0.9524 (1.1502)	loss 7.3742 (9.1634)	miou 0.1393	grad_norm 136.6821 (269.2135)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:46:05 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [8/200][50/54]	eta 0:00:04 lr 0.000063	time 1.0011 (1.1415)	loss 9.7750 (9.2571)	miou 0.1383	grad_norm 34.2555 (236.1538)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:46:08 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 8 training takes 0:01:01
[32m[2025-07-12 01:46:08 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
8
[32m[2025-07-12 01:46:18 3DDETR.yaml][33m(utils_help.py 202)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saving......
[32m[2025-07-12 01:46:18 3DDETR.yaml][33m(utils_help.py 204)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saved !!!
[32m[2025-07-12 01:46:18 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2237%
[32m[2025-07-12 01:46:18 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2237%
[32m[2025-07-12 01:46:20 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [9/200][0/54]	eta 0:01:00 lr 0.000060	time 1.1229 (1.1229)	loss 11.3267 (11.3267)	miou 0.2204	grad_norm 117.8146 (117.8146)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:46:31 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [9/200][10/54]	eta 0:00:48 lr 0.000052	time 1.1096 (1.1122)	loss 9.8731 (9.2818)	miou 0.1867	grad_norm 194.9884 (336.6758)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:46:42 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [9/200][20/54]	eta 0:00:37 lr 0.000045	time 1.0298 (1.0987)	loss 6.0357 (8.5191)	miou 0.1833	grad_norm 251.1343 (259.2407)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:46:52 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [9/200][30/54]	eta 0:00:25 lr 0.000037	time 0.9674 (1.0795)	loss 9.4005 (8.1335)	miou 0.1806	grad_norm 34.6853 (229.4345)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:47:04 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [9/200][40/54]	eta 0:00:15 lr 0.000029	time 1.3997 (1.1160)	loss 8.5508 (8.1696)	miou 0.1853	grad_norm 104.0195 (208.5987)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:47:15 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [9/200][50/54]	eta 0:00:04 lr 0.000023	time 0.9429 (1.1158)	loss 10.0415 (8.3381)	miou 0.1823	grad_norm 279.2190 (188.4642)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:47:18 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 9 training takes 0:00:59
[32m[2025-07-12 01:47:18 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
9
[32m[2025-07-12 01:47:28 3DDETR.yaml][33m(utils_help.py 202)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saving......
[32m[2025-07-12 01:47:29 3DDETR.yaml][33m(utils_help.py 204)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saved !!!
[32m[2025-07-12 01:47:29 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2597%
[32m[2025-07-12 01:47:29 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2597%
[32m[2025-07-12 01:47:30 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [10/200][0/54]	eta 0:01:26 lr 0.000020	time 1.6072 (1.6072)	loss 7.6562 (7.6562)	miou 0.2554	grad_norm 157.1966 (157.1966)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:47:41 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [10/200][10/54]	eta 0:00:47 lr 0.000014	time 1.2040 (1.0800)	loss 7.4353 (8.2050)	miou 0.2262	grad_norm 44.9110 (121.3594)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:47:52 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [10/200][20/54]	eta 0:00:37 lr 0.000009	time 0.8759 (1.0915)	loss 6.2579 (7.8023)	miou 0.2170	grad_norm 426.1117 (160.4548)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:48:03 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [10/200][30/54]	eta 0:00:26 lr 0.000005	time 0.8874 (1.0945)	loss 7.0406 (7.9838)	miou 0.2057	grad_norm 78.5059 (172.5453)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:48:14 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [10/200][40/54]	eta 0:00:15 lr 0.000002	time 1.0767 (1.0900)	loss 6.8048 (7.8187)	miou 0.2035	grad_norm 65.7172 (161.4745)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:48:25 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [10/200][50/54]	eta 0:00:04 lr 0.000000	time 1.1482 (1.1066)	loss 6.9916 (7.9067)	miou 0.2014	grad_norm 135.8262 (164.6660)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:48:29 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 10 training takes 0:01:00
[32m[2025-07-12 01:48:29 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 01:48:39 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2595%
[32m[2025-07-12 01:48:39 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2597%
[32m[2025-07-12 01:48:40 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [11/200][0/54]	eta 0:01:18 lr 0.000000	time 1.4450 (1.4450)	loss 8.4094 (8.4094)	miou 0.2513	grad_norm 171.5094 (171.5094)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:48:51 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [11/200][10/54]	eta 0:00:48 lr 0.000000	time 0.9572 (1.1017)	loss 9.2237 (8.6679)	miou 0.2133	grad_norm 46.2418 (148.1546)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:49:02 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [11/200][20/54]	eta 0:00:38 lr 0.000001	time 1.5347 (1.1178)	loss 8.1798 (8.3622)	miou 0.1979	grad_norm 66.1440 (155.6790)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:49:14 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [11/200][30/54]	eta 0:00:27 lr 0.000004	time 1.0363 (1.1393)	loss 8.7410 (8.2791)	miou 0.1970	grad_norm 49.9212 (143.9373)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:49:25 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [11/200][40/54]	eta 0:00:15 lr 0.000007	time 0.9367 (1.1210)	loss 7.7045 (8.0876)	miou 0.1916	grad_norm 274.4367 (140.2052)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:49:36 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [11/200][50/54]	eta 0:00:04 lr 0.000012	time 1.0083 (1.1101)	loss 12.6806 (8.1661)	miou 0.1919	grad_norm 90.7562 (148.3299)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:49:39 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 11 training takes 0:01:00
[32m[2025-07-12 01:49:39 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 01:49:49 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2548%
[32m[2025-07-12 01:49:49 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2597%
[32m[2025-07-12 01:49:51 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [12/200][0/54]	eta 0:01:20 lr 0.000014	time 1.4917 (1.4917)	loss 7.7167 (7.7167)	miou 0.2585	grad_norm 383.2986 (383.2986)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:50:02 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [12/200][10/54]	eta 0:00:49 lr 0.000020	time 0.9395 (1.1236)	loss 9.5265 (8.2471)	miou 0.2192	grad_norm 24.4915 (93.5147)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:50:14 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [12/200][20/54]	eta 0:00:39 lr 0.000027	time 1.2433 (1.1526)	loss 6.0631 (8.0970)	miou 0.2193	grad_norm 124.0368 (167.3090)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:50:25 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [12/200][30/54]	eta 0:00:27 lr 0.000034	time 1.0778 (1.1328)	loss 8.5968 (8.2590)	miou 0.2112	grad_norm 35.4226 (150.5013)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:50:36 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [12/200][40/54]	eta 0:00:15 lr 0.000041	time 1.0870 (1.1290)	loss 7.7022 (8.0882)	miou 0.2054	grad_norm 457.8901 (159.4237)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:50:47 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [12/200][50/54]	eta 0:00:04 lr 0.000049	time 0.8942 (1.1304)	loss 8.3502 (8.1107)	miou 0.2069	grad_norm 141.5466 (149.6428)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:50:50 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 12 training takes 0:01:00
[32m[2025-07-12 01:50:50 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 01:51:00 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2168%
[32m[2025-07-12 01:51:00 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2597%
[32m[2025-07-12 01:51:01 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [13/200][0/54]	eta 0:00:59 lr 0.000052	time 1.1056 (1.1056)	loss 8.3122 (8.3122)	miou 0.2158	grad_norm 128.8163 (128.8163)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:51:13 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [13/200][10/54]	eta 0:00:52 lr 0.000060	time 1.0390 (1.1909)	loss 7.2881 (8.0864)	miou 0.2119	grad_norm 53.3152 (385.8409)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:51:24 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [13/200][20/54]	eta 0:00:38 lr 0.000068	time 1.0092 (1.1317)	loss 8.9540 (7.7081)	miou 0.2017	grad_norm 43.1364 (281.6769)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:51:35 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [13/200][30/54]	eta 0:00:27 lr 0.000075	time 1.5091 (1.1284)	loss 5.5523 (7.5208)	miou 0.2008	grad_norm 81.0441 (228.4287)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:51:46 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [13/200][40/54]	eta 0:00:15 lr 0.000081	time 1.2099 (1.1181)	loss 6.8345 (7.4816)	miou 0.2003	grad_norm 34.6798 (202.9297)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:51:57 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [13/200][50/54]	eta 0:00:04 lr 0.000087	time 0.9021 (1.1207)	loss 10.7782 (7.7142)	miou 0.1961	grad_norm 48.0769 (204.3072)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:52:01 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 13 training takes 0:01:00
[32m[2025-07-12 01:52:01 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 01:52:11 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2220%
[32m[2025-07-12 01:52:11 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2597%
[32m[2025-07-12 01:52:12 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [14/200][0/54]	eta 0:01:01 lr 0.000089	time 1.1419 (1.1419)	loss 8.8112 (8.8112)	miou 0.2181	grad_norm 34.2084 (34.2084)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:52:24 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [14/200][10/54]	eta 0:00:50 lr 0.000093	time 0.8873 (1.1485)	loss 5.9396 (8.5633)	miou 0.2119	grad_norm 270.4885 (119.7423)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:52:35 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [14/200][20/54]	eta 0:00:38 lr 0.000097	time 1.0612 (1.1285)	loss 8.8600 (8.7019)	miou 0.1858	grad_norm 223.8797 (193.0030)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:52:46 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [14/200][30/54]	eta 0:00:26 lr 0.000099	time 0.8470 (1.1170)	loss 8.1334 (8.5491)	miou 0.1762	grad_norm 650.0822 (199.2500)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:52:57 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [14/200][40/54]	eta 0:00:15 lr 0.000100	time 1.0851 (1.1214)	loss 7.0335 (8.2181)	miou 0.1833	grad_norm 132.1260 (176.6947)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:53:08 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [14/200][50/54]	eta 0:00:04 lr 0.000100	time 1.0149 (1.1111)	loss 5.4539 (8.3032)	miou 0.1795	grad_norm 284.7610 (187.5353)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:53:11 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 14 training takes 0:01:00
[32m[2025-07-12 01:53:11 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 01:53:21 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2559%
[32m[2025-07-12 01:53:21 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2597%
[32m[2025-07-12 01:53:23 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [15/200][0/54]	eta 0:01:18 lr 0.000099	time 1.4547 (1.4547)	loss 6.6989 (6.6989)	miou 0.2548	grad_norm 136.0939 (136.0939)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:53:34 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [15/200][10/54]	eta 0:00:48 lr 0.000097	time 1.2704 (1.1027)	loss 5.8948 (7.7493)	miou 0.2193	grad_norm 550.7701 (170.9752)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:53:44 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [15/200][20/54]	eta 0:00:37 lr 0.000094	time 1.0070 (1.0893)	loss 7.1017 (8.0203)	miou 0.2125	grad_norm 47.5628 (122.4630)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:53:55 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [15/200][30/54]	eta 0:00:26 lr 0.000090	time 0.9139 (1.0937)	loss 8.1060 (7.9907)	miou 0.2044	grad_norm 100.9713 (106.8019)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:54:08 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [15/200][40/54]	eta 0:00:15 lr 0.000085	time 1.4453 (1.1242)	loss 8.6410 (7.7526)	miou 0.2059	grad_norm 571.1235 (127.5218)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:54:19 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [15/200][50/54]	eta 0:00:04 lr 0.000079	time 1.2517 (1.1211)	loss 7.0794 (7.8038)	miou 0.2031	grad_norm 311.9563 (120.5612)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:54:22 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 15 training takes 0:01:00
[32m[2025-07-12 01:54:22 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 01:54:32 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2187%
[32m[2025-07-12 01:54:32 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2597%
[32m[2025-07-12 01:54:33 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [16/200][0/54]	eta 0:00:54 lr 0.000076	time 1.0016 (1.0016)	loss 6.4286 (6.4286)	miou 0.2149	grad_norm 40.2241 (40.2241)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:54:43 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [16/200][10/54]	eta 0:00:45 lr 0.000069	time 0.9611 (1.0401)	loss 4.3197 (7.4595)	miou 0.2172	grad_norm 120.3361 (132.4767)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:54:55 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [16/200][20/54]	eta 0:00:37 lr 0.000062	time 1.1282 (1.1095)	loss 8.4183 (7.4493)	miou 0.2142	grad_norm 58.5710 (111.1227)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:55:07 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [16/200][30/54]	eta 0:00:26 lr 0.000054	time 1.2243 (1.1135)	loss 9.9967 (7.5828)	miou 0.2062	grad_norm 72.6263 (109.4176)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:55:17 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [16/200][40/54]	eta 0:00:15 lr 0.000046	time 1.1782 (1.1027)	loss 8.4043 (7.8444)	miou 0.2004	grad_norm 341.9140 (132.7681)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:55:29 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [16/200][50/54]	eta 0:00:04 lr 0.000038	time 1.0914 (1.1077)	loss 7.4696 (7.8141)	miou 0.2048	grad_norm 26.5858 (127.2393)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:55:32 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 16 training takes 0:00:59
[32m[2025-07-12 01:55:32 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 01:55:42 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1664%
[32m[2025-07-12 01:55:42 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2597%
[32m[2025-07-12 01:55:43 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [17/200][0/54]	eta 0:01:14 lr 0.000035	time 1.3755 (1.3755)	loss 4.9836 (4.9836)	miou 0.1712	grad_norm 136.1163 (136.1163)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:55:53 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [17/200][10/54]	eta 0:00:45 lr 0.000028	time 1.0134 (1.0405)	loss 7.2546 (7.1898)	miou 0.1689	grad_norm 56.8296 (115.0377)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:56:05 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [17/200][20/54]	eta 0:00:37 lr 0.000021	time 1.0576 (1.0902)	loss 9.0288 (7.1072)	miou 0.1729	grad_norm 28.3397 (109.8130)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:56:15 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [17/200][30/54]	eta 0:00:26 lr 0.000015	time 0.8707 (1.0847)	loss 6.9759 (7.2564)	miou 0.1692	grad_norm 24.9895 (92.6549)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:56:27 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [17/200][40/54]	eta 0:00:15 lr 0.000010	time 1.2419 (1.1039)	loss 7.6303 (7.2536)	miou 0.1689	grad_norm 31.8158 (100.8014)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:56:38 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [17/200][50/54]	eta 0:00:04 lr 0.000006	time 1.0540 (1.1115)	loss 6.9638 (7.2799)	miou 0.1756	grad_norm 75.0374 (91.5271)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:56:42 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 17 training takes 0:01:00
[32m[2025-07-12 01:56:42 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 01:56:52 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1783%
[32m[2025-07-12 01:56:52 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2597%
[32m[2025-07-12 01:56:54 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [18/200][0/54]	eta 0:01:37 lr 0.000004	time 1.8024 (1.8024)	loss 5.3452 (5.3452)	miou 0.1793	grad_norm 51.5589 (51.5589)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:57:05 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [18/200][10/54]	eta 0:00:52 lr 0.000002	time 1.0320 (1.1828)	loss 5.6335 (6.7169)	miou 0.1915	grad_norm 29.3765 (85.9833)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:57:16 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [18/200][20/54]	eta 0:00:39 lr 0.000000	time 1.0554 (1.1572)	loss 9.3088 (6.7693)	miou 0.1959	grad_norm 23.7921 (92.9974)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:57:28 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [18/200][30/54]	eta 0:00:27 lr 0.000000	time 1.2730 (1.1487)	loss 6.0259 (6.8544)	miou 0.1993	grad_norm 52.3905 (88.9433)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:57:39 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [18/200][40/54]	eta 0:00:15 lr 0.000001	time 0.9088 (1.1314)	loss 6.0847 (7.0722)	miou 0.1947	grad_norm 58.3738 (84.6078)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:57:50 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [18/200][50/54]	eta 0:00:04 lr 0.000003	time 1.1010 (1.1328)	loss 8.4413 (7.2057)	miou 0.1913	grad_norm 182.3488 (82.1923)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:57:54 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 18 training takes 0:01:01
[32m[2025-07-12 01:57:54 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 01:58:04 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2009%
[32m[2025-07-12 01:58:04 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2597%
[32m[2025-07-12 01:58:05 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [19/200][0/54]	eta 0:01:14 lr 0.000004	time 1.3877 (1.3877)	loss 7.5089 (7.5089)	miou 0.1990	grad_norm 118.3517 (118.3517)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:58:16 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [19/200][10/54]	eta 0:00:49 lr 0.000008	time 1.0563 (1.1160)	loss 8.4059 (7.4630)	miou 0.1942	grad_norm 244.5515 (83.8665)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:58:27 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [19/200][20/54]	eta 0:00:38 lr 0.000013	time 1.4975 (1.1214)	loss 9.2963 (7.3084)	miou 0.2009	grad_norm 106.7605 (92.9626)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:58:38 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [19/200][30/54]	eta 0:00:26 lr 0.000019	time 0.8515 (1.0951)	loss 6.6937 (6.9954)	miou 0.2056	grad_norm 43.7931 (79.7826)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:58:49 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [19/200][40/54]	eta 0:00:15 lr 0.000025	time 1.1434 (1.1018)	loss 9.7395 (7.0887)	miou 0.2070	grad_norm 98.0274 (85.3485)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:59:00 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [19/200][50/54]	eta 0:00:04 lr 0.000032	time 1.2557 (1.1064)	loss 7.6507 (7.2428)	miou 0.2068	grad_norm 21.7686 (83.2589)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:59:04 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 19 training takes 0:01:00
[32m[2025-07-12 01:59:04 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 01:59:14 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2288%
[32m[2025-07-12 01:59:14 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2597%
[32m[2025-07-12 01:59:15 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [20/200][0/54]	eta 0:01:08 lr 0.000035	time 1.2732 (1.2732)	loss 6.9948 (6.9948)	miou 0.2267	grad_norm 70.2181 (70.2181)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:59:26 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [20/200][10/54]	eta 0:00:50 lr 0.000043	time 1.0148 (1.1426)	loss 8.3118 (7.2615)	miou 0.2258	grad_norm 18.7009 (91.5480)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:59:37 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [20/200][20/54]	eta 0:00:37 lr 0.000051	time 1.0519 (1.1145)	loss 9.1157 (7.3553)	miou 0.2193	grad_norm 132.5496 (85.8908)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:59:48 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [20/200][30/54]	eta 0:00:26 lr 0.000059	time 0.7697 (1.1118)	loss 8.9321 (7.1718)	miou 0.2222	grad_norm 41.2219 (82.4877)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:00:00 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [20/200][40/54]	eta 0:00:15 lr 0.000066	time 1.1436 (1.1265)	loss 4.6849 (7.3275)	miou 0.2156	grad_norm 75.3680 (78.7037)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:00:11 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [20/200][50/54]	eta 0:00:04 lr 0.000073	time 1.0034 (1.1216)	loss 9.6593 (7.5281)	miou 0.2138	grad_norm 54.1314 (77.5327)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:00:14 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 20 training takes 0:01:00
[32m[2025-07-12 02:00:14 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 02:00:24 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1828%
[32m[2025-07-12 02:00:24 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2597%
[32m[2025-07-12 02:00:26 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [21/200][0/54]	eta 0:01:07 lr 0.000076	time 1.2562 (1.2562)	loss 6.3659 (6.3659)	miou 0.1883	grad_norm 134.1432 (134.1432)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:00:36 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [21/200][10/54]	eta 0:00:47 lr 0.000082	time 1.1315 (1.0793)	loss 5.2156 (7.8376)	miou 0.1977	grad_norm 103.5895 (70.9338)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:00:48 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [21/200][20/54]	eta 0:00:38 lr 0.000088	time 1.4052 (1.1432)	loss 8.5922 (7.9042)	miou 0.1936	grad_norm 64.9892 (85.7650)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:00:59 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [21/200][30/54]	eta 0:00:27 lr 0.000093	time 0.8245 (1.1317)	loss 6.7761 (7.6991)	miou 0.1967	grad_norm 60.5210 (104.5788)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:01:11 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [21/200][40/54]	eta 0:00:15 lr 0.000096	time 1.0919 (1.1278)	loss 5.5081 (7.3684)	miou 0.2041	grad_norm 33.4055 (98.1481)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:01:21 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [21/200][50/54]	eta 0:00:04 lr 0.000099	time 0.7863 (1.1142)	loss 8.6440 (7.3320)	miou 0.2032	grad_norm 76.8453 (97.8515)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:01:24 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 21 training takes 0:01:00
[32m[2025-07-12 02:01:24 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 02:01:34 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2265%
[32m[2025-07-12 02:01:34 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2597%
[32m[2025-07-12 02:01:36 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [22/200][0/54]	eta 0:01:15 lr 0.000099	time 1.4026 (1.4026)	loss 5.9372 (5.9372)	miou 0.2234	grad_norm 45.6868 (45.6868)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:01:47 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [22/200][10/54]	eta 0:00:49 lr 0.000100	time 1.1714 (1.1145)	loss 8.4074 (7.4155)	miou 0.2104	grad_norm 224.1319 (104.1199)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:01:58 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [22/200][20/54]	eta 0:00:38 lr 0.000100	time 0.8524 (1.1222)	loss 8.1760 (7.2661)	miou 0.2054	grad_norm 47.6583 (102.7766)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:02:09 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [22/200][30/54]	eta 0:00:26 lr 0.000098	time 1.2666 (1.1097)	loss 9.5746 (7.1433)	miou 0.1997	grad_norm 23.8432 (101.4423)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:02:20 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [22/200][40/54]	eta 0:00:15 lr 0.000095	time 1.4048 (1.1196)	loss 6.4402 (7.3294)	miou 0.1975	grad_norm 42.8866 (98.3945)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:02:31 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [22/200][50/54]	eta 0:00:04 lr 0.000091	time 0.9876 (1.1128)	loss 12.1800 (7.4492)	miou 0.1941	grad_norm 95.0274 (108.7002)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:02:35 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 22 training takes 0:01:00
[32m[2025-07-12 02:02:35 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 02:02:45 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2502%
[32m[2025-07-12 02:02:45 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2597%
[32m[2025-07-12 02:02:46 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [23/200][0/54]	eta 0:01:26 lr 0.000089	time 1.6104 (1.6104)	loss 7.6331 (7.6331)	miou 0.2458	grad_norm 97.1665 (97.1665)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:02:57 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [23/200][10/54]	eta 0:00:49 lr 0.000084	time 1.0867 (1.1184)	loss 8.0876 (7.1131)	miou 0.2301	grad_norm 40.2225 (61.5596)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:03:09 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [23/200][20/54]	eta 0:00:38 lr 0.000077	time 1.0433 (1.1363)	loss 7.3970 (6.9169)	miou 0.2212	grad_norm 174.4134 (74.1438)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:03:20 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [23/200][30/54]	eta 0:00:27 lr 0.000071	time 1.0937 (1.1295)	loss 6.1742 (6.9330)	miou 0.2136	grad_norm 58.4178 (66.9416)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:03:31 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [23/200][40/54]	eta 0:00:15 lr 0.000063	time 1.2157 (1.1287)	loss 6.5853 (7.1298)	miou 0.2141	grad_norm 78.6185 (70.3153)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:03:42 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [23/200][50/54]	eta 0:00:04 lr 0.000055	time 1.1589 (1.1203)	loss 6.5528 (7.0951)	miou 0.2077	grad_norm 64.2605 (75.4476)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:03:46 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 23 training takes 0:01:01
[32m[2025-07-12 02:03:46 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
23
[32m[2025-07-12 02:03:56 3DDETR.yaml][33m(utils_help.py 202)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saving......
[32m[2025-07-12 02:03:56 3DDETR.yaml][33m(utils_help.py 204)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saved !!!
[32m[2025-07-12 02:03:56 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2619%
[32m[2025-07-12 02:03:56 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 02:03:58 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [24/200][0/54]	eta 0:01:20 lr 0.000052	time 1.4823 (1.4823)	loss 6.6633 (6.6633)	miou 0.2724	grad_norm 56.3842 (56.3842)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:04:09 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [24/200][10/54]	eta 0:00:50 lr 0.000045	time 1.4819 (1.1392)	loss 5.4941 (6.6849)	miou 0.2518	grad_norm 84.4686 (99.7639)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:04:20 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [24/200][20/54]	eta 0:00:38 lr 0.000037	time 1.3953 (1.1439)	loss 8.5034 (7.2377)	miou 0.2378	grad_norm 72.5581 (80.3701)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:04:30 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [24/200][30/54]	eta 0:00:26 lr 0.000029	time 0.9796 (1.0877)	loss 8.6027 (7.3474)	miou 0.2309	grad_norm 128.9877 (75.1969)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:04:41 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [24/200][40/54]	eta 0:00:15 lr 0.000023	time 1.1826 (1.0941)	loss 7.5811 (7.2882)	miou 0.2330	grad_norm 96.9137 (71.1522)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:04:53 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [24/200][50/54]	eta 0:00:04 lr 0.000016	time 1.1981 (1.1103)	loss 7.6437 (7.2610)	miou 0.2274	grad_norm 128.2063 (72.1103)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:04:56 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 24 training takes 0:00:59
[32m[2025-07-12 02:04:56 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 02:05:06 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2306%
[32m[2025-07-12 02:05:06 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 02:05:08 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [25/200][0/54]	eta 0:01:25 lr 0.000014	time 1.5816 (1.5816)	loss 5.3706 (5.3706)	miou 0.2314	grad_norm 24.7487 (24.7487)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:05:19 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [25/200][10/54]	eta 0:00:50 lr 0.000009	time 1.0423 (1.1376)	loss 6.9037 (7.8412)	miou 0.2212	grad_norm 50.8676 (50.4416)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:05:29 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [25/200][20/54]	eta 0:00:36 lr 0.000005	time 1.0701 (1.0871)	loss 12.1066 (7.8950)	miou 0.2145	grad_norm 63.1226 (45.3237)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:05:40 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [25/200][30/54]	eta 0:00:26 lr 0.000002	time 1.4828 (1.0981)	loss 6.5937 (7.6010)	miou 0.2129	grad_norm 437.8592 (64.1578)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:05:51 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [25/200][40/54]	eta 0:00:15 lr 0.000000	time 0.9336 (1.1027)	loss 6.6906 (7.4543)	miou 0.2151	grad_norm 45.0163 (58.9282)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:06:03 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [25/200][50/54]	eta 0:00:04 lr 0.000000	time 1.2405 (1.1259)	loss 3.9476 (7.2671)	miou 0.2211	grad_norm 62.2178 (64.3259)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:06:07 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 25 training takes 0:01:01
[32m[2025-07-12 02:06:07 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 02:06:17 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2103%
[32m[2025-07-12 02:06:17 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 02:06:19 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [26/200][0/54]	eta 0:01:15 lr 0.000000	time 1.3943 (1.3943)	loss 6.4993 (6.4993)	miou 0.2076	grad_norm 71.3920 (71.3920)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:06:30 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [26/200][10/54]	eta 0:00:50 lr 0.000001	time 1.0534 (1.1591)	loss 4.9071 (6.7790)	miou 0.2044	grad_norm 32.3231 (53.5563)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:06:41 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [26/200][20/54]	eta 0:00:38 lr 0.000004	time 0.9727 (1.1374)	loss 5.3836 (6.8103)	miou 0.2082	grad_norm 150.2795 (74.2186)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:06:53 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [26/200][30/54]	eta 0:00:27 lr 0.000007	time 1.1777 (1.1451)	loss 6.7517 (6.9091)	miou 0.2118	grad_norm 19.3256 (62.3409)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:07:03 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [26/200][40/54]	eta 0:00:15 lr 0.000012	time 0.8529 (1.1159)	loss 8.0360 (7.1856)	miou 0.2180	grad_norm 55.1344 (74.8911)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:07:15 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [26/200][50/54]	eta 0:00:04 lr 0.000018	time 1.0659 (1.1284)	loss 5.5702 (7.0623)	miou 0.2215	grad_norm 31.2486 (71.9626)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:07:18 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 26 training takes 0:01:00
[32m[2025-07-12 02:07:18 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 02:07:28 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2516%
[32m[2025-07-12 02:07:28 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 02:07:30 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [27/200][0/54]	eta 0:01:40 lr 0.000020	time 1.8552 (1.8552)	loss 4.2184 (4.2184)	miou 0.2526	grad_norm 37.0256 (37.0256)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:07:41 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [27/200][10/54]	eta 0:00:52 lr 0.000027	time 1.0453 (1.2024)	loss 6.3402 (6.2920)	miou 0.2303	grad_norm 37.7472 (58.3264)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:07:52 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [27/200][20/54]	eta 0:00:39 lr 0.000034	time 1.2982 (1.1500)	loss 7.5062 (6.6901)	miou 0.2333	grad_norm 79.4157 (62.2575)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:08:04 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [27/200][30/54]	eta 0:00:28 lr 0.000041	time 1.1419 (1.1723)	loss 5.5104 (6.7703)	miou 0.2383	grad_norm 52.2135 (68.3664)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:08:15 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [27/200][40/54]	eta 0:00:15 lr 0.000049	time 0.8856 (1.1399)	loss 6.1832 (6.7906)	miou 0.2396	grad_norm 164.7798 (74.7222)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:08:25 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [27/200][50/54]	eta 0:00:04 lr 0.000057	time 0.9500 (1.1165)	loss 6.1817 (6.7412)	miou 0.2370	grad_norm 82.8766 (75.0496)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:08:28 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 27 training takes 0:01:00
[32m[2025-07-12 02:08:28 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 02:08:38 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1702%
[32m[2025-07-12 02:08:38 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 02:08:40 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [28/200][0/54]	eta 0:01:19 lr 0.000060	time 1.4718 (1.4718)	loss 6.5032 (6.5032)	miou 0.1686	grad_norm 34.8418 (34.8418)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:08:51 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [28/200][10/54]	eta 0:00:50 lr 0.000068	time 1.2468 (1.1401)	loss 4.2079 (7.2623)	miou 0.1839	grad_norm 186.3027 (90.2171)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:09:02 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [28/200][20/54]	eta 0:00:38 lr 0.000075	time 1.2683 (1.1398)	loss 5.8036 (7.0052)	miou 0.1979	grad_norm 20.3291 (76.1521)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:09:13 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [28/200][30/54]	eta 0:00:27 lr 0.000081	time 0.8784 (1.1281)	loss 5.5107 (6.9317)	miou 0.2060	grad_norm 32.5040 (71.3320)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:09:24 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [28/200][40/54]	eta 0:00:15 lr 0.000087	time 1.2910 (1.1221)	loss 7.2798 (7.1553)	miou 0.2167	grad_norm 44.3918 (67.1770)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:09:35 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [28/200][50/54]	eta 0:00:04 lr 0.000092	time 0.9483 (1.1134)	loss 4.1536 (7.2141)	miou 0.2167	grad_norm 31.9543 (68.3257)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:09:39 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 28 training takes 0:01:00
[32m[2025-07-12 02:09:39 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 02:09:49 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2217%
[32m[2025-07-12 02:09:49 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 02:09:50 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [29/200][0/54]	eta 0:01:22 lr 0.000093	time 1.5258 (1.5258)	loss 6.6128 (6.6128)	miou 0.2192	grad_norm 38.9462 (38.9462)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:10:01 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [29/200][10/54]	eta 0:00:50 lr 0.000097	time 0.9478 (1.1364)	loss 5.8233 (7.6925)	miou 0.2108	grad_norm 87.1036 (63.5779)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:10:12 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [29/200][20/54]	eta 0:00:38 lr 0.000099	time 0.9246 (1.1240)	loss 7.6142 (7.2840)	miou 0.2137	grad_norm 30.3783 (60.9945)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:10:23 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [29/200][30/54]	eta 0:00:26 lr 0.000100	time 1.0285 (1.1025)	loss 5.3857 (7.0676)	miou 0.2128	grad_norm 147.4993 (70.6900)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:10:35 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [29/200][40/54]	eta 0:00:15 lr 0.000100	time 1.0049 (1.1216)	loss 6.0423 (6.9764)	miou 0.2258	grad_norm 23.8410 (62.0280)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:10:47 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [29/200][50/54]	eta 0:00:04 lr 0.000098	time 1.0938 (1.1341)	loss 10.1699 (7.1108)	miou 0.2223	grad_norm 48.2959 (61.6747)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:10:50 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 29 training takes 0:01:00
[32m[2025-07-12 02:10:50 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 02:11:00 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1822%
[32m[2025-07-12 02:11:00 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 02:11:01 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [30/200][0/54]	eta 0:01:12 lr 0.000097	time 1.3371 (1.3371)	loss 12.1192 (12.1192)	miou 0.1796	grad_norm 58.6214 (58.6214)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:11:11 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [30/200][10/54]	eta 0:00:46 lr 0.000094	time 0.9718 (1.0621)	loss 6.8253 (7.4608)	miou 0.2090	grad_norm 46.1348 (41.8195)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:11:23 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [30/200][20/54]	eta 0:00:37 lr 0.000090	time 1.2459 (1.1004)	loss 5.7293 (6.9600)	miou 0.2228	grad_norm 44.3543 (46.3044)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:11:34 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [30/200][30/54]	eta 0:00:26 lr 0.000085	time 1.5392 (1.1031)	loss 7.7617 (6.9233)	miou 0.2141	grad_norm 245.4284 (79.4253)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:11:45 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [30/200][40/54]	eta 0:00:15 lr 0.000079	time 0.9913 (1.1088)	loss 8.3840 (7.0416)	miou 0.2128	grad_norm 29.3602 (72.1875)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:11:57 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [30/200][50/54]	eta 0:00:04 lr 0.000072	time 1.1653 (1.1282)	loss 9.1782 (7.1292)	miou 0.2092	grad_norm 23.3864 (69.4698)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:12:01 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 30 training takes 0:01:01
[32m[2025-07-12 02:12:01 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 02:12:11 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1118%
[32m[2025-07-12 02:12:11 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 02:12:13 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [31/200][0/54]	eta 0:01:29 lr 0.000069	time 1.6506 (1.6506)	loss 7.3196 (7.3196)	miou 0.1277	grad_norm 123.0807 (123.0807)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:12:24 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [31/200][10/54]	eta 0:00:52 lr 0.000062	time 1.2287 (1.2014)	loss 7.1720 (7.8389)	miou 0.1437	grad_norm 53.8202 (73.0654)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:12:35 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [31/200][20/54]	eta 0:00:39 lr 0.000054	time 1.0353 (1.1495)	loss 4.6941 (6.7992)	miou 0.1725	grad_norm 19.4178 (57.1192)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:12:45 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [31/200][30/54]	eta 0:00:26 lr 0.000046	time 0.8130 (1.1001)	loss 5.8047 (6.6359)	miou 0.1901	grad_norm 29.1840 (53.5811)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:12:56 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [31/200][40/54]	eta 0:00:15 lr 0.000038	time 0.8873 (1.0906)	loss 5.4137 (6.7368)	miou 0.1872	grad_norm 66.0689 (50.8439)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:13:07 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [31/200][50/54]	eta 0:00:04 lr 0.000031	time 1.0181 (1.1027)	loss 7.2502 (6.9207)	miou 0.1898	grad_norm 18.2704 (46.5146)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:13:11 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 31 training takes 0:01:00
[32m[2025-07-12 02:13:11 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 02:13:21 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2500%
[32m[2025-07-12 02:13:21 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 02:13:23 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [32/200][0/54]	eta 0:01:29 lr 0.000028	time 1.6595 (1.6595)	loss 5.2332 (5.2332)	miou 0.2592	grad_norm 359.6172 (359.6172)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:13:35 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [32/200][10/54]	eta 0:00:55 lr 0.000021	time 1.1562 (1.2709)	loss 6.3938 (6.6101)	miou 0.2493	grad_norm 44.9720 (63.7189)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:13:46 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [32/200][20/54]	eta 0:00:40 lr 0.000015	time 1.2530 (1.2015)	loss 7.2223 (7.0322)	miou 0.2355	grad_norm 100.8917 (57.9839)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:13:57 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [32/200][30/54]	eta 0:00:27 lr 0.000010	time 0.9665 (1.1465)	loss 5.4347 (6.8226)	miou 0.2401	grad_norm 51.9474 (49.7055)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:14:07 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [32/200][40/54]	eta 0:00:15 lr 0.000006	time 1.0440 (1.1308)	loss 5.6585 (6.8027)	miou 0.2472	grad_norm 32.5984 (45.2876)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:14:19 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [32/200][50/54]	eta 0:00:04 lr 0.000003	time 1.1454 (1.1333)	loss 9.9807 (6.7399)	miou 0.2401	grad_norm 31.3445 (42.8034)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:14:22 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 32 training takes 0:01:01
[32m[2025-07-12 02:14:22 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 02:14:32 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2549%
[32m[2025-07-12 02:14:32 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 02:14:33 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [33/200][0/54]	eta 0:01:07 lr 0.000002	time 1.2501 (1.2501)	loss 5.7024 (5.7024)	miou 0.2473	grad_norm 81.7305 (81.7305)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:14:45 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [33/200][10/54]	eta 0:00:50 lr 0.000000	time 1.0771 (1.1496)	loss 6.6994 (7.0264)	miou 0.2380	grad_norm 127.5185 (47.6348)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:14:56 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [33/200][20/54]	eta 0:00:38 lr 0.000000	time 0.9360 (1.1374)	loss 7.8465 (7.3097)	miou 0.2482	grad_norm 35.0009 (36.6635)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:15:07 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [33/200][30/54]	eta 0:00:27 lr 0.000001	time 1.0478 (1.1407)	loss 6.1915 (6.9851)	miou 0.2494	grad_norm 13.1608 (40.8719)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:15:19 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [33/200][40/54]	eta 0:00:16 lr 0.000003	time 0.9968 (1.1548)	loss 5.6442 (6.8764)	miou 0.2497	grad_norm 22.8923 (40.1720)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:15:31 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [33/200][50/54]	eta 0:00:04 lr 0.000007	time 1.0311 (1.1455)	loss 5.3793 (7.0418)	miou 0.2529	grad_norm 38.6490 (39.2149)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:15:33 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 33 training takes 0:01:01
[32m[2025-07-12 02:15:33 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 02:15:44 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2417%
[32m[2025-07-12 02:15:44 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 02:15:45 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [34/200][0/54]	eta 0:01:15 lr 0.000008	time 1.3922 (1.3922)	loss 8.3310 (8.3310)	miou 0.2405	grad_norm 18.3497 (18.3497)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:15:57 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [34/200][10/54]	eta 0:00:54 lr 0.000013	time 0.9366 (1.2367)	loss 7.3332 (7.4018)	miou 0.2292	grad_norm 35.3167 (24.6982)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:16:08 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [34/200][20/54]	eta 0:00:39 lr 0.000019	time 0.9455 (1.1689)	loss 6.9957 (7.0429)	miou 0.2480	grad_norm 16.9360 (31.1086)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:16:19 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [34/200][30/54]	eta 0:00:27 lr 0.000025	time 0.9231 (1.1517)	loss 7.5541 (6.7431)	miou 0.2513	grad_norm 32.0311 (33.8403)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:16:31 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [34/200][40/54]	eta 0:00:16 lr 0.000032	time 1.2914 (1.1585)	loss 6.1373 (7.0661)	miou 0.2556	grad_norm 36.0802 (37.7690)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:16:42 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [34/200][50/54]	eta 0:00:04 lr 0.000040	time 1.0207 (1.1455)	loss 5.8679 (7.0331)	miou 0.2503	grad_norm 17.4607 (35.6368)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:16:45 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 34 training takes 0:01:01
[32m[2025-07-12 02:16:45 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 02:16:56 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2268%
[32m[2025-07-12 02:16:56 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 02:16:57 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [35/200][0/54]	eta 0:01:30 lr 0.000043	time 1.6756 (1.6756)	loss 6.8908 (6.8908)	miou 0.2411	grad_norm 61.0355 (61.0355)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:17:08 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [35/200][10/54]	eta 0:00:49 lr 0.000051	time 0.8859 (1.1223)	loss 4.1678 (6.0086)	miou 0.2387	grad_norm 23.1465 (34.4221)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:17:19 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [35/200][20/54]	eta 0:00:38 lr 0.000059	time 1.0907 (1.1295)	loss 5.3680 (6.0893)	miou 0.2416	grad_norm 19.9490 (38.5820)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:17:30 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [35/200][30/54]	eta 0:00:26 lr 0.000066	time 0.9741 (1.1009)	loss 8.1815 (6.2004)	miou 0.2406	grad_norm 23.3217 (37.4326)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:17:40 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [35/200][40/54]	eta 0:00:15 lr 0.000073	time 0.9888 (1.0936)	loss 5.8791 (6.4514)	miou 0.2415	grad_norm 26.0854 (35.8495)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:17:52 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [35/200][50/54]	eta 0:00:04 lr 0.000080	time 1.0806 (1.1139)	loss 8.8289 (6.6294)	miou 0.2346	grad_norm 95.6847 (37.2436)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:17:56 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 35 training takes 0:01:00
[32m[2025-07-12 02:17:56 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 02:18:06 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1900%
[32m[2025-07-12 02:18:06 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 02:18:07 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [36/200][0/54]	eta 0:01:12 lr 0.000082	time 1.3501 (1.3501)	loss 9.8725 (9.8725)	miou 0.1886	grad_norm 36.5202 (36.5202)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:18:17 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [36/200][10/54]	eta 0:00:46 lr 0.000088	time 1.0702 (1.0493)	loss 7.3142 (7.2167)	miou 0.1959	grad_norm 55.5765 (49.9920)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:18:29 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [36/200][20/54]	eta 0:00:37 lr 0.000093	time 0.9861 (1.0890)	loss 5.1958 (7.0711)	miou 0.2152	grad_norm 76.8802 (51.3096)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:18:40 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [36/200][30/54]	eta 0:00:26 lr 0.000096	time 1.0079 (1.0988)	loss 8.8947 (7.2145)	miou 0.2189	grad_norm 39.0917 (49.5218)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:18:52 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [36/200][40/54]	eta 0:00:15 lr 0.000099	time 1.0795 (1.1117)	loss 4.8279 (7.0577)	miou 0.2222	grad_norm 35.6154 (49.7570)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:19:02 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [36/200][50/54]	eta 0:00:04 lr 0.000100	time 0.8344 (1.1032)	loss 8.7859 (7.0855)	miou 0.2229	grad_norm 22.5499 (55.5032)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:19:06 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 36 training takes 0:00:59
[32m[2025-07-12 02:19:06 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 02:19:16 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2229%
[32m[2025-07-12 02:19:16 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 02:19:17 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [37/200][0/54]	eta 0:00:57 lr 0.000100	time 1.0711 (1.0711)	loss 6.5541 (6.5541)	miou 0.2251	grad_norm 86.9662 (86.9662)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 02:19:27 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [37/200][10/54]	eta 0:00:46 lr 0.000100	time 0.9569 (1.0530)	loss 5.5586 (6.9963)	miou 0.2341	grad_norm 20.0411 (40.3410)	loss_scale 131072.0000 (125114.1818)	mem 3265MB
[32m[2025-07-12 02:19:39 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [37/200][20/54]	eta 0:00:37 lr 0.000098	time 1.1475 (1.0971)	loss 10.1665 (7.2749)	miou 0.2195	grad_norm 31.6973 (61.0219)	loss_scale 131072.0000 (127951.2381)	mem 3265MB
[32m[2025-07-12 02:19:50 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [37/200][30/54]	eta 0:00:26 lr 0.000095	time 1.2161 (1.1207)	loss 10.9977 (7.1673)	miou 0.2198	grad_norm 36.6139 (57.1767)	loss_scale 131072.0000 (128957.9355)	mem 3265MB
[32m[2025-07-12 02:20:01 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [37/200][40/54]	eta 0:00:15 lr 0.000091	time 1.0560 (1.1159)	loss 5.6216 (7.0589)	miou 0.2217	grad_norm 36.2153 (52.9823)	loss_scale 131072.0000 (129473.5610)	mem 3265MB
[32m[2025-07-12 02:20:13 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [37/200][50/54]	eta 0:00:04 lr 0.000086	time 1.5367 (1.1241)	loss 7.5923 (7.1907)	miou 0.2213	grad_norm 21.4475 (48.5859)	loss_scale 131072.0000 (129786.9804)	mem 3265MB
[32m[2025-07-12 02:20:16 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 37 training takes 0:01:00
[32m[2025-07-12 02:20:16 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 02:20:26 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2282%
[32m[2025-07-12 02:20:26 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 02:20:28 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [38/200][0/54]	eta 0:01:24 lr 0.000084	time 1.5601 (1.5601)	loss 6.5580 (6.5580)	miou 0.2197	grad_norm 15.3165 (15.3165)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:20:39 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [38/200][10/54]	eta 0:00:51 lr 0.000077	time 1.2650 (1.1744)	loss 8.9900 (6.7993)	miou 0.2153	grad_norm 52.7234 (38.0581)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:20:51 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [38/200][20/54]	eta 0:00:39 lr 0.000071	time 1.1060 (1.1619)	loss 6.0277 (6.8356)	miou 0.2211	grad_norm 15.9073 (36.7819)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:21:01 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [38/200][30/54]	eta 0:00:27 lr 0.000063	time 1.0904 (1.1292)	loss 6.4117 (6.6871)	miou 0.2289	grad_norm 23.5211 (32.5832)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:21:13 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [38/200][40/54]	eta 0:00:15 lr 0.000055	time 1.3879 (1.1348)	loss 8.9573 (6.7043)	miou 0.2281	grad_norm 41.5061 (33.3124)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:21:24 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [38/200][50/54]	eta 0:00:04 lr 0.000048	time 1.0072 (1.1297)	loss 7.2007 (6.7033)	miou 0.2317	grad_norm 47.2449 (33.5040)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:21:27 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 38 training takes 0:01:01
[32m[2025-07-12 02:21:27 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 02:21:38 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1762%
[32m[2025-07-12 02:21:38 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 02:21:39 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [39/200][0/54]	eta 0:01:08 lr 0.000045	time 1.2638 (1.2638)	loss 6.5052 (6.5052)	miou 0.1759	grad_norm 62.0689 (62.0689)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:21:49 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [39/200][10/54]	eta 0:00:47 lr 0.000037	time 1.1427 (1.0705)	loss 6.3501 (7.0425)	miou 0.1938	grad_norm 36.1335 (39.4148)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:22:01 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [39/200][20/54]	eta 0:00:37 lr 0.000029	time 1.2206 (1.1064)	loss 6.0862 (7.1316)	miou 0.2133	grad_norm 10.7667 (37.7514)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:22:11 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [39/200][30/54]	eta 0:00:26 lr 0.000023	time 0.9930 (1.0918)	loss 7.1575 (7.1265)	miou 0.2202	grad_norm 25.7177 (35.4372)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:22:23 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [39/200][40/54]	eta 0:00:15 lr 0.000016	time 1.2649 (1.1193)	loss 7.7871 (6.9144)	miou 0.2335	grad_norm 23.0293 (33.5076)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:22:34 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [39/200][50/54]	eta 0:00:04 lr 0.000011	time 0.9553 (1.1055)	loss 5.3495 (6.7880)	miou 0.2370	grad_norm 38.7449 (33.9048)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:22:37 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 39 training takes 0:00:59
[32m[2025-07-12 02:22:37 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 02:22:47 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2205%
[32m[2025-07-12 02:22:47 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 02:22:49 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [40/200][0/54]	eta 0:01:11 lr 0.000009	time 1.3226 (1.3226)	loss 7.6444 (7.6444)	miou 0.2156	grad_norm 13.3315 (13.3315)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:23:01 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [40/200][10/54]	eta 0:00:55 lr 0.000005	time 1.3844 (1.2552)	loss 7.0700 (7.1486)	miou 0.2187	grad_norm 48.7172 (44.1853)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:23:12 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [40/200][20/54]	eta 0:00:39 lr 0.000002	time 0.9635 (1.1688)	loss 8.0241 (6.8451)	miou 0.2306	grad_norm 22.3206 (36.0014)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:23:23 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [40/200][30/54]	eta 0:00:27 lr 0.000000	time 1.0964 (1.1426)	loss 5.3598 (6.9103)	miou 0.2392	grad_norm 12.5360 (30.1302)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:23:34 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [40/200][40/54]	eta 0:00:15 lr 0.000000	time 0.9841 (1.1342)	loss 8.2685 (6.7798)	miou 0.2441	grad_norm 20.9263 (32.0978)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:23:45 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [40/200][50/54]	eta 0:00:04 lr 0.000001	time 1.1353 (1.1345)	loss 5.1256 (6.7457)	miou 0.2502	grad_norm 19.3738 (30.5492)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:23:49 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 40 training takes 0:01:01
[32m[2025-07-12 02:23:49 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 02:23:59 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2019%
[32m[2025-07-12 02:23:59 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 02:24:00 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [41/200][0/54]	eta 0:01:05 lr 0.000001	time 1.2142 (1.2142)	loss 4.6719 (4.6719)	miou 0.2086	grad_norm 42.3274 (42.3274)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:24:12 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [41/200][10/54]	eta 0:00:51 lr 0.000004	time 1.0359 (1.1750)	loss 5.8249 (6.6125)	miou 0.2213	grad_norm 39.8704 (25.6131)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:24:22 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [41/200][20/54]	eta 0:00:37 lr 0.000007	time 1.2542 (1.1158)	loss 7.4599 (6.6913)	miou 0.2289	grad_norm 27.6480 (25.9477)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:24:33 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [41/200][30/54]	eta 0:00:26 lr 0.000012	time 1.1078 (1.1038)	loss 8.7369 (6.7867)	miou 0.2335	grad_norm 29.6623 (24.3578)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:24:44 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [41/200][40/54]	eta 0:00:15 lr 0.000018	time 1.0598 (1.1025)	loss 6.8179 (7.0891)	miou 0.2372	grad_norm 27.8158 (26.0339)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:24:55 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [41/200][50/54]	eta 0:00:04 lr 0.000024	time 0.9933 (1.1107)	loss 5.2688 (7.0931)	miou 0.2349	grad_norm 49.2127 (26.8282)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:24:59 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 41 training takes 0:01:00
[32m[2025-07-12 02:24:59 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 02:25:09 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2449%
[32m[2025-07-12 02:25:09 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 02:25:11 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [42/200][0/54]	eta 0:01:09 lr 0.000027	time 1.2961 (1.2961)	loss 5.3240 (5.3240)	miou 0.2476	grad_norm 18.0910 (18.0910)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:25:21 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [42/200][10/54]	eta 0:00:47 lr 0.000034	time 0.9294 (1.0795)	loss 5.2807 (6.2446)	miou 0.2572	grad_norm 18.4924 (26.1851)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:25:32 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [42/200][20/54]	eta 0:00:36 lr 0.000041	time 0.9123 (1.0823)	loss 8.8240 (6.6011)	miou 0.2538	grad_norm 14.1579 (25.3992)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:25:44 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [42/200][30/54]	eta 0:00:26 lr 0.000049	time 1.2721 (1.1004)	loss 7.0677 (6.5290)	miou 0.2617	grad_norm 35.2095 (28.5141)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:25:55 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [42/200][40/54]	eta 0:00:15 lr 0.000057	time 1.0164 (1.1136)	loss 6.3062 (6.6080)	miou 0.2609	grad_norm 65.8641 (34.5635)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:26:07 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [42/200][50/54]	eta 0:00:04 lr 0.000065	time 0.9313 (1.1188)	loss 6.8588 (6.5973)	miou 0.2564	grad_norm 32.7048 (33.0175)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:26:10 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 42 training takes 0:01:00
[32m[2025-07-12 02:26:10 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 02:26:20 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2306%
[32m[2025-07-12 02:26:20 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 02:26:21 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [43/200][0/54]	eta 0:01:17 lr 0.000068	time 1.4442 (1.4442)	loss 6.5560 (6.5560)	miou 0.2310	grad_norm 61.9301 (61.9301)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:26:33 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [43/200][10/54]	eta 0:00:51 lr 0.000075	time 1.0236 (1.1633)	loss 8.7743 (7.8299)	miou 0.2383	grad_norm 12.1188 (38.0004)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:26:45 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [43/200][20/54]	eta 0:00:41 lr 0.000081	time 1.3238 (1.2113)	loss 7.2172 (7.3680)	miou 0.2391	grad_norm 30.1205 (33.3054)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:26:56 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [43/200][30/54]	eta 0:00:27 lr 0.000087	time 0.9465 (1.1638)	loss 11.6839 (7.1842)	miou 0.2377	grad_norm 12.6431 (32.9719)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:27:07 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [43/200][40/54]	eta 0:00:15 lr 0.000092	time 1.0614 (1.1419)	loss 5.9181 (7.0179)	miou 0.2355	grad_norm 16.8297 (30.2969)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:27:17 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [43/200][50/54]	eta 0:00:04 lr 0.000096	time 0.9830 (1.1268)	loss 5.8461 (6.9104)	miou 0.2402	grad_norm 17.1594 (33.3807)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:27:21 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 43 training takes 0:01:00
[32m[2025-07-12 02:27:21 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 02:27:31 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2031%
[32m[2025-07-12 02:27:31 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 02:27:33 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [44/200][0/54]	eta 0:01:31 lr 0.000097	time 1.7037 (1.7037)	loss 7.0389 (7.0389)	miou 0.2032	grad_norm 31.8911 (31.8911)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:27:44 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [44/200][10/54]	eta 0:00:52 lr 0.000099	time 0.9142 (1.2044)	loss 9.4469 (6.5932)	miou 0.2156	grad_norm 24.0712 (41.7048)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:27:55 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [44/200][20/54]	eta 0:00:38 lr 0.000100	time 0.9118 (1.1286)	loss 7.7634 (6.4874)	miou 0.2281	grad_norm 34.4873 (39.6720)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:28:05 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [44/200][30/54]	eta 0:00:26 lr 0.000100	time 1.1606 (1.1155)	loss 6.9732 (6.6525)	miou 0.2428	grad_norm 40.3092 (37.5585)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:28:16 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [44/200][40/54]	eta 0:00:15 lr 0.000098	time 0.8292 (1.0894)	loss 7.9521 (6.8400)	miou 0.2330	grad_norm 51.3720 (38.5270)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:28:27 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [44/200][50/54]	eta 0:00:04 lr 0.000096	time 1.3061 (1.1035)	loss 7.0974 (6.8168)	miou 0.2510	grad_norm 20.7273 (36.9900)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:28:31 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 44 training takes 0:01:00
[32m[2025-07-12 02:28:31 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 02:28:41 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1745%
[32m[2025-07-12 02:28:41 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 02:28:42 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [45/200][0/54]	eta 0:01:16 lr 0.000094	time 1.4242 (1.4242)	loss 5.7936 (5.7936)	miou 0.1793	grad_norm 89.7578 (89.7578)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:28:54 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [45/200][10/54]	eta 0:00:53 lr 0.000090	time 1.4153 (1.2212)	loss 5.6672 (6.5172)	miou 0.1832	grad_norm 22.9212 (46.1483)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:29:06 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [45/200][20/54]	eta 0:00:40 lr 0.000085	time 1.2749 (1.1860)	loss 6.9065 (6.9556)	miou 0.2061	grad_norm 28.0303 (37.0947)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:29:17 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [45/200][30/54]	eta 0:00:27 lr 0.000079	time 1.0419 (1.1491)	loss 6.8691 (6.8289)	miou 0.2243	grad_norm 23.6393 (32.1915)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:29:27 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [45/200][40/54]	eta 0:00:15 lr 0.000072	time 1.1448 (1.1274)	loss 5.7407 (6.7082)	miou 0.2314	grad_norm 33.6343 (30.5392)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:29:38 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [45/200][50/54]	eta 0:00:04 lr 0.000065	time 0.8868 (1.1241)	loss 8.8529 (6.9682)	miou 0.2239	grad_norm 15.2199 (30.5141)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:29:42 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 45 training takes 0:01:00
[32m[2025-07-12 02:29:42 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 02:29:52 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2402%
[32m[2025-07-12 02:29:52 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 02:29:53 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [46/200][0/54]	eta 0:01:37 lr 0.000062	time 1.8138 (1.8138)	loss 5.0855 (5.0855)	miou 0.2403	grad_norm 34.6377 (34.6377)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:30:05 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [46/200][10/54]	eta 0:00:53 lr 0.000054	time 1.4680 (1.2141)	loss 5.7888 (6.3451)	miou 0.2446	grad_norm 19.1614 (27.2822)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:30:16 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [46/200][20/54]	eta 0:00:38 lr 0.000046	time 1.1545 (1.1449)	loss 10.5809 (6.4981)	miou 0.2505	grad_norm 11.0524 (25.4262)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:30:27 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [46/200][30/54]	eta 0:00:26 lr 0.000038	time 0.9330 (1.1235)	loss 5.6335 (6.5355)	miou 0.2477	grad_norm 19.5482 (24.8247)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:30:38 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [46/200][40/54]	eta 0:00:15 lr 0.000031	time 0.9939 (1.1177)	loss 5.7716 (6.6021)	miou 0.2452	grad_norm 18.7565 (24.4000)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:30:49 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [46/200][50/54]	eta 0:00:04 lr 0.000024	time 0.9455 (1.1176)	loss 6.6923 (6.6867)	miou 0.2491	grad_norm 46.2907 (24.6619)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:30:52 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 46 training takes 0:01:00
[32m[2025-07-12 02:30:52 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 02:31:02 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2148%
[32m[2025-07-12 02:31:02 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 02:31:03 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [47/200][0/54]	eta 0:01:01 lr 0.000021	time 1.1330 (1.1330)	loss 8.6986 (8.6986)	miou 0.2130	grad_norm 10.3491 (10.3491)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:31:15 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [47/200][10/54]	eta 0:00:49 lr 0.000015	time 1.0997 (1.1228)	loss 7.7021 (6.9915)	miou 0.2528	grad_norm 55.1750 (24.5811)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:31:26 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [47/200][20/54]	eta 0:00:38 lr 0.000010	time 0.9571 (1.1459)	loss 9.7950 (6.8178)	miou 0.2458	grad_norm 15.5587 (23.1454)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:31:37 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [47/200][30/54]	eta 0:00:27 lr 0.000006	time 1.0291 (1.1354)	loss 10.3177 (6.8729)	miou 0.2500	grad_norm 13.1866 (22.6008)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:31:49 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [47/200][40/54]	eta 0:00:15 lr 0.000003	time 1.3848 (1.1300)	loss 5.8572 (6.6730)	miou 0.2577	grad_norm 38.5327 (23.5584)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:31:59 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [47/200][50/54]	eta 0:00:04 lr 0.000001	time 1.1518 (1.1219)	loss 5.3306 (6.6400)	miou 0.2519	grad_norm 8.6143 (24.8638)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:32:02 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 47 training takes 0:01:00
[32m[2025-07-12 02:32:02 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 02:32:13 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2210%
[32m[2025-07-12 02:32:13 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 02:32:14 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [48/200][0/54]	eta 0:01:00 lr 0.000000	time 1.1220 (1.1220)	loss 7.4946 (7.4946)	miou 0.2200	grad_norm 43.8950 (43.8950)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:32:24 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [48/200][10/54]	eta 0:00:46 lr 0.000000	time 0.9153 (1.0618)	loss 5.1776 (6.2484)	miou 0.2496	grad_norm 20.0836 (30.4757)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:32:35 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [48/200][20/54]	eta 0:00:36 lr 0.000001	time 1.0844 (1.0851)	loss 5.8123 (6.6464)	miou 0.2521	grad_norm 21.3768 (26.6428)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:32:47 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [48/200][30/54]	eta 0:00:26 lr 0.000003	time 1.2511 (1.1004)	loss 6.3354 (6.5332)	miou 0.2552	grad_norm 42.6352 (27.3749)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:32:57 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [48/200][40/54]	eta 0:00:15 lr 0.000007	time 0.8715 (1.0924)	loss 4.8048 (6.4029)	miou 0.2637	grad_norm 20.9810 (26.0332)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:33:09 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [48/200][50/54]	eta 0:00:04 lr 0.000011	time 1.2373 (1.1103)	loss 4.0410 (6.3737)	miou 0.2648	grad_norm 18.1429 (27.1744)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:33:13 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 48 training takes 0:01:00
[32m[2025-07-12 02:33:13 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 02:33:23 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1787%
[32m[2025-07-12 02:33:23 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 02:33:24 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [49/200][0/54]	eta 0:01:13 lr 0.000013	time 1.3588 (1.3588)	loss 6.6052 (6.6052)	miou 0.1883	grad_norm 101.9259 (101.9259)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:33:36 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [49/200][10/54]	eta 0:00:51 lr 0.000019	time 1.3863 (1.1768)	loss 5.1981 (6.8884)	miou 0.2284	grad_norm 15.7133 (35.6272)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:33:47 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [49/200][20/54]	eta 0:00:38 lr 0.000025	time 0.9982 (1.1326)	loss 4.6148 (6.3563)	miou 0.2351	grad_norm 29.3251 (29.5682)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:33:58 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [49/200][30/54]	eta 0:00:27 lr 0.000032	time 0.9606 (1.1355)	loss 5.8856 (6.4056)	miou 0.2385	grad_norm 15.4923 (28.1479)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:34:09 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [49/200][40/54]	eta 0:00:15 lr 0.000040	time 1.2714 (1.1230)	loss 5.1764 (6.3957)	miou 0.2467	grad_norm 40.5413 (26.9955)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:34:20 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [49/200][50/54]	eta 0:00:04 lr 0.000048	time 1.1766 (1.1203)	loss 6.0323 (6.3861)	miou 0.2530	grad_norm 15.9505 (27.0070)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:34:24 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 49 training takes 0:01:00
[32m[2025-07-12 02:34:24 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 02:34:34 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2416%
[32m[2025-07-12 02:34:34 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 02:34:35 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [50/200][0/54]	eta 0:01:03 lr 0.000051	time 1.1706 (1.1706)	loss 4.6071 (4.6071)	miou 0.2486	grad_norm 22.6309 (22.6309)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:34:46 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [50/200][10/54]	eta 0:00:49 lr 0.000059	time 1.5174 (1.1178)	loss 8.2065 (6.5377)	miou 0.2452	grad_norm 12.3591 (23.8767)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:34:57 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [50/200][20/54]	eta 0:00:38 lr 0.000066	time 1.1837 (1.1247)	loss 6.6128 (6.4074)	miou 0.2549	grad_norm 21.8411 (27.4646)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:35:09 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [50/200][30/54]	eta 0:00:26 lr 0.000073	time 1.1452 (1.1230)	loss 5.5544 (6.6954)	miou 0.2550	grad_norm 38.7659 (28.0721)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:35:20 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [50/200][40/54]	eta 0:00:15 lr 0.000080	time 1.2023 (1.1230)	loss 6.7880 (6.6241)	miou 0.2502	grad_norm 17.5670 (28.7450)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:35:31 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [50/200][50/54]	eta 0:00:04 lr 0.000086	time 1.0232 (1.1301)	loss 6.7473 (6.6524)	miou 0.2463	grad_norm 21.2760 (28.3084)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:35:35 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 50 training takes 0:01:00
[32m[2025-07-12 02:35:35 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 02:35:45 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2574%
[32m[2025-07-12 02:35:45 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 02:35:46 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [51/200][0/54]	eta 0:01:04 lr 0.000088	time 1.1958 (1.1958)	loss 9.5047 (9.5047)	miou 0.2590	grad_norm 41.1139 (41.1139)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:35:57 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [51/200][10/54]	eta 0:00:47 lr 0.000093	time 1.0091 (1.0710)	loss 11.5090 (7.3636)	miou 0.2522	grad_norm 14.2924 (29.6570)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:36:08 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [51/200][20/54]	eta 0:00:36 lr 0.000096	time 1.1503 (1.0869)	loss 8.2061 (7.1659)	miou 0.2498	grad_norm 9.7477 (30.1518)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:36:19 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [51/200][30/54]	eta 0:00:26 lr 0.000099	time 0.9543 (1.1194)	loss 5.7822 (6.9697)	miou 0.2453	grad_norm 41.8739 (33.8998)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:36:30 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [51/200][40/54]	eta 0:00:15 lr 0.000100	time 1.0129 (1.1042)	loss 7.8708 (6.9126)	miou 0.2392	grad_norm 16.6175 (31.5287)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:36:41 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [51/200][50/54]	eta 0:00:04 lr 0.000100	time 1.0462 (1.1110)	loss 7.4721 (6.9905)	miou 0.2393	grad_norm 53.1510 (29.9697)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:36:45 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 51 training takes 0:01:00
[32m[2025-07-12 02:36:45 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 02:36:55 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2433%
[32m[2025-07-12 02:36:55 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 02:36:56 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [52/200][0/54]	eta 0:01:24 lr 0.000100	time 1.5619 (1.5619)	loss 6.0445 (6.0445)	miou 0.2383	grad_norm 21.0821 (21.0821)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:37:07 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [52/200][10/54]	eta 0:00:50 lr 0.000098	time 0.9577 (1.1468)	loss 6.4920 (6.9692)	miou 0.2326	grad_norm 41.5969 (35.7239)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:37:18 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [52/200][20/54]	eta 0:00:37 lr 0.000095	time 0.8705 (1.0961)	loss 5.5477 (6.6518)	miou 0.2423	grad_norm 24.9922 (33.1689)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:37:29 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [52/200][30/54]	eta 0:00:26 lr 0.000091	time 1.0381 (1.1032)	loss 8.7471 (6.6266)	miou 0.2546	grad_norm 18.1311 (30.8274)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:37:40 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [52/200][40/54]	eta 0:00:15 lr 0.000086	time 0.9895 (1.1072)	loss 6.0784 (6.8053)	miou 0.2473	grad_norm 13.1868 (29.2610)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:37:51 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [52/200][50/54]	eta 0:00:04 lr 0.000080	time 0.9233 (1.1091)	loss 6.8901 (6.9654)	miou 0.2425	grad_norm 31.0805 (28.0726)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:37:55 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 52 training takes 0:01:00
[32m[2025-07-12 02:37:55 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 02:38:05 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2029%
[32m[2025-07-12 02:38:05 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 02:38:06 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [53/200][0/54]	eta 0:01:03 lr 0.000077	time 1.1674 (1.1674)	loss 8.6651 (8.6651)	miou 0.2031	grad_norm 35.5450 (35.5450)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:38:18 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [53/200][10/54]	eta 0:00:52 lr 0.000071	time 1.1848 (1.2001)	loss 7.2904 (6.6085)	miou 0.2248	grad_norm 13.3455 (27.4138)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:38:30 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [53/200][20/54]	eta 0:00:39 lr 0.000063	time 1.1396 (1.1661)	loss 5.4644 (6.6419)	miou 0.2327	grad_norm 84.8680 (32.4088)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:38:40 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [53/200][30/54]	eta 0:00:27 lr 0.000055	time 1.2756 (1.1306)	loss 6.6644 (6.2119)	miou 0.2389	grad_norm 8.0567 (32.9774)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:38:51 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [53/200][40/54]	eta 0:00:15 lr 0.000048	time 1.4771 (1.1222)	loss 6.0926 (6.2782)	miou 0.2481	grad_norm 22.1405 (30.9847)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:39:02 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [53/200][50/54]	eta 0:00:04 lr 0.000040	time 0.8366 (1.1162)	loss 8.0041 (6.4123)	miou 0.2475	grad_norm 16.0848 (29.1498)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:39:05 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 53 training takes 0:01:00
[32m[2025-07-12 02:39:05 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 02:39:15 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2268%
[32m[2025-07-12 02:39:15 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 02:39:16 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [54/200][0/54]	eta 0:01:02 lr 0.000037	time 1.1590 (1.1590)	loss 6.2220 (6.2220)	miou 0.2241	grad_norm 15.8787 (15.8787)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:39:28 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [54/200][10/54]	eta 0:00:50 lr 0.000029	time 0.9756 (1.1568)	loss 7.5969 (6.7921)	miou 0.2352	grad_norm 12.8948 (25.2364)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:39:39 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [54/200][20/54]	eta 0:00:39 lr 0.000023	time 1.4929 (1.1531)	loss 8.5177 (6.7829)	miou 0.2291	grad_norm 16.9038 (21.9345)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:39:51 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [54/200][30/54]	eta 0:00:27 lr 0.000016	time 0.8362 (1.1418)	loss 5.4528 (6.7923)	miou 0.2322	grad_norm 17.0066 (21.1148)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:40:02 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [54/200][40/54]	eta 0:00:16 lr 0.000011	time 1.0629 (1.1515)	loss 5.3099 (6.5056)	miou 0.2441	grad_norm 13.7868 (20.8915)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:40:14 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [54/200][50/54]	eta 0:00:04 lr 0.000007	time 1.0363 (1.1494)	loss 7.0120 (6.5363)	miou 0.2468	grad_norm 17.6848 (20.5829)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:40:17 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 54 training takes 0:01:01
[32m[2025-07-12 02:40:17 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 02:40:27 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2223%
[32m[2025-07-12 02:40:27 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 02:40:28 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [55/200][0/54]	eta 0:01:13 lr 0.000005	time 1.3527 (1.3527)	loss 5.8257 (5.8257)	miou 0.2194	grad_norm 12.9434 (12.9434)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:40:40 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [55/200][10/54]	eta 0:00:49 lr 0.000002	time 1.0377 (1.1339)	loss 7.7317 (7.4280)	miou 0.2502	grad_norm 16.9645 (23.8486)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:40:51 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [55/200][20/54]	eta 0:00:38 lr 0.000000	time 1.0668 (1.1318)	loss 8.9065 (7.0787)	miou 0.2371	grad_norm 40.5860 (22.4835)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:41:02 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [55/200][30/54]	eta 0:00:26 lr 0.000000	time 1.0758 (1.1104)	loss 6.1471 (6.7416)	miou 0.2436	grad_norm 16.7223 (22.1892)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:41:13 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [55/200][40/54]	eta 0:00:15 lr 0.000001	time 1.0756 (1.1249)	loss 4.1555 (6.6956)	miou 0.2424	grad_norm 19.1790 (21.4913)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:41:25 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [55/200][50/54]	eta 0:00:04 lr 0.000003	time 1.1151 (1.1353)	loss 8.3373 (6.7672)	miou 0.2480	grad_norm 11.2744 (21.0726)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:41:28 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 55 training takes 0:01:01
[32m[2025-07-12 02:41:28 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 02:41:39 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2196%
[32m[2025-07-12 02:41:39 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 02:41:40 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [56/200][0/54]	eta 0:01:16 lr 0.000004	time 1.4134 (1.4134)	loss 6.2890 (6.2890)	miou 0.2107	grad_norm 20.4259 (20.4259)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:41:52 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [56/200][10/54]	eta 0:00:51 lr 0.000007	time 1.0513 (1.1803)	loss 7.4683 (7.1980)	miou 0.2231	grad_norm 17.9557 (18.9940)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:42:02 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [56/200][20/54]	eta 0:00:38 lr 0.000012	time 0.9299 (1.1309)	loss 6.3185 (7.1509)	miou 0.2321	grad_norm 40.1724 (19.6742)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:42:14 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [56/200][30/54]	eta 0:00:27 lr 0.000018	time 0.8543 (1.1328)	loss 5.5298 (6.8621)	miou 0.2412	grad_norm 15.2413 (21.9992)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:42:25 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [56/200][40/54]	eta 0:00:15 lr 0.000024	time 1.3859 (1.1261)	loss 5.3500 (6.5964)	miou 0.2457	grad_norm 27.7031 (22.2059)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:42:36 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [56/200][50/54]	eta 0:00:04 lr 0.000031	time 1.0905 (1.1233)	loss 6.2013 (6.5311)	miou 0.2567	grad_norm 20.5505 (21.9317)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:42:39 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 56 training takes 0:01:00
[32m[2025-07-12 02:42:39 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 02:42:49 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1910%
[32m[2025-07-12 02:42:49 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 02:42:51 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [57/200][0/54]	eta 0:01:22 lr 0.000034	time 1.5244 (1.5244)	loss 7.6796 (7.6796)	miou 0.2020	grad_norm 22.8922 (22.8922)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:43:02 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [57/200][10/54]	eta 0:00:51 lr 0.000041	time 1.0592 (1.1814)	loss 8.3223 (6.1293)	miou 0.2391	grad_norm 18.5255 (20.2347)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:43:14 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [57/200][20/54]	eta 0:00:39 lr 0.000049	time 1.0103 (1.1589)	loss 7.2759 (6.4185)	miou 0.2537	grad_norm 19.6252 (19.8481)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:43:25 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [57/200][30/54]	eta 0:00:27 lr 0.000057	time 0.8836 (1.1363)	loss 5.2231 (6.3666)	miou 0.2600	grad_norm 25.0504 (20.2808)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:43:37 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [57/200][40/54]	eta 0:00:16 lr 0.000065	time 1.0494 (1.1556)	loss 5.7690 (6.3320)	miou 0.2645	grad_norm 25.2538 (21.8597)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:43:47 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [57/200][50/54]	eta 0:00:04 lr 0.000072	time 1.3927 (1.1251)	loss 6.1248 (6.4151)	miou 0.2614	grad_norm 19.0851 (21.7012)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:43:50 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 57 training takes 0:01:00
[32m[2025-07-12 02:43:50 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 02:44:00 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2099%
[32m[2025-07-12 02:44:00 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 02:44:02 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [58/200][0/54]	eta 0:01:12 lr 0.000075	time 1.3454 (1.3454)	loss 7.2930 (7.2930)	miou 0.2119	grad_norm 30.3375 (30.3375)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:44:12 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [58/200][10/54]	eta 0:00:48 lr 0.000081	time 0.9132 (1.0960)	loss 10.2286 (6.6383)	miou 0.2384	grad_norm 23.0357 (22.7445)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:44:24 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [58/200][20/54]	eta 0:00:38 lr 0.000087	time 1.0611 (1.1382)	loss 4.8881 (6.5476)	miou 0.2429	grad_norm 12.2841 (21.5628)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:44:36 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [58/200][30/54]	eta 0:00:27 lr 0.000092	time 1.1732 (1.1384)	loss 7.3304 (6.3404)	miou 0.2475	grad_norm 23.3192 (24.9939)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:44:47 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [58/200][40/54]	eta 0:00:15 lr 0.000096	time 1.1331 (1.1391)	loss 6.3931 (6.5479)	miou 0.2482	grad_norm 13.2588 (23.8519)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:44:58 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [58/200][50/54]	eta 0:00:04 lr 0.000098	time 1.1147 (1.1366)	loss 5.6750 (6.5116)	miou 0.2419	grad_norm 17.7361 (28.7431)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:45:02 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 58 training takes 0:01:01
[32m[2025-07-12 02:45:02 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 02:45:12 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2190%
[32m[2025-07-12 02:45:12 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 02:45:13 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [59/200][0/54]	eta 0:01:15 lr 0.000099	time 1.3981 (1.3981)	loss 4.7624 (4.7624)	miou 0.2220	grad_norm 17.7508 (17.7508)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:45:24 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [59/200][10/54]	eta 0:00:48 lr 0.000100	time 1.0095 (1.1069)	loss 8.4107 (7.1920)	miou 0.2414	grad_norm 19.6444 (27.4645)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:45:36 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [59/200][20/54]	eta 0:00:39 lr 0.000100	time 1.5720 (1.1654)	loss 6.8267 (6.9330)	miou 0.2386	grad_norm 21.7169 (32.5149)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:45:47 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [59/200][30/54]	eta 0:00:27 lr 0.000098	time 1.2985 (1.1555)	loss 9.1469 (7.0663)	miou 0.2360	grad_norm 6.1370 (29.5065)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:45:58 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [59/200][40/54]	eta 0:00:15 lr 0.000096	time 1.4060 (1.1276)	loss 5.0958 (6.9050)	miou 0.2433	grad_norm 18.9986 (27.2357)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:46:09 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [59/200][50/54]	eta 0:00:04 lr 0.000092	time 1.1678 (1.1204)	loss 8.1059 (6.9390)	miou 0.2432	grad_norm 12.6954 (26.8497)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:46:12 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 59 training takes 0:01:00
[32m[2025-07-12 02:46:12 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 02:46:22 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1897%
[32m[2025-07-12 02:46:22 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 02:46:24 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [60/200][0/54]	eta 0:01:31 lr 0.000090	time 1.7027 (1.7027)	loss 5.4377 (5.4377)	miou 0.1956	grad_norm 89.9055 (89.9055)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:46:36 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [60/200][10/54]	eta 0:00:55 lr 0.000085	time 1.4144 (1.2601)	loss 9.2939 (6.7684)	miou 0.2126	grad_norm 15.9259 (22.9196)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:46:46 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [60/200][20/54]	eta 0:00:38 lr 0.000079	time 1.0944 (1.1405)	loss 5.0696 (6.5518)	miou 0.2211	grad_norm 24.6063 (21.4839)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:46:58 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [60/200][30/54]	eta 0:00:27 lr 0.000072	time 1.2353 (1.1366)	loss 6.6369 (6.7706)	miou 0.2256	grad_norm 20.1893 (21.0996)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:47:09 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [60/200][40/54]	eta 0:00:15 lr 0.000065	time 0.8704 (1.1385)	loss 6.8054 (6.5909)	miou 0.2332	grad_norm 19.0970 (22.0116)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:47:19 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [60/200][50/54]	eta 0:00:04 lr 0.000057	time 1.4970 (1.1175)	loss 8.6916 (6.8052)	miou 0.2377	grad_norm 13.8570 (21.0831)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:47:23 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 60 training takes 0:01:00
[32m[2025-07-12 02:47:23 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 02:47:33 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1654%
[32m[2025-07-12 02:47:33 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 02:47:35 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [61/200][0/54]	eta 0:01:09 lr 0.000054	time 1.2922 (1.2922)	loss 6.2179 (6.2179)	miou 0.1678	grad_norm 19.3152 (19.3152)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:47:46 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [61/200][10/54]	eta 0:00:49 lr 0.000046	time 1.2425 (1.1291)	loss 6.9774 (6.8614)	miou 0.2031	grad_norm 8.9658 (17.2645)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:47:57 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [61/200][20/54]	eta 0:00:37 lr 0.000038	time 1.0720 (1.1144)	loss 7.4047 (6.5299)	miou 0.2315	grad_norm 95.6971 (22.6474)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:48:09 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [61/200][30/54]	eta 0:00:27 lr 0.000031	time 1.2598 (1.1473)	loss 5.8164 (6.5163)	miou 0.2303	grad_norm 10.4761 (32.2660)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:48:20 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [61/200][40/54]	eta 0:00:16 lr 0.000024	time 1.2426 (1.1430)	loss 6.5745 (6.5630)	miou 0.2412	grad_norm 20.6649 (29.6080)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:48:31 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [61/200][50/54]	eta 0:00:04 lr 0.000018	time 1.1217 (1.1405)	loss 5.4562 (6.4679)	miou 0.2504	grad_norm 9.6560 (27.5079)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:48:34 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 61 training takes 0:01:01
[32m[2025-07-12 02:48:34 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 02:48:44 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2091%
[32m[2025-07-12 02:48:44 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 02:48:46 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [62/200][0/54]	eta 0:01:08 lr 0.000015	time 1.2661 (1.2661)	loss 6.9277 (6.9277)	miou 0.2097	grad_norm 17.8989 (17.8989)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:48:57 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [62/200][10/54]	eta 0:00:49 lr 0.000010	time 1.2800 (1.1142)	loss 6.4139 (7.0671)	miou 0.2390	grad_norm 24.8322 (16.4452)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:49:08 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [62/200][20/54]	eta 0:00:37 lr 0.000006	time 1.0465 (1.1158)	loss 4.5538 (6.5891)	miou 0.2460	grad_norm 21.8184 (19.5073)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:49:19 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [62/200][30/54]	eta 0:00:26 lr 0.000003	time 1.2567 (1.1060)	loss 9.2550 (6.6252)	miou 0.2427	grad_norm 18.4963 (19.8659)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:49:31 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [62/200][40/54]	eta 0:00:15 lr 0.000001	time 1.2886 (1.1248)	loss 6.1156 (6.5882)	miou 0.2511	grad_norm 12.1981 (18.7183)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:49:41 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [62/200][50/54]	eta 0:00:04 lr 0.000000	time 1.0817 (1.1184)	loss 7.7247 (6.5520)	miou 0.2497	grad_norm 17.7898 (18.1800)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:49:45 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 62 training takes 0:01:00
[32m[2025-07-12 02:49:45 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 02:49:55 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2244%
[32m[2025-07-12 02:49:55 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 02:49:56 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [63/200][0/54]	eta 0:01:28 lr 0.000000	time 1.6305 (1.6305)	loss 5.9188 (5.9188)	miou 0.2183	grad_norm 13.2265 (13.2265)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:50:07 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [63/200][10/54]	eta 0:00:50 lr 0.000001	time 1.2792 (1.1477)	loss 7.3699 (6.4206)	miou 0.2340	grad_norm 45.2292 (21.2499)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:50:19 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [63/200][20/54]	eta 0:00:38 lr 0.000003	time 0.9609 (1.1436)	loss 7.4441 (6.7528)	miou 0.2382	grad_norm 13.9738 (23.3707)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:50:30 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [63/200][30/54]	eta 0:00:27 lr 0.000007	time 1.2405 (1.1336)	loss 6.4272 (6.5142)	miou 0.2436	grad_norm 10.9137 (22.1618)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:50:40 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [63/200][40/54]	eta 0:00:15 lr 0.000011	time 1.1829 (1.1104)	loss 5.3409 (6.7067)	miou 0.2412	grad_norm 19.1697 (20.8659)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:50:52 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [63/200][50/54]	eta 0:00:04 lr 0.000016	time 1.4921 (1.1247)	loss 7.9834 (6.5973)	miou 0.2425	grad_norm 16.4287 (20.5151)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:50:55 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 63 training takes 0:01:00
[32m[2025-07-12 02:50:55 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 02:51:05 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2346%
[32m[2025-07-12 02:51:05 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 02:51:07 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [64/200][0/54]	eta 0:01:10 lr 0.000019	time 1.3002 (1.3002)	loss 5.4465 (5.4465)	miou 0.2450	grad_norm 14.1757 (14.1757)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:51:18 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [64/200][10/54]	eta 0:00:50 lr 0.000025	time 1.1053 (1.1405)	loss 7.9971 (7.4235)	miou 0.2468	grad_norm 50.1259 (24.1571)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:51:30 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [64/200][20/54]	eta 0:00:39 lr 0.000032	time 1.5081 (1.1560)	loss 6.4499 (6.8122)	miou 0.2566	grad_norm 24.9062 (22.2677)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:51:41 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [64/200][30/54]	eta 0:00:27 lr 0.000040	time 0.9072 (1.1423)	loss 8.0122 (7.0319)	miou 0.2641	grad_norm 14.1615 (21.5437)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:51:52 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [64/200][40/54]	eta 0:00:15 lr 0.000048	time 1.0616 (1.1302)	loss 7.6606 (6.9694)	miou 0.2740	grad_norm 24.7980 (21.0260)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:52:03 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [64/200][50/54]	eta 0:00:04 lr 0.000055	time 0.7380 (1.1211)	loss 5.2512 (6.8777)	miou 0.2695	grad_norm 12.7394 (20.7771)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:52:06 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 64 training takes 0:01:00
[32m[2025-07-12 02:52:06 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 02:52:16 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2315%
[32m[2025-07-12 02:52:16 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 02:52:17 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [65/200][0/54]	eta 0:00:59 lr 0.000059	time 1.1049 (1.1049)	loss 5.0486 (5.0486)	miou 0.2445	grad_norm 15.0694 (15.0694)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:52:28 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [65/200][10/54]	eta 0:00:49 lr 0.000066	time 1.1332 (1.1169)	loss 8.4105 (6.2214)	miou 0.2451	grad_norm 9.8394 (24.9192)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:52:39 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [65/200][20/54]	eta 0:00:36 lr 0.000073	time 0.8734 (1.0878)	loss 4.1260 (6.1874)	miou 0.2496	grad_norm 22.1237 (22.0554)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:52:50 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [65/200][30/54]	eta 0:00:26 lr 0.000080	time 0.9618 (1.1052)	loss 7.5429 (6.2407)	miou 0.2438	grad_norm 11.4350 (20.4277)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:53:01 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [65/200][40/54]	eta 0:00:15 lr 0.000086	time 0.6272 (1.1026)	loss 6.7207 (6.4193)	miou 0.2466	grad_norm 45.0899 (20.7818)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:53:12 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [65/200][50/54]	eta 0:00:04 lr 0.000091	time 1.1471 (1.1085)	loss 5.3362 (6.5068)	miou 0.2487	grad_norm 15.0270 (20.3711)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:53:16 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 65 training takes 0:01:00
[32m[2025-07-12 02:53:16 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 02:53:26 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2248%
[32m[2025-07-12 02:53:26 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 02:53:27 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [66/200][0/54]	eta 0:01:06 lr 0.000093	time 1.2348 (1.2348)	loss 4.0974 (4.0974)	miou 0.2276	grad_norm 17.8387 (17.8387)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:53:38 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [66/200][10/54]	eta 0:00:48 lr 0.000096	time 1.1603 (1.1064)	loss 5.9866 (6.7139)	miou 0.2321	grad_norm 14.3874 (20.1384)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:53:50 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [66/200][20/54]	eta 0:00:39 lr 0.000099	time 1.3976 (1.1556)	loss 8.4980 (6.3230)	miou 0.2331	grad_norm 33.8125 (21.5954)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:54:02 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [66/200][30/54]	eta 0:00:27 lr 0.000100	time 1.0435 (1.1546)	loss 7.9910 (6.4274)	miou 0.2413	grad_norm 17.8002 (20.5691)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:54:13 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [66/200][40/54]	eta 0:00:16 lr 0.000100	time 0.9995 (1.1444)	loss 8.1302 (6.5638)	miou 0.2443	grad_norm 12.1731 (20.4424)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:54:24 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [66/200][50/54]	eta 0:00:04 lr 0.000099	time 1.2604 (1.1388)	loss 8.8713 (6.6208)	miou 0.2482	grad_norm 36.5987 (20.7060)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:54:27 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 66 training takes 0:01:00
[32m[2025-07-12 02:54:27 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 02:54:37 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1218%
[32m[2025-07-12 02:54:37 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 02:54:38 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [67/200][0/54]	eta 0:00:58 lr 0.000098	time 1.0807 (1.0807)	loss 8.3873 (8.3873)	miou 0.1221	grad_norm 44.2581 (44.2581)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:54:49 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [67/200][10/54]	eta 0:00:47 lr 0.000095	time 1.4467 (1.0854)	loss 4.0885 (6.7197)	miou 0.1876	grad_norm 36.6955 (28.3968)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:55:01 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [67/200][20/54]	eta 0:00:38 lr 0.000091	time 1.2533 (1.1315)	loss 6.7727 (7.1186)	miou 0.2022	grad_norm 24.3418 (24.2255)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:55:12 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [67/200][30/54]	eta 0:00:26 lr 0.000086	time 1.0276 (1.1211)	loss 5.9447 (6.8869)	miou 0.2206	grad_norm 18.6640 (25.3870)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:55:23 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [67/200][40/54]	eta 0:00:15 lr 0.000080	time 0.8718 (1.1202)	loss 5.3516 (6.8602)	miou 0.2232	grad_norm 11.6020 (23.6932)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:55:34 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [67/200][50/54]	eta 0:00:04 lr 0.000073	time 1.3813 (1.1166)	loss 3.4852 (6.8077)	miou 0.2240	grad_norm 46.9268 (23.2498)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:55:37 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 67 training takes 0:01:00
[32m[2025-07-12 02:55:37 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 02:55:47 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2171%
[32m[2025-07-12 02:55:47 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 02:55:49 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [68/200][0/54]	eta 0:01:24 lr 0.000071	time 1.5715 (1.5715)	loss 5.9086 (5.9086)	miou 0.2127	grad_norm 12.6310 (12.6310)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:56:00 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [68/200][10/54]	eta 0:00:51 lr 0.000063	time 1.0195 (1.1749)	loss 5.9807 (6.5901)	miou 0.2266	grad_norm 40.6435 (21.0497)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:56:11 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [68/200][20/54]	eta 0:00:37 lr 0.000055	time 1.2747 (1.1153)	loss 8.6393 (6.6992)	miou 0.2256	grad_norm 12.8290 (19.0801)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:56:23 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [68/200][30/54]	eta 0:00:27 lr 0.000048	time 1.4868 (1.1599)	loss 5.9478 (6.5697)	miou 0.2328	grad_norm 19.2948 (20.5370)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:56:34 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [68/200][40/54]	eta 0:00:15 lr 0.000040	time 0.9565 (1.1360)	loss 5.2892 (6.2641)	miou 0.2411	grad_norm 21.3501 (21.2397)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:56:45 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [68/200][50/54]	eta 0:00:04 lr 0.000032	time 0.9001 (1.1347)	loss 8.8031 (6.3934)	miou 0.2444	grad_norm 15.6767 (20.3840)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:56:49 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 68 training takes 0:01:01
[32m[2025-07-12 02:56:49 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 02:56:59 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2184%
[32m[2025-07-12 02:56:59 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 02:57:00 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [69/200][0/54]	eta 0:01:10 lr 0.000029	time 1.3123 (1.3123)	loss 6.1439 (6.1439)	miou 0.2241	grad_norm 9.7390 (9.7390)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:57:12 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [69/200][10/54]	eta 0:00:52 lr 0.000023	time 1.0991 (1.2011)	loss 7.0827 (6.4927)	miou 0.2467	grad_norm 11.9759 (17.4722)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:57:23 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [69/200][20/54]	eta 0:00:39 lr 0.000016	time 1.5412 (1.1659)	loss 6.5034 (6.4333)	miou 0.2488	grad_norm 21.6619 (17.3381)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:57:35 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [69/200][30/54]	eta 0:00:27 lr 0.000011	time 1.1124 (1.1646)	loss 6.6238 (6.5070)	miou 0.2441	grad_norm 16.6098 (17.0066)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:57:46 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [69/200][40/54]	eta 0:00:16 lr 0.000007	time 1.2458 (1.1497)	loss 6.2173 (6.2780)	miou 0.2429	grad_norm 15.8001 (17.1904)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:57:56 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [69/200][50/54]	eta 0:00:04 lr 0.000003	time 1.0828 (1.1208)	loss 7.7241 (6.3929)	miou 0.2512	grad_norm 21.6200 (17.4921)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:57:59 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 69 training takes 0:01:00
[32m[2025-07-12 02:57:59 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 02:58:09 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1896%
[32m[2025-07-12 02:58:09 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 02:58:11 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [70/200][0/54]	eta 0:01:16 lr 0.000002	time 1.4148 (1.4148)	loss 3.7651 (3.7651)	miou 0.1920	grad_norm 20.5034 (20.5034)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:58:22 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [70/200][10/54]	eta 0:00:50 lr 0.000000	time 0.9636 (1.1525)	loss 8.2858 (6.9915)	miou 0.2130	grad_norm 13.7448 (19.0727)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:58:34 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [70/200][20/54]	eta 0:00:39 lr 0.000000	time 0.8927 (1.1493)	loss 4.7501 (7.0454)	miou 0.2307	grad_norm 22.2600 (17.7695)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:58:44 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [70/200][30/54]	eta 0:00:27 lr 0.000001	time 0.9317 (1.1260)	loss 8.8173 (6.6989)	miou 0.2371	grad_norm 17.1245 (17.4336)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:58:55 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [70/200][40/54]	eta 0:00:15 lr 0.000003	time 0.8978 (1.1025)	loss 7.1114 (6.8440)	miou 0.2433	grad_norm 13.4641 (18.9892)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:59:06 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [70/200][50/54]	eta 0:00:04 lr 0.000006	time 0.9643 (1.1147)	loss 5.0652 (6.5884)	miou 0.2477	grad_norm 23.5582 (18.1038)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:59:10 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 70 training takes 0:01:00
[32m[2025-07-12 02:59:10 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 02:59:20 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2053%
[32m[2025-07-12 02:59:20 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 02:59:21 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [71/200][0/54]	eta 0:01:13 lr 0.000007	time 1.3665 (1.3665)	loss 5.1249 (5.1249)	miou 0.2139	grad_norm 19.0198 (19.0198)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:59:33 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [71/200][10/54]	eta 0:00:50 lr 0.000012	time 1.1469 (1.1548)	loss 5.1611 (5.7588)	miou 0.2326	grad_norm 28.8246 (19.2589)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:59:44 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [71/200][20/54]	eta 0:00:39 lr 0.000018	time 1.1404 (1.1517)	loss 5.2383 (6.3825)	miou 0.2479	grad_norm 12.0084 (21.2250)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 02:59:55 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [71/200][30/54]	eta 0:00:27 lr 0.000024	time 0.9691 (1.1301)	loss 4.9258 (6.5309)	miou 0.2611	grad_norm 21.0781 (20.0093)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 03:00:05 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [71/200][40/54]	eta 0:00:15 lr 0.000031	time 1.1551 (1.1026)	loss 6.6813 (6.4838)	miou 0.2701	grad_norm 13.9954 (18.9501)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 03:00:16 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [71/200][50/54]	eta 0:00:04 lr 0.000038	time 1.2123 (1.0993)	loss 7.0976 (6.4171)	miou 0.2652	grad_norm 15.6348 (18.1251)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 03:00:20 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 71 training takes 0:00:59
[32m[2025-07-12 03:00:20 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 03:00:30 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2265%
[32m[2025-07-12 03:00:30 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 03:00:31 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [72/200][0/54]	eta 0:01:23 lr 0.000041	time 1.5471 (1.5471)	loss 7.0792 (7.0792)	miou 0.2284	grad_norm 13.8175 (13.8175)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 03:00:43 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [72/200][10/54]	eta 0:00:52 lr 0.000049	time 0.9164 (1.2005)	loss 5.9605 (6.0290)	miou 0.2450	grad_norm 16.3452 (18.7012)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 03:00:54 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [72/200][20/54]	eta 0:00:39 lr 0.000057	time 1.2775 (1.1720)	loss 5.6215 (6.4297)	miou 0.2331	grad_norm 18.4440 (18.0948)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 03:01:05 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [72/200][30/54]	eta 0:00:27 lr 0.000065	time 1.0738 (1.1500)	loss 8.5069 (6.6118)	miou 0.2365	grad_norm 12.3283 (17.9779)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 03:01:16 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [72/200][40/54]	eta 0:00:15 lr 0.000072	time 1.0531 (1.1300)	loss 5.0226 (6.3501)	miou 0.2579	grad_norm 33.1026 (17.9594)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 03:01:27 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [72/200][50/54]	eta 0:00:04 lr 0.000079	time 1.0778 (1.1260)	loss 6.3628 (6.2608)	miou 0.2600	grad_norm 14.4917 (20.1873)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 03:01:30 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 72 training takes 0:01:00
[32m[2025-07-12 03:01:30 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 03:01:40 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1550%
[32m[2025-07-12 03:01:40 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 03:01:42 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [73/200][0/54]	eta 0:01:01 lr 0.000081	time 1.1471 (1.1471)	loss 5.7213 (5.7213)	miou 0.1598	grad_norm 20.4857 (20.4857)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 03:01:52 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [73/200][10/54]	eta 0:00:45 lr 0.000087	time 0.9179 (1.0409)	loss 5.7285 (6.4175)	miou 0.2063	grad_norm 15.0362 (28.1225)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 03:02:03 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [73/200][20/54]	eta 0:00:35 lr 0.000092	time 1.0645 (1.0556)	loss 8.4189 (6.2021)	miou 0.2163	grad_norm 29.4604 (25.7094)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 03:02:14 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [73/200][30/54]	eta 0:00:25 lr 0.000096	time 0.9710 (1.0749)	loss 11.3119 (6.6634)	miou 0.2230	grad_norm 14.0955 (23.3247)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 03:02:25 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [73/200][40/54]	eta 0:00:15 lr 0.000098	time 1.5250 (1.0962)	loss 7.9374 (6.4932)	miou 0.2215	grad_norm 19.3737 (21.9773)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 03:02:37 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [73/200][50/54]	eta 0:00:04 lr 0.000100	time 1.0001 (1.1183)	loss 8.0916 (6.5919)	miou 0.2272	grad_norm 16.9772 (21.2087)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 03:02:41 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 73 training takes 0:01:00
[32m[2025-07-12 03:02:41 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 03:02:51 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2134%
[32m[2025-07-12 03:02:51 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 03:02:53 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [74/200][0/54]	eta 0:01:39 lr 0.000100	time 1.8453 (1.8453)	loss 5.7543 (5.7543)	miou 0.2109	grad_norm 25.2376 (25.2376)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 03:03:03 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [74/200][10/54]	eta 0:00:49 lr 0.000100	time 1.1827 (1.1320)	loss 5.3617 (6.5818)	miou 0.2393	grad_norm 12.7220 (20.3358)	loss_scale 262144.0000 (226397.0909)	mem 3265MB
[32m[2025-07-12 03:03:15 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [74/200][20/54]	eta 0:00:38 lr 0.000098	time 1.0931 (1.1383)	loss 6.8661 (6.6760)	miou 0.2540	grad_norm 8.8868 (17.1261)	loss_scale 262144.0000 (243419.4286)	mem 3265MB
[32m[2025-07-12 03:03:26 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [74/200][30/54]	eta 0:00:26 lr 0.000096	time 1.2777 (1.1145)	loss 7.8113 (6.3834)	miou 0.2633	grad_norm 160.1976 (21.4439)	loss_scale 262144.0000 (249459.6129)	mem 3265MB
[32m[2025-07-12 03:03:37 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [74/200][40/54]	eta 0:00:15 lr 0.000092	time 1.2773 (1.1259)	loss 7.0370 (6.4701)	miou 0.2718	grad_norm 17.3999 (20.6570)	loss_scale 262144.0000 (252553.3659)	mem 3265MB
[32m[2025-07-12 03:03:48 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [74/200][50/54]	eta 0:00:04 lr 0.000087	time 0.9208 (1.1163)	loss 7.6736 (6.5024)	miou 0.2638	grad_norm 23.6292 (20.0776)	loss_scale 262144.0000 (254433.8824)	mem 3265MB
[32m[2025-07-12 03:03:52 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 74 training takes 0:01:00
[32m[2025-07-12 03:03:52 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 03:04:02 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1881%
[32m[2025-07-12 03:04:02 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 03:04:03 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [75/200][0/54]	eta 0:01:21 lr 0.000085	time 1.5075 (1.5075)	loss 7.1435 (7.1435)	miou 0.1925	grad_norm 13.7485 (13.7485)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:04:14 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [75/200][10/54]	eta 0:00:50 lr 0.000079	time 0.9097 (1.1557)	loss 3.6271 (6.6336)	miou 0.2196	grad_norm 27.6589 (18.2043)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:04:25 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [75/200][20/54]	eta 0:00:38 lr 0.000072	time 1.0070 (1.1245)	loss 8.8323 (6.7590)	miou 0.2259	grad_norm 28.0075 (18.6977)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:04:37 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [75/200][30/54]	eta 0:00:27 lr 0.000065	time 1.0715 (1.1262)	loss 8.4329 (6.4693)	miou 0.2267	grad_norm 20.9317 (19.1160)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:04:48 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [75/200][40/54]	eta 0:00:15 lr 0.000057	time 1.2295 (1.1205)	loss 7.9409 (6.4149)	miou 0.2477	grad_norm 16.1421 (19.5551)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:04:58 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [75/200][50/54]	eta 0:00:04 lr 0.000049	time 1.4017 (1.1147)	loss 5.2074 (6.3964)	miou 0.2499	grad_norm 12.5326 (20.5317)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:05:02 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 75 training takes 0:01:00
[32m[2025-07-12 03:05:02 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 03:05:13 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1908%
[32m[2025-07-12 03:05:13 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 03:05:14 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [76/200][0/54]	eta 0:01:10 lr 0.000046	time 1.3090 (1.3090)	loss 5.6808 (5.6808)	miou 0.1928	grad_norm 47.5936 (47.5936)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:05:26 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [76/200][10/54]	eta 0:00:52 lr 0.000038	time 1.0544 (1.1838)	loss 5.7827 (5.9988)	miou 0.2345	grad_norm 14.3168 (20.3510)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:05:37 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [76/200][20/54]	eta 0:00:39 lr 0.000031	time 1.1036 (1.1593)	loss 6.2178 (6.1551)	miou 0.2350	grad_norm 10.7249 (17.7578)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:05:48 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [76/200][30/54]	eta 0:00:27 lr 0.000024	time 1.1583 (1.1297)	loss 8.2754 (6.2177)	miou 0.2443	grad_norm 31.9318 (18.1611)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:05:59 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [76/200][40/54]	eta 0:00:15 lr 0.000018	time 1.2400 (1.1341)	loss 6.1764 (6.1611)	miou 0.2532	grad_norm 14.6410 (18.1068)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:06:10 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [76/200][50/54]	eta 0:00:04 lr 0.000012	time 1.0801 (1.1247)	loss 6.6324 (6.1697)	miou 0.2757	grad_norm 16.4798 (17.1914)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:06:14 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 76 training takes 0:01:01
[32m[2025-07-12 03:06:14 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 03:06:24 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1763%
[32m[2025-07-12 03:06:24 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 03:06:25 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [77/200][0/54]	eta 0:01:11 lr 0.000010	time 1.3258 (1.3258)	loss 5.0352 (5.0352)	miou 0.1960	grad_norm 13.2533 (13.2533)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:06:36 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [77/200][10/54]	eta 0:00:50 lr 0.000006	time 0.9637 (1.1502)	loss 4.9559 (6.0937)	miou 0.2160	grad_norm 13.3433 (17.0579)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:06:48 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [77/200][20/54]	eta 0:00:39 lr 0.000003	time 0.8119 (1.1556)	loss 4.4175 (5.8908)	miou 0.2413	grad_norm 19.6316 (15.8780)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:06:58 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [77/200][30/54]	eta 0:00:26 lr 0.000001	time 1.0396 (1.1159)	loss 6.6553 (6.1384)	miou 0.2474	grad_norm 16.6804 (15.2440)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:07:10 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [77/200][40/54]	eta 0:00:15 lr 0.000000	time 1.2220 (1.1332)	loss 7.3732 (6.0761)	miou 0.2546	grad_norm 34.2677 (16.5186)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:07:22 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [77/200][50/54]	eta 0:00:04 lr 0.000000	time 0.9576 (1.1350)	loss 7.7243 (6.2561)	miou 0.2525	grad_norm 12.9849 (15.9903)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:07:25 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 77 training takes 0:01:00
[32m[2025-07-12 03:07:25 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 03:07:35 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1864%
[32m[2025-07-12 03:07:35 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 03:07:36 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [78/200][0/54]	eta 0:01:20 lr 0.000001	time 1.4967 (1.4967)	loss 5.4747 (5.4747)	miou 0.2014	grad_norm 9.1893 (9.1893)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:07:47 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [78/200][10/54]	eta 0:00:47 lr 0.000003	time 0.9536 (1.0816)	loss 5.4426 (5.8886)	miou 0.2303	grad_norm 29.3955 (17.8356)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:07:58 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [78/200][20/54]	eta 0:00:38 lr 0.000007	time 1.0733 (1.1214)	loss 7.0601 (6.4220)	miou 0.2440	grad_norm 9.5997 (15.9546)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:08:10 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [78/200][30/54]	eta 0:00:27 lr 0.000011	time 1.0419 (1.1307)	loss 7.8015 (6.3407)	miou 0.2514	grad_norm 8.7917 (15.3493)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:08:21 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [78/200][40/54]	eta 0:00:15 lr 0.000016	time 0.9368 (1.1300)	loss 5.6733 (6.3767)	miou 0.2680	grad_norm 11.1057 (15.5863)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:08:32 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [78/200][50/54]	eta 0:00:04 lr 0.000023	time 0.8004 (1.1232)	loss 6.7281 (6.2639)	miou 0.2739	grad_norm 13.2963 (17.5842)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:08:36 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 78 training takes 0:01:01
[32m[2025-07-12 03:08:36 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 03:08:46 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2143%
[32m[2025-07-12 03:08:46 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 03:08:47 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [79/200][0/54]	eta 0:01:11 lr 0.000025	time 1.3311 (1.3311)	loss 6.2540 (6.2540)	miou 0.2290	grad_norm 10.8707 (10.8707)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:08:59 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [79/200][10/54]	eta 0:00:51 lr 0.000032	time 1.2420 (1.1595)	loss 6.8425 (6.5530)	miou 0.2576	grad_norm 30.8655 (16.5155)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:09:10 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [79/200][20/54]	eta 0:00:38 lr 0.000040	time 1.4863 (1.1360)	loss 4.9967 (6.5072)	miou 0.2684	grad_norm 9.2988 (16.2439)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:09:21 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [79/200][30/54]	eta 0:00:26 lr 0.000048	time 1.1175 (1.1226)	loss 6.3442 (6.4955)	miou 0.2728	grad_norm 12.7919 (17.2469)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:09:33 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [79/200][40/54]	eta 0:00:15 lr 0.000055	time 1.4534 (1.1424)	loss 4.1246 (6.4014)	miou 0.2799	grad_norm 18.8944 (16.7596)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:09:43 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [79/200][50/54]	eta 0:00:04 lr 0.000063	time 1.0789 (1.1253)	loss 5.2282 (6.4786)	miou 0.2731	grad_norm 10.5296 (17.8751)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:09:47 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 79 training takes 0:01:00
[32m[2025-07-12 03:09:47 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 03:09:57 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2443%
[32m[2025-07-12 03:09:57 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 03:09:58 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [80/200][0/54]	eta 0:00:59 lr 0.000066	time 1.1033 (1.1033)	loss 5.2887 (5.2887)	miou 0.2587	grad_norm 12.4835 (12.4835)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:10:09 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [80/200][10/54]	eta 0:00:48 lr 0.000073	time 1.0520 (1.0982)	loss 5.2946 (5.9380)	miou 0.2767	grad_norm 12.3295 (19.4732)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:10:21 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [80/200][20/54]	eta 0:00:39 lr 0.000080	time 1.3029 (1.1533)	loss 5.9928 (5.7402)	miou 0.2911	grad_norm 16.4896 (20.4946)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:10:31 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [80/200][30/54]	eta 0:00:26 lr 0.000086	time 0.9491 (1.1103)	loss 5.5681 (5.7774)	miou 0.2871	grad_norm 14.1067 (20.4469)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:10:42 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [80/200][40/54]	eta 0:00:15 lr 0.000091	time 1.0770 (1.1113)	loss 4.1361 (5.8576)	miou 0.2872	grad_norm 21.8623 (18.8538)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:10:54 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [80/200][50/54]	eta 0:00:04 lr 0.000095	time 1.1658 (1.1207)	loss 7.7710 (6.0255)	miou 0.2831	grad_norm 21.9093 (18.6020)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:10:57 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 80 training takes 0:01:00
[32m[2025-07-12 03:10:57 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 03:11:07 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1081%
[32m[2025-07-12 03:11:07 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 03:11:09 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [81/200][0/54]	eta 0:01:13 lr 0.000096	time 1.3652 (1.3652)	loss 6.9667 (6.9667)	miou 0.1204	grad_norm 11.8619 (11.8619)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:11:20 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [81/200][10/54]	eta 0:00:50 lr 0.000099	time 1.2263 (1.1399)	loss 3.5505 (6.3610)	miou 0.1837	grad_norm 28.4044 (16.8454)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:11:31 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [81/200][20/54]	eta 0:00:37 lr 0.000100	time 0.9156 (1.1164)	loss 8.0748 (6.2772)	miou 0.1986	grad_norm 17.1974 (18.6131)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:11:43 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [81/200][30/54]	eta 0:00:27 lr 0.000100	time 1.0120 (1.1456)	loss 6.8771 (6.4545)	miou 0.2046	grad_norm 11.1751 (20.1379)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:11:54 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [81/200][40/54]	eta 0:00:15 lr 0.000099	time 1.2439 (1.1375)	loss 6.2871 (6.2589)	miou 0.2248	grad_norm 12.8602 (19.7861)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:12:04 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [81/200][50/54]	eta 0:00:04 lr 0.000096	time 1.1537 (1.1201)	loss 7.5219 (6.2982)	miou 0.2311	grad_norm 14.9568 (19.9132)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:12:07 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 81 training takes 0:01:00
[32m[2025-07-12 03:12:08 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 03:12:18 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.0812%
[32m[2025-07-12 03:12:18 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 03:12:19 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [82/200][0/54]	eta 0:01:11 lr 0.000095	time 1.3221 (1.3221)	loss 6.3787 (6.3787)	miou 0.0850	grad_norm 13.2064 (13.2064)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:12:30 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [82/200][10/54]	eta 0:00:50 lr 0.000091	time 1.3285 (1.1382)	loss 5.9965 (7.2785)	miou 0.1427	grad_norm 19.0213 (16.9827)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:12:42 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [82/200][20/54]	eta 0:00:39 lr 0.000086	time 1.0483 (1.1704)	loss 6.6820 (6.5307)	miou 0.1777	grad_norm 20.4051 (20.4471)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:12:53 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [82/200][30/54]	eta 0:00:27 lr 0.000080	time 1.0574 (1.1295)	loss 8.6461 (6.5877)	miou 0.1980	grad_norm 15.6299 (19.5172)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:13:03 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [82/200][40/54]	eta 0:00:15 lr 0.000073	time 1.1690 (1.1195)	loss 8.1177 (6.7397)	miou 0.2171	grad_norm 20.0808 (19.0275)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:13:15 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [82/200][50/54]	eta 0:00:04 lr 0.000066	time 1.1342 (1.1234)	loss 8.2449 (6.6904)	miou 0.2282	grad_norm 10.0330 (20.9038)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:13:18 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 82 training takes 0:01:00
[32m[2025-07-12 03:13:18 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 03:13:28 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2221%
[32m[2025-07-12 03:13:28 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 03:13:29 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [83/200][0/54]	eta 0:00:47 lr 0.000063	time 0.8793 (0.8793)	loss 7.8155 (7.8155)	miou 0.2172	grad_norm 21.2154 (21.2154)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:13:40 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [83/200][10/54]	eta 0:00:47 lr 0.000055	time 0.9874 (1.0796)	loss 7.7448 (6.8737)	miou 0.2413	grad_norm 19.9066 (16.6589)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:13:51 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [83/200][20/54]	eta 0:00:36 lr 0.000048	time 1.2805 (1.0795)	loss 4.9704 (6.5741)	miou 0.2406	grad_norm 46.1003 (18.0149)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:14:03 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [83/200][30/54]	eta 0:00:26 lr 0.000040	time 1.1009 (1.1162)	loss 8.0580 (6.5915)	miou 0.2388	grad_norm 11.1795 (17.6795)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:14:15 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [83/200][40/54]	eta 0:00:15 lr 0.000032	time 1.0077 (1.1339)	loss 7.7545 (6.6103)	miou 0.2390	grad_norm 10.0420 (17.7161)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:14:25 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [83/200][50/54]	eta 0:00:04 lr 0.000025	time 1.0663 (1.1154)	loss 6.1703 (6.4977)	miou 0.2419	grad_norm 25.5600 (17.9384)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:14:29 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 83 training takes 0:01:00
[32m[2025-07-12 03:14:29 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 03:14:39 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.0978%
[32m[2025-07-12 03:14:39 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 03:14:40 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [84/200][0/54]	eta 0:01:29 lr 0.000023	time 1.6635 (1.6635)	loss 8.6272 (8.6272)	miou 0.1073	grad_norm 20.4147 (20.4147)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:14:51 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [84/200][10/54]	eta 0:00:48 lr 0.000016	time 1.0427 (1.0933)	loss 4.4901 (6.5393)	miou 0.1772	grad_norm 13.1938 (17.2320)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:15:02 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [84/200][20/54]	eta 0:00:37 lr 0.000011	time 1.1033 (1.0965)	loss 4.9892 (6.2350)	miou 0.2185	grad_norm 7.4950 (18.8840)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:15:13 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [84/200][30/54]	eta 0:00:26 lr 0.000007	time 1.1347 (1.1168)	loss 7.0029 (6.4227)	miou 0.2360	grad_norm 9.5799 (17.1923)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:15:25 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [84/200][40/54]	eta 0:00:15 lr 0.000003	time 1.1759 (1.1237)	loss 6.4736 (6.4178)	miou 0.2414	grad_norm 11.5535 (16.7891)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:15:36 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [84/200][50/54]	eta 0:00:04 lr 0.000001	time 1.3781 (1.1318)	loss 3.8865 (6.3882)	miou 0.2531	grad_norm 32.1445 (16.8026)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:15:40 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 84 training takes 0:01:01
[32m[2025-07-12 03:15:40 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 03:15:50 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1500%
[32m[2025-07-12 03:15:50 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 03:15:52 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [85/200][0/54]	eta 0:01:37 lr 0.000000	time 1.8139 (1.8139)	loss 3.9600 (3.9600)	miou 0.1505	grad_norm 32.1599 (32.1599)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:16:02 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [85/200][10/54]	eta 0:00:50 lr 0.000000	time 1.1024 (1.1439)	loss 4.0006 (6.5037)	miou 0.2047	grad_norm 31.1377 (20.1263)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:16:12 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [85/200][20/54]	eta 0:00:36 lr 0.000001	time 1.1014 (1.0730)	loss 7.8207 (6.4460)	miou 0.2267	grad_norm 9.2457 (17.9286)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:16:24 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [85/200][30/54]	eta 0:00:26 lr 0.000003	time 1.5504 (1.0856)	loss 6.3189 (6.4347)	miou 0.2319	grad_norm 19.9623 (17.4273)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:16:36 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [85/200][40/54]	eta 0:00:15 lr 0.000006	time 1.1637 (1.1142)	loss 4.9796 (6.4732)	miou 0.2384	grad_norm 27.1989 (17.0033)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:16:47 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [85/200][50/54]	eta 0:00:04 lr 0.000010	time 0.9718 (1.1191)	loss 6.1695 (6.4996)	miou 0.2456	grad_norm 12.1146 (16.2220)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:16:51 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 85 training takes 0:01:00
[32m[2025-07-12 03:16:51 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 03:17:01 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1130%
[32m[2025-07-12 03:17:01 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 03:17:02 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [86/200][0/54]	eta 0:01:12 lr 0.000012	time 1.3411 (1.3411)	loss 5.4718 (5.4718)	miou 0.1370	grad_norm 11.3312 (11.3312)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:17:14 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [86/200][10/54]	eta 0:00:52 lr 0.000018	time 0.9138 (1.2035)	loss 5.5071 (6.5507)	miou 0.1921	grad_norm 18.7481 (16.1581)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:17:25 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [86/200][20/54]	eta 0:00:38 lr 0.000024	time 1.3844 (1.1416)	loss 4.4584 (6.0393)	miou 0.2153	grad_norm 17.9664 (18.9513)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:17:35 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [86/200][30/54]	eta 0:00:26 lr 0.000031	time 1.0374 (1.1191)	loss 7.6254 (6.2199)	miou 0.2397	grad_norm 10.9444 (16.9741)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:17:47 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [86/200][40/54]	eta 0:00:15 lr 0.000038	time 1.0167 (1.1259)	loss 5.6081 (6.4722)	miou 0.2413	grad_norm 17.9057 (17.8645)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:17:58 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [86/200][50/54]	eta 0:00:04 lr 0.000046	time 0.9008 (1.1147)	loss 5.5341 (6.3040)	miou 0.2466	grad_norm 14.4629 (17.8843)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:18:01 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 86 training takes 0:01:00
[32m[2025-07-12 03:18:01 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 03:18:11 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2097%
[32m[2025-07-12 03:18:11 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 03:18:12 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [87/200][0/54]	eta 0:01:07 lr 0.000049	time 1.2541 (1.2541)	loss 6.7784 (6.7784)	miou 0.2112	grad_norm 18.4367 (18.4367)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:18:24 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [87/200][10/54]	eta 0:00:51 lr 0.000057	time 1.1603 (1.1680)	loss 6.9438 (6.2706)	miou 0.2545	grad_norm 24.1411 (17.1540)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:18:35 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [87/200][20/54]	eta 0:00:39 lr 0.000065	time 1.1661 (1.1637)	loss 6.6973 (6.4619)	miou 0.2614	grad_norm 8.8456 (17.4931)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:18:47 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [87/200][30/54]	eta 0:00:27 lr 0.000072	time 0.9810 (1.1651)	loss 10.9722 (6.6317)	miou 0.2630	grad_norm 11.3642 (17.7617)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:18:59 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [87/200][40/54]	eta 0:00:16 lr 0.000079	time 1.3951 (1.1629)	loss 6.2651 (6.5391)	miou 0.2729	grad_norm 11.0152 (17.6491)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:19:09 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [87/200][50/54]	eta 0:00:04 lr 0.000085	time 1.0294 (1.1447)	loss 6.4953 (6.3847)	miou 0.2743	grad_norm 10.7219 (17.0021)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:19:13 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 87 training takes 0:01:01
[32m[2025-07-12 03:19:13 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 03:19:23 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1120%
[32m[2025-07-12 03:19:23 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 03:19:24 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [88/200][0/54]	eta 0:00:50 lr 0.000087	time 0.9357 (0.9357)	loss 4.9438 (4.9438)	miou 0.1306	grad_norm 11.4656 (11.4656)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:19:36 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [88/200][10/54]	eta 0:00:51 lr 0.000092	time 1.3791 (1.1684)	loss 6.8629 (6.6182)	miou 0.1980	grad_norm 10.7083 (17.7514)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:19:47 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [88/200][20/54]	eta 0:00:38 lr 0.000096	time 1.0073 (1.1260)	loss 5.4393 (6.7601)	miou 0.2171	grad_norm 9.0234 (19.3296)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:19:57 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [88/200][30/54]	eta 0:00:26 lr 0.000098	time 1.1717 (1.1093)	loss 5.3624 (6.6983)	miou 0.2159	grad_norm 25.5826 (18.9650)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:20:08 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [88/200][40/54]	eta 0:00:15 lr 0.000100	time 0.9223 (1.0900)	loss 7.7212 (6.6570)	miou 0.2223	grad_norm 22.8209 (19.1571)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:20:19 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [88/200][50/54]	eta 0:00:04 lr 0.000100	time 1.1479 (1.0974)	loss 5.3982 (6.5588)	miou 0.2287	grad_norm 8.7581 (20.0463)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:20:22 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 88 training takes 0:00:59
[32m[2025-07-12 03:20:22 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 03:20:32 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1467%
[32m[2025-07-12 03:20:32 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 03:20:34 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [89/200][0/54]	eta 0:01:18 lr 0.000100	time 1.4477 (1.4477)	loss 6.8438 (6.8438)	miou 0.1635	grad_norm 13.2394 (13.2394)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:20:45 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [89/200][10/54]	eta 0:00:51 lr 0.000098	time 1.1670 (1.1737)	loss 5.1216 (6.4285)	miou 0.1980	grad_norm 11.8541 (17.5237)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:20:56 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [89/200][20/54]	eta 0:00:38 lr 0.000096	time 1.1052 (1.1319)	loss 4.4062 (6.4168)	miou 0.2360	grad_norm 24.4717 (17.7316)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:21:07 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [89/200][30/54]	eta 0:00:26 lr 0.000092	time 1.2667 (1.1056)	loss 5.9638 (6.6903)	miou 0.2393	grad_norm 12.5333 (18.3819)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:21:19 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [89/200][40/54]	eta 0:00:15 lr 0.000087	time 1.0040 (1.1414)	loss 6.3828 (6.7420)	miou 0.2363	grad_norm 14.0318 (19.4134)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:21:30 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [89/200][50/54]	eta 0:00:04 lr 0.000081	time 1.1586 (1.1305)	loss 5.5184 (6.7066)	miou 0.2416	grad_norm 15.2113 (18.1579)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:21:33 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 89 training takes 0:01:00
[32m[2025-07-12 03:21:33 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 03:21:43 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1190%
[32m[2025-07-12 03:21:43 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 03:21:45 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [90/200][0/54]	eta 0:01:22 lr 0.000079	time 1.5298 (1.5298)	loss 7.5708 (7.5708)	miou 0.1244	grad_norm 18.0153 (18.0153)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:21:56 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [90/200][10/54]	eta 0:00:50 lr 0.000072	time 1.5362 (1.1570)	loss 6.0877 (6.3465)	miou 0.1652	grad_norm 10.4132 (18.1847)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:22:07 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [90/200][20/54]	eta 0:00:38 lr 0.000065	time 1.1521 (1.1307)	loss 5.5284 (6.7663)	miou 0.1881	grad_norm 15.4587 (15.7265)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:22:18 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [90/200][30/54]	eta 0:00:27 lr 0.000057	time 1.0437 (1.1286)	loss 7.4518 (6.5509)	miou 0.2155	grad_norm 14.0641 (15.4400)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:22:29 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [90/200][40/54]	eta 0:00:15 lr 0.000049	time 1.0313 (1.1253)	loss 5.8734 (6.5192)	miou 0.2225	grad_norm 14.9094 (16.5327)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:22:41 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [90/200][50/54]	eta 0:00:04 lr 0.000041	time 1.4633 (1.1276)	loss 5.9360 (6.5946)	miou 0.2287	grad_norm 14.3872 (16.4610)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:22:44 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 90 training takes 0:01:01
[32m[2025-07-12 03:22:44 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 03:22:54 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1185%
[32m[2025-07-12 03:22:54 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 03:22:56 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [91/200][0/54]	eta 0:01:16 lr 0.000038	time 1.4089 (1.4089)	loss 7.0150 (7.0150)	miou 0.1192	grad_norm 21.3133 (21.3133)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:23:07 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [91/200][10/54]	eta 0:00:50 lr 0.000031	time 0.9689 (1.1453)	loss 4.2227 (7.1229)	miou 0.1799	grad_norm 9.4464 (13.1361)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:23:18 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [91/200][20/54]	eta 0:00:38 lr 0.000024	time 1.1459 (1.1431)	loss 5.1563 (6.6744)	miou 0.2067	grad_norm 16.6992 (14.2793)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:23:29 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [91/200][30/54]	eta 0:00:26 lr 0.000018	time 1.0594 (1.1220)	loss 8.7112 (6.6511)	miou 0.2203	grad_norm 11.1949 (14.4646)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:23:40 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [91/200][40/54]	eta 0:00:15 lr 0.000012	time 0.9489 (1.1024)	loss 6.3653 (6.5107)	miou 0.2320	grad_norm 12.6182 (15.0897)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:23:51 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [91/200][50/54]	eta 0:00:04 lr 0.000007	time 1.1039 (1.1171)	loss 5.3934 (6.4087)	miou 0.2443	grad_norm 47.8315 (15.6322)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:23:55 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 91 training takes 0:01:00
[32m[2025-07-12 03:23:55 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 03:24:05 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1557%
[32m[2025-07-12 03:24:05 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 03:24:07 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [92/200][0/54]	eta 0:01:12 lr 0.000006	time 1.3508 (1.3508)	loss 8.1450 (8.1450)	miou 0.1577	grad_norm 14.2184 (14.2184)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:24:18 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [92/200][10/54]	eta 0:00:50 lr 0.000003	time 0.9561 (1.1436)	loss 5.8831 (6.6919)	miou 0.2281	grad_norm 10.4760 (12.3251)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:24:29 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [92/200][20/54]	eta 0:00:39 lr 0.000001	time 1.0160 (1.1514)	loss 5.0665 (6.1412)	miou 0.2591	grad_norm 12.0942 (15.1822)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:24:40 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [92/200][30/54]	eta 0:00:26 lr 0.000000	time 0.9655 (1.1239)	loss 7.6529 (6.1265)	miou 0.2622	grad_norm 8.9270 (14.4766)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:24:51 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [92/200][40/54]	eta 0:00:15 lr 0.000000	time 1.4894 (1.1109)	loss 5.3484 (6.1717)	miou 0.2697	grad_norm 13.2710 (15.4212)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:25:02 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [92/200][50/54]	eta 0:00:04 lr 0.000002	time 0.9177 (1.1176)	loss 4.6590 (6.2569)	miou 0.2737	grad_norm 20.1499 (15.2510)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:25:06 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 92 training takes 0:01:00
[32m[2025-07-12 03:25:06 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 03:25:16 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1334%
[32m[2025-07-12 03:25:16 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 03:25:17 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [93/200][0/54]	eta 0:01:10 lr 0.000003	time 1.3029 (1.3029)	loss 10.4330 (10.4330)	miou 0.1336	grad_norm 13.7511 (13.7511)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:25:28 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [93/200][10/54]	eta 0:00:48 lr 0.000007	time 1.0136 (1.0925)	loss 6.5477 (7.5528)	miou 0.1897	grad_norm 8.5678 (14.3465)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:25:38 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [93/200][20/54]	eta 0:00:35 lr 0.000011	time 1.1247 (1.0558)	loss 7.3452 (7.1158)	miou 0.2198	grad_norm 12.8503 (15.4103)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:25:49 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [93/200][30/54]	eta 0:00:25 lr 0.000016	time 1.4974 (1.0764)	loss 7.3658 (7.0297)	miou 0.2303	grad_norm 7.8838 (14.2352)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:26:02 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [93/200][40/54]	eta 0:00:15 lr 0.000023	time 1.3230 (1.1126)	loss 5.8964 (6.7694)	miou 0.2413	grad_norm 14.8357 (15.9021)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:26:13 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [93/200][50/54]	eta 0:00:04 lr 0.000029	time 1.2404 (1.1190)	loss 5.1812 (6.5601)	miou 0.2422	grad_norm 13.4681 (15.7833)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:26:17 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 93 training takes 0:01:00
[32m[2025-07-12 03:26:17 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 03:26:27 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1717%
[32m[2025-07-12 03:26:27 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 03:26:28 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [94/200][0/54]	eta 0:01:03 lr 0.000032	time 1.1697 (1.1697)	loss 7.6305 (7.6305)	miou 0.1731	grad_norm 15.1766 (15.1766)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:26:39 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [94/200][10/54]	eta 0:00:47 lr 0.000040	time 1.2079 (1.0824)	loss 6.7847 (6.1114)	miou 0.2081	grad_norm 16.0323 (18.2810)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:26:50 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [94/200][20/54]	eta 0:00:37 lr 0.000048	time 1.1521 (1.0973)	loss 6.2094 (5.8540)	miou 0.2272	grad_norm 9.6690 (17.3175)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:27:02 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [94/200][30/54]	eta 0:00:27 lr 0.000055	time 1.1095 (1.1354)	loss 8.3617 (5.9408)	miou 0.2333	grad_norm 12.2940 (16.3355)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:27:13 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [94/200][40/54]	eta 0:00:15 lr 0.000063	time 0.9594 (1.1394)	loss 5.5539 (5.9280)	miou 0.2523	grad_norm 10.3849 (15.2716)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:27:25 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [94/200][50/54]	eta 0:00:04 lr 0.000071	time 1.2635 (1.1344)	loss 6.7065 (5.9733)	miou 0.2573	grad_norm 16.4356 (15.2286)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:27:28 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 94 training takes 0:01:01
[32m[2025-07-12 03:27:28 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 03:27:38 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2105%
[32m[2025-07-12 03:27:38 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 03:27:39 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [95/200][0/54]	eta 0:00:45 lr 0.000073	time 0.8356 (0.8356)	loss 5.7946 (5.7946)	miou 0.2204	grad_norm 10.9714 (10.9714)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:27:50 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [95/200][10/54]	eta 0:00:48 lr 0.000080	time 0.9727 (1.0951)	loss 8.4278 (6.5906)	miou 0.2457	grad_norm 24.8665 (24.4181)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:28:01 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [95/200][20/54]	eta 0:00:37 lr 0.000086	time 1.0956 (1.0910)	loss 5.0028 (6.2598)	miou 0.2585	grad_norm 13.5359 (19.6327)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:28:12 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [95/200][30/54]	eta 0:00:26 lr 0.000091	time 1.2782 (1.0931)	loss 6.4666 (5.9676)	miou 0.2689	grad_norm 10.4809 (18.1017)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:28:23 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [95/200][40/54]	eta 0:00:15 lr 0.000095	time 0.9542 (1.0883)	loss 6.8333 (6.2715)	miou 0.2620	grad_norm 10.7665 (17.7996)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:28:34 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [95/200][50/54]	eta 0:00:04 lr 0.000098	time 1.1158 (1.0944)	loss 6.1926 (6.4572)	miou 0.2627	grad_norm 11.0660 (17.5309)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:28:37 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 95 training takes 0:00:58
[32m[2025-07-12 03:28:37 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 03:28:47 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.0670%
[32m[2025-07-12 03:28:47 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 03:28:49 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [96/200][0/54]	eta 0:01:25 lr 0.000099	time 1.5863 (1.5863)	loss 6.4227 (6.4227)	miou 0.0690	grad_norm 19.5948 (19.5948)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:28:59 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [96/200][10/54]	eta 0:00:48 lr 0.000100	time 1.0075 (1.0949)	loss 6.1780 (6.6089)	miou 0.1578	grad_norm 16.1779 (24.1322)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:29:11 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [96/200][20/54]	eta 0:00:38 lr 0.000100	time 1.0371 (1.1219)	loss 5.8276 (6.6649)	miou 0.1971	grad_norm 16.3746 (20.7858)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:29:22 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [96/200][30/54]	eta 0:00:27 lr 0.000099	time 1.1332 (1.1294)	loss 5.5123 (6.4399)	miou 0.2247	grad_norm 22.2027 (18.7859)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:29:33 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [96/200][40/54]	eta 0:00:15 lr 0.000096	time 0.9576 (1.1089)	loss 5.2754 (6.4303)	miou 0.2327	grad_norm 17.2163 (18.3087)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:29:45 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [96/200][50/54]	eta 0:00:04 lr 0.000093	time 1.0056 (1.1355)	loss 8.9633 (6.3245)	miou 0.2359	grad_norm 20.4158 (19.3715)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:29:48 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 96 training takes 0:01:01
[32m[2025-07-12 03:29:48 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 03:29:58 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1833%
[32m[2025-07-12 03:29:59 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 03:30:00 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [97/200][0/54]	eta 0:01:28 lr 0.000091	time 1.6469 (1.6469)	loss 4.1039 (4.1039)	miou 0.1883	grad_norm 16.7995 (16.7995)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:30:11 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [97/200][10/54]	eta 0:00:49 lr 0.000086	time 1.1108 (1.1245)	loss 7.4326 (6.5246)	miou 0.2241	grad_norm 24.5574 (34.1380)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:30:22 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [97/200][20/54]	eta 0:00:37 lr 0.000080	time 1.0963 (1.1110)	loss 5.1919 (6.2963)	miou 0.2332	grad_norm 12.6866 (25.9795)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:30:34 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [97/200][30/54]	eta 0:00:27 lr 0.000073	time 0.8953 (1.1299)	loss 3.9248 (6.2215)	miou 0.2508	grad_norm 22.7893 (23.7584)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:30:45 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [97/200][40/54]	eta 0:00:15 lr 0.000066	time 1.5040 (1.1241)	loss 7.8489 (6.2214)	miou 0.2545	grad_norm 24.3682 (22.4203)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:30:55 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [97/200][50/54]	eta 0:00:04 lr 0.000059	time 0.8259 (1.1081)	loss 5.1048 (6.3446)	miou 0.2647	grad_norm 10.0726 (21.2878)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:30:59 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 97 training takes 0:01:00
[32m[2025-07-12 03:30:59 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 03:31:09 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.0902%
[32m[2025-07-12 03:31:09 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 03:31:10 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [98/200][0/54]	eta 0:00:59 lr 0.000055	time 1.1018 (1.1018)	loss 5.2813 (5.2813)	miou 0.1182	grad_norm 14.8773 (14.8773)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:31:22 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [98/200][10/54]	eta 0:00:50 lr 0.000048	time 1.2420 (1.1541)	loss 9.1257 (6.1769)	miou 0.1876	grad_norm 11.4220 (16.6326)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:31:33 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [98/200][20/54]	eta 0:00:39 lr 0.000040	time 1.0281 (1.1489)	loss 5.9368 (6.3209)	miou 0.2288	grad_norm 21.5138 (16.0771)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:31:44 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [98/200][30/54]	eta 0:00:26 lr 0.000032	time 1.1461 (1.1183)	loss 7.5657 (6.4240)	miou 0.2463	grad_norm 9.1497 (16.7425)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:31:54 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [98/200][40/54]	eta 0:00:15 lr 0.000025	time 0.9652 (1.1002)	loss 7.3355 (6.6248)	miou 0.2436	grad_norm 18.5249 (15.9631)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:32:06 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [98/200][50/54]	eta 0:00:04 lr 0.000019	time 1.0053 (1.1073)	loss 6.9168 (6.5519)	miou 0.2518	grad_norm 16.2697 (15.7325)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:32:09 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 98 training takes 0:00:59
[32m[2025-07-12 03:32:09 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 03:32:19 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1791%
[32m[2025-07-12 03:32:19 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 03:32:20 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [99/200][0/54]	eta 0:01:01 lr 0.000016	time 1.1303 (1.1303)	loss 5.5994 (5.5994)	miou 0.1816	grad_norm 11.6136 (11.6136)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:32:31 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [99/200][10/54]	eta 0:00:47 lr 0.000011	time 1.0286 (1.0808)	loss 6.6023 (5.9104)	miou 0.2337	grad_norm 12.2438 (15.3718)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:32:42 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [99/200][20/54]	eta 0:00:37 lr 0.000007	time 0.8725 (1.0961)	loss 4.4314 (5.9100)	miou 0.2563	grad_norm 16.3073 (16.6384)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:32:54 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [99/200][30/54]	eta 0:00:26 lr 0.000003	time 1.3886 (1.1183)	loss 6.6501 (5.9716)	miou 0.2704	grad_norm 17.7949 (16.2500)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:33:05 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [99/200][40/54]	eta 0:00:15 lr 0.000001	time 1.0879 (1.1100)	loss 7.8391 (6.1106)	miou 0.2734	grad_norm 16.4626 (16.4972)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:33:16 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [99/200][50/54]	eta 0:00:04 lr 0.000000	time 1.4294 (1.1236)	loss 6.9645 (6.0976)	miou 0.2768	grad_norm 12.6692 (15.8869)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:33:19 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 99 training takes 0:01:00
[32m[2025-07-12 03:33:19 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 03:33:29 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1597%
[32m[2025-07-12 03:33:29 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 03:33:31 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [100/200][0/54]	eta 0:01:16 lr 0.000000	time 1.4208 (1.4208)	loss 7.4730 (7.4730)	miou 0.1643	grad_norm 9.5628 (9.5628)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:33:41 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [100/200][10/54]	eta 0:00:47 lr 0.000001	time 1.4769 (1.0861)	loss 10.2625 (6.2864)	miou 0.2258	grad_norm 19.8806 (17.3433)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:33:53 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [100/200][20/54]	eta 0:00:38 lr 0.000003	time 0.9039 (1.1326)	loss 6.1519 (6.3873)	miou 0.2378	grad_norm 16.4973 (16.9063)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:34:05 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [100/200][30/54]	eta 0:00:27 lr 0.000006	time 1.0532 (1.1413)	loss 6.3323 (6.4746)	miou 0.2536	grad_norm 11.0639 (16.3590)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:34:16 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [100/200][40/54]	eta 0:00:15 lr 0.000010	time 1.4394 (1.1397)	loss 7.9478 (6.2910)	miou 0.2604	grad_norm 9.7528 (15.4046)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:34:26 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [100/200][50/54]	eta 0:00:04 lr 0.000015	time 0.8327 (1.1095)	loss 8.1831 (6.3349)	miou 0.2697	grad_norm 16.3416 (15.1039)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:34:29 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 100 training takes 0:01:00
[32m[2025-07-12 03:34:29 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 03:34:40 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1409%
[32m[2025-07-12 03:34:40 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 03:34:41 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [101/200][0/54]	eta 0:01:01 lr 0.000018	time 1.1409 (1.1409)	loss 6.2648 (6.2648)	miou 0.1435	grad_norm 8.8779 (8.8779)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:34:52 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [101/200][10/54]	eta 0:00:49 lr 0.000024	time 1.1237 (1.1263)	loss 4.9603 (6.4252)	miou 0.2271	grad_norm 10.0686 (15.9260)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:35:03 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [101/200][20/54]	eta 0:00:38 lr 0.000031	time 0.9604 (1.1234)	loss 4.9894 (6.1262)	miou 0.2413	grad_norm 27.6892 (17.4030)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:35:15 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [101/200][30/54]	eta 0:00:27 lr 0.000038	time 1.0683 (1.1300)	loss 8.5368 (6.1313)	miou 0.2568	grad_norm 29.1102 (16.6304)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:35:25 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [101/200][40/54]	eta 0:00:15 lr 0.000046	time 1.2462 (1.1116)	loss 6.1925 (6.2511)	miou 0.2615	grad_norm 9.7431 (16.1469)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:35:36 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [101/200][50/54]	eta 0:00:04 lr 0.000054	time 0.9833 (1.1103)	loss 6.8055 (6.3055)	miou 0.2650	grad_norm 13.6002 (15.8388)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:35:40 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 101 training takes 0:01:00
[32m[2025-07-12 03:35:40 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 03:35:50 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1939%
[32m[2025-07-12 03:35:50 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 03:35:51 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [102/200][0/54]	eta 0:00:47 lr 0.000057	time 0.8706 (0.8706)	loss 7.6904 (7.6904)	miou 0.1974	grad_norm 17.7714 (17.7714)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:36:01 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [102/200][10/54]	eta 0:00:45 lr 0.000065	time 0.9729 (1.0276)	loss 6.3462 (5.8806)	miou 0.2477	grad_norm 12.9328 (15.0690)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:36:13 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [102/200][20/54]	eta 0:00:37 lr 0.000072	time 1.1080 (1.1046)	loss 5.5233 (6.0095)	miou 0.2599	grad_norm 13.9994 (16.1647)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:36:23 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [102/200][30/54]	eta 0:00:25 lr 0.000079	time 1.2564 (1.0792)	loss 6.6995 (6.2179)	miou 0.2730	grad_norm 16.9191 (15.4437)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:36:35 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [102/200][40/54]	eta 0:00:15 lr 0.000085	time 1.1389 (1.1035)	loss 5.0463 (6.0177)	miou 0.2779	grad_norm 11.6468 (15.8150)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:36:47 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [102/200][50/54]	eta 0:00:04 lr 0.000090	time 0.9309 (1.1122)	loss 4.2079 (6.0289)	miou 0.2847	grad_norm 14.1307 (15.6979)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:36:50 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 102 training takes 0:01:00
[32m[2025-07-12 03:36:50 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 03:37:00 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1270%
[32m[2025-07-12 03:37:00 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 03:37:02 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [103/200][0/54]	eta 0:01:19 lr 0.000092	time 1.4727 (1.4727)	loss 8.4023 (8.4023)	miou 0.1339	grad_norm 28.8830 (28.8830)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:37:13 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [103/200][10/54]	eta 0:00:51 lr 0.000096	time 0.9514 (1.1733)	loss 5.5960 (7.4510)	miou 0.1900	grad_norm 17.9488 (21.1717)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:37:24 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [103/200][20/54]	eta 0:00:39 lr 0.000098	time 0.9137 (1.1487)	loss 6.6976 (7.1247)	miou 0.2350	grad_norm 19.5168 (20.2681)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:37:36 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [103/200][30/54]	eta 0:00:27 lr 0.000100	time 1.5950 (1.1592)	loss 6.3366 (6.9434)	miou 0.2471	grad_norm 13.9061 (18.4820)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:37:47 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [103/200][40/54]	eta 0:00:16 lr 0.000100	time 1.4162 (1.1493)	loss 5.4416 (6.7416)	miou 0.2517	grad_norm 22.7091 (18.0893)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:37:58 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [103/200][50/54]	eta 0:00:04 lr 0.000099	time 0.8304 (1.1398)	loss 4.5958 (6.6450)	miou 0.2544	grad_norm 13.8586 (17.7616)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:38:02 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 103 training takes 0:01:01
[32m[2025-07-12 03:38:02 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 03:38:12 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1062%
[32m[2025-07-12 03:38:12 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 03:38:13 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [104/200][0/54]	eta 0:01:13 lr 0.000098	time 1.3541 (1.3541)	loss 7.9981 (7.9981)	miou 0.1070	grad_norm 17.7811 (17.7811)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:38:25 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [104/200][10/54]	eta 0:00:52 lr 0.000096	time 1.1689 (1.1826)	loss 5.1076 (5.9316)	miou 0.1799	grad_norm 18.4390 (17.6367)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:38:36 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [104/200][20/54]	eta 0:00:40 lr 0.000092	time 1.2753 (1.1820)	loss 6.9283 (6.4557)	miou 0.2075	grad_norm 21.4389 (19.5977)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:38:48 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [104/200][30/54]	eta 0:00:27 lr 0.000087	time 0.8884 (1.1609)	loss 5.6381 (6.5780)	miou 0.2215	grad_norm 14.8754 (21.7588)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:38:58 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [104/200][40/54]	eta 0:00:15 lr 0.000081	time 0.8307 (1.1428)	loss 5.3847 (6.4580)	miou 0.2292	grad_norm 21.3315 (20.1011)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:39:09 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [104/200][50/54]	eta 0:00:04 lr 0.000075	time 0.9628 (1.1330)	loss 5.0031 (6.3972)	miou 0.2385	grad_norm 17.8866 (19.3891)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:39:12 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 104 training takes 0:01:00
[32m[2025-07-12 03:39:12 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 03:39:23 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.0745%
[32m[2025-07-12 03:39:23 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 03:39:24 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [105/200][0/54]	eta 0:01:04 lr 0.000072	time 1.1874 (1.1874)	loss 4.6276 (4.6276)	miou 0.0739	grad_norm 29.8978 (29.8978)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:39:35 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [105/200][10/54]	eta 0:00:48 lr 0.000065	time 1.1624 (1.0945)	loss 5.4289 (6.3745)	miou 0.1637	grad_norm 12.1793 (16.8830)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:39:45 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [105/200][20/54]	eta 0:00:35 lr 0.000057	time 0.8367 (1.0578)	loss 5.4931 (6.4472)	miou 0.2063	grad_norm 18.0829 (15.1413)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:39:56 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [105/200][30/54]	eta 0:00:25 lr 0.000049	time 1.0635 (1.0705)	loss 4.9067 (6.3504)	miou 0.2402	grad_norm 9.1940 (16.9473)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:40:07 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [105/200][40/54]	eta 0:00:15 lr 0.000041	time 1.1578 (1.0890)	loss 7.7898 (6.5127)	miou 0.2437	grad_norm 10.6562 (16.9932)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:40:19 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [105/200][50/54]	eta 0:00:04 lr 0.000034	time 1.1571 (1.1145)	loss 6.5234 (6.3500)	miou 0.2487	grad_norm 13.6729 (18.3758)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:40:23 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 105 training takes 0:00:59
[32m[2025-07-12 03:40:23 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 03:40:33 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1184%
[32m[2025-07-12 03:40:33 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 03:40:34 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [106/200][0/54]	eta 0:01:10 lr 0.000031	time 1.3110 (1.3110)	loss 6.4351 (6.4351)	miou 0.1319	grad_norm 22.0034 (22.0034)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:40:44 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [106/200][10/54]	eta 0:00:47 lr 0.000024	time 0.9739 (1.0731)	loss 5.2047 (5.7868)	miou 0.1920	grad_norm 34.6728 (18.8985)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:40:56 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [106/200][20/54]	eta 0:00:38 lr 0.000018	time 1.2495 (1.1211)	loss 5.8786 (6.1977)	miou 0.2002	grad_norm 10.2801 (18.5176)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:41:07 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [106/200][30/54]	eta 0:00:26 lr 0.000012	time 0.9000 (1.1147)	loss 8.0235 (6.2269)	miou 0.2067	grad_norm 13.9166 (16.4508)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:41:18 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [106/200][40/54]	eta 0:00:15 lr 0.000007	time 1.0077 (1.1161)	loss 4.8739 (6.1494)	miou 0.2136	grad_norm 24.8286 (16.4291)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:41:30 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [106/200][50/54]	eta 0:00:04 lr 0.000004	time 1.0841 (1.1198)	loss 7.7361 (6.1049)	miou 0.2314	grad_norm 17.8255 (16.7492)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:41:33 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 106 training takes 0:01:00
[32m[2025-07-12 03:41:33 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 03:41:43 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1089%
[32m[2025-07-12 03:41:43 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 03:41:45 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [107/200][0/54]	eta 0:01:37 lr 0.000003	time 1.8047 (1.8047)	loss 7.4440 (7.4440)	miou 0.1157	grad_norm 15.4610 (15.4610)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:41:57 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [107/200][10/54]	eta 0:00:54 lr 0.000001	time 1.1548 (1.2341)	loss 7.7352 (6.7991)	miou 0.1700	grad_norm 14.7942 (15.0842)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:42:09 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [107/200][20/54]	eta 0:00:41 lr 0.000000	time 1.0450 (1.2070)	loss 4.9767 (6.9672)	miou 0.2089	grad_norm 22.3050 (15.7533)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:42:18 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [107/200][30/54]	eta 0:00:26 lr 0.000000	time 0.9004 (1.1249)	loss 6.5727 (6.7774)	miou 0.2224	grad_norm 8.4674 (15.6044)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:42:29 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [107/200][40/54]	eta 0:00:15 lr 0.000002	time 1.1611 (1.1273)	loss 4.8347 (6.7248)	miou 0.2397	grad_norm 10.3057 (15.6810)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:42:41 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [107/200][50/54]	eta 0:00:04 lr 0.000005	time 1.0738 (1.1293)	loss 4.6182 (6.4414)	miou 0.2525	grad_norm 9.2218 (15.7795)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:42:44 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 107 training takes 0:01:01
[32m[2025-07-12 03:42:44 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 03:42:55 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1072%
[32m[2025-07-12 03:42:55 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 03:42:56 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [108/200][0/54]	eta 0:01:14 lr 0.000007	time 1.3817 (1.3817)	loss 3.5791 (3.5791)	miou 0.1128	grad_norm 11.5688 (11.5688)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:43:07 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [108/200][10/54]	eta 0:00:49 lr 0.000011	time 0.8875 (1.1281)	loss 5.6904 (5.8817)	miou 0.2076	grad_norm 8.9859 (14.2601)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:43:19 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [108/200][20/54]	eta 0:00:39 lr 0.000016	time 1.2912 (1.1504)	loss 6.0374 (5.9524)	miou 0.2235	grad_norm 18.1122 (15.1793)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:43:29 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [108/200][30/54]	eta 0:00:26 lr 0.000023	time 0.8878 (1.1108)	loss 3.4667 (5.9664)	miou 0.2527	grad_norm 13.4095 (15.5223)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:43:40 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [108/200][40/54]	eta 0:00:15 lr 0.000029	time 1.1062 (1.1202)	loss 6.2842 (6.0997)	miou 0.2539	grad_norm 11.3250 (15.6157)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:43:51 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [108/200][50/54]	eta 0:00:04 lr 0.000037	time 0.9065 (1.1123)	loss 8.5888 (6.1473)	miou 0.2585	grad_norm 19.8776 (17.5846)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:43:55 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 108 training takes 0:01:00
[32m[2025-07-12 03:43:55 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 03:44:05 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1181%
[32m[2025-07-12 03:44:05 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 03:44:07 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [109/200][0/54]	eta 0:01:43 lr 0.000040	time 1.9167 (1.9167)	loss 6.0324 (6.0324)	miou 0.1235	grad_norm 11.8389 (11.8389)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:44:18 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [109/200][10/54]	eta 0:00:52 lr 0.000048	time 1.2649 (1.1908)	loss 5.2816 (5.5912)	miou 0.1991	grad_norm 11.6055 (15.0318)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:44:29 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [109/200][20/54]	eta 0:00:39 lr 0.000055	time 1.4894 (1.1649)	loss 5.5303 (5.5733)	miou 0.2314	grad_norm 14.5323 (15.4538)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:44:41 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [109/200][30/54]	eta 0:00:27 lr 0.000063	time 1.0442 (1.1556)	loss 4.7983 (5.9502)	miou 0.2258	grad_norm 18.4780 (16.0375)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:44:51 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [109/200][40/54]	eta 0:00:15 lr 0.000071	time 0.9482 (1.1324)	loss 6.9462 (6.1295)	miou 0.2372	grad_norm 16.3976 (16.2962)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:45:03 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [109/200][50/54]	eta 0:00:04 lr 0.000077	time 1.1705 (1.1381)	loss 7.3178 (6.3382)	miou 0.2428	grad_norm 12.6797 (16.2213)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:45:07 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 109 training takes 0:01:01
[32m[2025-07-12 03:45:07 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 03:45:17 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2034%
[32m[2025-07-12 03:45:17 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 03:45:18 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [110/200][0/54]	eta 0:01:07 lr 0.000080	time 1.2544 (1.2544)	loss 7.7286 (7.7286)	miou 0.2084	grad_norm 17.4797 (17.4797)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:45:29 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [110/200][10/54]	eta 0:00:48 lr 0.000086	time 0.9979 (1.1000)	loss 7.1638 (7.1368)	miou 0.2346	grad_norm 8.4681 (15.2269)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:45:41 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [110/200][20/54]	eta 0:00:38 lr 0.000091	time 1.0947 (1.1384)	loss 7.7668 (6.6899)	miou 0.2484	grad_norm 10.3258 (15.9988)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:45:51 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [110/200][30/54]	eta 0:00:26 lr 0.000095	time 1.0293 (1.1027)	loss 6.3104 (6.6796)	miou 0.2517	grad_norm 15.6531 (16.3872)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:46:03 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [110/200][40/54]	eta 0:00:15 lr 0.000098	time 1.3997 (1.1203)	loss 6.5847 (6.4890)	miou 0.2567	grad_norm 15.9602 (16.1353)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:46:14 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [110/200][50/54]	eta 0:00:04 lr 0.000100	time 1.3968 (1.1146)	loss 6.7743 (6.4147)	miou 0.2578	grad_norm 12.0602 (16.9972)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:46:17 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 110 training takes 0:00:59
[32m[2025-07-12 03:46:17 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 03:46:27 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1623%
[32m[2025-07-12 03:46:27 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 03:46:28 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [111/200][0/54]	eta 0:01:27 lr 0.000100	time 1.6190 (1.6190)	loss 5.9741 (5.9741)	miou 0.1803	grad_norm 14.6688 (14.6688)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 03:46:39 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [111/200][10/54]	eta 0:00:49 lr 0.000100	time 0.9660 (1.1318)	loss 4.6459 (5.6464)	miou 0.2099	grad_norm 14.5219 (28.0048)	loss_scale 524288.0000 (405131.6364)	mem 3265MB
[32m[2025-07-12 03:46:50 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [111/200][20/54]	eta 0:00:37 lr 0.000099	time 0.9577 (1.1022)	loss 10.7045 (6.2316)	miou 0.2242	grad_norm 21.7657 (22.2303)	loss_scale 524288.0000 (461872.7619)	mem 3265MB
[32m[2025-07-12 03:47:02 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [111/200][30/54]	eta 0:00:26 lr 0.000096	time 1.3929 (1.1241)	loss 7.5100 (6.1879)	miou 0.2340	grad_norm 9.2498 (19.6914)	loss_scale 524288.0000 (482006.7097)	mem 3265MB
[32m[2025-07-12 03:47:14 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [111/200][40/54]	eta 0:00:16 lr 0.000093	time 0.9350 (1.1439)	loss 6.1577 (6.3044)	miou 0.2424	grad_norm 17.7364 (18.6439)	loss_scale 524288.0000 (492319.2195)	mem 3265MB
[32m[2025-07-12 03:47:24 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [111/200][50/54]	eta 0:00:04 lr 0.000088	time 1.1100 (1.1291)	loss 7.5910 (6.2773)	miou 0.2471	grad_norm 12.8299 (22.1955)	loss_scale 524288.0000 (498587.6078)	mem 3265MB
[32m[2025-07-12 03:47:28 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 111 training takes 0:01:00
[32m[2025-07-12 03:47:28 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 03:47:38 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1590%
[32m[2025-07-12 03:47:38 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 03:47:39 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [112/200][0/54]	eta 0:01:06 lr 0.000086	time 1.2259 (1.2259)	loss 6.8360 (6.8360)	miou 0.1578	grad_norm 14.9518 (14.9518)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 03:47:50 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [112/200][10/54]	eta 0:00:47 lr 0.000080	time 1.2202 (1.0840)	loss 7.4752 (6.1236)	miou 0.2378	grad_norm 7.7735 (12.0731)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 03:48:02 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [112/200][20/54]	eta 0:00:38 lr 0.000073	time 1.0140 (1.1215)	loss 7.7781 (6.3575)	miou 0.2372	grad_norm 23.5858 (15.6827)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 03:48:12 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [112/200][30/54]	eta 0:00:26 lr 0.000066	time 0.9762 (1.1022)	loss 5.5233 (6.3901)	miou 0.2439	grad_norm 16.1257 (15.6781)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 03:48:23 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [112/200][40/54]	eta 0:00:15 lr 0.000059	time 1.0241 (1.1062)	loss 4.9361 (6.3630)	miou 0.2498	grad_norm 15.1780 (15.1662)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 03:48:35 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [112/200][50/54]	eta 0:00:04 lr 0.000051	time 1.4420 (1.1101)	loss 4.7927 (6.2772)	miou 0.2558	grad_norm 11.1406 (15.3553)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 03:48:38 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 112 training takes 0:01:00
[32m[2025-07-12 03:48:38 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 03:48:48 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1312%
[32m[2025-07-12 03:48:48 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 03:48:49 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [113/200][0/54]	eta 0:00:56 lr 0.000048	time 1.0486 (1.0486)	loss 4.9132 (4.9132)	miou 0.1563	grad_norm 14.7506 (14.7506)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 03:49:01 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [113/200][10/54]	eta 0:00:49 lr 0.000040	time 1.0986 (1.1340)	loss 7.4207 (6.4837)	miou 0.2015	grad_norm 31.5263 (19.1912)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 03:49:12 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [113/200][20/54]	eta 0:00:37 lr 0.000032	time 1.1012 (1.1100)	loss 8.6419 (6.5197)	miou 0.2380	grad_norm 13.8079 (18.1957)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 03:49:22 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [113/200][30/54]	eta 0:00:25 lr 0.000025	time 0.8808 (1.0772)	loss 4.5655 (6.2285)	miou 0.2539	grad_norm 10.2564 (16.4837)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 03:49:33 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [113/200][40/54]	eta 0:00:15 lr 0.000019	time 0.9836 (1.0860)	loss 7.9730 (6.0705)	miou 0.2666	grad_norm 28.7145 (15.9579)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 03:49:45 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [113/200][50/54]	eta 0:00:04 lr 0.000013	time 0.9617 (1.1103)	loss 4.1893 (6.0809)	miou 0.2647	grad_norm 27.3923 (16.6887)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 03:49:49 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 113 training takes 0:01:00
[32m[2025-07-12 03:49:49 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 03:49:59 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1648%
[32m[2025-07-12 03:49:59 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 03:50:01 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [114/200][0/54]	eta 0:01:35 lr 0.000011	time 1.7654 (1.7654)	loss 3.7596 (3.7596)	miou 0.1673	grad_norm 13.1196 (13.1196)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 03:50:11 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [114/200][10/54]	eta 0:00:50 lr 0.000007	time 1.0139 (1.1475)	loss 7.6659 (6.0741)	miou 0.2262	grad_norm 10.7948 (11.6697)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 03:50:23 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [114/200][20/54]	eta 0:00:38 lr 0.000003	time 1.1033 (1.1360)	loss 4.9983 (6.0314)	miou 0.2381	grad_norm 15.3535 (15.2945)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 03:50:34 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [114/200][30/54]	eta 0:00:27 lr 0.000001	time 1.0502 (1.1302)	loss 7.3596 (6.1591)	miou 0.2620	grad_norm 12.2181 (14.0942)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 03:50:45 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [114/200][40/54]	eta 0:00:15 lr 0.000000	time 1.4878 (1.1257)	loss 5.6989 (6.0294)	miou 0.2642	grad_norm 11.5535 (14.2720)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 03:50:56 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [114/200][50/54]	eta 0:00:04 lr 0.000000	time 1.2559 (1.1321)	loss 7.0999 (5.9967)	miou 0.2655	grad_norm 10.6577 (14.1925)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 03:51:00 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 114 training takes 0:01:01
[32m[2025-07-12 03:51:00 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 03:51:10 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1644%
[32m[2025-07-12 03:51:10 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 03:51:12 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [115/200][0/54]	eta 0:01:21 lr 0.000001	time 1.5063 (1.5063)	loss 5.3841 (5.3841)	miou 0.1889	grad_norm 15.5517 (15.5517)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 03:51:22 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [115/200][10/54]	eta 0:00:47 lr 0.000003	time 0.9268 (1.0782)	loss 4.8226 (6.9108)	miou 0.2300	grad_norm 8.2468 (14.0253)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 03:51:34 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [115/200][20/54]	eta 0:00:38 lr 0.000006	time 0.8985 (1.1297)	loss 7.8117 (6.7880)	miou 0.2575	grad_norm 9.2005 (13.9973)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 03:51:45 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [115/200][30/54]	eta 0:00:27 lr 0.000010	time 1.1416 (1.1344)	loss 4.8301 (6.5280)	miou 0.2740	grad_norm 7.6659 (14.5221)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 03:51:56 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [115/200][40/54]	eta 0:00:15 lr 0.000015	time 1.2257 (1.1168)	loss 5.2709 (6.4545)	miou 0.2749	grad_norm 9.8870 (15.7235)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 03:52:08 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [115/200][50/54]	eta 0:00:04 lr 0.000021	time 1.3833 (1.1269)	loss 9.2551 (6.4611)	miou 0.2771	grad_norm 15.3703 (15.5136)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 03:52:11 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 115 training takes 0:01:01
[32m[2025-07-12 03:52:11 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 03:52:21 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1424%
[32m[2025-07-12 03:52:21 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 03:52:23 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [116/200][0/54]	eta 0:01:05 lr 0.000024	time 1.2126 (1.2126)	loss 6.0800 (6.0800)	miou 0.1521	grad_norm 18.3095 (18.3095)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 03:52:34 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [116/200][10/54]	eta 0:00:48 lr 0.000031	time 0.8832 (1.1123)	loss 6.3059 (6.4086)	miou 0.1923	grad_norm 8.1667 (17.7365)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 03:52:45 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [116/200][20/54]	eta 0:00:38 lr 0.000038	time 1.2935 (1.1214)	loss 5.6451 (6.0563)	miou 0.2207	grad_norm 11.0670 (15.4224)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 03:52:57 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [116/200][30/54]	eta 0:00:27 lr 0.000046	time 1.5572 (1.1335)	loss 6.5406 (5.9783)	miou 0.2474	grad_norm 29.1889 (15.4386)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 03:53:08 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [116/200][40/54]	eta 0:00:15 lr 0.000054	time 1.5005 (1.1309)	loss 7.5958 (6.1456)	miou 0.2493	grad_norm 9.4724 (15.3077)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 03:53:19 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [116/200][50/54]	eta 0:00:04 lr 0.000062	time 1.1004 (1.1366)	loss 5.6102 (6.1110)	miou 0.2567	grad_norm 19.8628 (15.3030)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 03:53:23 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 116 training takes 0:01:01
[32m[2025-07-12 03:53:23 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 03:53:33 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2080%
[32m[2025-07-12 03:53:33 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 03:53:34 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [117/200][0/54]	eta 0:01:22 lr 0.000065	time 1.5306 (1.5306)	loss 4.1712 (4.1712)	miou 0.2077	grad_norm 19.9042 (19.9042)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 03:53:46 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [117/200][10/54]	eta 0:00:53 lr 0.000072	time 1.4570 (1.2190)	loss 7.6880 (6.6627)	miou 0.2197	grad_norm 9.1992 (17.1429)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 03:53:57 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [117/200][20/54]	eta 0:00:39 lr 0.000079	time 1.2570 (1.1590)	loss 6.4433 (6.1153)	miou 0.2525	grad_norm 18.6502 (16.0961)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 03:54:09 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [117/200][30/54]	eta 0:00:27 lr 0.000085	time 1.0497 (1.1577)	loss 7.6890 (6.1305)	miou 0.2693	grad_norm 19.6267 (15.8061)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 03:54:19 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [117/200][40/54]	eta 0:00:15 lr 0.000090	time 0.9532 (1.1258)	loss 7.7545 (6.0714)	miou 0.2775	grad_norm 30.6828 (15.2668)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 03:54:30 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [117/200][50/54]	eta 0:00:04 lr 0.000094	time 1.1658 (1.1254)	loss 6.4480 (6.0475)	miou 0.2800	grad_norm 11.2756 (15.3902)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 03:54:34 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 117 training takes 0:01:00
[32m[2025-07-12 03:54:34 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 03:54:44 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1118%
[32m[2025-07-12 03:54:44 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 03:54:45 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [118/200][0/54]	eta 0:01:01 lr 0.000096	time 1.1339 (1.1339)	loss 3.8219 (3.8219)	miou 0.1129	grad_norm 30.6424 (30.6424)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 03:54:56 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [118/200][10/54]	eta 0:00:48 lr 0.000098	time 1.4966 (1.1026)	loss 5.9985 (6.0542)	miou 0.1711	grad_norm 10.9972 (20.1832)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 03:55:07 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [118/200][20/54]	eta 0:00:37 lr 0.000100	time 1.0335 (1.1159)	loss 6.5341 (6.4139)	miou 0.2239	grad_norm 8.6475 (16.4173)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 03:55:18 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [118/200][30/54]	eta 0:00:26 lr 0.000100	time 1.0448 (1.1074)	loss 6.7554 (6.2126)	miou 0.2397	grad_norm 17.7702 (16.6786)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 03:55:29 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [118/200][40/54]	eta 0:00:15 lr 0.000099	time 1.3129 (1.1045)	loss 7.7379 (6.3797)	miou 0.2526	grad_norm 7.8597 (15.7755)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 03:55:40 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [118/200][50/54]	eta 0:00:04 lr 0.000097	time 0.9379 (1.1069)	loss 4.7175 (6.1968)	miou 0.2549	grad_norm 20.9031 (15.6037)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 03:55:43 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 118 training takes 0:00:59
[32m[2025-07-12 03:55:43 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 03:55:54 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1174%
[32m[2025-07-12 03:55:54 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 03:55:55 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [119/200][0/54]	eta 0:01:12 lr 0.000096	time 1.3449 (1.3449)	loss 5.0251 (5.0251)	miou 0.1299	grad_norm 23.9378 (23.9378)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 03:56:06 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [119/200][10/54]	eta 0:00:48 lr 0.000092	time 1.1398 (1.1116)	loss 5.3782 (6.9422)	miou 0.1691	grad_norm 32.6139 (18.4589)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 03:56:16 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [119/200][20/54]	eta 0:00:36 lr 0.000087	time 0.8955 (1.0819)	loss 7.5889 (6.6228)	miou 0.1830	grad_norm 14.8952 (16.5487)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 03:56:27 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [119/200][30/54]	eta 0:00:26 lr 0.000081	time 1.0988 (1.0864)	loss 6.0809 (6.5230)	miou 0.2045	grad_norm 9.6153 (16.0262)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 03:56:40 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [119/200][40/54]	eta 0:00:15 lr 0.000075	time 1.0751 (1.1219)	loss 7.6979 (6.6483)	miou 0.2299	grad_norm 9.4995 (15.6192)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 03:56:50 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [119/200][50/54]	eta 0:00:04 lr 0.000068	time 1.0135 (1.1122)	loss 8.0261 (6.6274)	miou 0.2337	grad_norm 12.3845 (15.7712)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 03:56:54 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 119 training takes 0:01:00
[32m[2025-07-12 03:56:54 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 03:57:04 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1780%
[32m[2025-07-12 03:57:04 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 03:57:06 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [120/200][0/54]	eta 0:01:26 lr 0.000065	time 1.6111 (1.6111)	loss 3.7888 (3.7888)	miou 0.1811	grad_norm 21.5251 (21.5251)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 03:57:16 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [120/200][10/54]	eta 0:00:48 lr 0.000057	time 1.0835 (1.1030)	loss 6.0569 (6.6023)	miou 0.2191	grad_norm 25.2880 (15.9191)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 03:57:27 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [120/200][20/54]	eta 0:00:37 lr 0.000049	time 1.3254 (1.0910)	loss 6.8396 (6.5369)	miou 0.2591	grad_norm 19.0353 (14.6924)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 03:57:38 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [120/200][30/54]	eta 0:00:26 lr 0.000041	time 0.9715 (1.1030)	loss 4.0696 (6.2955)	miou 0.2632	grad_norm 18.0154 (15.6159)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 03:57:50 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [120/200][40/54]	eta 0:00:15 lr 0.000034	time 1.0620 (1.1147)	loss 6.9070 (6.3835)	miou 0.2682	grad_norm 14.6889 (15.1337)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 03:58:01 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [120/200][50/54]	eta 0:00:04 lr 0.000027	time 0.8740 (1.1215)	loss 5.8680 (6.3573)	miou 0.2756	grad_norm 17.5286 (14.8498)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 03:58:05 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 120 training takes 0:01:00
[32m[2025-07-12 03:58:05 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 03:58:15 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1708%
[32m[2025-07-12 03:58:15 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 03:58:16 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [121/200][0/54]	eta 0:01:26 lr 0.000024	time 1.5967 (1.5967)	loss 5.5612 (5.5612)	miou 0.1740	grad_norm 11.5785 (11.5785)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 03:58:27 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [121/200][10/54]	eta 0:00:50 lr 0.000018	time 1.4891 (1.1506)	loss 4.8766 (5.7432)	miou 0.2374	grad_norm 11.9031 (17.4032)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 03:58:39 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [121/200][20/54]	eta 0:00:38 lr 0.000012	time 1.1246 (1.1439)	loss 4.6211 (6.1759)	miou 0.2423	grad_norm 12.8789 (15.6964)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 03:58:50 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [121/200][30/54]	eta 0:00:27 lr 0.000007	time 1.1083 (1.1335)	loss 5.5937 (6.1367)	miou 0.2545	grad_norm 10.0109 (15.4320)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 03:59:02 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [121/200][40/54]	eta 0:00:15 lr 0.000004	time 1.5476 (1.1411)	loss 5.9795 (6.2916)	miou 0.2623	grad_norm 18.0935 (14.4708)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 03:59:12 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [121/200][50/54]	eta 0:00:04 lr 0.000001	time 1.3401 (1.1272)	loss 6.7839 (6.2306)	miou 0.2699	grad_norm 6.3349 (14.1431)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 03:59:16 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 121 training takes 0:01:00
[32m[2025-07-12 03:59:16 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 03:59:26 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1665%
[32m[2025-07-12 03:59:26 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 03:59:27 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [122/200][0/54]	eta 0:01:11 lr 0.000001	time 1.3326 (1.3326)	loss 3.8906 (3.8906)	miou 0.1747	grad_norm 30.1846 (30.1846)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 03:59:39 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [122/200][10/54]	eta 0:00:52 lr 0.000000	time 1.2853 (1.1855)	loss 6.5873 (5.7222)	miou 0.2438	grad_norm 12.9516 (14.3078)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 03:59:51 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [122/200][20/54]	eta 0:00:40 lr 0.000000	time 1.2547 (1.1899)	loss 6.7836 (5.8804)	miou 0.2509	grad_norm 13.2334 (13.1324)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:00:01 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [122/200][30/54]	eta 0:00:27 lr 0.000002	time 1.1059 (1.1514)	loss 7.1052 (5.9281)	miou 0.2590	grad_norm 14.5029 (13.8916)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:00:13 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [122/200][40/54]	eta 0:00:16 lr 0.000005	time 1.1311 (1.1474)	loss 5.2173 (5.9165)	miou 0.2683	grad_norm 12.6115 (13.7342)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:00:23 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [122/200][50/54]	eta 0:00:04 lr 0.000009	time 1.0341 (1.1299)	loss 7.4373 (5.9333)	miou 0.2711	grad_norm 20.2924 (13.9029)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:00:26 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 122 training takes 0:01:00
[32m[2025-07-12 04:00:26 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 04:00:36 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1572%
[32m[2025-07-12 04:00:36 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 04:00:38 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [123/200][0/54]	eta 0:01:11 lr 0.000011	time 1.3273 (1.3273)	loss 8.3224 (8.3224)	miou 0.1598	grad_norm 10.2149 (10.2149)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:00:49 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [123/200][10/54]	eta 0:00:50 lr 0.000016	time 1.5769 (1.1463)	loss 6.7734 (6.5527)	miou 0.2311	grad_norm 15.2478 (13.3218)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:01:00 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [123/200][20/54]	eta 0:00:38 lr 0.000023	time 1.3981 (1.1360)	loss 5.7514 (6.1178)	miou 0.2632	grad_norm 18.6292 (14.8439)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:01:12 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [123/200][30/54]	eta 0:00:27 lr 0.000029	time 1.2865 (1.1458)	loss 6.8748 (6.1768)	miou 0.2784	grad_norm 9.6080 (14.6979)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:01:23 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [123/200][40/54]	eta 0:00:15 lr 0.000037	time 0.8768 (1.1261)	loss 7.5176 (6.1298)	miou 0.2832	grad_norm 11.8550 (14.2165)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:01:34 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [123/200][50/54]	eta 0:00:04 lr 0.000045	time 0.9681 (1.1248)	loss 6.2042 (6.1153)	miou 0.2888	grad_norm 12.6777 (14.2687)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:01:38 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 123 training takes 0:01:01
[32m[2025-07-12 04:01:38 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 04:01:48 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1809%
[32m[2025-07-12 04:01:48 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 04:01:49 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [124/200][0/54]	eta 0:00:53 lr 0.000048	time 0.9871 (0.9871)	loss 3.7930 (3.7930)	miou 0.1859	grad_norm 14.1969 (14.1969)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:02:00 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [124/200][10/54]	eta 0:00:50 lr 0.000055	time 1.2356 (1.1525)	loss 7.0920 (6.0253)	miou 0.2468	grad_norm 13.5278 (12.6287)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:02:11 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [124/200][20/54]	eta 0:00:38 lr 0.000063	time 1.0732 (1.1315)	loss 6.7560 (5.9978)	miou 0.2627	grad_norm 11.4609 (16.2836)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:02:23 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [124/200][30/54]	eta 0:00:27 lr 0.000071	time 1.1545 (1.1520)	loss 6.0349 (6.0176)	miou 0.2777	grad_norm 13.0259 (14.6862)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:02:34 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [124/200][40/54]	eta 0:00:15 lr 0.000077	time 1.1682 (1.1294)	loss 4.8923 (5.9154)	miou 0.2766	grad_norm 18.4691 (15.3676)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:02:46 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [124/200][50/54]	eta 0:00:04 lr 0.000084	time 1.0160 (1.1339)	loss 3.8657 (5.9446)	miou 0.2775	grad_norm 23.7781 (15.3544)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:02:49 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 124 training takes 0:01:01
[32m[2025-07-12 04:02:49 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 04:02:59 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2162%
[32m[2025-07-12 04:02:59 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 04:03:00 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [125/200][0/54]	eta 0:01:13 lr 0.000086	time 1.3546 (1.3546)	loss 7.2016 (7.2016)	miou 0.2146	grad_norm 11.7338 (11.7338)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:03:12 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [125/200][10/54]	eta 0:00:51 lr 0.000091	time 1.1192 (1.1709)	loss 4.6486 (6.0938)	miou 0.2453	grad_norm 14.2867 (14.1160)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:03:23 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [125/200][20/54]	eta 0:00:38 lr 0.000095	time 1.0103 (1.1252)	loss 7.4543 (6.6239)	miou 0.2595	grad_norm 7.7400 (13.8953)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:03:34 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [125/200][30/54]	eta 0:00:26 lr 0.000098	time 1.1092 (1.1182)	loss 8.4397 (6.6206)	miou 0.2570	grad_norm 23.4825 (15.2782)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:03:45 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [125/200][40/54]	eta 0:00:15 lr 0.000100	time 1.2211 (1.1181)	loss 4.1639 (6.5065)	miou 0.2634	grad_norm 19.1907 (14.8982)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:03:56 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [125/200][50/54]	eta 0:00:04 lr 0.000100	time 1.2863 (1.1190)	loss 5.8107 (6.4305)	miou 0.2658	grad_norm 11.5462 (15.2703)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:04:00 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 125 training takes 0:01:00
[32m[2025-07-12 04:04:00 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 04:04:10 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2223%
[32m[2025-07-12 04:04:10 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 04:04:11 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [126/200][0/54]	eta 0:01:10 lr 0.000100	time 1.3141 (1.3141)	loss 5.3391 (5.3391)	miou 0.2264	grad_norm 12.8649 (12.8649)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:04:23 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [126/200][10/54]	eta 0:00:51 lr 0.000099	time 0.9122 (1.1726)	loss 8.0216 (6.0391)	miou 0.2395	grad_norm 14.3295 (24.4730)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:04:34 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [126/200][20/54]	eta 0:00:39 lr 0.000096	time 1.1406 (1.1543)	loss 8.9959 (6.2570)	miou 0.2545	grad_norm 15.4461 (20.3219)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:04:45 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [126/200][30/54]	eta 0:00:27 lr 0.000093	time 0.9671 (1.1391)	loss 7.2478 (6.3713)	miou 0.2647	grad_norm 11.4854 (18.2874)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:04:56 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [126/200][40/54]	eta 0:00:15 lr 0.000088	time 1.0758 (1.1258)	loss 7.5094 (6.3079)	miou 0.2665	grad_norm 12.8756 (17.0957)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:05:07 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [126/200][50/54]	eta 0:00:04 lr 0.000082	time 1.1424 (1.1196)	loss 6.3327 (6.3380)	miou 0.2681	grad_norm 7.7213 (16.4533)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:05:10 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 126 training takes 0:01:00
[32m[2025-07-12 04:05:10 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 04:05:20 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1684%
[32m[2025-07-12 04:05:20 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 04:05:22 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [127/200][0/54]	eta 0:01:15 lr 0.000080	time 1.3921 (1.3921)	loss 6.6920 (6.6920)	miou 0.1894	grad_norm 9.8658 (9.8658)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:05:33 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [127/200][10/54]	eta 0:00:52 lr 0.000073	time 1.0716 (1.1845)	loss 5.7903 (6.6187)	miou 0.2207	grad_norm 8.6933 (13.3805)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:05:45 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [127/200][20/54]	eta 0:00:39 lr 0.000066	time 0.9606 (1.1526)	loss 5.3326 (6.3860)	miou 0.2477	grad_norm 22.3660 (13.2764)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:05:56 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [127/200][30/54]	eta 0:00:27 lr 0.000059	time 0.9343 (1.1524)	loss 5.2948 (6.5088)	miou 0.2493	grad_norm 10.9352 (15.8475)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:06:07 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [127/200][40/54]	eta 0:00:15 lr 0.000051	time 0.8807 (1.1411)	loss 5.8467 (6.3012)	miou 0.2542	grad_norm 19.7366 (15.3556)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:06:18 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [127/200][50/54]	eta 0:00:04 lr 0.000043	time 1.0092 (1.1305)	loss 7.5916 (6.4739)	miou 0.2517	grad_norm 14.3411 (15.2242)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:06:21 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 127 training takes 0:01:00
[32m[2025-07-12 04:06:21 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 04:06:31 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2367%
[32m[2025-07-12 04:06:31 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 04:06:33 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [128/200][0/54]	eta 0:01:12 lr 0.000040	time 1.3422 (1.3422)	loss 5.6088 (5.6088)	miou 0.2514	grad_norm 11.3476 (11.3476)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:06:43 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [128/200][10/54]	eta 0:00:45 lr 0.000032	time 0.8995 (1.0404)	loss 4.2889 (5.7909)	miou 0.2577	grad_norm 8.8669 (12.6514)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:06:54 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [128/200][20/54]	eta 0:00:36 lr 0.000025	time 1.0376 (1.0713)	loss 6.6966 (6.2894)	miou 0.2704	grad_norm 22.2533 (16.2835)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:07:05 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [128/200][30/54]	eta 0:00:26 lr 0.000019	time 1.3507 (1.0980)	loss 6.8375 (6.0575)	miou 0.2879	grad_norm 16.5543 (15.7767)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:07:17 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [128/200][40/54]	eta 0:00:15 lr 0.000013	time 0.9250 (1.1152)	loss 5.1845 (6.1061)	miou 0.2857	grad_norm 13.6035 (14.4182)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:07:29 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [128/200][50/54]	eta 0:00:04 lr 0.000008	time 0.9106 (1.1288)	loss 4.9868 (6.1492)	miou 0.2868	grad_norm 10.5516 (14.1159)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:07:32 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 128 training takes 0:01:00
[32m[2025-07-12 04:07:32 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 04:07:42 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1697%
[32m[2025-07-12 04:07:42 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 04:07:44 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [129/200][0/54]	eta 0:01:21 lr 0.000007	time 1.5103 (1.5103)	loss 5.1998 (5.1998)	miou 0.1843	grad_norm 12.6796 (12.6796)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:07:55 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [129/200][10/54]	eta 0:00:51 lr 0.000003	time 0.9996 (1.1698)	loss 5.3786 (5.4431)	miou 0.2636	grad_norm 10.8836 (12.5609)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:08:06 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [129/200][20/54]	eta 0:00:38 lr 0.000001	time 1.0659 (1.1317)	loss 8.8336 (5.8467)	miou 0.2744	grad_norm 36.8142 (15.9167)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:08:18 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [129/200][30/54]	eta 0:00:27 lr 0.000000	time 1.6093 (1.1646)	loss 5.7157 (5.9771)	miou 0.2808	grad_norm 10.9548 (15.2780)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:08:29 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [129/200][40/54]	eta 0:00:16 lr 0.000000	time 1.2515 (1.1511)	loss 5.7231 (6.0856)	miou 0.2873	grad_norm 8.8979 (14.3582)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:08:40 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [129/200][50/54]	eta 0:00:04 lr 0.000002	time 1.2256 (1.1322)	loss 5.0066 (6.0790)	miou 0.2854	grad_norm 9.6857 (16.4676)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:08:43 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 129 training takes 0:01:01
[32m[2025-07-12 04:08:43 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 04:08:54 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2448%
[32m[2025-07-12 04:08:54 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 04:08:55 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [130/200][0/54]	eta 0:01:06 lr 0.000003	time 1.2370 (1.2370)	loss 6.6510 (6.6510)	miou 0.2589	grad_norm 8.4765 (8.4765)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:09:06 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [130/200][10/54]	eta 0:00:50 lr 0.000006	time 1.0624 (1.1589)	loss 5.1276 (5.9081)	miou 0.2814	grad_norm 12.8276 (14.6835)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:09:17 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [130/200][20/54]	eta 0:00:38 lr 0.000010	time 0.9869 (1.1211)	loss 6.4871 (5.9255)	miou 0.2794	grad_norm 9.1777 (16.4550)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:09:29 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [130/200][30/54]	eta 0:00:27 lr 0.000015	time 1.0154 (1.1296)	loss 3.8856 (6.1736)	miou 0.2615	grad_norm 21.1078 (15.6992)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:09:39 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [130/200][40/54]	eta 0:00:15 lr 0.000021	time 0.9577 (1.1109)	loss 4.6460 (6.1492)	miou 0.2745	grad_norm 17.8217 (15.1470)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:09:51 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [130/200][50/54]	eta 0:00:04 lr 0.000028	time 1.0367 (1.1183)	loss 3.6271 (6.0590)	miou 0.2828	grad_norm 26.6172 (14.8643)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:09:54 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 130 training takes 0:01:00
[32m[2025-07-12 04:09:54 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 04:10:04 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1782%
[32m[2025-07-12 04:10:04 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 04:10:06 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [131/200][0/54]	eta 0:01:31 lr 0.000031	time 1.6876 (1.6876)	loss 5.4293 (5.4293)	miou 0.1777	grad_norm 20.3306 (20.3306)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:10:17 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [131/200][10/54]	eta 0:00:51 lr 0.000038	time 1.1235 (1.1716)	loss 4.8846 (6.0418)	miou 0.2263	grad_norm 10.6551 (12.8913)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:10:28 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [131/200][20/54]	eta 0:00:38 lr 0.000046	time 1.0351 (1.1187)	loss 6.3884 (5.7822)	miou 0.2592	grad_norm 12.4347 (13.3532)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:10:39 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [131/200][30/54]	eta 0:00:26 lr 0.000054	time 0.8226 (1.1119)	loss 5.8921 (5.8333)	miou 0.2662	grad_norm 23.2285 (13.8837)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:10:49 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [131/200][40/54]	eta 0:00:15 lr 0.000062	time 1.0378 (1.0939)	loss 5.0800 (6.1985)	miou 0.2622	grad_norm 9.7698 (13.8661)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:11:01 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [131/200][50/54]	eta 0:00:04 lr 0.000069	time 1.3876 (1.1135)	loss 4.5853 (6.1384)	miou 0.2675	grad_norm 14.8427 (13.7528)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:11:05 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 131 training takes 0:01:00
[32m[2025-07-12 04:11:05 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 04:11:15 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2072%
[32m[2025-07-12 04:11:15 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 04:11:16 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [132/200][0/54]	eta 0:01:04 lr 0.000072	time 1.2004 (1.2004)	loss 6.6384 (6.6384)	miou 0.2105	grad_norm 9.8339 (9.8339)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:11:27 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [132/200][10/54]	eta 0:00:46 lr 0.000079	time 1.0820 (1.0644)	loss 6.4962 (7.5346)	miou 0.2239	grad_norm 10.6372 (15.0956)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:11:37 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [132/200][20/54]	eta 0:00:35 lr 0.000085	time 1.0584 (1.0564)	loss 4.8230 (6.6913)	miou 0.2408	grad_norm 11.2732 (14.9305)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:11:48 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [132/200][30/54]	eta 0:00:25 lr 0.000090	time 1.2030 (1.0645)	loss 6.3753 (6.4092)	miou 0.2639	grad_norm 8.6284 (14.2143)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:12:00 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [132/200][40/54]	eta 0:00:15 lr 0.000094	time 0.7558 (1.1022)	loss 8.2523 (6.3625)	miou 0.2674	grad_norm 16.9659 (15.4566)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:12:12 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [132/200][50/54]	eta 0:00:04 lr 0.000097	time 0.8985 (1.1073)	loss 5.1202 (6.2175)	miou 0.2783	grad_norm 14.9802 (15.4564)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:12:15 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 132 training takes 0:00:59
[32m[2025-07-12 04:12:15 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 04:12:25 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2138%
[32m[2025-07-12 04:12:25 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 04:12:26 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [133/200][0/54]	eta 0:01:20 lr 0.000098	time 1.4876 (1.4876)	loss 5.8316 (5.8316)	miou 0.2202	grad_norm 23.3591 (23.3591)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:12:37 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [133/200][10/54]	eta 0:00:50 lr 0.000100	time 1.2543 (1.1371)	loss 5.9864 (6.2711)	miou 0.2403	grad_norm 9.4236 (15.3608)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:12:49 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [133/200][20/54]	eta 0:00:38 lr 0.000100	time 1.0319 (1.1416)	loss 10.4251 (6.4039)	miou 0.2505	grad_norm 10.6816 (16.3346)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:13:00 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [133/200][30/54]	eta 0:00:26 lr 0.000099	time 1.1110 (1.1230)	loss 7.8192 (6.3515)	miou 0.2625	grad_norm 13.0729 (15.3486)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:13:11 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [133/200][40/54]	eta 0:00:15 lr 0.000097	time 1.1767 (1.1253)	loss 6.7404 (6.1316)	miou 0.2775	grad_norm 17.8939 (15.3299)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:13:22 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [133/200][50/54]	eta 0:00:04 lr 0.000093	time 1.0220 (1.1222)	loss 7.8786 (6.1716)	miou 0.2803	grad_norm 11.8948 (16.4547)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:13:25 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 133 training takes 0:01:00
[32m[2025-07-12 04:13:25 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 04:13:35 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1912%
[32m[2025-07-12 04:13:35 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 04:13:37 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [134/200][0/54]	eta 0:01:14 lr 0.000092	time 1.3796 (1.3796)	loss 6.6644 (6.6644)	miou 0.1974	grad_norm 11.3507 (11.3507)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:13:48 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [134/200][10/54]	eta 0:00:52 lr 0.000087	time 1.3944 (1.1823)	loss 8.0496 (5.8756)	miou 0.2635	grad_norm 16.5823 (15.7158)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:13:59 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [134/200][20/54]	eta 0:00:38 lr 0.000081	time 1.1494 (1.1432)	loss 7.3216 (6.2808)	miou 0.2650	grad_norm 20.8144 (15.6243)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:14:10 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [134/200][30/54]	eta 0:00:27 lr 0.000075	time 1.0258 (1.1301)	loss 4.8118 (6.1173)	miou 0.2800	grad_norm 18.8543 (16.1920)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:14:21 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [134/200][40/54]	eta 0:00:15 lr 0.000068	time 0.9700 (1.1224)	loss 7.2389 (6.1612)	miou 0.2822	grad_norm 12.4671 (16.2226)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:14:33 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [134/200][50/54]	eta 0:00:04 lr 0.000060	time 1.3934 (1.1341)	loss 7.2355 (6.1385)	miou 0.2839	grad_norm 31.4278 (16.1361)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:14:36 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 134 training takes 0:01:00
[32m[2025-07-12 04:14:36 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 04:14:46 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2196%
[32m[2025-07-12 04:14:46 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 04:14:48 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [135/200][0/54]	eta 0:01:23 lr 0.000057	time 1.5384 (1.5384)	loss 5.2854 (5.2854)	miou 0.2337	grad_norm 8.2385 (8.2385)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:14:59 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [135/200][10/54]	eta 0:00:51 lr 0.000049	time 1.3918 (1.1773)	loss 5.4888 (6.2979)	miou 0.2632	grad_norm 8.3244 (15.3970)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:15:11 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [135/200][20/54]	eta 0:00:39 lr 0.000041	time 0.9531 (1.1504)	loss 5.5160 (5.9992)	miou 0.2735	grad_norm 24.5381 (16.0379)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:15:22 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [135/200][30/54]	eta 0:00:27 lr 0.000034	time 1.1171 (1.1621)	loss 5.4801 (6.0620)	miou 0.2720	grad_norm 9.0938 (15.8025)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:15:33 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [135/200][40/54]	eta 0:00:15 lr 0.000027	time 0.9431 (1.1352)	loss 4.7520 (6.0815)	miou 0.2775	grad_norm 8.8458 (15.4618)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:15:44 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [135/200][50/54]	eta 0:00:04 lr 0.000020	time 1.3114 (1.1208)	loss 8.3974 (6.1232)	miou 0.2820	grad_norm 7.3983 (14.9127)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:15:47 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 135 training takes 0:01:00
[32m[2025-07-12 04:15:47 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 04:15:57 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1289%
[32m[2025-07-12 04:15:57 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 04:15:58 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [136/200][0/54]	eta 0:01:05 lr 0.000018	time 1.2150 (1.2150)	loss 5.3960 (5.3960)	miou 0.1390	grad_norm 17.9673 (17.9673)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:16:09 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [136/200][10/54]	eta 0:00:47 lr 0.000012	time 0.9116 (1.0763)	loss 6.7469 (5.6685)	miou 0.2025	grad_norm 13.8166 (13.2875)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:16:20 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [136/200][20/54]	eta 0:00:36 lr 0.000007	time 0.8662 (1.0778)	loss 5.6927 (6.4741)	miou 0.2294	grad_norm 12.9776 (14.1742)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:16:31 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [136/200][30/54]	eta 0:00:26 lr 0.000004	time 1.1002 (1.1052)	loss 5.2712 (6.4668)	miou 0.2479	grad_norm 11.9556 (14.1669)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:16:43 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [136/200][40/54]	eta 0:00:15 lr 0.000001	time 1.4143 (1.1305)	loss 4.6790 (6.1367)	miou 0.2644	grad_norm 13.3281 (14.2634)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:16:54 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [136/200][50/54]	eta 0:00:04 lr 0.000000	time 0.9477 (1.1258)	loss 7.9575 (6.0646)	miou 0.2692	grad_norm 9.8319 (13.5497)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:16:58 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 136 training takes 0:01:01
[32m[2025-07-12 04:16:58 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 04:17:08 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1708%
[32m[2025-07-12 04:17:08 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 04:17:10 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [137/200][0/54]	eta 0:01:25 lr 0.000000	time 1.5823 (1.5823)	loss 8.4579 (8.4579)	miou 0.1824	grad_norm 8.1831 (8.1831)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:17:22 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [137/200][10/54]	eta 0:00:54 lr 0.000000	time 0.9976 (1.2291)	loss 6.2778 (5.9992)	miou 0.2321	grad_norm 10.7210 (14.0186)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:17:33 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [137/200][20/54]	eta 0:00:39 lr 0.000002	time 1.0212 (1.1756)	loss 6.1797 (6.1641)	miou 0.2575	grad_norm 8.1084 (14.6206)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:17:45 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [137/200][30/54]	eta 0:00:28 lr 0.000005	time 1.2254 (1.1709)	loss 7.8319 (6.1980)	miou 0.2708	grad_norm 12.7439 (14.3657)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:17:55 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [137/200][40/54]	eta 0:00:16 lr 0.000009	time 1.0590 (1.1478)	loss 4.5407 (6.1110)	miou 0.2774	grad_norm 9.1469 (15.1402)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:18:06 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [137/200][50/54]	eta 0:00:04 lr 0.000014	time 0.8445 (1.1309)	loss 5.0142 (6.0274)	miou 0.2831	grad_norm 12.1230 (14.2902)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:18:09 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 137 training takes 0:01:00
[32m[2025-07-12 04:18:09 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 04:18:19 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1796%
[32m[2025-07-12 04:18:19 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 04:18:21 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [138/200][0/54]	eta 0:01:13 lr 0.000016	time 1.3698 (1.3698)	loss 4.5409 (4.5409)	miou 0.1912	grad_norm 8.3300 (8.3300)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:18:32 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [138/200][10/54]	eta 0:00:49 lr 0.000023	time 1.2810 (1.1268)	loss 6.8663 (6.0033)	miou 0.2570	grad_norm 23.9124 (11.3748)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:18:43 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [138/200][20/54]	eta 0:00:38 lr 0.000029	time 1.1855 (1.1318)	loss 6.3159 (5.9179)	miou 0.2723	grad_norm 10.8638 (11.3572)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:18:55 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [138/200][30/54]	eta 0:00:27 lr 0.000037	time 1.0042 (1.1514)	loss 4.8839 (5.9678)	miou 0.2785	grad_norm 17.3203 (15.7853)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:19:06 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [138/200][40/54]	eta 0:00:15 lr 0.000045	time 1.0789 (1.1375)	loss 4.2295 (5.9217)	miou 0.2889	grad_norm 13.8450 (15.7405)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:19:17 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [138/200][50/54]	eta 0:00:04 lr 0.000052	time 1.1236 (1.1250)	loss 7.5134 (5.9783)	miou 0.2870	grad_norm 9.2703 (15.5104)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:19:20 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 138 training takes 0:01:00
[32m[2025-07-12 04:19:20 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 04:19:30 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1485%
[32m[2025-07-12 04:19:30 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 04:19:32 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [139/200][0/54]	eta 0:01:46 lr 0.000055	time 1.9742 (1.9742)	loss 6.9639 (6.9639)	miou 0.1617	grad_norm 10.6003 (10.6003)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:19:43 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [139/200][10/54]	eta 0:00:50 lr 0.000063	time 0.9495 (1.1495)	loss 9.9665 (6.0677)	miou 0.2014	grad_norm 13.6128 (14.0131)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:19:54 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [139/200][20/54]	eta 0:00:39 lr 0.000071	time 1.1159 (1.1640)	loss 4.6218 (6.2873)	miou 0.2308	grad_norm 16.8373 (12.6574)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:20:06 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [139/200][30/54]	eta 0:00:27 lr 0.000077	time 1.4290 (1.1575)	loss 5.5210 (6.4526)	miou 0.2396	grad_norm 11.3612 (13.4866)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:20:16 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [139/200][40/54]	eta 0:00:15 lr 0.000084	time 1.1443 (1.1283)	loss 5.1419 (6.2011)	miou 0.2586	grad_norm 5.3118 (13.6006)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:20:27 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [139/200][50/54]	eta 0:00:04 lr 0.000089	time 0.9091 (1.1161)	loss 7.0395 (5.9954)	miou 0.2737	grad_norm 15.9231 (13.5615)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:20:31 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 139 training takes 0:01:00
[32m[2025-07-12 04:20:31 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 04:20:41 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2293%
[32m[2025-07-12 04:20:41 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 04:20:42 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [140/200][0/54]	eta 0:01:03 lr 0.000091	time 1.1718 (1.1718)	loss 4.2111 (4.2111)	miou 0.2312	grad_norm 16.8014 (16.8014)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:20:53 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [140/200][10/54]	eta 0:00:47 lr 0.000095	time 0.9994 (1.0851)	loss 5.1415 (5.7002)	miou 0.2633	grad_norm 18.8868 (15.5820)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:21:04 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [140/200][20/54]	eta 0:00:37 lr 0.000098	time 1.1103 (1.1017)	loss 6.6648 (6.0475)	miou 0.2719	grad_norm 12.6899 (14.6671)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:21:15 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [140/200][30/54]	eta 0:00:26 lr 0.000100	time 1.0998 (1.1001)	loss 6.6530 (6.1176)	miou 0.2824	grad_norm 17.2877 (16.2335)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:21:27 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [140/200][40/54]	eta 0:00:15 lr 0.000100	time 1.0900 (1.1275)	loss 5.5217 (6.1661)	miou 0.2800	grad_norm 22.8887 (16.2846)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:21:38 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [140/200][50/54]	eta 0:00:04 lr 0.000099	time 1.4340 (1.1215)	loss 5.9493 (6.0750)	miou 0.2838	grad_norm 12.7926 (15.8741)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:21:42 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 140 training takes 0:01:00
[32m[2025-07-12 04:21:42 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 04:21:52 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1995%
[32m[2025-07-12 04:21:52 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 04:21:53 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [141/200][0/54]	eta 0:01:24 lr 0.000099	time 1.5651 (1.5651)	loss 5.4521 (5.4521)	miou 0.2048	grad_norm 10.5969 (10.5969)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:22:04 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [141/200][10/54]	eta 0:00:49 lr 0.000096	time 1.0618 (1.1195)	loss 5.0352 (6.1650)	miou 0.2374	grad_norm 10.4245 (15.8217)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:22:15 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [141/200][20/54]	eta 0:00:37 lr 0.000093	time 1.0857 (1.1105)	loss 7.8650 (6.1102)	miou 0.2549	grad_norm 7.1816 (14.1733)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:22:26 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [141/200][30/54]	eta 0:00:26 lr 0.000088	time 1.2783 (1.1186)	loss 5.7476 (6.2673)	miou 0.2631	grad_norm 10.3692 (14.2384)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:22:38 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [141/200][40/54]	eta 0:00:15 lr 0.000082	time 0.9198 (1.1424)	loss 7.1306 (6.2891)	miou 0.2770	grad_norm 13.8259 (15.4744)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:22:49 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [141/200][50/54]	eta 0:00:04 lr 0.000076	time 1.0236 (1.1237)	loss 5.3724 (6.2140)	miou 0.2821	grad_norm 15.9992 (14.8594)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:22:53 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 141 training takes 0:01:01
[32m[2025-07-12 04:22:53 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 04:23:03 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1322%
[32m[2025-07-12 04:23:03 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 04:23:05 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [142/200][0/54]	eta 0:01:32 lr 0.000073	time 1.7117 (1.7117)	loss 5.8763 (5.8763)	miou 0.1384	grad_norm 8.7788 (8.7788)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:23:16 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [142/200][10/54]	eta 0:00:52 lr 0.000066	time 0.9847 (1.1934)	loss 4.9841 (5.0562)	miou 0.2302	grad_norm 15.1867 (14.0443)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:23:28 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [142/200][20/54]	eta 0:00:40 lr 0.000059	time 1.1586 (1.1826)	loss 5.0301 (5.6859)	miou 0.2513	grad_norm 6.1024 (13.3870)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:23:39 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [142/200][30/54]	eta 0:00:28 lr 0.000051	time 1.1119 (1.1741)	loss 7.5903 (5.7628)	miou 0.2641	grad_norm 6.8766 (14.6651)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:23:50 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [142/200][40/54]	eta 0:00:15 lr 0.000043	time 1.2588 (1.1405)	loss 6.4803 (6.0067)	miou 0.2647	grad_norm 8.0293 (14.6733)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:24:01 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [142/200][50/54]	eta 0:00:04 lr 0.000035	time 1.2917 (1.1382)	loss 5.8098 (6.0365)	miou 0.2672	grad_norm 22.4638 (14.8212)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:24:04 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 142 training takes 0:01:01
[32m[2025-07-12 04:24:04 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 04:24:14 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1730%
[32m[2025-07-12 04:24:14 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 04:24:16 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [143/200][0/54]	eta 0:01:10 lr 0.000032	time 1.2998 (1.2998)	loss 5.1682 (5.1682)	miou 0.1784	grad_norm 8.9126 (8.9126)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:24:26 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [143/200][10/54]	eta 0:00:47 lr 0.000025	time 1.0101 (1.0820)	loss 8.3715 (6.8454)	miou 0.2325	grad_norm 15.6946 (13.2630)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:24:37 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [143/200][20/54]	eta 0:00:37 lr 0.000019	time 0.8174 (1.0964)	loss 4.0153 (6.8513)	miou 0.2410	grad_norm 16.1998 (13.5112)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:24:49 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [143/200][30/54]	eta 0:00:26 lr 0.000013	time 1.0413 (1.1225)	loss 3.8147 (6.6334)	miou 0.2618	grad_norm 18.8148 (13.3191)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:25:01 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [143/200][40/54]	eta 0:00:15 lr 0.000008	time 1.2055 (1.1279)	loss 7.1958 (6.5301)	miou 0.2750	grad_norm 10.2804 (14.4458)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:25:12 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [143/200][50/54]	eta 0:00:04 lr 0.000004	time 1.2494 (1.1377)	loss 8.2821 (6.4789)	miou 0.2765	grad_norm 8.9304 (15.9327)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:25:16 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 143 training takes 0:01:01
[32m[2025-07-12 04:25:16 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 04:25:26 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2146%
[32m[2025-07-12 04:25:26 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 04:25:27 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [144/200][0/54]	eta 0:01:00 lr 0.000003	time 1.1145 (1.1145)	loss 6.4505 (6.4505)	miou 0.2315	grad_norm 29.9635 (29.9635)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:25:38 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [144/200][10/54]	eta 0:00:47 lr 0.000001	time 0.9585 (1.0882)	loss 5.0138 (6.2051)	miou 0.2504	grad_norm 10.2052 (14.9592)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:25:48 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [144/200][20/54]	eta 0:00:36 lr 0.000000	time 0.9577 (1.0810)	loss 5.9209 (5.9338)	miou 0.2739	grad_norm 20.2287 (15.0771)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:26:00 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [144/200][30/54]	eta 0:00:26 lr 0.000000	time 1.0253 (1.1060)	loss 6.8492 (5.9388)	miou 0.2898	grad_norm 18.2484 (16.7808)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:26:11 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [144/200][40/54]	eta 0:00:15 lr 0.000002	time 1.2058 (1.1101)	loss 3.2593 (5.8167)	miou 0.2903	grad_norm 21.9824 (16.2639)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:26:23 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [144/200][50/54]	eta 0:00:04 lr 0.000004	time 1.1563 (1.1162)	loss 6.8271 (5.8391)	miou 0.2855	grad_norm 6.5542 (15.2312)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:26:27 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 144 training takes 0:01:00
[32m[2025-07-12 04:26:27 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 04:26:37 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2363%
[32m[2025-07-12 04:26:37 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 04:26:38 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [145/200][0/54]	eta 0:01:07 lr 0.000006	time 1.2558 (1.2558)	loss 5.7382 (5.7382)	miou 0.2504	grad_norm 7.6214 (7.6214)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:26:50 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [145/200][10/54]	eta 0:00:51 lr 0.000010	time 1.2035 (1.1761)	loss 7.0977 (6.1602)	miou 0.2728	grad_norm 14.3840 (11.3126)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:27:01 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [145/200][20/54]	eta 0:00:39 lr 0.000015	time 1.1168 (1.1584)	loss 5.3681 (6.0449)	miou 0.2873	grad_norm 25.6687 (13.5112)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:27:12 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [145/200][30/54]	eta 0:00:27 lr 0.000021	time 1.0563 (1.1311)	loss 7.4935 (6.3130)	miou 0.2947	grad_norm 15.5911 (15.0234)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:27:23 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [145/200][40/54]	eta 0:00:15 lr 0.000028	time 1.0546 (1.1234)	loss 7.2154 (6.3077)	miou 0.3007	grad_norm 8.7841 (14.2985)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:27:34 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [145/200][50/54]	eta 0:00:04 lr 0.000035	time 1.0444 (1.1171)	loss 4.9270 (6.2156)	miou 0.3050	grad_norm 16.1787 (13.8200)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:27:37 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 145 training takes 0:01:00
[32m[2025-07-12 04:27:37 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 04:27:47 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1790%
[32m[2025-07-12 04:27:47 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 04:27:49 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [146/200][0/54]	eta 0:01:21 lr 0.000038	time 1.5034 (1.5034)	loss 7.3228 (7.3228)	miou 0.1823	grad_norm 9.2808 (9.2808)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:28:00 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [146/200][10/54]	eta 0:00:49 lr 0.000046	time 1.0016 (1.1300)	loss 6.2839 (6.3864)	miou 0.2378	grad_norm 12.4492 (15.6412)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:28:10 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [146/200][20/54]	eta 0:00:36 lr 0.000054	time 1.1085 (1.0833)	loss 5.5104 (5.9013)	miou 0.2416	grad_norm 12.1943 (32.3675)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:28:22 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [146/200][30/54]	eta 0:00:26 lr 0.000062	time 1.1781 (1.1120)	loss 4.8445 (6.0932)	miou 0.2533	grad_norm 9.4676 (27.5684)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:28:33 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [146/200][40/54]	eta 0:00:15 lr 0.000069	time 1.0521 (1.1171)	loss 3.8717 (6.2539)	miou 0.2603	grad_norm 14.9293 (23.7012)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:28:43 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [146/200][50/54]	eta 0:00:04 lr 0.000076	time 0.9102 (1.1013)	loss 3.3696 (6.0210)	miou 0.2739	grad_norm 31.8403 (22.0262)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:28:47 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 146 training takes 0:00:59
[32m[2025-07-12 04:28:47 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 04:28:57 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1987%
[32m[2025-07-12 04:28:57 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 04:28:59 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [147/200][0/54]	eta 0:01:15 lr 0.000079	time 1.4053 (1.4053)	loss 4.6182 (4.6182)	miou 0.2047	grad_norm 21.2183 (21.2183)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:29:09 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [147/200][10/54]	eta 0:00:49 lr 0.000085	time 0.9297 (1.1150)	loss 4.6661 (6.1001)	miou 0.2396	grad_norm 12.1015 (15.9608)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:29:20 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [147/200][20/54]	eta 0:00:37 lr 0.000090	time 1.5252 (1.0960)	loss 4.8520 (5.7672)	miou 0.2672	grad_norm 14.4774 (14.7350)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:29:31 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [147/200][30/54]	eta 0:00:26 lr 0.000094	time 0.8999 (1.0878)	loss 3.9677 (5.8200)	miou 0.2747	grad_norm 19.6633 (14.7641)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:29:42 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [147/200][40/54]	eta 0:00:15 lr 0.000097	time 1.0320 (1.1045)	loss 4.5109 (5.8820)	miou 0.2781	grad_norm 15.7522 (15.0711)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:29:54 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [147/200][50/54]	eta 0:00:04 lr 0.000099	time 0.8836 (1.1115)	loss 6.1191 (5.9781)	miou 0.2781	grad_norm 10.9380 (14.8172)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:29:58 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 147 training takes 0:01:00
[32m[2025-07-12 04:29:58 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 04:30:08 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1551%
[32m[2025-07-12 04:30:08 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 04:30:09 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [148/200][0/54]	eta 0:01:14 lr 0.000100	time 1.3844 (1.3844)	loss 6.6862 (6.6862)	miou 0.1777	grad_norm 9.6723 (9.6723)	loss_scale 524288.0000 (524288.0000)	mem 3265MB
[32m[2025-07-12 04:30:20 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [148/200][10/54]	eta 0:00:48 lr 0.000100	time 0.9927 (1.0958)	loss 4.2658 (5.6722)	miou 0.2352	grad_norm 12.2043 (17.1894)	loss_scale 1048576.0000 (714938.1818)	mem 3265MB
[32m[2025-07-12 04:30:31 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [148/200][20/54]	eta 0:00:37 lr 0.000099	time 1.2231 (1.1127)	loss 3.7873 (5.6040)	miou 0.2451	grad_norm 19.1835 (15.7774)	loss_scale 1048576.0000 (873813.3333)	mem 3265MB
[32m[2025-07-12 04:30:43 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [148/200][30/54]	eta 0:00:27 lr 0.000097	time 1.1565 (1.1337)	loss 8.4259 (5.9647)	miou 0.2518	grad_norm 8.6217 (15.7085)	loss_scale 1048576.0000 (930188.3871)	mem 3265MB
[32m[2025-07-12 04:30:53 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [148/200][40/54]	eta 0:00:15 lr 0.000093	time 1.0520 (1.1174)	loss 5.0572 (5.9694)	miou 0.2653	grad_norm 12.3217 (14.9945)	loss_scale 1048576.0000 (959063.4146)	mem 3265MB
[32m[2025-07-12 04:31:05 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [148/200][50/54]	eta 0:00:04 lr 0.000089	time 0.9945 (1.1195)	loss 4.4562 (5.9000)	miou 0.2701	grad_norm 10.4435 (14.5544)	loss_scale 1048576.0000 (976614.9020)	mem 3265MB
[32m[2025-07-12 04:31:08 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 148 training takes 0:01:00
[32m[2025-07-12 04:31:08 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 04:31:18 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1787%
[32m[2025-07-12 04:31:18 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 04:31:19 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [149/200][0/54]	eta 0:00:57 lr 0.000087	time 1.0658 (1.0658)	loss 4.7394 (4.7394)	miou 0.1929	grad_norm 9.7124 (9.7124)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:31:31 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [149/200][10/54]	eta 0:00:49 lr 0.000081	time 1.0492 (1.1251)	loss 3.2542 (6.2497)	miou 0.2165	grad_norm 19.9417 (14.7623)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:31:42 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [149/200][20/54]	eta 0:00:38 lr 0.000075	time 1.1134 (1.1365)	loss 4.5781 (6.0210)	miou 0.2607	grad_norm 8.8964 (13.8919)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:31:54 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [149/200][30/54]	eta 0:00:27 lr 0.000068	time 1.0929 (1.1501)	loss 5.0196 (5.9112)	miou 0.2695	grad_norm 10.4915 (13.6235)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:32:05 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [149/200][40/54]	eta 0:00:15 lr 0.000060	time 1.4988 (1.1390)	loss 4.9249 (5.9770)	miou 0.2698	grad_norm 11.1355 (15.9578)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:32:16 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [149/200][50/54]	eta 0:00:04 lr 0.000052	time 0.8000 (1.1298)	loss 5.0737 (6.0044)	miou 0.2742	grad_norm 11.3655 (15.1954)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:32:19 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 149 training takes 0:01:01
[32m[2025-07-12 04:32:19 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 04:32:29 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2583%
[32m[2025-07-12 04:32:29 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2619%
[32m[2025-07-12 04:32:31 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [150/200][0/54]	eta 0:01:50 lr 0.000049	time 2.0375 (2.0375)	loss 7.0681 (7.0681)	miou 0.2595	grad_norm 10.6059 (10.6059)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:32:42 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [150/200][10/54]	eta 0:00:52 lr 0.000041	time 0.9604 (1.1824)	loss 5.7008 (6.0509)	miou 0.2788	grad_norm 20.8979 (11.2807)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:32:54 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [150/200][20/54]	eta 0:00:39 lr 0.000034	time 1.2824 (1.1543)	loss 6.7064 (6.0959)	miou 0.2864	grad_norm 41.5799 (13.4354)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:33:05 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [150/200][30/54]	eta 0:00:27 lr 0.000027	time 1.4385 (1.1496)	loss 3.4596 (6.1904)	miou 0.2860	grad_norm 23.2060 (13.1812)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:33:16 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [150/200][40/54]	eta 0:00:15 lr 0.000020	time 0.9100 (1.1382)	loss 6.8264 (6.2010)	miou 0.2871	grad_norm 15.5794 (14.2369)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:33:28 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [150/200][50/54]	eta 0:00:04 lr 0.000014	time 0.8902 (1.1406)	loss 3.1980 (6.0561)	miou 0.2835	grad_norm 21.5299 (13.9770)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:33:31 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 150 training takes 0:01:01
[32m[2025-07-12 04:33:31 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
150
[32m[2025-07-12 04:33:42 3DDETR.yaml][33m(utils_help.py 202)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saving......
[32m[2025-07-12 04:33:42 3DDETR.yaml][33m(utils_help.py 204)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saved !!!
[32m[2025-07-12 04:33:42 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2840%
[32m[2025-07-12 04:33:42 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2840%
[32m[2025-07-12 04:33:43 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [151/200][0/54]	eta 0:00:55 lr 0.000012	time 1.0253 (1.0253)	loss 8.6882 (8.6882)	miou 0.2802	grad_norm 20.9986 (20.9986)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:33:55 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [151/200][10/54]	eta 0:00:53 lr 0.000007	time 1.1780 (1.2248)	loss 4.6957 (6.2481)	miou 0.2860	grad_norm 13.4204 (16.5208)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:34:07 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [151/200][20/54]	eta 0:00:40 lr 0.000004	time 1.2417 (1.1835)	loss 6.0004 (6.0627)	miou 0.2940	grad_norm 6.3290 (14.0055)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:34:17 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [151/200][30/54]	eta 0:00:27 lr 0.000001	time 0.7830 (1.1338)	loss 7.9091 (6.0443)	miou 0.2995	grad_norm 14.0351 (13.2622)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:34:28 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [151/200][40/54]	eta 0:00:15 lr 0.000000	time 1.3943 (1.1367)	loss 4.7934 (6.0857)	miou 0.3080	grad_norm 15.0696 (13.0452)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:34:39 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [151/200][50/54]	eta 0:00:04 lr 0.000000	time 1.2281 (1.1255)	loss 5.1031 (6.0259)	miou 0.3113	grad_norm 28.6132 (13.0146)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:34:42 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 151 training takes 0:01:00
[32m[2025-07-12 04:34:42 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 04:34:53 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2574%
[32m[2025-07-12 04:34:53 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2840%
[32m[2025-07-12 04:34:54 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [152/200][0/54]	eta 0:01:30 lr 0.000000	time 1.6769 (1.6769)	loss 7.7228 (7.7228)	miou 0.2535	grad_norm 9.5260 (9.5260)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:35:06 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [152/200][10/54]	eta 0:00:52 lr 0.000002	time 1.1303 (1.1887)	loss 4.9585 (6.7083)	miou 0.2826	grad_norm 5.7350 (15.5159)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:35:17 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [152/200][20/54]	eta 0:00:39 lr 0.000005	time 0.9692 (1.1550)	loss 4.9039 (6.5041)	miou 0.2833	grad_norm 13.0422 (14.7250)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:35:28 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [152/200][30/54]	eta 0:00:27 lr 0.000009	time 1.3183 (1.1431)	loss 5.5099 (6.2921)	miou 0.2863	grad_norm 12.1656 (13.4865)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:35:39 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [152/200][40/54]	eta 0:00:15 lr 0.000014	time 1.1723 (1.1258)	loss 5.3741 (6.1787)	miou 0.2912	grad_norm 12.5176 (13.6830)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:35:50 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [152/200][50/54]	eta 0:00:04 lr 0.000020	time 1.2646 (1.1187)	loss 8.5197 (6.2037)	miou 0.2943	grad_norm 15.9932 (13.4371)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:35:54 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 152 training takes 0:01:01
[32m[2025-07-12 04:35:54 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 04:36:04 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2371%
[32m[2025-07-12 04:36:04 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2840%
[32m[2025-07-12 04:36:05 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [153/200][0/54]	eta 0:01:20 lr 0.000023	time 1.4870 (1.4870)	loss 5.2800 (5.2800)	miou 0.2422	grad_norm 13.9375 (13.9375)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:36:17 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [153/200][10/54]	eta 0:00:50 lr 0.000029	time 1.1608 (1.1570)	loss 4.9834 (6.2771)	miou 0.2699	grad_norm 7.1464 (12.2751)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:36:29 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [153/200][20/54]	eta 0:00:40 lr 0.000037	time 1.0889 (1.2055)	loss 4.7403 (5.9495)	miou 0.2802	grad_norm 6.6790 (14.0640)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:36:40 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [153/200][30/54]	eta 0:00:28 lr 0.000045	time 1.0652 (1.1790)	loss 4.7786 (6.2981)	miou 0.2960	grad_norm 8.3527 (13.5234)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:36:51 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [153/200][40/54]	eta 0:00:16 lr 0.000052	time 1.0678 (1.1438)	loss 4.9186 (6.3044)	miou 0.2930	grad_norm 6.7928 (13.4171)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:37:01 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [153/200][50/54]	eta 0:00:04 lr 0.000060	time 1.0068 (1.1224)	loss 4.5717 (6.1931)	miou 0.2935	grad_norm 10.5349 (13.1585)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:37:04 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 153 training takes 0:01:00
[32m[2025-07-12 04:37:04 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 04:37:14 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2616%
[32m[2025-07-12 04:37:14 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2840%
[32m[2025-07-12 04:37:16 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [154/200][0/54]	eta 0:01:03 lr 0.000063	time 1.1719 (1.1719)	loss 6.3611 (6.3611)	miou 0.2563	grad_norm 10.4629 (10.4629)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:37:26 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [154/200][10/54]	eta 0:00:47 lr 0.000071	time 0.8338 (1.0791)	loss 8.5066 (5.9211)	miou 0.2733	grad_norm 15.5673 (13.4816)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:37:39 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [154/200][20/54]	eta 0:00:39 lr 0.000077	time 1.5015 (1.1499)	loss 4.5364 (5.9070)	miou 0.3011	grad_norm 15.5835 (19.5814)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:37:49 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [154/200][30/54]	eta 0:00:26 lr 0.000084	time 0.9645 (1.1182)	loss 7.2301 (6.0934)	miou 0.3038	grad_norm 12.3290 (17.2577)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:38:00 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [154/200][40/54]	eta 0:00:15 lr 0.000089	time 1.1629 (1.1106)	loss 4.9589 (6.1235)	miou 0.2998	grad_norm 16.7613 (16.2962)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:38:11 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [154/200][50/54]	eta 0:00:04 lr 0.000093	time 1.0409 (1.1148)	loss 5.2224 (6.1661)	miou 0.2995	grad_norm 8.4935 (15.6991)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:38:15 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 154 training takes 0:01:00
[32m[2025-07-12 04:38:15 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 04:38:25 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2417%
[32m[2025-07-12 04:38:25 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2840%
[32m[2025-07-12 04:38:26 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [155/200][0/54]	eta 0:01:11 lr 0.000095	time 1.3311 (1.3311)	loss 4.7952 (4.7952)	miou 0.2435	grad_norm 12.9802 (12.9802)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:38:37 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [155/200][10/54]	eta 0:00:47 lr 0.000098	time 1.0419 (1.0868)	loss 5.6876 (6.5312)	miou 0.2608	grad_norm 12.0121 (14.5769)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:38:48 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [155/200][20/54]	eta 0:00:36 lr 0.000100	time 1.0633 (1.0877)	loss 7.9231 (6.3898)	miou 0.2674	grad_norm 10.1383 (15.4144)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:38:59 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [155/200][30/54]	eta 0:00:26 lr 0.000100	time 1.1148 (1.1150)	loss 8.5693 (6.5128)	miou 0.2623	grad_norm 15.6932 (14.7918)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:39:11 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [155/200][40/54]	eta 0:00:15 lr 0.000099	time 0.8615 (1.1236)	loss 4.1845 (6.3554)	miou 0.2728	grad_norm 12.2762 (14.1068)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:39:22 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [155/200][50/54]	eta 0:00:04 lr 0.000097	time 1.0290 (1.1161)	loss 7.5874 (6.3347)	miou 0.2860	grad_norm 14.5184 (13.9874)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:39:25 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 155 training takes 0:01:00
[32m[2025-07-12 04:39:25 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 04:39:36 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2067%
[32m[2025-07-12 04:39:36 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2840%
[32m[2025-07-12 04:39:37 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [156/200][0/54]	eta 0:00:52 lr 0.000096	time 0.9787 (0.9787)	loss 4.6783 (4.6783)	miou 0.2161	grad_norm 12.4227 (12.4227)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:39:48 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [156/200][10/54]	eta 0:00:48 lr 0.000093	time 1.1737 (1.1016)	loss 7.6736 (5.6918)	miou 0.2504	grad_norm 15.9068 (20.3289)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:39:59 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [156/200][20/54]	eta 0:00:37 lr 0.000088	time 1.0275 (1.0907)	loss 5.3708 (5.5941)	miou 0.2751	grad_norm 16.9772 (17.1795)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:40:11 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [156/200][30/54]	eta 0:00:27 lr 0.000082	time 0.9654 (1.1270)	loss 5.2201 (5.8647)	miou 0.2755	grad_norm 16.4395 (16.6045)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:40:22 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [156/200][40/54]	eta 0:00:15 lr 0.000076	time 1.0381 (1.1280)	loss 7.1036 (5.7803)	miou 0.2776	grad_norm 15.8225 (16.4695)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:40:34 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [156/200][50/54]	eta 0:00:04 lr 0.000069	time 1.1738 (1.1333)	loss 8.0038 (5.8162)	miou 0.2788	grad_norm 10.7202 (16.0357)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:40:37 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 156 training takes 0:01:00
[32m[2025-07-12 04:40:37 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 04:40:47 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1975%
[32m[2025-07-12 04:40:47 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2840%
[32m[2025-07-12 04:40:48 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [157/200][0/54]	eta 0:01:08 lr 0.000066	time 1.2634 (1.2634)	loss 5.7104 (5.7104)	miou 0.1979	grad_norm 12.7111 (12.7111)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:41:00 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [157/200][10/54]	eta 0:00:52 lr 0.000059	time 1.2682 (1.1859)	loss 5.1329 (6.1105)	miou 0.2400	grad_norm 40.2765 (19.1188)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:41:11 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [157/200][20/54]	eta 0:00:38 lr 0.000051	time 1.1054 (1.1445)	loss 6.8364 (5.8784)	miou 0.2649	grad_norm 9.2686 (16.1033)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:41:21 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [157/200][30/54]	eta 0:00:26 lr 0.000043	time 1.3059 (1.1158)	loss 8.2802 (6.1981)	miou 0.2618	grad_norm 12.3814 (16.2434)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:41:33 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [157/200][40/54]	eta 0:00:15 lr 0.000035	time 1.1815 (1.1255)	loss 7.0832 (6.0792)	miou 0.2618	grad_norm 24.3511 (15.5493)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:41:45 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [157/200][50/54]	eta 0:00:04 lr 0.000028	time 1.1389 (1.1358)	loss 6.6378 (5.9787)	miou 0.2730	grad_norm 22.7133 (15.3287)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:41:48 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 157 training takes 0:01:01
[32m[2025-07-12 04:41:48 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 04:41:58 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2460%
[32m[2025-07-12 04:41:58 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2840%
[32m[2025-07-12 04:42:00 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [158/200][0/54]	eta 0:01:41 lr 0.000025	time 1.8869 (1.8869)	loss 4.5816 (4.5816)	miou 0.2534	grad_norm 7.3625 (7.3625)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:42:12 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [158/200][10/54]	eta 0:00:53 lr 0.000019	time 1.0166 (1.2253)	loss 4.8062 (5.6826)	miou 0.2937	grad_norm 25.2462 (12.8319)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:42:22 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [158/200][20/54]	eta 0:00:39 lr 0.000013	time 0.9052 (1.1489)	loss 5.1297 (5.8108)	miou 0.2885	grad_norm 8.7560 (12.9823)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:42:32 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [158/200][30/54]	eta 0:00:26 lr 0.000008	time 1.0347 (1.1064)	loss 4.4378 (5.8472)	miou 0.2931	grad_norm 18.8909 (12.5944)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:42:44 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [158/200][40/54]	eta 0:00:15 lr 0.000004	time 1.4075 (1.1191)	loss 7.5283 (5.8250)	miou 0.2958	grad_norm 15.5194 (13.0440)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:42:56 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [158/200][50/54]	eta 0:00:04 lr 0.000002	time 1.2314 (1.1376)	loss 8.5430 (6.0354)	miou 0.2973	grad_norm 7.8950 (13.4609)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:43:00 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 158 training takes 0:01:01
[32m[2025-07-12 04:43:00 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 04:43:10 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2165%
[32m[2025-07-12 04:43:10 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2840%
[32m[2025-07-12 04:43:12 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [159/200][0/54]	eta 0:01:20 lr 0.000001	time 1.5000 (1.5000)	loss 4.6865 (4.6865)	miou 0.2307	grad_norm 9.3480 (9.3480)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:43:22 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [159/200][10/54]	eta 0:00:49 lr 0.000000	time 1.0853 (1.1192)	loss 4.9109 (5.7708)	miou 0.2530	grad_norm 18.0636 (16.7307)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:43:33 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [159/200][20/54]	eta 0:00:36 lr 0.000000	time 1.1060 (1.0800)	loss 5.7013 (5.9974)	miou 0.2690	grad_norm 8.1124 (14.1676)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:43:44 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [159/200][30/54]	eta 0:00:25 lr 0.000002	time 1.2429 (1.0798)	loss 5.6639 (5.8824)	miou 0.2812	grad_norm 9.0816 (13.7026)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:43:55 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [159/200][40/54]	eta 0:00:15 lr 0.000004	time 1.4566 (1.0953)	loss 7.4124 (6.0455)	miou 0.2858	grad_norm 12.6954 (13.0958)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:44:07 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [159/200][50/54]	eta 0:00:04 lr 0.000008	time 1.3204 (1.1172)	loss 6.5510 (6.1006)	miou 0.2881	grad_norm 13.8332 (13.7547)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:44:10 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 159 training takes 0:01:00
[32m[2025-07-12 04:44:10 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 04:44:21 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1503%
[32m[2025-07-12 04:44:21 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2840%
[32m[2025-07-12 04:44:22 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [160/200][0/54]	eta 0:01:16 lr 0.000010	time 1.4188 (1.4188)	loss 7.4601 (7.4601)	miou 0.1560	grad_norm 6.3158 (6.3158)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:44:34 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [160/200][10/54]	eta 0:00:52 lr 0.000015	time 1.2206 (1.1929)	loss 6.5259 (5.9725)	miou 0.2151	grad_norm 19.5733 (12.2253)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:44:46 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [160/200][20/54]	eta 0:00:40 lr 0.000021	time 1.1361 (1.2036)	loss 5.2251 (5.9699)	miou 0.2315	grad_norm 12.4543 (31.4045)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:44:56 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [160/200][30/54]	eta 0:00:27 lr 0.000028	time 1.0196 (1.1492)	loss 5.1248 (5.8336)	miou 0.2514	grad_norm 10.7193 (24.5070)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:45:08 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [160/200][40/54]	eta 0:00:16 lr 0.000035	time 1.0530 (1.1453)	loss 7.0461 (5.9212)	miou 0.2708	grad_norm 8.3539 (22.0946)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:45:19 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [160/200][50/54]	eta 0:00:04 lr 0.000043	time 1.1674 (1.1389)	loss 7.5613 (6.0167)	miou 0.2736	grad_norm 16.0007 (20.5286)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:45:22 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 160 training takes 0:01:01
[32m[2025-07-12 04:45:22 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 04:45:32 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1582%
[32m[2025-07-12 04:45:32 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2840%
[32m[2025-07-12 04:45:33 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [161/200][0/54]	eta 0:01:07 lr 0.000046	time 1.2476 (1.2476)	loss 5.1752 (5.1752)	miou 0.1683	grad_norm 10.3184 (10.3184)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:45:45 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [161/200][10/54]	eta 0:00:51 lr 0.000054	time 1.0747 (1.1684)	loss 7.4469 (5.8731)	miou 0.1940	grad_norm 7.2430 (15.2369)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:45:56 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [161/200][20/54]	eta 0:00:38 lr 0.000062	time 1.1216 (1.1289)	loss 4.1469 (5.6946)	miou 0.2323	grad_norm 13.1287 (14.3940)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:46:07 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [161/200][30/54]	eta 0:00:26 lr 0.000069	time 0.9513 (1.1185)	loss 9.0507 (5.7189)	miou 0.2581	grad_norm 21.0644 (14.0481)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:46:19 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [161/200][40/54]	eta 0:00:15 lr 0.000076	time 0.9758 (1.1346)	loss 5.5883 (5.8958)	miou 0.2655	grad_norm 10.1583 (15.1380)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:46:30 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [161/200][50/54]	eta 0:00:04 lr 0.000082	time 1.1551 (1.1404)	loss 4.9170 (6.0339)	miou 0.2736	grad_norm 16.9234 (14.9424)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:46:34 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 161 training takes 0:01:01
[32m[2025-07-12 04:46:34 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 04:46:44 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2250%
[32m[2025-07-12 04:46:44 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2840%
[32m[2025-07-12 04:46:45 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [162/200][0/54]	eta 0:01:18 lr 0.000085	time 1.4565 (1.4565)	loss 7.8082 (7.8082)	miou 0.2227	grad_norm 12.0134 (12.0134)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:46:56 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [162/200][10/54]	eta 0:00:49 lr 0.000090	time 1.2451 (1.1314)	loss 8.4378 (6.2537)	miou 0.2651	grad_norm 10.9413 (10.3599)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:47:07 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [162/200][20/54]	eta 0:00:37 lr 0.000094	time 1.0410 (1.1051)	loss 4.4926 (5.8600)	miou 0.2679	grad_norm 11.0115 (12.6167)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:47:17 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [162/200][30/54]	eta 0:00:25 lr 0.000097	time 1.0673 (1.0585)	loss 5.9998 (5.9274)	miou 0.2717	grad_norm 13.6145 (13.7222)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:47:28 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [162/200][40/54]	eta 0:00:14 lr 0.000099	time 1.2370 (1.0671)	loss 3.2553 (5.8530)	miou 0.2790	grad_norm 23.4969 (14.2082)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:47:40 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [162/200][50/54]	eta 0:00:04 lr 0.000100	time 0.9963 (1.1038)	loss 5.1859 (5.9027)	miou 0.2797	grad_norm 11.9986 (14.9143)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:47:44 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 162 training takes 0:00:59
[32m[2025-07-12 04:47:44 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 04:47:54 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1703%
[32m[2025-07-12 04:47:54 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2840%
[32m[2025-07-12 04:47:55 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [163/200][0/54]	eta 0:01:20 lr 0.000100	time 1.4882 (1.4882)	loss 5.3389 (5.3389)	miou 0.1805	grad_norm 9.6484 (9.6484)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:48:06 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [163/200][10/54]	eta 0:00:49 lr 0.000099	time 1.1374 (1.1213)	loss 7.8358 (5.8598)	miou 0.2372	grad_norm 13.2936 (10.3118)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:48:18 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [163/200][20/54]	eta 0:00:38 lr 0.000097	time 1.0055 (1.1271)	loss 4.4557 (5.5886)	miou 0.2552	grad_norm 12.7827 (12.8880)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:48:29 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [163/200][30/54]	eta 0:00:26 lr 0.000093	time 0.7750 (1.1182)	loss 5.0594 (5.7941)	miou 0.2685	grad_norm 12.1461 (12.3230)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:48:40 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [163/200][40/54]	eta 0:00:15 lr 0.000089	time 0.8801 (1.1183)	loss 6.5252 (5.8274)	miou 0.2711	grad_norm 10.7646 (13.1621)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:48:51 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [163/200][50/54]	eta 0:00:04 lr 0.000084	time 1.0556 (1.1229)	loss 4.9178 (5.8996)	miou 0.2788	grad_norm 6.6705 (13.4262)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:48:55 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 163 training takes 0:01:00
[32m[2025-07-12 04:48:55 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 04:49:05 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2550%
[32m[2025-07-12 04:49:05 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2840%
[32m[2025-07-12 04:49:06 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [164/200][0/54]	eta 0:01:09 lr 0.000081	time 1.2931 (1.2931)	loss 4.7510 (4.7510)	miou 0.2585	grad_norm 14.0528 (14.0528)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:49:17 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [164/200][10/54]	eta 0:00:49 lr 0.000075	time 1.1000 (1.1191)	loss 6.3989 (5.7889)	miou 0.3008	grad_norm 6.5340 (11.8141)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:49:28 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [164/200][20/54]	eta 0:00:37 lr 0.000068	time 1.2968 (1.1107)	loss 5.7479 (5.8941)	miou 0.3080	grad_norm 12.5164 (13.2341)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:49:39 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [164/200][30/54]	eta 0:00:26 lr 0.000060	time 0.9145 (1.1133)	loss 5.9453 (6.1076)	miou 0.2908	grad_norm 13.4684 (17.5000)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:49:50 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [164/200][40/54]	eta 0:00:15 lr 0.000052	time 1.5134 (1.1137)	loss 6.6777 (6.0043)	miou 0.2960	grad_norm 11.0628 (16.8738)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:50:02 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [164/200][50/54]	eta 0:00:04 lr 0.000045	time 1.2351 (1.1174)	loss 6.1928 (5.8951)	miou 0.3010	grad_norm 8.8317 (17.0076)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:50:05 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 164 training takes 0:01:00
[32m[2025-07-12 04:50:05 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 04:50:15 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2370%
[32m[2025-07-12 04:50:15 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2840%
[32m[2025-07-12 04:50:17 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [165/200][0/54]	eta 0:01:39 lr 0.000041	time 1.8513 (1.8513)	loss 4.3831 (4.3831)	miou 0.2436	grad_norm 11.7944 (11.7944)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:50:28 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [165/200][10/54]	eta 0:00:52 lr 0.000034	time 1.1469 (1.1946)	loss 2.7714 (5.6659)	miou 0.2574	grad_norm 28.5002 (12.6509)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:50:39 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [165/200][20/54]	eta 0:00:38 lr 0.000027	time 0.8833 (1.1277)	loss 4.4782 (6.0103)	miou 0.2603	grad_norm 14.5407 (13.2830)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:50:49 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [165/200][30/54]	eta 0:00:26 lr 0.000020	time 1.2289 (1.0964)	loss 5.5104 (6.0163)	miou 0.2626	grad_norm 13.6226 (12.7839)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:51:00 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [165/200][40/54]	eta 0:00:15 lr 0.000014	time 1.2745 (1.1050)	loss 7.6589 (5.9934)	miou 0.2661	grad_norm 8.3908 (12.3338)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:51:12 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [165/200][50/54]	eta 0:00:04 lr 0.000009	time 1.2558 (1.1216)	loss 6.8335 (5.9655)	miou 0.2795	grad_norm 24.2032 (13.3039)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:51:15 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 165 training takes 0:01:00
[32m[2025-07-12 04:51:15 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 04:51:25 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2231%
[32m[2025-07-12 04:51:25 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2840%
[32m[2025-07-12 04:51:27 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [166/200][0/54]	eta 0:01:08 lr 0.000007	time 1.2598 (1.2598)	loss 9.9127 (9.9127)	miou 0.2209	grad_norm 13.3965 (13.3965)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:51:38 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [166/200][10/54]	eta 0:00:50 lr 0.000004	time 0.9924 (1.1383)	loss 7.0386 (6.4823)	miou 0.2362	grad_norm 10.8114 (18.0884)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:51:49 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [166/200][20/54]	eta 0:00:37 lr 0.000001	time 0.9463 (1.1160)	loss 4.7762 (6.3972)	miou 0.2566	grad_norm 8.6834 (15.4424)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:52:00 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [166/200][30/54]	eta 0:00:26 lr 0.000000	time 1.1483 (1.1134)	loss 7.8172 (6.4666)	miou 0.2625	grad_norm 7.6927 (13.9227)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:52:11 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [166/200][40/54]	eta 0:00:15 lr 0.000000	time 1.1437 (1.1187)	loss 7.8444 (6.2815)	miou 0.2675	grad_norm 6.2746 (14.7978)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:52:23 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [166/200][50/54]	eta 0:00:04 lr 0.000001	time 1.2548 (1.1211)	loss 6.6322 (6.0868)	miou 0.2790	grad_norm 16.7722 (14.8431)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:52:26 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 166 training takes 0:01:00
[32m[2025-07-12 04:52:26 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 04:52:36 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1960%
[32m[2025-07-12 04:52:36 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2840%
[32m[2025-07-12 04:52:38 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [167/200][0/54]	eta 0:01:09 lr 0.000002	time 1.2808 (1.2808)	loss 4.6921 (4.6921)	miou 0.2002	grad_norm 12.2057 (12.2057)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:52:49 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [167/200][10/54]	eta 0:00:52 lr 0.000005	time 1.0415 (1.1906)	loss 4.9755 (5.4266)	miou 0.2412	grad_norm 15.5437 (11.7818)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:53:01 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [167/200][20/54]	eta 0:00:39 lr 0.000009	time 1.0160 (1.1703)	loss 4.6056 (6.0324)	miou 0.2617	grad_norm 11.0075 (11.6070)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:53:13 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [167/200][30/54]	eta 0:00:28 lr 0.000014	time 1.0304 (1.1715)	loss 5.0933 (6.1132)	miou 0.2768	grad_norm 8.3846 (12.6531)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:53:23 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [167/200][40/54]	eta 0:00:15 lr 0.000020	time 1.2564 (1.1341)	loss 4.6416 (6.0028)	miou 0.2819	grad_norm 13.0921 (13.6621)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:53:34 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [167/200][50/54]	eta 0:00:04 lr 0.000027	time 0.8936 (1.1309)	loss 5.4461 (5.9866)	miou 0.2844	grad_norm 17.5303 (13.3542)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:53:37 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 167 training takes 0:01:00
[32m[2025-07-12 04:53:37 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 04:53:47 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2284%
[32m[2025-07-12 04:53:47 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2840%
[32m[2025-07-12 04:53:48 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [168/200][0/54]	eta 0:01:02 lr 0.000029	time 1.1571 (1.1571)	loss 7.7041 (7.7041)	miou 0.2286	grad_norm 9.8258 (9.8258)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:53:59 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [168/200][10/54]	eta 0:00:49 lr 0.000037	time 1.2212 (1.1279)	loss 6.4747 (6.2415)	miou 0.2651	grad_norm 13.0035 (18.1298)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:54:10 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [168/200][20/54]	eta 0:00:37 lr 0.000045	time 1.1571 (1.1119)	loss 4.9221 (6.5758)	miou 0.2684	grad_norm 19.5628 (16.1690)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:54:21 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [168/200][30/54]	eta 0:00:26 lr 0.000052	time 1.1656 (1.0940)	loss 8.1966 (6.5500)	miou 0.2710	grad_norm 11.0156 (15.5832)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:54:32 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [168/200][40/54]	eta 0:00:15 lr 0.000060	time 1.0308 (1.1070)	loss 6.6579 (6.4202)	miou 0.2823	grad_norm 11.2933 (14.7643)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:54:44 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [168/200][50/54]	eta 0:00:04 lr 0.000068	time 1.0075 (1.1123)	loss 7.8756 (6.3232)	miou 0.2856	grad_norm 11.1423 (14.2921)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:54:47 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 168 training takes 0:01:00
[32m[2025-07-12 04:54:47 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 04:54:57 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2110%
[32m[2025-07-12 04:54:57 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2840%
[32m[2025-07-12 04:54:59 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [169/200][0/54]	eta 0:01:00 lr 0.000071	time 1.1194 (1.1194)	loss 5.2232 (5.2232)	miou 0.2133	grad_norm 18.0116 (18.0116)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:55:10 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [169/200][10/54]	eta 0:00:49 lr 0.000077	time 0.9965 (1.1297)	loss 5.5436 (5.9517)	miou 0.2255	grad_norm 5.5422 (15.5615)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:55:22 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [169/200][20/54]	eta 0:00:39 lr 0.000084	time 1.1665 (1.1471)	loss 4.4481 (5.8894)	miou 0.2283	grad_norm 15.3438 (14.3898)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:55:32 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [169/200][30/54]	eta 0:00:27 lr 0.000089	time 0.9236 (1.1289)	loss 4.4627 (5.9931)	miou 0.2495	grad_norm 8.2372 (13.8129)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:55:44 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [169/200][40/54]	eta 0:00:15 lr 0.000093	time 0.8032 (1.1249)	loss 8.4993 (6.2359)	miou 0.2593	grad_norm 17.2322 (13.7599)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:55:54 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [169/200][50/54]	eta 0:00:04 lr 0.000097	time 1.0733 (1.1088)	loss 3.9329 (6.0492)	miou 0.2741	grad_norm 15.0649 (14.4472)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:55:57 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 169 training takes 0:00:59
[32m[2025-07-12 04:55:57 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 04:56:07 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1817%
[32m[2025-07-12 04:56:07 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2840%
[32m[2025-07-12 04:56:09 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [170/200][0/54]	eta 0:01:10 lr 0.000098	time 1.2975 (1.2975)	loss 3.8748 (3.8748)	miou 0.1852	grad_norm 8.8410 (8.8410)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:56:20 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [170/200][10/54]	eta 0:00:50 lr 0.000100	time 0.9534 (1.1452)	loss 5.7605 (5.3098)	miou 0.2601	grad_norm 10.0991 (12.4405)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:56:31 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [170/200][20/54]	eta 0:00:37 lr 0.000100	time 1.5105 (1.1049)	loss 7.1135 (5.5469)	miou 0.2711	grad_norm 9.6562 (12.7593)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:56:43 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [170/200][30/54]	eta 0:00:27 lr 0.000099	time 1.4334 (1.1360)	loss 7.7096 (5.7358)	miou 0.2784	grad_norm 13.0031 (12.9858)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:56:53 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [170/200][40/54]	eta 0:00:15 lr 0.000097	time 1.0859 (1.1192)	loss 3.5531 (5.7800)	miou 0.2851	grad_norm 13.8930 (12.5593)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:57:04 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [170/200][50/54]	eta 0:00:04 lr 0.000094	time 0.9848 (1.1156)	loss 6.2580 (5.8758)	miou 0.2828	grad_norm 9.4161 (13.7306)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:57:07 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 170 training takes 0:00:59
[32m[2025-07-12 04:57:07 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 04:57:18 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1600%
[32m[2025-07-12 04:57:18 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2840%
[32m[2025-07-12 04:57:19 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [171/200][0/54]	eta 0:01:17 lr 0.000093	time 1.4375 (1.4375)	loss 4.6166 (4.6166)	miou 0.1764	grad_norm 11.6945 (11.6945)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:57:30 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [171/200][10/54]	eta 0:00:49 lr 0.000088	time 1.2754 (1.1139)	loss 5.7429 (5.4396)	miou 0.2417	grad_norm 8.7683 (16.1922)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:57:41 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [171/200][20/54]	eta 0:00:37 lr 0.000082	time 1.1358 (1.1016)	loss 5.2026 (5.3977)	miou 0.2668	grad_norm 17.1582 (15.3644)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:57:53 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [171/200][30/54]	eta 0:00:27 lr 0.000076	time 1.4455 (1.1318)	loss 7.7918 (5.5815)	miou 0.2746	grad_norm 8.0298 (14.7830)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:58:03 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [171/200][40/54]	eta 0:00:15 lr 0.000069	time 0.8556 (1.1186)	loss 5.7879 (5.6810)	miou 0.2832	grad_norm 16.1166 (14.4214)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:58:16 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [171/200][50/54]	eta 0:00:04 lr 0.000062	time 1.0386 (1.1384)	loss 4.8159 (5.6147)	miou 0.2863	grad_norm 8.4894 (14.8795)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:58:19 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 171 training takes 0:01:01
[32m[2025-07-12 04:58:19 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 04:58:29 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2252%
[32m[2025-07-12 04:58:29 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2840%
[32m[2025-07-12 04:58:30 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [172/200][0/54]	eta 0:01:17 lr 0.000059	time 1.4300 (1.4300)	loss 4.9469 (4.9469)	miou 0.2446	grad_norm 9.7434 (9.7434)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:58:41 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [172/200][10/54]	eta 0:00:49 lr 0.000051	time 1.0945 (1.1236)	loss 8.5060 (6.1092)	miou 0.2681	grad_norm 7.9657 (13.0154)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:58:52 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [172/200][20/54]	eta 0:00:37 lr 0.000043	time 1.2508 (1.1030)	loss 5.8786 (6.1837)	miou 0.2701	grad_norm 7.4795 (13.1514)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:59:03 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [172/200][30/54]	eta 0:00:26 lr 0.000035	time 0.9344 (1.1024)	loss 7.8694 (6.2129)	miou 0.2748	grad_norm 10.3370 (13.2169)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:59:15 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [172/200][40/54]	eta 0:00:15 lr 0.000028	time 1.4661 (1.1357)	loss 7.2351 (6.1499)	miou 0.2789	grad_norm 8.9189 (13.1447)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:59:27 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [172/200][50/54]	eta 0:00:04 lr 0.000021	time 0.9887 (1.1347)	loss 4.4137 (5.9308)	miou 0.2803	grad_norm 20.7241 (13.0370)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:59:30 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 172 training takes 0:01:01
[32m[2025-07-12 04:59:30 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 04:59:40 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1809%
[32m[2025-07-12 04:59:40 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2840%
[32m[2025-07-12 04:59:41 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [173/200][0/54]	eta 0:00:55 lr 0.000019	time 1.0239 (1.0239)	loss 4.7618 (4.7618)	miou 0.2015	grad_norm 6.4295 (6.4295)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 04:59:52 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [173/200][10/54]	eta 0:00:47 lr 0.000013	time 1.2478 (1.0782)	loss 7.8702 (5.6832)	miou 0.2336	grad_norm 11.0788 (11.8904)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:00:04 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [173/200][20/54]	eta 0:00:38 lr 0.000008	time 1.2684 (1.1324)	loss 3.9755 (5.6401)	miou 0.2697	grad_norm 20.6879 (11.8543)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:00:16 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [173/200][30/54]	eta 0:00:27 lr 0.000004	time 1.0527 (1.1444)	loss 5.6360 (5.6751)	miou 0.2674	grad_norm 8.0794 (11.3257)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:00:27 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [173/200][40/54]	eta 0:00:15 lr 0.000002	time 1.1489 (1.1402)	loss 6.3535 (5.5923)	miou 0.2778	grad_norm 6.6271 (11.9806)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:00:38 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [173/200][50/54]	eta 0:00:04 lr 0.000000	time 0.8354 (1.1322)	loss 8.0135 (5.7194)	miou 0.2782	grad_norm 13.9035 (12.0506)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:00:41 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 173 training takes 0:01:00
[32m[2025-07-12 05:00:41 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 05:00:51 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1772%
[32m[2025-07-12 05:00:51 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2840%
[32m[2025-07-12 05:00:52 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [174/200][0/54]	eta 0:00:48 lr 0.000000	time 0.8987 (0.8987)	loss 3.7091 (3.7091)	miou 0.1823	grad_norm 13.8884 (13.8884)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:01:03 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [174/200][10/54]	eta 0:00:48 lr 0.000000	time 1.0381 (1.0932)	loss 8.3365 (5.8643)	miou 0.2453	grad_norm 17.6542 (13.8035)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:01:14 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [174/200][20/54]	eta 0:00:37 lr 0.000002	time 1.2109 (1.1031)	loss 6.1866 (6.1751)	miou 0.2459	grad_norm 11.8947 (12.1178)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:01:26 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [174/200][30/54]	eta 0:00:26 lr 0.000004	time 1.0747 (1.1177)	loss 4.7881 (6.1098)	miou 0.2637	grad_norm 7.0242 (13.9181)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:01:37 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [174/200][40/54]	eta 0:00:15 lr 0.000008	time 0.9589 (1.1275)	loss 7.1883 (6.2171)	miou 0.2662	grad_norm 21.9335 (13.4519)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:01:49 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [174/200][50/54]	eta 0:00:04 lr 0.000013	time 0.9491 (1.1294)	loss 5.6236 (6.1531)	miou 0.2716	grad_norm 18.4239 (13.6446)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:01:52 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 174 training takes 0:01:00
[32m[2025-07-12 05:01:52 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 05:02:02 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1330%
[32m[2025-07-12 05:02:02 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2840%
[32m[2025-07-12 05:02:03 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [175/200][0/54]	eta 0:01:09 lr 0.000015	time 1.2914 (1.2914)	loss 4.2008 (4.2008)	miou 0.1361	grad_norm 10.6360 (10.6360)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:02:14 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [175/200][10/54]	eta 0:00:48 lr 0.000021	time 1.0071 (1.1120)	loss 3.1925 (5.6655)	miou 0.1927	grad_norm 18.0146 (12.0260)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:02:25 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [175/200][20/54]	eta 0:00:37 lr 0.000028	time 1.3765 (1.1065)	loss 7.1814 (5.8243)	miou 0.2440	grad_norm 24.1191 (12.7327)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:02:36 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [175/200][30/54]	eta 0:00:26 lr 0.000035	time 0.9391 (1.1035)	loss 5.1139 (5.9196)	miou 0.2485	grad_norm 11.5389 (12.5382)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:02:46 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [175/200][40/54]	eta 0:00:15 lr 0.000043	time 0.9035 (1.0882)	loss 6.2608 (5.9801)	miou 0.2623	grad_norm 15.0600 (12.6791)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:02:59 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [175/200][50/54]	eta 0:00:04 lr 0.000051	time 1.3993 (1.1284)	loss 3.4145 (5.8949)	miou 0.2777	grad_norm 23.2978 (12.8028)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:03:03 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 175 training takes 0:01:00
[32m[2025-07-12 05:03:03 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 05:03:13 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1808%
[32m[2025-07-12 05:03:13 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2840%
[32m[2025-07-12 05:03:14 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [176/200][0/54]	eta 0:01:10 lr 0.000054	time 1.3025 (1.3025)	loss 7.6296 (7.6296)	miou 0.1902	grad_norm 11.2637 (11.2637)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:03:25 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [176/200][10/54]	eta 0:00:48 lr 0.000062	time 1.3017 (1.0990)	loss 5.4129 (6.3390)	miou 0.2362	grad_norm 7.9639 (14.7349)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:03:37 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [176/200][20/54]	eta 0:00:38 lr 0.000069	time 1.0517 (1.1373)	loss 7.1173 (6.0195)	miou 0.2771	grad_norm 7.8349 (13.9909)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:03:47 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [176/200][30/54]	eta 0:00:26 lr 0.000076	time 0.9883 (1.1021)	loss 4.2085 (6.0830)	miou 0.2743	grad_norm 12.5251 (13.5695)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:03:58 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [176/200][40/54]	eta 0:00:15 lr 0.000082	time 1.1749 (1.1022)	loss 5.1258 (6.0495)	miou 0.2803	grad_norm 14.0599 (13.5259)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:04:10 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [176/200][50/54]	eta 0:00:04 lr 0.000088	time 1.4934 (1.1167)	loss 4.8340 (6.0820)	miou 0.2775	grad_norm 20.8560 (14.6397)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:04:13 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 176 training takes 0:01:00
[32m[2025-07-12 05:04:13 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 05:04:23 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.0845%
[32m[2025-07-12 05:04:23 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2840%
[32m[2025-07-12 05:04:25 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [177/200][0/54]	eta 0:01:10 lr 0.000090	time 1.3042 (1.3042)	loss 5.2237 (5.2237)	miou 0.0933	grad_norm 13.0926 (13.0926)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:04:37 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [177/200][10/54]	eta 0:00:52 lr 0.000094	time 0.9301 (1.2045)	loss 4.7887 (6.0498)	miou 0.2117	grad_norm 7.7380 (12.8339)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:04:48 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [177/200][20/54]	eta 0:00:39 lr 0.000097	time 0.8517 (1.1636)	loss 3.5018 (5.8738)	miou 0.2214	grad_norm 17.4477 (15.5002)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:04:59 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [177/200][30/54]	eta 0:00:27 lr 0.000099	time 0.8487 (1.1396)	loss 6.1944 (6.0211)	miou 0.2565	grad_norm 7.3423 (16.1879)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:05:10 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [177/200][40/54]	eta 0:00:16 lr 0.000100	time 1.1693 (1.1483)	loss 4.8530 (5.9757)	miou 0.2633	grad_norm 14.0630 (15.8839)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:05:21 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [177/200][50/54]	eta 0:00:04 lr 0.000100	time 0.9481 (1.1274)	loss 5.3715 (5.9912)	miou 0.2624	grad_norm 23.1752 (15.6562)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:05:24 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 177 training takes 0:01:00
[32m[2025-07-12 05:05:24 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 05:05:34 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1746%
[32m[2025-07-12 05:05:34 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2840%
[32m[2025-07-12 05:05:35 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [178/200][0/54]	eta 0:01:09 lr 0.000099	time 1.2958 (1.2958)	loss 4.3667 (4.3667)	miou 0.1777	grad_norm 15.5600 (15.5600)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:05:47 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [178/200][10/54]	eta 0:00:50 lr 0.000097	time 1.0485 (1.1504)	loss 5.0836 (6.1799)	miou 0.2207	grad_norm 14.8140 (14.8924)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:05:57 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [178/200][20/54]	eta 0:00:36 lr 0.000093	time 0.9906 (1.0768)	loss 5.2939 (5.6283)	miou 0.2419	grad_norm 8.0744 (14.5989)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:06:09 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [178/200][30/54]	eta 0:00:27 lr 0.000089	time 1.2770 (1.1254)	loss 5.4179 (5.6440)	miou 0.2574	grad_norm 11.3482 (14.6783)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:06:20 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [178/200][40/54]	eta 0:00:15 lr 0.000084	time 1.3871 (1.1271)	loss 6.9914 (5.6384)	miou 0.2669	grad_norm 18.0451 (14.4650)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:06:31 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [178/200][50/54]	eta 0:00:04 lr 0.000077	time 0.8890 (1.1254)	loss 8.0244 (5.7448)	miou 0.2754	grad_norm 9.2244 (14.3704)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:06:35 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 178 training takes 0:01:00
[32m[2025-07-12 05:06:35 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 05:06:45 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2139%
[32m[2025-07-12 05:06:45 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2840%
[32m[2025-07-12 05:06:46 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [179/200][0/54]	eta 0:01:04 lr 0.000075	time 1.1885 (1.1885)	loss 6.8753 (6.8753)	miou 0.2198	grad_norm 10.2986 (10.2986)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:06:58 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [179/200][10/54]	eta 0:00:52 lr 0.000068	time 1.2420 (1.1873)	loss 5.5641 (7.2707)	miou 0.2467	grad_norm 16.5882 (14.2847)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:07:09 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [179/200][20/54]	eta 0:00:39 lr 0.000060	time 1.2622 (1.1631)	loss 4.9145 (6.6827)	miou 0.2811	grad_norm 11.8612 (13.5478)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:07:20 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [179/200][30/54]	eta 0:00:27 lr 0.000052	time 1.1434 (1.1457)	loss 8.0543 (6.4032)	miou 0.2807	grad_norm 19.9853 (13.8210)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:07:31 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [179/200][40/54]	eta 0:00:15 lr 0.000045	time 0.9599 (1.1223)	loss 5.5195 (6.2679)	miou 0.2863	grad_norm 10.8465 (13.4507)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:07:43 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [179/200][50/54]	eta 0:00:04 lr 0.000037	time 1.2940 (1.1314)	loss 5.4419 (6.1837)	miou 0.2938	grad_norm 9.5616 (14.0790)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:07:46 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 179 training takes 0:01:00
[32m[2025-07-12 05:07:46 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 05:07:56 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2494%
[32m[2025-07-12 05:07:56 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2840%
[32m[2025-07-12 05:07:57 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [180/200][0/54]	eta 0:01:10 lr 0.000034	time 1.3098 (1.3098)	loss 6.8450 (6.8450)	miou 0.2533	grad_norm 12.5409 (12.5409)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:08:08 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [180/200][10/54]	eta 0:00:49 lr 0.000027	time 1.2257 (1.1216)	loss 6.8300 (6.4584)	miou 0.2616	grad_norm 8.7290 (13.6242)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:08:20 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [180/200][20/54]	eta 0:00:38 lr 0.000020	time 1.6132 (1.1364)	loss 6.2075 (6.2142)	miou 0.2753	grad_norm 33.8628 (13.1549)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:08:31 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [180/200][30/54]	eta 0:00:27 lr 0.000014	time 1.1193 (1.1352)	loss 7.6843 (6.0564)	miou 0.2817	grad_norm 7.4091 (13.5344)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:08:43 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [180/200][40/54]	eta 0:00:15 lr 0.000009	time 1.1533 (1.1409)	loss 5.1221 (5.9816)	miou 0.2926	grad_norm 13.0272 (12.9522)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:08:54 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [180/200][50/54]	eta 0:00:04 lr 0.000005	time 1.3964 (1.1379)	loss 8.2104 (6.1945)	miou 0.2951	grad_norm 22.2879 (13.5510)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:08:57 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 180 training takes 0:01:01
[32m[2025-07-12 05:08:57 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 05:09:08 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1373%
[32m[2025-07-12 05:09:08 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2840%
[32m[2025-07-12 05:09:09 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [181/200][0/54]	eta 0:01:31 lr 0.000004	time 1.6866 (1.6866)	loss 6.3777 (6.3777)	miou 0.1682	grad_norm 8.0550 (8.0550)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:09:21 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [181/200][10/54]	eta 0:00:52 lr 0.000001	time 0.9664 (1.1857)	loss 5.0388 (6.0450)	miou 0.2238	grad_norm 10.8413 (10.2181)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:09:32 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [181/200][20/54]	eta 0:00:40 lr 0.000000	time 1.4378 (1.1811)	loss 7.8255 (6.0951)	miou 0.2416	grad_norm 9.0577 (12.0708)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:09:43 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [181/200][30/54]	eta 0:00:27 lr 0.000000	time 1.1149 (1.1464)	loss 7.4084 (5.9543)	miou 0.2578	grad_norm 17.1611 (12.8055)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:09:55 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [181/200][40/54]	eta 0:00:16 lr 0.000001	time 1.2882 (1.1537)	loss 7.5900 (5.8953)	miou 0.2637	grad_norm 25.9133 (13.3789)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:10:06 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [181/200][50/54]	eta 0:00:04 lr 0.000004	time 0.9425 (1.1493)	loss 4.6047 (5.7785)	miou 0.2762	grad_norm 8.2679 (13.1563)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:10:10 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 181 training takes 0:01:02
[32m[2025-07-12 05:10:10 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 05:10:20 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2151%
[32m[2025-07-12 05:10:20 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2840%
[32m[2025-07-12 05:10:21 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [182/200][0/54]	eta 0:01:16 lr 0.000005	time 1.4205 (1.4205)	loss 4.6129 (4.6129)	miou 0.2249	grad_norm 5.6079 (5.6079)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:10:32 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [182/200][10/54]	eta 0:00:49 lr 0.000009	time 0.9205 (1.1288)	loss 8.5383 (6.1461)	miou 0.2609	grad_norm 7.9696 (10.2896)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:10:44 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [182/200][20/54]	eta 0:00:38 lr 0.000014	time 0.9337 (1.1316)	loss 9.0441 (6.4269)	miou 0.2716	grad_norm 14.7998 (11.2342)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:10:55 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [182/200][30/54]	eta 0:00:26 lr 0.000020	time 0.9142 (1.1201)	loss 3.9022 (6.4396)	miou 0.2740	grad_norm 15.1673 (11.6647)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:11:07 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [182/200][40/54]	eta 0:00:15 lr 0.000027	time 1.5552 (1.1382)	loss 7.0735 (6.1839)	miou 0.2772	grad_norm 9.4729 (12.3133)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:11:17 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [182/200][50/54]	eta 0:00:04 lr 0.000034	time 0.8869 (1.1279)	loss 4.7840 (6.2130)	miou 0.2807	grad_norm 14.2377 (12.6719)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:11:21 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 182 training takes 0:01:00
[32m[2025-07-12 05:11:21 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 05:11:31 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1708%
[32m[2025-07-12 05:11:31 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2840%
[32m[2025-07-12 05:11:32 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [183/200][0/54]	eta 0:01:18 lr 0.000037	time 1.4525 (1.4525)	loss 4.7764 (4.7764)	miou 0.1953	grad_norm 10.2990 (10.2990)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:11:43 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [183/200][10/54]	eta 0:00:46 lr 0.000045	time 1.1704 (1.0630)	loss 4.7549 (5.6518)	miou 0.2419	grad_norm 7.4245 (12.5294)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:11:54 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [183/200][20/54]	eta 0:00:36 lr 0.000052	time 1.0273 (1.0759)	loss 5.1303 (5.6037)	miou 0.2681	grad_norm 7.5966 (14.2454)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:12:05 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [183/200][30/54]	eta 0:00:26 lr 0.000060	time 1.1248 (1.0894)	loss 4.7127 (5.7909)	miou 0.2732	grad_norm 14.8682 (12.9687)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:12:16 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [183/200][40/54]	eta 0:00:15 lr 0.000068	time 1.0334 (1.1055)	loss 6.4861 (5.8469)	miou 0.2686	grad_norm 13.4873 (13.4220)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:12:28 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [183/200][50/54]	eta 0:00:04 lr 0.000075	time 0.9474 (1.1106)	loss 5.4408 (6.0669)	miou 0.2643	grad_norm 10.5289 (13.3576)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:12:31 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 183 training takes 0:01:00
[32m[2025-07-12 05:12:31 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 05:12:41 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2036%
[32m[2025-07-12 05:12:41 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2840%
[32m[2025-07-12 05:12:43 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [184/200][0/54]	eta 0:01:27 lr 0.000077	time 1.6251 (1.6251)	loss 7.0179 (7.0179)	miou 0.2182	grad_norm 14.5094 (14.5094)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:12:54 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [184/200][10/54]	eta 0:00:51 lr 0.000084	time 1.0810 (1.1729)	loss 3.7410 (6.1420)	miou 0.2534	grad_norm 16.3665 (14.8389)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:13:05 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [184/200][20/54]	eta 0:00:39 lr 0.000089	time 0.9966 (1.1493)	loss 5.2300 (5.8631)	miou 0.2714	grad_norm 10.2276 (13.9616)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:13:16 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [184/200][30/54]	eta 0:00:27 lr 0.000093	time 0.8922 (1.1270)	loss 4.0768 (5.9627)	miou 0.2675	grad_norm 18.0066 (13.5618)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:13:28 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [184/200][40/54]	eta 0:00:15 lr 0.000097	time 0.9475 (1.1277)	loss 8.6413 (6.1907)	miou 0.2687	grad_norm 15.6504 (13.5294)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:13:39 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [184/200][50/54]	eta 0:00:04 lr 0.000099	time 1.2166 (1.1334)	loss 5.4027 (6.1263)	miou 0.2707	grad_norm 8.8996 (13.9820)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:13:43 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 184 training takes 0:01:01
[32m[2025-07-12 05:13:43 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 05:13:53 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1656%
[32m[2025-07-12 05:13:53 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2840%
[32m[2025-07-12 05:13:54 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [185/200][0/54]	eta 0:01:13 lr 0.000100	time 1.3596 (1.3596)	loss 7.4971 (7.4971)	miou 0.1724	grad_norm 9.4555 (9.4555)	loss_scale 1048576.0000 (1048576.0000)	mem 3265MB
[32m[2025-07-12 05:14:05 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [185/200][10/54]	eta 0:00:48 lr 0.000100	time 1.0613 (1.1035)	loss 5.2799 (6.1709)	miou 0.2031	grad_norm 19.3868 (14.0990)	loss_scale 2097152.0000 (1239226.1818)	mem 3265MB
[32m[2025-07-12 05:14:16 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [185/200][20/54]	eta 0:00:37 lr 0.000099	time 1.1382 (1.1131)	loss 6.5221 (5.8275)	miou 0.2315	grad_norm 10.3111 (13.0943)	loss_scale 2097152.0000 (1647762.2857)	mem 3265MB
[32m[2025-07-12 05:14:27 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [185/200][30/54]	eta 0:00:26 lr 0.000097	time 1.1667 (1.1165)	loss 6.0991 (5.8030)	miou 0.2367	grad_norm 11.6665 (14.3060)	loss_scale 2097152.0000 (1792726.7097)	mem 3265MB
[32m[2025-07-12 05:14:39 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [185/200][40/54]	eta 0:00:15 lr 0.000094	time 1.0555 (1.1253)	loss 5.0154 (5.7145)	miou 0.2568	grad_norm 70.7951 (15.5414)	loss_scale 2097152.0000 (1866976.7805)	mem 3265MB
[32m[2025-07-12 05:14:50 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [185/200][50/54]	eta 0:00:04 lr 0.000090	time 1.0337 (1.1206)	loss 8.1874 (5.8073)	miou 0.2611	grad_norm 8.3598 (14.3468)	loss_scale 2097152.0000 (1912109.1765)	mem 3265MB
[32m[2025-07-12 05:14:53 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 185 training takes 0:00:59
[32m[2025-07-12 05:14:53 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 05:15:03 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2362%
[32m[2025-07-12 05:15:03 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2840%
[32m[2025-07-12 05:15:05 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [186/200][0/54]	eta 0:01:36 lr 0.000088	time 1.7809 (1.7809)	loss 5.7273 (5.7273)	miou 0.2325	grad_norm 10.5530 (10.5530)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:15:15 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [186/200][10/54]	eta 0:00:50 lr 0.000082	time 1.0677 (1.1386)	loss 7.6726 (6.8906)	miou 0.2481	grad_norm 12.1525 (13.9767)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:15:27 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [186/200][20/54]	eta 0:00:38 lr 0.000076	time 0.7918 (1.1448)	loss 4.7045 (6.5040)	miou 0.2528	grad_norm 24.0029 (13.7427)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:15:38 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [186/200][30/54]	eta 0:00:27 lr 0.000069	time 1.3029 (1.1420)	loss 5.8138 (6.4238)	miou 0.2676	grad_norm 8.6410 (13.2370)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:15:49 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [186/200][40/54]	eta 0:00:15 lr 0.000062	time 0.8886 (1.1356)	loss 5.5823 (6.1529)	miou 0.2733	grad_norm 11.7633 (12.8995)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:16:01 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [186/200][50/54]	eta 0:00:04 lr 0.000054	time 0.9923 (1.1378)	loss 7.4907 (6.0851)	miou 0.2803	grad_norm 9.1337 (13.3783)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:16:05 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 186 training takes 0:01:01
[32m[2025-07-12 05:16:05 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 05:16:15 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2194%
[32m[2025-07-12 05:16:15 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2840%
[32m[2025-07-12 05:16:17 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [187/200][0/54]	eta 0:01:36 lr 0.000051	time 1.7779 (1.7779)	loss 6.9353 (6.9353)	miou 0.2187	grad_norm 14.3525 (14.3525)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:16:28 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [187/200][10/54]	eta 0:00:52 lr 0.000043	time 1.5431 (1.2003)	loss 3.7350 (5.7135)	miou 0.2450	grad_norm 20.6080 (15.2263)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:16:39 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [187/200][20/54]	eta 0:00:39 lr 0.000035	time 1.0030 (1.1724)	loss 4.7820 (5.9355)	miou 0.2855	grad_norm 9.9216 (12.9982)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:16:50 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [187/200][30/54]	eta 0:00:27 lr 0.000028	time 0.9174 (1.1312)	loss 5.6997 (6.0117)	miou 0.2795	grad_norm 10.0932 (13.4617)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:17:01 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [187/200][40/54]	eta 0:00:15 lr 0.000021	time 1.1701 (1.1312)	loss 6.1064 (5.8511)	miou 0.2911	grad_norm 13.0143 (13.2523)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:17:12 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [187/200][50/54]	eta 0:00:04 lr 0.000015	time 1.0995 (1.1208)	loss 5.0656 (5.9298)	miou 0.2868	grad_norm 11.0291 (13.0116)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:17:15 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 187 training takes 0:01:00
[32m[2025-07-12 05:17:15 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 05:17:25 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2544%
[32m[2025-07-12 05:17:25 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2840%
[32m[2025-07-12 05:17:27 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [188/200][0/54]	eta 0:01:18 lr 0.000013	time 1.4571 (1.4571)	loss 7.1187 (7.1187)	miou 0.2672	grad_norm 30.5374 (30.5374)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:17:38 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [188/200][10/54]	eta 0:00:48 lr 0.000008	time 1.3895 (1.1079)	loss 5.7559 (5.8437)	miou 0.2828	grad_norm 12.7956 (14.1584)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:17:48 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [188/200][20/54]	eta 0:00:37 lr 0.000004	time 0.9558 (1.0934)	loss 6.0900 (6.0056)	miou 0.2906	grad_norm 9.2858 (13.2619)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:17:59 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [188/200][30/54]	eta 0:00:26 lr 0.000002	time 1.0740 (1.0975)	loss 6.1052 (6.1812)	miou 0.2873	grad_norm 10.8769 (13.0876)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:18:12 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [188/200][40/54]	eta 0:00:15 lr 0.000000	time 1.1815 (1.1275)	loss 5.4154 (5.9491)	miou 0.2934	grad_norm 9.1014 (13.0802)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:18:22 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [188/200][50/54]	eta 0:00:04 lr 0.000000	time 1.0546 (1.1178)	loss 6.2062 (5.9962)	miou 0.2944	grad_norm 10.5905 (13.0307)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:18:26 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 188 training takes 0:01:00
[32m[2025-07-12 05:18:26 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 05:18:36 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1872%
[32m[2025-07-12 05:18:36 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2840%
[32m[2025-07-12 05:18:38 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [189/200][0/54]	eta 0:01:21 lr 0.000000	time 1.5160 (1.5160)	loss 5.3039 (5.3039)	miou 0.1955	grad_norm 6.2394 (6.2394)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:18:48 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [189/200][10/54]	eta 0:00:48 lr 0.000002	time 0.8688 (1.1080)	loss 4.2108 (6.0026)	miou 0.2339	grad_norm 27.3013 (14.4102)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:19:00 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [189/200][20/54]	eta 0:00:38 lr 0.000004	time 1.0776 (1.1449)	loss 5.4881 (6.2178)	miou 0.2365	grad_norm 20.8477 (13.5179)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:19:12 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [189/200][30/54]	eta 0:00:27 lr 0.000008	time 1.0270 (1.1650)	loss 7.3968 (6.1558)	miou 0.2511	grad_norm 21.2116 (13.9521)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:19:24 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [189/200][40/54]	eta 0:00:16 lr 0.000013	time 1.4506 (1.1724)	loss 4.3241 (5.9785)	miou 0.2611	grad_norm 12.2451 (12.7918)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:19:35 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [189/200][50/54]	eta 0:00:04 lr 0.000019	time 0.9433 (1.1480)	loss 9.4452 (6.0583)	miou 0.2672	grad_norm 18.7504 (12.5269)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:19:38 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 189 training takes 0:01:02
[32m[2025-07-12 05:19:38 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 05:19:49 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2145%
[32m[2025-07-12 05:19:49 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2840%
[32m[2025-07-12 05:19:50 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [190/200][0/54]	eta 0:01:14 lr 0.000021	time 1.3822 (1.3822)	loss 6.2498 (6.2498)	miou 0.2238	grad_norm 12.0359 (12.0359)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:20:00 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [190/200][10/54]	eta 0:00:47 lr 0.000028	time 1.1121 (1.0731)	loss 5.0756 (5.4085)	miou 0.2887	grad_norm 9.6041 (12.9962)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:20:12 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [190/200][20/54]	eta 0:00:37 lr 0.000035	time 1.3317 (1.0979)	loss 5.4368 (5.6328)	miou 0.2975	grad_norm 8.7214 (12.9022)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:20:23 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [190/200][30/54]	eta 0:00:26 lr 0.000043	time 1.2360 (1.1131)	loss 5.5665 (5.7231)	miou 0.3022	grad_norm 9.1633 (13.1150)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:20:35 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [190/200][40/54]	eta 0:00:15 lr 0.000051	time 0.9703 (1.1203)	loss 4.8164 (5.5956)	miou 0.3037	grad_norm 8.2834 (12.6510)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:20:45 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [190/200][50/54]	eta 0:00:04 lr 0.000059	time 0.9340 (1.1068)	loss 6.8378 (5.7051)	miou 0.3031	grad_norm 8.0237 (12.7034)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:20:49 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 190 training takes 0:01:00
[32m[2025-07-12 05:20:49 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 05:20:59 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2409%
[32m[2025-07-12 05:20:59 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2840%
[32m[2025-07-12 05:21:00 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [191/200][0/54]	eta 0:01:04 lr 0.000062	time 1.1958 (1.1958)	loss 4.9119 (4.9119)	miou 0.2441	grad_norm 28.8634 (28.8634)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:21:11 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [191/200][10/54]	eta 0:00:49 lr 0.000069	time 0.9393 (1.1240)	loss 6.6888 (5.7282)	miou 0.2670	grad_norm 13.6071 (14.6791)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:21:23 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [191/200][20/54]	eta 0:00:38 lr 0.000076	time 1.1135 (1.1286)	loss 4.6220 (5.6844)	miou 0.2906	grad_norm 7.7391 (13.8014)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:21:33 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [191/200][30/54]	eta 0:00:26 lr 0.000082	time 0.8884 (1.0891)	loss 8.8657 (5.9793)	miou 0.2843	grad_norm 10.9535 (15.1688)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:21:43 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [191/200][40/54]	eta 0:00:15 lr 0.000088	time 1.2050 (1.0874)	loss 6.0756 (5.8429)	miou 0.2903	grad_norm 18.0957 (15.2618)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:21:55 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [191/200][50/54]	eta 0:00:04 lr 0.000093	time 1.1967 (1.0969)	loss 6.9939 (5.7543)	miou 0.2959	grad_norm 10.0656 (14.8902)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:21:59 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 191 training takes 0:00:59
[32m[2025-07-12 05:21:59 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 05:22:09 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1957%
[32m[2025-07-12 05:22:09 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2840%
[32m[2025-07-12 05:22:10 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [192/200][0/54]	eta 0:01:13 lr 0.000094	time 1.3617 (1.3617)	loss 7.3272 (7.3272)	miou 0.2048	grad_norm 13.0642 (13.0642)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:22:21 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [192/200][10/54]	eta 0:00:48 lr 0.000097	time 1.2545 (1.1129)	loss 6.4562 (6.3904)	miou 0.2465	grad_norm 15.3487 (15.1847)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:22:32 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [192/200][20/54]	eta 0:00:37 lr 0.000099	time 1.3834 (1.1015)	loss 4.8067 (6.0063)	miou 0.2648	grad_norm 7.9239 (15.0112)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:22:44 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [192/200][30/54]	eta 0:00:27 lr 0.000100	time 1.5210 (1.1441)	loss 7.0409 (6.3027)	miou 0.2785	grad_norm 9.9759 (14.9321)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:22:56 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [192/200][40/54]	eta 0:00:15 lr 0.000100	time 1.1474 (1.1382)	loss 4.6953 (6.1265)	miou 0.2872	grad_norm 11.8708 (14.4163)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:23:06 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [192/200][50/54]	eta 0:00:04 lr 0.000098	time 1.2311 (1.1127)	loss 7.2493 (6.0833)	miou 0.2874	grad_norm 8.8095 (14.1825)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:23:09 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 192 training takes 0:01:00
[32m[2025-07-12 05:23:09 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 05:23:20 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2243%
[32m[2025-07-12 05:23:20 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2840%
[32m[2025-07-12 05:23:21 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [193/200][0/54]	eta 0:01:17 lr 0.000097	time 1.4295 (1.4295)	loss 6.5107 (6.5107)	miou 0.2270	grad_norm 16.3629 (16.3629)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:23:32 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [193/200][10/54]	eta 0:00:51 lr 0.000093	time 1.0690 (1.1687)	loss 6.0904 (5.7893)	miou 0.2501	grad_norm 8.5647 (14.6610)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:23:43 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [193/200][20/54]	eta 0:00:38 lr 0.000089	time 0.8845 (1.1242)	loss 6.9557 (6.0956)	miou 0.2623	grad_norm 11.5000 (14.4389)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:23:55 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [193/200][30/54]	eta 0:00:27 lr 0.000084	time 1.5402 (1.1364)	loss 7.1330 (6.1109)	miou 0.2673	grad_norm 15.7666 (14.1405)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:24:06 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [193/200][40/54]	eta 0:00:15 lr 0.000077	time 1.2578 (1.1286)	loss 7.1183 (6.1455)	miou 0.2792	grad_norm 10.1196 (13.8377)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:24:18 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [193/200][50/54]	eta 0:00:04 lr 0.000071	time 1.1502 (1.1378)	loss 6.4795 (6.1503)	miou 0.2912	grad_norm 21.3504 (14.3858)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:24:21 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 193 training takes 0:01:01
[32m[2025-07-12 05:24:21 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 05:24:31 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2170%
[32m[2025-07-12 05:24:31 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2840%
[32m[2025-07-12 05:24:32 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [194/200][0/54]	eta 0:01:17 lr 0.000068	time 1.4370 (1.4370)	loss 5.0351 (5.0351)	miou 0.2167	grad_norm 7.1211 (7.1211)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:24:44 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [194/200][10/54]	eta 0:00:51 lr 0.000060	time 0.9716 (1.1754)	loss 7.7724 (6.1145)	miou 0.2528	grad_norm 12.1235 (11.5939)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:24:55 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [194/200][20/54]	eta 0:00:38 lr 0.000052	time 0.9530 (1.1219)	loss 7.1904 (5.9099)	miou 0.2797	grad_norm 17.4746 (11.8234)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:25:05 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [194/200][30/54]	eta 0:00:26 lr 0.000045	time 1.2478 (1.0973)	loss 5.6251 (5.8391)	miou 0.2848	grad_norm 8.4114 (14.1246)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:25:17 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [194/200][40/54]	eta 0:00:15 lr 0.000037	time 1.0586 (1.1134)	loss 4.6760 (5.9419)	miou 0.2817	grad_norm 7.4530 (13.4253)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:25:28 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [194/200][50/54]	eta 0:00:04 lr 0.000029	time 1.1373 (1.1142)	loss 6.9999 (5.9675)	miou 0.2821	grad_norm 8.5993 (13.1615)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:25:31 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 194 training takes 0:00:59
[32m[2025-07-12 05:25:31 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 05:25:41 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1728%
[32m[2025-07-12 05:25:41 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2840%
[32m[2025-07-12 05:25:43 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [195/200][0/54]	eta 0:01:14 lr 0.000027	time 1.3732 (1.3732)	loss 5.2123 (5.2123)	miou 0.1914	grad_norm 8.5597 (8.5597)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:25:53 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [195/200][10/54]	eta 0:00:48 lr 0.000020	time 1.2425 (1.1013)	loss 6.8172 (6.3018)	miou 0.2479	grad_norm 9.4613 (13.0291)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:26:05 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [195/200][20/54]	eta 0:00:39 lr 0.000014	time 1.0506 (1.1520)	loss 6.2056 (5.9368)	miou 0.2721	grad_norm 11.8641 (12.6727)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:26:16 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [195/200][30/54]	eta 0:00:26 lr 0.000009	time 0.9691 (1.1156)	loss 5.0150 (5.8949)	miou 0.2826	grad_norm 10.4279 (13.2245)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:26:27 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [195/200][40/54]	eta 0:00:15 lr 0.000005	time 1.3715 (1.1105)	loss 4.6545 (5.9991)	miou 0.2885	grad_norm 15.7289 (13.1206)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:26:38 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [195/200][50/54]	eta 0:00:04 lr 0.000002	time 1.1121 (1.1107)	loss 4.8232 (5.9185)	miou 0.2922	grad_norm 14.7270 (13.6213)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:26:41 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 195 training takes 0:00:59
[32m[2025-07-12 05:26:41 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 05:26:51 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2298%
[32m[2025-07-12 05:26:51 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2840%
[32m[2025-07-12 05:26:53 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [196/200][0/54]	eta 0:01:09 lr 0.000001	time 1.2954 (1.2954)	loss 7.6614 (7.6614)	miou 0.2260	grad_norm 6.7896 (6.7896)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:27:03 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [196/200][10/54]	eta 0:00:47 lr 0.000000	time 0.9144 (1.0783)	loss 3.2808 (6.6845)	miou 0.2469	grad_norm 17.2718 (10.8167)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:27:15 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [196/200][20/54]	eta 0:00:38 lr 0.000000	time 1.0627 (1.1265)	loss 4.8696 (6.3871)	miou 0.2653	grad_norm 8.7805 (11.2545)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:27:27 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [196/200][30/54]	eta 0:00:27 lr 0.000001	time 1.0385 (1.1446)	loss 4.4545 (6.2740)	miou 0.2769	grad_norm 14.2151 (11.4899)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:27:38 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [196/200][40/54]	eta 0:00:16 lr 0.000004	time 0.9142 (1.1477)	loss 8.1380 (6.1690)	miou 0.2782	grad_norm 8.6033 (12.0233)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:27:50 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [196/200][50/54]	eta 0:00:04 lr 0.000007	time 0.8661 (1.1472)	loss 6.2163 (6.1173)	miou 0.2869	grad_norm 20.5666 (12.1891)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:27:53 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 196 training takes 0:01:02
[32m[2025-07-12 05:27:53 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 05:28:04 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2102%
[32m[2025-07-12 05:28:04 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2840%
[32m[2025-07-12 05:28:05 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [197/200][0/54]	eta 0:01:06 lr 0.000009	time 1.2296 (1.2296)	loss 4.8420 (4.8420)	miou 0.2276	grad_norm 6.6655 (6.6655)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:28:16 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [197/200][10/54]	eta 0:00:49 lr 0.000014	time 1.5871 (1.1161)	loss 6.1598 (5.6343)	miou 0.2778	grad_norm 7.3399 (9.3390)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:28:27 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [197/200][20/54]	eta 0:00:37 lr 0.000020	time 1.1628 (1.1100)	loss 5.0625 (5.4666)	miou 0.2812	grad_norm 10.7525 (10.7743)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:28:38 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [197/200][30/54]	eta 0:00:26 lr 0.000027	time 1.0593 (1.1100)	loss 4.0666 (5.5666)	miou 0.2931	grad_norm 11.4134 (10.5666)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:28:50 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [197/200][40/54]	eta 0:00:15 lr 0.000034	time 1.1088 (1.1244)	loss 7.8535 (5.7712)	miou 0.2911	grad_norm 16.6625 (12.6721)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:29:01 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [197/200][50/54]	eta 0:00:04 lr 0.000041	time 0.8740 (1.1240)	loss 4.6795 (5.7847)	miou 0.2871	grad_norm 9.5169 (12.8516)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:29:04 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 197 training takes 0:01:00
[32m[2025-07-12 05:29:04 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 05:29:14 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1898%
[32m[2025-07-12 05:29:14 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2840%
[32m[2025-07-12 05:29:15 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [198/200][0/54]	eta 0:01:07 lr 0.000045	time 1.2524 (1.2524)	loss 8.2104 (8.2104)	miou 0.2152	grad_norm 7.6458 (7.6458)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:29:26 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [198/200][10/54]	eta 0:00:49 lr 0.000052	time 0.9092 (1.1144)	loss 7.2912 (6.5488)	miou 0.2516	grad_norm 7.8383 (12.1351)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:29:38 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [198/200][20/54]	eta 0:00:38 lr 0.000060	time 1.1353 (1.1185)	loss 5.5365 (6.5025)	miou 0.2656	grad_norm 26.6145 (13.9957)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:29:49 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [198/200][30/54]	eta 0:00:27 lr 0.000068	time 1.2873 (1.1307)	loss 7.9987 (6.3804)	miou 0.2735	grad_norm 7.1611 (13.1785)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:29:59 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [198/200][40/54]	eta 0:00:15 lr 0.000075	time 1.5305 (1.1020)	loss 6.1145 (6.3346)	miou 0.2755	grad_norm 8.0533 (12.5538)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:30:11 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [198/200][50/54]	eta 0:00:04 lr 0.000081	time 1.0542 (1.1157)	loss 5.6532 (6.0402)	miou 0.2859	grad_norm 10.6463 (12.9377)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:30:14 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 198 training takes 0:00:59
[32m[2025-07-12 05:30:14 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 05:30:24 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1618%
[32m[2025-07-12 05:30:24 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2840%
[32m[2025-07-12 05:30:26 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [199/200][0/54]	eta 0:01:05 lr 0.000084	time 1.2181 (1.2181)	loss 8.9292 (8.9292)	miou 0.1635	grad_norm 16.1253 (16.1253)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:30:37 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [199/200][10/54]	eta 0:00:49 lr 0.000089	time 1.4782 (1.1307)	loss 7.6957 (6.4847)	miou 0.2078	grad_norm 11.0226 (13.5213)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:30:47 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [199/200][20/54]	eta 0:00:37 lr 0.000093	time 1.2529 (1.1000)	loss 7.0575 (6.2770)	miou 0.2472	grad_norm 8.9713 (13.6147)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:30:59 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [199/200][30/54]	eta 0:00:26 lr 0.000097	time 1.0443 (1.1236)	loss 4.9841 (6.0760)	miou 0.2648	grad_norm 12.9072 (12.8167)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:31:10 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [199/200][40/54]	eta 0:00:15 lr 0.000099	time 1.1686 (1.1211)	loss 4.6683 (5.9133)	miou 0.2783	grad_norm 10.3591 (13.3600)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:31:22 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [199/200][50/54]	eta 0:00:04 lr 0.000100	time 0.8570 (1.1220)	loss 7.6029 (5.9681)	miou 0.2803	grad_norm 10.6480 (12.9278)	loss_scale 2097152.0000 (2097152.0000)	mem 3265MB
[32m[2025-07-12 05:31:25 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 199 training takes 0:01:00
[32m[2025-07-12 05:31:25 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 05:31:35 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1815%
[32m[2025-07-12 05:31:35 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2840%
[32m[2025-07-12 05:31:35 3DDETR.yaml][33m(main.py 173)[39m: INFO Training time 3:56:37