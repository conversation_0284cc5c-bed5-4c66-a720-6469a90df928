2025-07-13 18:13:26,736 INFO    StreamThr :2940711 [internal.py:wandb_internal():89] W&B internal server running at pid: 2940711, started at: 2025-07-13 18:13:26.736125
2025-07-13 18:13:26,740 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: status
2025-07-13 18:13:26,743 INFO    WriterThread:2940711 [datastore.py:open_for_write():85] open: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-3hp3mpn0/run-3hp3mpn0.wandb
2025-07-13 18:13:26,743 DEBUG   SenderThread:2940711 [sender.py:send():369] send: header
2025-07-13 18:13:26,758 DEBUG   SenderThread:2940711 [sender.py:send():369] send: run
2025-07-13 18:13:27,051 INFO    SenderThread:2940711 [dir_watcher.py:__init__():211] watching files in: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-3hp3mpn0/files
2025-07-13 18:13:27,051 INFO    SenderThread:2940711 [sender.py:_start_run_threads():1103] run started: 3hp3mpn0 with start time 1752444806.73278
2025-07-13 18:13:27,051 DEBUG   SenderThread:2940711 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:13:27,051 INFO    SenderThread:2940711 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:13:27,059 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: check_version
2025-07-13 18:13:27,059 DEBUG   SenderThread:2940711 [sender.py:send_request():396] send_request: check_version
2025-07-13 18:13:27,122 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: run_start
2025-07-13 18:13:27,135 DEBUG   HandlerThread:2940711 [system_info.py:__init__():31] System info init
2025-07-13 18:13:27,135 DEBUG   HandlerThread:2940711 [system_info.py:__init__():46] System info init done
2025-07-13 18:13:27,135 INFO    HandlerThread:2940711 [system_monitor.py:start():181] Starting system monitor
2025-07-13 18:13:27,135 INFO    SystemMonitor:2940711 [system_monitor.py:_start():145] Starting system asset monitoring threads
2025-07-13 18:13:27,136 INFO    HandlerThread:2940711 [system_monitor.py:probe():201] Collecting system info
2025-07-13 18:13:27,136 INFO    SystemMonitor:2940711 [interfaces.py:start():190] Started cpu monitoring
2025-07-13 18:13:27,137 INFO    SystemMonitor:2940711 [interfaces.py:start():190] Started disk monitoring
2025-07-13 18:13:27,139 INFO    SystemMonitor:2940711 [interfaces.py:start():190] Started gpu monitoring
2025-07-13 18:13:27,142 INFO    SystemMonitor:2940711 [interfaces.py:start():190] Started memory monitoring
2025-07-13 18:13:27,143 INFO    SystemMonitor:2940711 [interfaces.py:start():190] Started network monitoring
2025-07-13 18:13:27,159 DEBUG   HandlerThread:2940711 [system_info.py:probe():195] Probing system
2025-07-13 18:13:27,165 DEBUG   HandlerThread:2940711 [system_info.py:_probe_git():180] Probing git
2025-07-13 18:13:27,182 DEBUG   HandlerThread:2940711 [system_info.py:_probe_git():188] Probing git done
2025-07-13 18:13:27,183 DEBUG   HandlerThread:2940711 [system_info.py:probe():240] Probing system done
2025-07-13 18:13:27,183 DEBUG   HandlerThread:2940711 [system_monitor.py:probe():210] {'os': 'Linux-5.4.0-77-generic-x86_64-with-debian-bullseye-sid', 'python': '3.7.16', 'heartbeatAt': '2025-07-13T22:13:27.159226', 'startedAt': '2025-07-13T22:13:26.715191', 'docker': None, 'cuda': None, 'args': ('--local_rank=1', '--cfg', 'config/base_train.yaml', '--output', '/home-local2/akath.extra.nobkp/sereact', '--data-path', '/home-local2/akath.extra.nobkp/dl_challenge', '--batch-size', '2'), 'state': 'running', 'program': 'main.py', 'codePath': 'main.py', 'git': {'remote': 'https://github.com/Shrinidhibhat87/codingchallenge_sereact.git', 'commit': '667b7ded061f079ea281aa2193d47b61df08fec6'}, 'email': '<EMAIL>', 'root': '/home-local/akath.nobkp/sereact', 'host': 'lv3-32190', 'username': 'akath', 'executable': '/gel/usr/akath/.conda/envs/swin/bin/python', 'cpu_count': 6, 'cpu_count_logical': 12, 'cpu_freq': {'current': 1.793916666666667, 'min': 1200.0, 'max': 4000.0}, 'cpu_freq_per_core': [{'current': 1.594, 'min': 1200.0, 'max': 4000.0}, {'current': 1.337, 'min': 1200.0, 'max': 4000.0}, {'current': 2.499, 'min': 1200.0, 'max': 4000.0}, {'current': 1.316, 'min': 1200.0, 'max': 4000.0}, {'current': 1.199, 'min': 1200.0, 'max': 4000.0}, {'current': 2.299, 'min': 1200.0, 'max': 4000.0}, {'current': 1.353, 'min': 1200.0, 'max': 4000.0}, {'current': 1.474, 'min': 1200.0, 'max': 4000.0}, {'current': 3.575, 'min': 1200.0, 'max': 4000.0}, {'current': 1.382, 'min': 1200.0, 'max': 4000.0}, {'current': 1.199, 'min': 1200.0, 'max': 4000.0}, {'current': 2.3, 'min': 1200.0, 'max': 4000.0}], 'disk': {'total': 111.2200813293457, 'used': 53.13279342651367}, 'gpu': 'TITAN Xp', 'gpu_count': 3, 'gpu_devices': [{'name': 'TITAN Xp', 'memory_total': 12787122176}, {'name': 'TITAN Xp', 'memory_total': 12788498432}, {'name': 'TITAN Xp', 'memory_total': 12788498432}], 'memory': {'total': 62.72977066040039}}
2025-07-13 18:13:27,183 INFO    HandlerThread:2940711 [system_monitor.py:probe():211] Finished collecting system info
2025-07-13 18:13:27,183 INFO    HandlerThread:2940711 [system_monitor.py:probe():214] Publishing system info
2025-07-13 18:13:27,183 DEBUG   HandlerThread:2940711 [system_info.py:_save_pip():52] Saving list of pip packages installed into the current environment
2025-07-13 18:13:27,184 DEBUG   HandlerThread:2940711 [system_info.py:_save_pip():67] Saving pip packages done
2025-07-13 18:13:27,184 DEBUG   HandlerThread:2940711 [system_info.py:_save_conda():75] Saving list of conda packages installed into the current environment
2025-07-13 18:13:28,052 INFO    Thread-12 :2940711 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-3hp3mpn0/files/wandb-summary.json
2025-07-13 18:13:28,053 INFO    Thread-12 :2940711 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-3hp3mpn0/files/requirements.txt
2025-07-13 18:13:28,053 INFO    Thread-12 :2940711 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-3hp3mpn0/files/conda-environment.yaml
2025-07-13 18:13:33,473 DEBUG   HandlerThread:2940711 [system_info.py:_save_conda():86] Saving conda packages done
2025-07-13 18:13:33,474 INFO    HandlerThread:2940711 [system_monitor.py:probe():216] Finished publishing system info
2025-07-13 18:13:33,480 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:13:33,481 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: keepalive
2025-07-13 18:13:33,481 DEBUG   SenderThread:2940711 [sender.py:send():369] send: files
2025-07-13 18:13:33,481 INFO    SenderThread:2940711 [sender.py:_save_file():1354] saving file wandb-metadata.json with policy now
2025-07-13 18:13:33,488 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: stop_status
2025-07-13 18:13:33,489 DEBUG   SenderThread:2940711 [sender.py:send_request():396] send_request: stop_status
2025-07-13 18:13:33,639 DEBUG   SenderThread:2940711 [sender.py:send():369] send: telemetry
2025-07-13 18:13:33,872 INFO    wandb-upload_0:2940711 [upload_job.py:push():133] Uploaded file /tmp/tmpohe79f14wandb/vp9rjrzr-wandb-metadata.json
2025-07-13 18:13:34,057 INFO    Thread-12 :2940711 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-3hp3mpn0/files/conda-environment.yaml
2025-07-13 18:13:34,058 INFO    Thread-12 :2940711 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-3hp3mpn0/files/wandb-metadata.json
2025-07-13 18:13:34,058 INFO    Thread-12 :2940711 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-3hp3mpn0/files/output.log
2025-07-13 18:13:36,058 INFO    Thread-12 :2940711 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-3hp3mpn0/files/output.log
2025-07-13 18:13:37,612 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:13:38,058 INFO    Thread-12 :2940711 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-3hp3mpn0/files/output.log
2025-07-13 18:13:42,576 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:13:42,577 DEBUG   SenderThread:2940711 [sender.py:send():369] send: history
2025-07-13 18:13:42,577 DEBUG   SenderThread:2940711 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:13:42,580 INFO    SenderThread:2940711 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:13:43,060 INFO    Thread-12 :2940711 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-3hp3mpn0/files/wandb-summary.json
2025-07-13 18:13:43,580 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:13:46,028 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:13:46,030 DEBUG   SenderThread:2940711 [sender.py:send():369] send: history
2025-07-13 18:13:46,030 DEBUG   SenderThread:2940711 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:13:46,030 INFO    SenderThread:2940711 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:13:46,061 INFO    Thread-12 :2940711 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-3hp3mpn0/files/wandb-summary.json
2025-07-13 18:13:48,486 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: stop_status
2025-07-13 18:13:48,487 DEBUG   SenderThread:2940711 [sender.py:send_request():396] send_request: stop_status
2025-07-13 18:13:48,648 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:13:49,439 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:13:49,440 DEBUG   SenderThread:2940711 [sender.py:send():369] send: history
2025-07-13 18:13:49,440 DEBUG   SenderThread:2940711 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:13:49,440 INFO    SenderThread:2940711 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:13:50,062 INFO    Thread-12 :2940711 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-3hp3mpn0/files/wandb-summary.json
2025-07-13 18:13:52,625 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:13:52,626 DEBUG   SenderThread:2940711 [sender.py:send():369] send: history
2025-07-13 18:13:52,626 DEBUG   SenderThread:2940711 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:13:52,626 INFO    SenderThread:2940711 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:13:53,063 INFO    Thread-12 :2940711 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-3hp3mpn0/files/wandb-summary.json
2025-07-13 18:13:54,628 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:13:55,632 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:13:55,633 DEBUG   SenderThread:2940711 [sender.py:send():369] send: history
2025-07-13 18:13:55,633 DEBUG   SenderThread:2940711 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:13:55,633 INFO    SenderThread:2940711 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:13:56,064 INFO    Thread-12 :2940711 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-3hp3mpn0/files/wandb-summary.json
2025-07-13 18:13:59,632 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:13:59,633 DEBUG   SenderThread:2940711 [sender.py:send():369] send: history
2025-07-13 18:13:59,638 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:13:59,730 DEBUG   SenderThread:2940711 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:13:59,731 INFO    SenderThread:2940711 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:14:00,065 INFO    Thread-12 :2940711 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-3hp3mpn0/files/wandb-summary.json
2025-07-13 18:14:00,066 INFO    Thread-12 :2940711 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-3hp3mpn0/files/config.yaml
2025-07-13 18:14:02,902 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:14:02,903 DEBUG   SenderThread:2940711 [sender.py:send():369] send: history
2025-07-13 18:14:02,903 DEBUG   SenderThread:2940711 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:14:02,904 INFO    SenderThread:2940711 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:14:03,067 INFO    Thread-12 :2940711 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-3hp3mpn0/files/wandb-summary.json
2025-07-13 18:14:03,486 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: stop_status
2025-07-13 18:14:03,487 DEBUG   SenderThread:2940711 [sender.py:send_request():396] send_request: stop_status
2025-07-13 18:14:05,586 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:14:06,403 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:14:06,404 DEBUG   SenderThread:2940711 [sender.py:send():369] send: history
2025-07-13 18:14:06,404 DEBUG   SenderThread:2940711 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:14:06,404 INFO    SenderThread:2940711 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:14:07,068 INFO    Thread-12 :2940711 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-3hp3mpn0/files/wandb-summary.json
2025-07-13 18:14:10,071 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:14:10,072 DEBUG   SenderThread:2940711 [sender.py:send():369] send: history
2025-07-13 18:14:10,072 DEBUG   SenderThread:2940711 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:14:10,073 INFO    SenderThread:2940711 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:14:11,070 INFO    Thread-12 :2940711 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-3hp3mpn0/files/wandb-summary.json
2025-07-13 18:14:11,074 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:14:13,651 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:14:13,652 DEBUG   SenderThread:2940711 [sender.py:send():369] send: history
2025-07-13 18:14:13,652 DEBUG   SenderThread:2940711 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:14:13,652 INFO    SenderThread:2940711 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:14:14,070 INFO    Thread-12 :2940711 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-3hp3mpn0/files/wandb-summary.json
2025-07-13 18:14:16,654 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:14:17,032 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:14:17,033 DEBUG   SenderThread:2940711 [sender.py:send():369] send: history
2025-07-13 18:14:17,033 DEBUG   SenderThread:2940711 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:14:17,033 INFO    SenderThread:2940711 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:14:17,071 INFO    Thread-12 :2940711 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-3hp3mpn0/files/wandb-summary.json
2025-07-13 18:14:18,486 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: stop_status
2025-07-13 18:14:18,487 DEBUG   SenderThread:2940711 [sender.py:send_request():396] send_request: stop_status
2025-07-13 18:14:20,087 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:14:20,088 DEBUG   SenderThread:2940711 [sender.py:send():369] send: history
2025-07-13 18:14:20,088 DEBUG   SenderThread:2940711 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:14:20,089 INFO    SenderThread:2940711 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:14:21,073 INFO    Thread-12 :2940711 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-3hp3mpn0/files/wandb-summary.json
2025-07-13 18:14:22,091 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:14:22,994 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:14:22,996 DEBUG   SenderThread:2940711 [sender.py:send():369] send: history
2025-07-13 18:14:22,996 DEBUG   SenderThread:2940711 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:14:22,997 INFO    SenderThread:2940711 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:14:23,073 INFO    Thread-12 :2940711 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-3hp3mpn0/files/wandb-summary.json
2025-07-13 18:14:26,435 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:14:26,436 DEBUG   SenderThread:2940711 [sender.py:send():369] send: history
2025-07-13 18:14:26,436 DEBUG   SenderThread:2940711 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:14:26,437 INFO    SenderThread:2940711 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:14:27,075 INFO    Thread-12 :2940711 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-3hp3mpn0/files/wandb-summary.json
2025-07-13 18:14:27,143 DEBUG   SystemMonitor:2940711 [system_monitor.py:_start():159] Starting system metrics aggregation loop
2025-07-13 18:14:27,145 DEBUG   SenderThread:2940711 [sender.py:send():369] send: stats
2025-07-13 18:14:27,146 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:14:30,284 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:14:30,285 DEBUG   SenderThread:2940711 [sender.py:send():369] send: history
2025-07-13 18:14:30,286 DEBUG   SenderThread:2940711 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:14:30,286 INFO    SenderThread:2940711 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:14:31,076 INFO    Thread-12 :2940711 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-3hp3mpn0/files/wandb-summary.json
2025-07-13 18:14:32,287 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:14:33,486 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: stop_status
2025-07-13 18:14:33,487 DEBUG   SenderThread:2940711 [sender.py:send_request():396] send_request: stop_status
2025-07-13 18:14:33,646 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:14:33,648 DEBUG   SenderThread:2940711 [sender.py:send():369] send: history
2025-07-13 18:14:33,648 DEBUG   SenderThread:2940711 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:14:33,649 INFO    SenderThread:2940711 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:14:34,077 INFO    Thread-12 :2940711 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-3hp3mpn0/files/wandb-summary.json
2025-07-13 18:14:36,611 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:14:36,612 DEBUG   SenderThread:2940711 [sender.py:send():369] send: history
2025-07-13 18:14:36,612 DEBUG   SenderThread:2940711 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:14:36,614 INFO    SenderThread:2940711 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:14:37,078 INFO    Thread-12 :2940711 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-3hp3mpn0/files/wandb-summary.json
2025-07-13 18:14:37,615 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:14:38,775 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:14:38,776 DEBUG   SenderThread:2940711 [sender.py:send():369] send: history
2025-07-13 18:14:38,777 DEBUG   SenderThread:2940711 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:14:38,777 INFO    SenderThread:2940711 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:14:39,079 INFO    Thread-12 :2940711 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-3hp3mpn0/files/wandb-summary.json
2025-07-13 18:14:41,704 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:14:41,705 DEBUG   SenderThread:2940711 [sender.py:send():369] send: history
2025-07-13 18:14:41,706 DEBUG   SenderThread:2940711 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:14:41,706 INFO    SenderThread:2940711 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:14:42,080 INFO    Thread-12 :2940711 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-3hp3mpn0/files/wandb-summary.json
2025-07-13 18:14:42,706 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:14:44,250 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:14:44,251 DEBUG   SenderThread:2940711 [sender.py:send():369] send: history
2025-07-13 18:14:44,252 DEBUG   SenderThread:2940711 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:14:44,252 INFO    SenderThread:2940711 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:14:45,081 INFO    Thread-12 :2940711 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-3hp3mpn0/files/wandb-summary.json
2025-07-13 18:14:47,492 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:14:47,494 DEBUG   SenderThread:2940711 [sender.py:send():369] send: history
2025-07-13 18:14:47,494 DEBUG   SenderThread:2940711 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:14:47,497 INFO    SenderThread:2940711 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:14:48,082 INFO    Thread-12 :2940711 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-3hp3mpn0/files/wandb-summary.json
2025-07-13 18:14:48,487 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: stop_status
2025-07-13 18:14:48,488 DEBUG   SenderThread:2940711 [sender.py:send_request():396] send_request: stop_status
2025-07-13 18:14:48,650 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:14:51,233 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:14:51,233 DEBUG   SenderThread:2940711 [sender.py:send():369] send: history
2025-07-13 18:14:51,234 DEBUG   SenderThread:2940711 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:14:51,234 INFO    SenderThread:2940711 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:14:52,083 INFO    Thread-12 :2940711 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-3hp3mpn0/files/wandb-summary.json
2025-07-13 18:14:53,369 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:14:53,371 DEBUG   SenderThread:2940711 [sender.py:send():369] send: history
2025-07-13 18:14:53,371 DEBUG   SenderThread:2940711 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:14:53,371 INFO    SenderThread:2940711 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:14:54,084 INFO    Thread-12 :2940711 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-3hp3mpn0/files/wandb-summary.json
2025-07-13 18:14:54,372 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:14:56,131 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:14:56,131 DEBUG   SenderThread:2940711 [sender.py:send():369] send: history
2025-07-13 18:14:56,132 DEBUG   SenderThread:2940711 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:14:56,132 INFO    SenderThread:2940711 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:14:57,085 INFO    Thread-12 :2940711 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-3hp3mpn0/files/wandb-summary.json
2025-07-13 18:14:57,147 DEBUG   SenderThread:2940711 [sender.py:send():369] send: stats
2025-07-13 18:14:59,065 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:14:59,067 DEBUG   SenderThread:2940711 [sender.py:send():369] send: history
2025-07-13 18:14:59,067 DEBUG   SenderThread:2940711 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:14:59,069 INFO    SenderThread:2940711 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:14:59,086 INFO    Thread-12 :2940711 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-3hp3mpn0/files/wandb-summary.json
2025-07-13 18:15:00,069 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:15:02,538 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:15:02,539 DEBUG   SenderThread:2940711 [sender.py:send():369] send: history
2025-07-13 18:15:02,540 DEBUG   SenderThread:2940711 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:15:02,540 INFO    SenderThread:2940711 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:15:03,087 INFO    Thread-12 :2940711 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-3hp3mpn0/files/wandb-summary.json
2025-07-13 18:15:03,487 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: stop_status
2025-07-13 18:15:03,488 DEBUG   SenderThread:2940711 [sender.py:send_request():396] send_request: stop_status
2025-07-13 18:15:05,656 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:15:06,250 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:15:06,251 DEBUG   SenderThread:2940711 [sender.py:send():369] send: history
2025-07-13 18:15:06,252 DEBUG   SenderThread:2940711 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:15:06,252 INFO    SenderThread:2940711 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:15:07,088 INFO    Thread-12 :2940711 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-3hp3mpn0/files/wandb-summary.json
2025-07-13 18:15:07,186 DEBUG   SenderThread:2940711 [sender.py:send():369] send: exit
2025-07-13 18:15:07,186 INFO    SenderThread:2940711 [sender.py:send_exit():574] handling exit code: 1
2025-07-13 18:15:07,186 INFO    SenderThread:2940711 [sender.py:send_exit():576] handling runtime: 100
2025-07-13 18:15:07,187 INFO    SenderThread:2940711 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:15:07,187 INFO    SenderThread:2940711 [sender.py:send_exit():582] send defer
2025-07-13 18:15:07,187 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:15:07,187 INFO    HandlerThread:2940711 [handler.py:handle_request_defer():170] handle defer: 0
2025-07-13 18:15:07,188 DEBUG   SenderThread:2940711 [sender.py:send_request():396] send_request: defer
2025-07-13 18:15:07,188 INFO    SenderThread:2940711 [sender.py:send_request_defer():598] handle sender defer: 0
2025-07-13 18:15:07,188 INFO    SenderThread:2940711 [sender.py:transition_state():602] send defer: 1
2025-07-13 18:15:07,188 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:15:07,188 INFO    HandlerThread:2940711 [handler.py:handle_request_defer():170] handle defer: 1
2025-07-13 18:15:07,188 DEBUG   SenderThread:2940711 [sender.py:send_request():396] send_request: defer
2025-07-13 18:15:07,188 INFO    SenderThread:2940711 [sender.py:send_request_defer():598] handle sender defer: 1
2025-07-13 18:15:07,188 INFO    SenderThread:2940711 [sender.py:transition_state():602] send defer: 2
2025-07-13 18:15:07,188 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:15:07,188 INFO    HandlerThread:2940711 [handler.py:handle_request_defer():170] handle defer: 2
2025-07-13 18:15:07,188 INFO    HandlerThread:2940711 [system_monitor.py:finish():190] Stopping system monitor
2025-07-13 18:15:07,189 DEBUG   SystemMonitor:2940711 [system_monitor.py:_start():166] Finished system metrics aggregation loop
2025-07-13 18:15:07,189 DEBUG   SystemMonitor:2940711 [system_monitor.py:_start():170] Publishing last batch of metrics
2025-07-13 18:15:07,191 INFO    HandlerThread:2940711 [interfaces.py:finish():202] Joined cpu monitor
2025-07-13 18:15:07,191 INFO    HandlerThread:2940711 [interfaces.py:finish():202] Joined disk monitor
2025-07-13 18:15:07,318 INFO    HandlerThread:2940711 [interfaces.py:finish():202] Joined gpu monitor
2025-07-13 18:15:07,319 INFO    HandlerThread:2940711 [interfaces.py:finish():202] Joined memory monitor
2025-07-13 18:15:07,320 INFO    HandlerThread:2940711 [interfaces.py:finish():202] Joined network monitor
2025-07-13 18:15:07,320 DEBUG   SenderThread:2940711 [sender.py:send_request():396] send_request: defer
2025-07-13 18:15:07,320 INFO    SenderThread:2940711 [sender.py:send_request_defer():598] handle sender defer: 2
2025-07-13 18:15:07,320 INFO    SenderThread:2940711 [sender.py:transition_state():602] send defer: 3
2025-07-13 18:15:07,320 DEBUG   SenderThread:2940711 [sender.py:send():369] send: stats
2025-07-13 18:15:07,321 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:15:07,321 INFO    HandlerThread:2940711 [handler.py:handle_request_defer():170] handle defer: 3
2025-07-13 18:15:07,321 DEBUG   SenderThread:2940711 [sender.py:send_request():396] send_request: defer
2025-07-13 18:15:07,322 INFO    SenderThread:2940711 [sender.py:send_request_defer():598] handle sender defer: 3
2025-07-13 18:15:07,322 INFO    SenderThread:2940711 [sender.py:transition_state():602] send defer: 4
2025-07-13 18:15:07,322 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:15:07,322 INFO    HandlerThread:2940711 [handler.py:handle_request_defer():170] handle defer: 4
2025-07-13 18:15:07,322 DEBUG   SenderThread:2940711 [sender.py:send_request():396] send_request: defer
2025-07-13 18:15:07,322 INFO    SenderThread:2940711 [sender.py:send_request_defer():598] handle sender defer: 4
2025-07-13 18:15:07,323 INFO    SenderThread:2940711 [sender.py:transition_state():602] send defer: 5
2025-07-13 18:15:07,323 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:15:07,323 INFO    HandlerThread:2940711 [handler.py:handle_request_defer():170] handle defer: 5
2025-07-13 18:15:07,324 DEBUG   SenderThread:2940711 [sender.py:send():369] send: summary
2025-07-13 18:15:07,324 INFO    SenderThread:2940711 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:15:07,325 DEBUG   SenderThread:2940711 [sender.py:send_request():396] send_request: defer
2025-07-13 18:15:07,325 INFO    SenderThread:2940711 [sender.py:send_request_defer():598] handle sender defer: 5
2025-07-13 18:15:07,325 INFO    SenderThread:2940711 [sender.py:transition_state():602] send defer: 6
2025-07-13 18:15:07,325 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:15:07,325 INFO    HandlerThread:2940711 [handler.py:handle_request_defer():170] handle defer: 6
2025-07-13 18:15:07,326 DEBUG   SenderThread:2940711 [sender.py:send_request():396] send_request: defer
2025-07-13 18:15:07,326 INFO    SenderThread:2940711 [sender.py:send_request_defer():598] handle sender defer: 6
2025-07-13 18:15:07,326 INFO    SenderThread:2940711 [sender.py:transition_state():602] send defer: 7
2025-07-13 18:15:07,326 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:15:07,326 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:15:07,326 INFO    HandlerThread:2940711 [handler.py:handle_request_defer():170] handle defer: 7
2025-07-13 18:15:07,327 DEBUG   SenderThread:2940711 [sender.py:send_request():396] send_request: defer
2025-07-13 18:15:07,327 INFO    SenderThread:2940711 [sender.py:send_request_defer():598] handle sender defer: 7
2025-07-13 18:15:08,089 INFO    Thread-12 :2940711 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-3hp3mpn0/files/wandb-summary.json
2025-07-13 18:15:08,186 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: poll_exit
2025-07-13 18:15:09,192 INFO    SenderThread:2940711 [sender.py:transition_state():602] send defer: 8
2025-07-13 18:15:09,192 DEBUG   SenderThread:2940711 [sender.py:send_request():396] send_request: poll_exit
2025-07-13 18:15:09,192 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:15:09,194 INFO    HandlerThread:2940711 [handler.py:handle_request_defer():170] handle defer: 8
2025-07-13 18:15:09,194 DEBUG   SenderThread:2940711 [sender.py:send_request():396] send_request: defer
2025-07-13 18:15:09,195 INFO    SenderThread:2940711 [sender.py:send_request_defer():598] handle sender defer: 8
2025-07-13 18:15:09,195 INFO    SenderThread:2940711 [job_builder.py:build():232] Attempting to build job artifact
2025-07-13 18:15:09,195 INFO    SenderThread:2940711 [job_builder.py:build():256] is repo sourced job
2025-07-13 18:15:09,199 INFO    SenderThread:2940711 [job_builder.py:build():297] adding wandb-job metadata file
2025-07-13 18:15:09,207 INFO    SenderThread:2940711 [sender.py:transition_state():602] send defer: 9
2025-07-13 18:15:09,208 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:15:09,208 DEBUG   SenderThread:2940711 [sender.py:send():369] send: artifact
2025-07-13 18:15:09,208 INFO    HandlerThread:2940711 [handler.py:handle_request_defer():170] handle defer: 9
2025-07-13 18:15:09,951 INFO    wandb-upload_2:2940711 [upload_job.py:push():91] Uploaded file /gel/usr/akath/.local/share/wandb/artifacts/staging/tmp9mdlfmkn
2025-07-13 18:15:09,952 INFO    wandb-upload_0:2940711 [upload_job.py:push():91] Uploaded file /gel/usr/akath/.local/share/wandb/artifacts/staging/tmpq9ab8ysl
2025-07-13 18:15:10,089 INFO    Thread-12 :2940711 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-3hp3mpn0/files/output.log
2025-07-13 18:15:10,706 INFO    SenderThread:2940711 [sender.py:send_artifact():1450] sent artifact job-https___github.com_Shrinidhibhat87_codingchallenge_sereact.git_main.py - {'id': 'QXJ0aWZhY3Q6MTg3NzkyMzY4Mg==', 'digest': '01f3dd739b2c8dbc3409e0ca40376bfa', 'state': 'PENDING', 'aliases': [], 'artifactSequence': {'id': 'QXJ0aWZhY3RDb2xsZWN0aW9uOjY4NDgwMzQ0Ng==', 'latestArtifact': {'id': 'QXJ0aWZhY3Q6MTg3NTAxNDMwNQ==', 'versionIndex': 5}}, 'version': 'latest'}
2025-07-13 18:15:10,706 DEBUG   SenderThread:2940711 [sender.py:send_request():396] send_request: defer
2025-07-13 18:15:10,706 INFO    SenderThread:2940711 [sender.py:send_request_defer():598] handle sender defer: 9
2025-07-13 18:15:10,706 INFO    SenderThread:2940711 [dir_watcher.py:finish():359] shutting down directory watcher
2025-07-13 18:15:11,090 INFO    SenderThread:2940711 [dir_watcher.py:finish():389] scan: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-3hp3mpn0/files
2025-07-13 18:15:11,091 INFO    SenderThread:2940711 [dir_watcher.py:finish():403] scan save: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-3hp3mpn0/files/config.yaml config.yaml
2025-07-13 18:15:11,091 INFO    SenderThread:2940711 [dir_watcher.py:finish():403] scan save: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-3hp3mpn0/files/wandb-summary.json wandb-summary.json
2025-07-13 18:15:11,092 INFO    SenderThread:2940711 [dir_watcher.py:finish():403] scan save: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-3hp3mpn0/files/requirements.txt requirements.txt
2025-07-13 18:15:11,097 INFO    SenderThread:2940711 [dir_watcher.py:finish():403] scan save: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-3hp3mpn0/files/conda-environment.yaml conda-environment.yaml
2025-07-13 18:15:11,100 INFO    SenderThread:2940711 [dir_watcher.py:finish():403] scan save: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-3hp3mpn0/files/wandb-metadata.json wandb-metadata.json
2025-07-13 18:15:11,106 INFO    SenderThread:2940711 [dir_watcher.py:finish():403] scan save: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-3hp3mpn0/files/output.log output.log
2025-07-13 18:15:11,108 INFO    SenderThread:2940711 [sender.py:transition_state():602] send defer: 10
2025-07-13 18:15:11,109 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:15:11,114 INFO    HandlerThread:2940711 [handler.py:handle_request_defer():170] handle defer: 10
2025-07-13 18:15:11,123 DEBUG   SenderThread:2940711 [sender.py:send_request():396] send_request: defer
2025-07-13 18:15:11,123 INFO    SenderThread:2940711 [sender.py:send_request_defer():598] handle sender defer: 10
2025-07-13 18:15:11,124 INFO    SenderThread:2940711 [file_pusher.py:finish():159] shutting down file pusher
2025-07-13 18:15:11,333 INFO    wandb-upload_2:2940711 [upload_job.py:push():133] Uploaded file /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-3hp3mpn0/files/wandb-summary.json
2025-07-13 18:15:11,417 INFO    wandb-upload_3:2940711 [upload_job.py:push():133] Uploaded file /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-3hp3mpn0/files/config.yaml
2025-07-13 18:15:11,447 INFO    wandb-upload_5:2940711 [upload_job.py:push():133] Uploaded file /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-3hp3mpn0/files/requirements.txt
2025-07-13 18:15:11,449 INFO    wandb-upload_6:2940711 [upload_job.py:push():133] Uploaded file /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-3hp3mpn0/files/conda-environment.yaml
2025-07-13 18:15:11,490 INFO    wandb-upload_4:2940711 [upload_job.py:push():133] Uploaded file /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-3hp3mpn0/files/output.log
2025-07-13 18:15:11,691 INFO    Thread-11 :2940711 [sender.py:transition_state():602] send defer: 11
2025-07-13 18:15:11,691 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:15:11,692 INFO    HandlerThread:2940711 [handler.py:handle_request_defer():170] handle defer: 11
2025-07-13 18:15:11,692 DEBUG   SenderThread:2940711 [sender.py:send_request():396] send_request: defer
2025-07-13 18:15:11,692 INFO    SenderThread:2940711 [sender.py:send_request_defer():598] handle sender defer: 11
2025-07-13 18:15:11,693 INFO    SenderThread:2940711 [file_pusher.py:join():164] waiting for file pusher
2025-07-13 18:15:11,693 INFO    SenderThread:2940711 [sender.py:transition_state():602] send defer: 12
2025-07-13 18:15:11,693 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:15:11,693 INFO    HandlerThread:2940711 [handler.py:handle_request_defer():170] handle defer: 12
2025-07-13 18:15:11,694 DEBUG   SenderThread:2940711 [sender.py:send_request():396] send_request: defer
2025-07-13 18:15:11,694 INFO    SenderThread:2940711 [sender.py:send_request_defer():598] handle sender defer: 12
2025-07-13 18:15:11,790 INFO    SenderThread:2940711 [sender.py:transition_state():602] send defer: 13
2025-07-13 18:15:11,790 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:15:11,790 INFO    HandlerThread:2940711 [handler.py:handle_request_defer():170] handle defer: 13
2025-07-13 18:15:11,791 DEBUG   SenderThread:2940711 [sender.py:send_request():396] send_request: defer
2025-07-13 18:15:11,791 INFO    SenderThread:2940711 [sender.py:send_request_defer():598] handle sender defer: 13
2025-07-13 18:15:11,791 INFO    SenderThread:2940711 [sender.py:transition_state():602] send defer: 14
2025-07-13 18:15:11,792 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:15:11,792 DEBUG   SenderThread:2940711 [sender.py:send():369] send: final
2025-07-13 18:15:11,792 INFO    HandlerThread:2940711 [handler.py:handle_request_defer():170] handle defer: 14
2025-07-13 18:15:11,792 DEBUG   SenderThread:2940711 [sender.py:send():369] send: footer
2025-07-13 18:15:11,792 DEBUG   SenderThread:2940711 [sender.py:send_request():396] send_request: defer
2025-07-13 18:15:11,793 INFO    SenderThread:2940711 [sender.py:send_request_defer():598] handle sender defer: 14
2025-07-13 18:15:11,793 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: poll_exit
2025-07-13 18:15:11,794 DEBUG   SenderThread:2940711 [sender.py:send_request():396] send_request: poll_exit
2025-07-13 18:15:11,795 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: server_info
2025-07-13 18:15:11,795 DEBUG   SenderThread:2940711 [sender.py:send_request():396] send_request: server_info
2025-07-13 18:15:11,796 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: get_summary
2025-07-13 18:15:11,801 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: sampled_history
2025-07-13 18:15:11,855 INFO    MainThread:2940711 [wandb_run.py:_footer_history_summary_info():3467] rendering history
2025-07-13 18:15:11,855 INFO    MainThread:2940711 [wandb_run.py:_footer_history_summary_info():3499] rendering summary
2025-07-13 18:15:11,855 INFO    MainThread:2940711 [wandb_run.py:_footer_sync_info():3426] logging synced files
2025-07-13 18:15:11,856 DEBUG   HandlerThread:2940711 [handler.py:handle_request():144] handle_request: shutdown
2025-07-13 18:15:11,856 INFO    HandlerThread:2940711 [handler.py:finish():854] shutting down handler
2025-07-13 18:15:12,795 INFO    WriterThread:2940711 [datastore.py:close():298] close: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-3hp3mpn0/run-3hp3mpn0.wandb
2025-07-13 18:15:12,854 INFO    SenderThread:2940711 [sender.py:finish():1526] shutting down sender
2025-07-13 18:15:12,855 INFO    SenderThread:2940711 [file_pusher.py:finish():159] shutting down file pusher
2025-07-13 18:15:12,855 INFO    SenderThread:2940711 [file_pusher.py:join():164] waiting for file pusher
