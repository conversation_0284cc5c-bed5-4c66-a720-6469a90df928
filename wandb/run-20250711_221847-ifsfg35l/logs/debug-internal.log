2025-07-11 22:18:47,822 INFO    StreamThr :2831850 [internal.py:wandb_internal():89] W&B internal server running at pid: 2831850, started at: 2025-07-11 22:18:47.821450
2025-07-11 22:18:47,823 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: status
2025-07-11 22:18:47,831 INFO    WriterThread:2831850 [datastore.py:open_for_write():85] open: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-ifsfg35l/run-ifsfg35l.wandb
2025-07-11 22:18:47,832 DEBUG   SenderThread:2831850 [sender.py:send():369] send: header
2025-07-11 22:18:47,849 DEBUG   SenderThread:2831850 [sender.py:send():369] send: run
2025-07-11 22:18:48,040 ERROR   SenderThread:2831850 [internal_api.py:execute():323] 500 response executing GraphQL.
2025-07-11 22:18:48,040 ERROR   SenderThread:2831850 [internal_api.py:execute():324] {"errors":[{"message":"An internal error occurred. Please contact support.","path":["upsertBucket"]}],"data":{"upsertBucket":null}}
2025-07-11 22:18:49,387 INFO    SenderThread:2831850 [dir_watcher.py:__init__():211] watching files in: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-ifsfg35l/files
2025-07-11 22:18:49,388 INFO    SenderThread:2831850 [sender.py:_start_run_threads():1103] run started: ifsfg35l with start time 1752286727.823309
2025-07-11 22:18:49,392 DEBUG   SenderThread:2831850 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:18:49,393 INFO    SenderThread:2831850 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:18:49,400 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: check_version
2025-07-11 22:18:49,401 DEBUG   SenderThread:2831850 [sender.py:send_request():396] send_request: check_version
2025-07-11 22:18:49,458 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: run_start
2025-07-11 22:18:49,462 DEBUG   HandlerThread:2831850 [system_info.py:__init__():31] System info init
2025-07-11 22:18:49,462 DEBUG   HandlerThread:2831850 [system_info.py:__init__():46] System info init done
2025-07-11 22:18:49,462 INFO    HandlerThread:2831850 [system_monitor.py:start():181] Starting system monitor
2025-07-11 22:18:49,462 INFO    SystemMonitor:2831850 [system_monitor.py:_start():145] Starting system asset monitoring threads
2025-07-11 22:18:49,463 INFO    HandlerThread:2831850 [system_monitor.py:probe():201] Collecting system info
2025-07-11 22:18:49,463 INFO    SystemMonitor:2831850 [interfaces.py:start():190] Started cpu monitoring
2025-07-11 22:18:49,464 INFO    SystemMonitor:2831850 [interfaces.py:start():190] Started disk monitoring
2025-07-11 22:18:49,465 INFO    SystemMonitor:2831850 [interfaces.py:start():190] Started gpu monitoring
2025-07-11 22:18:49,466 INFO    SystemMonitor:2831850 [interfaces.py:start():190] Started memory monitoring
2025-07-11 22:18:49,467 INFO    SystemMonitor:2831850 [interfaces.py:start():190] Started network monitoring
2025-07-11 22:18:49,485 DEBUG   HandlerThread:2831850 [system_info.py:probe():195] Probing system
2025-07-11 22:18:49,494 DEBUG   HandlerThread:2831850 [system_info.py:_probe_git():180] Probing git
2025-07-11 22:18:49,511 DEBUG   HandlerThread:2831850 [system_info.py:_probe_git():188] Probing git done
2025-07-11 22:18:49,512 DEBUG   HandlerThread:2831850 [system_info.py:probe():240] Probing system done
2025-07-11 22:18:49,512 DEBUG   HandlerThread:2831850 [system_monitor.py:probe():210] {'os': 'Linux-5.4.0-77-generic-x86_64-with-debian-bullseye-sid', 'python': '3.7.16', 'heartbeatAt': '2025-07-12T02:18:49.485536', 'startedAt': '2025-07-12T02:18:47.802219', 'docker': None, 'cuda': None, 'args': ('--local_rank=2', '--cfg', 'config/base_train.yaml', '--output', '/home-local2/akath.extra.nobkp/sereact', '--data-path', '/home-local2/akath.extra.nobkp/dl_challenge', '--batch-size', '2'), 'state': 'running', 'program': 'main.py', 'codePath': 'main.py', 'git': {'remote': 'https://github.com/Shrinidhibhat87/codingchallenge_sereact.git', 'commit': 'a3ff60e13dfced588b500c8a1de00f36fdf22d49'}, 'email': '<EMAIL>', 'root': '/home-local/akath.nobkp/codingchallenge_sereact', 'host': 'lv3-32190', 'username': 'akath', 'executable': '/gel/usr/akath/.conda/envs/swin/bin/python', 'cpu_count': 6, 'cpu_count_logical': 12, 'cpu_freq': {'current': 2.2780833333333335, 'min': 1200.0, 'max': 4000.0}, 'cpu_freq_per_core': [{'current': 2.651, 'min': 1200.0, 'max': 4000.0}, {'current': 2.239, 'min': 1200.0, 'max': 4000.0}, {'current': 2.4, 'min': 1200.0, 'max': 4000.0}, {'current': 1.238, 'min': 1200.0, 'max': 4000.0}, {'current': 1.544, 'min': 1200.0, 'max': 4000.0}, {'current': 3.682, 'min': 1200.0, 'max': 4000.0}, {'current': 2.267, 'min': 1200.0, 'max': 4000.0}, {'current': 1.653, 'min': 1200.0, 'max': 4000.0}, {'current': 3.219, 'min': 1200.0, 'max': 4000.0}, {'current': 1.476, 'min': 1200.0, 'max': 4000.0}, {'current': 1.271, 'min': 1200.0, 'max': 4000.0}, {'current': 3.697, 'min': 1200.0, 'max': 4000.0}], 'disk': {'total': 111.2200813293457, 'used': 53.229248046875}, 'gpu': 'TITAN Xp', 'gpu_count': 3, 'gpu_devices': [{'name': 'TITAN Xp', 'memory_total': 12787122176}, {'name': 'TITAN Xp', 'memory_total': 12788498432}, {'name': 'TITAN Xp', 'memory_total': 12788498432}], 'memory': {'total': 62.72977066040039}}
2025-07-11 22:18:49,512 INFO    HandlerThread:2831850 [system_monitor.py:probe():211] Finished collecting system info
2025-07-11 22:18:49,512 INFO    HandlerThread:2831850 [system_monitor.py:probe():214] Publishing system info
2025-07-11 22:18:49,512 DEBUG   HandlerThread:2831850 [system_info.py:_save_pip():52] Saving list of pip packages installed into the current environment
2025-07-11 22:18:49,512 DEBUG   HandlerThread:2831850 [system_info.py:_save_pip():67] Saving pip packages done
2025-07-11 22:18:49,512 DEBUG   HandlerThread:2831850 [system_info.py:_save_conda():75] Saving list of conda packages installed into the current environment
2025-07-11 22:18:50,391 INFO    Thread-12 :2831850 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-ifsfg35l/files/wandb-summary.json
2025-07-11 22:18:50,391 INFO    Thread-12 :2831850 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-ifsfg35l/files/requirements.txt
2025-07-11 22:18:50,392 INFO    Thread-12 :2831850 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-ifsfg35l/files/conda-environment.yaml
2025-07-11 22:18:52,073 DEBUG   HandlerThread:2831850 [system_info.py:_save_conda():86] Saving conda packages done
2025-07-11 22:18:52,073 INFO    HandlerThread:2831850 [system_monitor.py:probe():216] Finished publishing system info
2025-07-11 22:18:52,080 DEBUG   SenderThread:2831850 [sender.py:send():369] send: files
2025-07-11 22:18:52,080 INFO    SenderThread:2831850 [sender.py:_save_file():1354] saving file wandb-metadata.json with policy now
2025-07-11 22:18:52,090 DEBUG   SenderThread:2831850 [sender.py:send():369] send: telemetry
2025-07-11 22:18:52,118 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:18:52,119 DEBUG   SenderThread:2831850 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:18:52,388 INFO    Thread-12 :2831850 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-ifsfg35l/files/conda-environment.yaml
2025-07-11 22:18:52,388 INFO    Thread-12 :2831850 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-ifsfg35l/files/output.log
2025-07-11 22:18:52,389 INFO    Thread-12 :2831850 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-ifsfg35l/files/wandb-metadata.json
2025-07-11 22:18:52,456 INFO    wandb-upload_0:2831850 [upload_job.py:push():133] Uploaded file /tmp/tmpk13embcdwandb/32qjwdzx-wandb-metadata.json
2025-07-11 22:18:53,236 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:18:54,388 INFO    Thread-12 :2831850 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-ifsfg35l/files/output.log
2025-07-11 22:18:56,389 INFO    Thread-12 :2831850 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-ifsfg35l/files/output.log
2025-07-11 22:18:59,208 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:18:59,254 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:18:59,256 DEBUG   SenderThread:2831850 [sender.py:send():369] send: history
2025-07-11 22:18:59,256 DEBUG   SenderThread:2831850 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:18:59,260 INFO    SenderThread:2831850 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:18:59,390 INFO    Thread-12 :2831850 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-ifsfg35l/files/wandb-summary.json
2025-07-11 22:19:02,335 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:19:02,336 DEBUG   SenderThread:2831850 [sender.py:send():369] send: history
2025-07-11 22:19:02,337 DEBUG   SenderThread:2831850 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:19:02,337 INFO    SenderThread:2831850 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:19:02,391 INFO    Thread-12 :2831850 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-ifsfg35l/files/wandb-summary.json
2025-07-11 22:19:04,339 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:19:05,265 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:19:05,266 DEBUG   SenderThread:2831850 [sender.py:send():369] send: history
2025-07-11 22:19:05,266 DEBUG   SenderThread:2831850 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:19:05,267 INFO    SenderThread:2831850 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:19:05,392 INFO    Thread-12 :2831850 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-ifsfg35l/files/wandb-summary.json
2025-07-11 22:19:07,090 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:19:07,090 DEBUG   SenderThread:2831850 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:19:08,028 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:19:08,029 DEBUG   SenderThread:2831850 [sender.py:send():369] send: history
2025-07-11 22:19:08,030 DEBUG   SenderThread:2831850 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:19:08,030 INFO    SenderThread:2831850 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:19:08,393 INFO    Thread-12 :2831850 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-ifsfg35l/files/wandb-summary.json
2025-07-11 22:19:10,032 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:19:10,693 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:19:10,694 DEBUG   SenderThread:2831850 [sender.py:send():369] send: history
2025-07-11 22:19:10,695 DEBUG   SenderThread:2831850 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:19:10,695 INFO    SenderThread:2831850 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:19:11,394 INFO    Thread-12 :2831850 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-ifsfg35l/files/wandb-summary.json
2025-07-11 22:19:14,076 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:19:14,077 DEBUG   SenderThread:2831850 [sender.py:send():369] send: history
2025-07-11 22:19:14,077 DEBUG   SenderThread:2831850 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:19:14,078 INFO    SenderThread:2831850 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:19:14,395 INFO    Thread-12 :2831850 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-ifsfg35l/files/wandb-summary.json
2025-07-11 22:19:15,078 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:19:16,611 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:19:16,611 DEBUG   SenderThread:2831850 [sender.py:send():369] send: history
2025-07-11 22:19:16,612 DEBUG   SenderThread:2831850 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:19:16,612 INFO    SenderThread:2831850 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:19:17,396 INFO    Thread-12 :2831850 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-ifsfg35l/files/wandb-summary.json
2025-07-11 22:19:19,282 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:19:19,283 DEBUG   SenderThread:2831850 [sender.py:send():369] send: history
2025-07-11 22:19:19,283 DEBUG   SenderThread:2831850 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:19:19,284 INFO    SenderThread:2831850 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:19:19,397 INFO    Thread-12 :2831850 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-ifsfg35l/files/wandb-summary.json
2025-07-11 22:19:20,289 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:19:20,397 INFO    Thread-12 :2831850 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-ifsfg35l/files/config.yaml
2025-07-11 22:19:22,089 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:19:22,089 DEBUG   SenderThread:2831850 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:19:22,632 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:19:22,633 DEBUG   SenderThread:2831850 [sender.py:send():369] send: history
2025-07-11 22:19:22,633 DEBUG   SenderThread:2831850 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:19:22,634 INFO    SenderThread:2831850 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:19:23,398 INFO    Thread-12 :2831850 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-ifsfg35l/files/wandb-summary.json
2025-07-11 22:19:25,605 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:19:25,606 DEBUG   SenderThread:2831850 [sender.py:send():369] send: history
2025-07-11 22:19:25,607 DEBUG   SenderThread:2831850 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:19:25,607 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:19:25,608 INFO    SenderThread:2831850 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:19:26,400 INFO    Thread-12 :2831850 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-ifsfg35l/files/wandb-summary.json
2025-07-11 22:19:28,689 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:19:28,691 DEBUG   SenderThread:2831850 [sender.py:send():369] send: history
2025-07-11 22:19:28,691 DEBUG   SenderThread:2831850 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:19:28,691 INFO    SenderThread:2831850 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:19:29,401 INFO    Thread-12 :2831850 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-ifsfg35l/files/wandb-summary.json
2025-07-11 22:19:30,693 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:19:31,063 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:19:31,064 DEBUG   SenderThread:2831850 [sender.py:send():369] send: history
2025-07-11 22:19:31,064 DEBUG   SenderThread:2831850 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:19:31,064 INFO    SenderThread:2831850 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:19:31,401 INFO    Thread-12 :2831850 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-ifsfg35l/files/wandb-summary.json
2025-07-11 22:19:33,349 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:19:33,352 DEBUG   SenderThread:2831850 [sender.py:send():369] send: history
2025-07-11 22:19:33,352 DEBUG   SenderThread:2831850 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:19:33,353 INFO    SenderThread:2831850 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:19:33,402 INFO    Thread-12 :2831850 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-ifsfg35l/files/wandb-summary.json
2025-07-11 22:19:36,282 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:19:36,283 DEBUG   SenderThread:2831850 [sender.py:send():369] send: history
2025-07-11 22:19:36,283 DEBUG   SenderThread:2831850 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:19:36,283 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:19:36,284 INFO    SenderThread:2831850 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:19:36,403 INFO    Thread-12 :2831850 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-ifsfg35l/files/wandb-summary.json
2025-07-11 22:19:37,088 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:19:37,089 DEBUG   SenderThread:2831850 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:19:39,805 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:19:39,807 DEBUG   SenderThread:2831850 [sender.py:send():369] send: history
2025-07-11 22:19:39,807 DEBUG   SenderThread:2831850 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:19:39,808 INFO    SenderThread:2831850 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:19:40,404 INFO    Thread-12 :2831850 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-ifsfg35l/files/wandb-summary.json
2025-07-11 22:19:41,810 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:19:42,592 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:19:42,593 DEBUG   SenderThread:2831850 [sender.py:send():369] send: history
2025-07-11 22:19:42,594 DEBUG   SenderThread:2831850 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:19:42,594 INFO    SenderThread:2831850 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:19:43,405 INFO    Thread-12 :2831850 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-ifsfg35l/files/wandb-summary.json
2025-07-11 22:19:45,162 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:19:45,163 DEBUG   SenderThread:2831850 [sender.py:send():369] send: history
2025-07-11 22:19:45,163 DEBUG   SenderThread:2831850 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:19:45,164 INFO    SenderThread:2831850 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:19:45,406 INFO    Thread-12 :2831850 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-ifsfg35l/files/wandb-summary.json
2025-07-11 22:19:47,166 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:19:47,841 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:19:47,843 DEBUG   SenderThread:2831850 [sender.py:send():369] send: history
2025-07-11 22:19:47,843 DEBUG   SenderThread:2831850 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:19:47,843 INFO    SenderThread:2831850 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:19:48,407 INFO    Thread-12 :2831850 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-ifsfg35l/files/wandb-summary.json
2025-07-11 22:19:49,467 DEBUG   SystemMonitor:2831850 [system_monitor.py:_start():159] Starting system metrics aggregation loop
2025-07-11 22:19:49,469 DEBUG   SenderThread:2831850 [sender.py:send():369] send: stats
2025-07-11 22:19:50,232 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:19:50,233 DEBUG   SenderThread:2831850 [sender.py:send():369] send: history
2025-07-11 22:19:50,233 DEBUG   SenderThread:2831850 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:19:50,234 INFO    SenderThread:2831850 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:19:50,408 INFO    Thread-12 :2831850 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-ifsfg35l/files/wandb-summary.json
2025-07-11 22:19:52,089 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:19:52,090 DEBUG   SenderThread:2831850 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:19:52,173 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:19:52,984 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:19:52,985 DEBUG   SenderThread:2831850 [sender.py:send():369] send: history
2025-07-11 22:19:52,986 DEBUG   SenderThread:2831850 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:19:52,986 INFO    SenderThread:2831850 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:19:53,409 INFO    Thread-12 :2831850 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-ifsfg35l/files/wandb-summary.json
2025-07-11 22:19:55,610 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:19:55,611 DEBUG   SenderThread:2831850 [sender.py:send():369] send: history
2025-07-11 22:19:55,611 DEBUG   SenderThread:2831850 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:19:55,611 INFO    SenderThread:2831850 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:19:56,410 INFO    Thread-12 :2831850 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-ifsfg35l/files/wandb-summary.json
2025-07-11 22:19:57,612 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:19:58,765 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:19:58,766 DEBUG   SenderThread:2831850 [sender.py:send():369] send: history
2025-07-11 22:19:58,766 DEBUG   SenderThread:2831850 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:19:58,769 INFO    SenderThread:2831850 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:19:59,411 INFO    Thread-12 :2831850 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-ifsfg35l/files/wandb-summary.json
2025-07-11 22:20:00,483 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:20:00,483 DEBUG   SenderThread:2831850 [sender.py:send():369] send: history
2025-07-11 22:20:00,484 DEBUG   SenderThread:2831850 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:20:00,484 INFO    SenderThread:2831850 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:20:01,412 INFO    Thread-12 :2831850 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-ifsfg35l/files/wandb-summary.json
2025-07-11 22:20:02,285 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:20:02,286 DEBUG   SenderThread:2831850 [sender.py:send():369] send: history
2025-07-11 22:20:02,287 DEBUG   SenderThread:2831850 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:20:02,287 INFO    SenderThread:2831850 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:20:02,412 INFO    Thread-12 :2831850 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-ifsfg35l/files/wandb-summary.json
2025-07-11 22:20:03,288 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:20:04,576 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:20:04,576 DEBUG   SenderThread:2831850 [sender.py:send():369] send: history
2025-07-11 22:20:04,577 DEBUG   SenderThread:2831850 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:20:04,577 INFO    SenderThread:2831850 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:20:05,413 INFO    Thread-12 :2831850 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-ifsfg35l/files/wandb-summary.json
2025-07-11 22:20:07,089 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:20:07,090 DEBUG   SenderThread:2831850 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:20:07,474 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:20:07,475 DEBUG   SenderThread:2831850 [sender.py:send():369] send: history
2025-07-11 22:20:07,475 DEBUG   SenderThread:2831850 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:20:07,476 INFO    SenderThread:2831850 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:20:08,414 INFO    Thread-12 :2831850 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-ifsfg35l/files/wandb-summary.json
2025-07-11 22:20:08,476 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:20:10,683 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:20:10,685 DEBUG   SenderThread:2831850 [sender.py:send():369] send: history
2025-07-11 22:20:10,685 DEBUG   SenderThread:2831850 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:20:10,688 INFO    SenderThread:2831850 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:20:10,895 DEBUG   SenderThread:2831850 [sender.py:send():369] send: exit
2025-07-11 22:20:10,895 INFO    SenderThread:2831850 [sender.py:send_exit():574] handling exit code: 1
2025-07-11 22:20:10,895 INFO    SenderThread:2831850 [sender.py:send_exit():576] handling runtime: 81
2025-07-11 22:20:10,896 INFO    SenderThread:2831850 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:20:10,896 INFO    SenderThread:2831850 [sender.py:send_exit():582] send defer
2025-07-11 22:20:10,897 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: defer
2025-07-11 22:20:10,897 INFO    HandlerThread:2831850 [handler.py:handle_request_defer():170] handle defer: 0
2025-07-11 22:20:10,898 DEBUG   SenderThread:2831850 [sender.py:send_request():396] send_request: defer
2025-07-11 22:20:10,898 INFO    SenderThread:2831850 [sender.py:send_request_defer():598] handle sender defer: 0
2025-07-11 22:20:10,898 INFO    SenderThread:2831850 [sender.py:transition_state():602] send defer: 1
2025-07-11 22:20:10,899 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: defer
2025-07-11 22:20:10,899 INFO    HandlerThread:2831850 [handler.py:handle_request_defer():170] handle defer: 1
2025-07-11 22:20:10,899 DEBUG   SenderThread:2831850 [sender.py:send_request():396] send_request: defer
2025-07-11 22:20:10,899 INFO    SenderThread:2831850 [sender.py:send_request_defer():598] handle sender defer: 1
2025-07-11 22:20:10,899 INFO    SenderThread:2831850 [sender.py:transition_state():602] send defer: 2
2025-07-11 22:20:10,900 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: defer
2025-07-11 22:20:10,900 INFO    HandlerThread:2831850 [handler.py:handle_request_defer():170] handle defer: 2
2025-07-11 22:20:10,900 INFO    HandlerThread:2831850 [system_monitor.py:finish():190] Stopping system monitor
2025-07-11 22:20:10,900 DEBUG   SystemMonitor:2831850 [system_monitor.py:_start():166] Finished system metrics aggregation loop
2025-07-11 22:20:10,901 DEBUG   SystemMonitor:2831850 [system_monitor.py:_start():170] Publishing last batch of metrics
2025-07-11 22:20:10,902 INFO    HandlerThread:2831850 [interfaces.py:finish():202] Joined cpu monitor
2025-07-11 22:20:10,903 INFO    HandlerThread:2831850 [interfaces.py:finish():202] Joined disk monitor
2025-07-11 22:20:11,049 INFO    HandlerThread:2831850 [interfaces.py:finish():202] Joined gpu monitor
2025-07-11 22:20:11,050 INFO    HandlerThread:2831850 [interfaces.py:finish():202] Joined memory monitor
2025-07-11 22:20:11,050 INFO    HandlerThread:2831850 [interfaces.py:finish():202] Joined network monitor
2025-07-11 22:20:11,051 DEBUG   SenderThread:2831850 [sender.py:send_request():396] send_request: defer
2025-07-11 22:20:11,051 INFO    SenderThread:2831850 [sender.py:send_request_defer():598] handle sender defer: 2
2025-07-11 22:20:11,051 INFO    SenderThread:2831850 [sender.py:transition_state():602] send defer: 3
2025-07-11 22:20:11,051 DEBUG   SenderThread:2831850 [sender.py:send():369] send: stats
2025-07-11 22:20:11,052 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: defer
2025-07-11 22:20:11,052 INFO    HandlerThread:2831850 [handler.py:handle_request_defer():170] handle defer: 3
2025-07-11 22:20:11,053 DEBUG   SenderThread:2831850 [sender.py:send_request():396] send_request: defer
2025-07-11 22:20:11,053 INFO    SenderThread:2831850 [sender.py:send_request_defer():598] handle sender defer: 3
2025-07-11 22:20:11,053 INFO    SenderThread:2831850 [sender.py:transition_state():602] send defer: 4
2025-07-11 22:20:11,053 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: defer
2025-07-11 22:20:11,053 INFO    HandlerThread:2831850 [handler.py:handle_request_defer():170] handle defer: 4
2025-07-11 22:20:11,054 DEBUG   SenderThread:2831850 [sender.py:send_request():396] send_request: defer
2025-07-11 22:20:11,054 INFO    SenderThread:2831850 [sender.py:send_request_defer():598] handle sender defer: 4
2025-07-11 22:20:11,054 INFO    SenderThread:2831850 [sender.py:transition_state():602] send defer: 5
2025-07-11 22:20:11,054 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: defer
2025-07-11 22:20:11,054 INFO    HandlerThread:2831850 [handler.py:handle_request_defer():170] handle defer: 5
2025-07-11 22:20:11,055 DEBUG   SenderThread:2831850 [sender.py:send():369] send: summary
2025-07-11 22:20:11,055 INFO    SenderThread:2831850 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:20:11,056 DEBUG   SenderThread:2831850 [sender.py:send_request():396] send_request: defer
2025-07-11 22:20:11,056 INFO    SenderThread:2831850 [sender.py:send_request_defer():598] handle sender defer: 5
2025-07-11 22:20:11,056 INFO    SenderThread:2831850 [sender.py:transition_state():602] send defer: 6
2025-07-11 22:20:11,056 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: defer
2025-07-11 22:20:11,056 INFO    HandlerThread:2831850 [handler.py:handle_request_defer():170] handle defer: 6
2025-07-11 22:20:11,056 DEBUG   SenderThread:2831850 [sender.py:send_request():396] send_request: defer
2025-07-11 22:20:11,056 INFO    SenderThread:2831850 [sender.py:send_request_defer():598] handle sender defer: 6
2025-07-11 22:20:11,057 INFO    SenderThread:2831850 [sender.py:transition_state():602] send defer: 7
2025-07-11 22:20:11,057 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:20:11,057 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: defer
2025-07-11 22:20:11,057 INFO    HandlerThread:2831850 [handler.py:handle_request_defer():170] handle defer: 7
2025-07-11 22:20:11,058 DEBUG   SenderThread:2831850 [sender.py:send_request():396] send_request: defer
2025-07-11 22:20:11,058 INFO    SenderThread:2831850 [sender.py:send_request_defer():598] handle sender defer: 7
2025-07-11 22:20:11,415 INFO    Thread-12 :2831850 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-ifsfg35l/files/wandb-summary.json
2025-07-11 22:20:11,893 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: poll_exit
2025-07-11 22:20:12,907 INFO    SenderThread:2831850 [sender.py:transition_state():602] send defer: 8
2025-07-11 22:20:12,907 DEBUG   SenderThread:2831850 [sender.py:send_request():396] send_request: poll_exit
2025-07-11 22:20:12,907 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: defer
2025-07-11 22:20:12,908 INFO    HandlerThread:2831850 [handler.py:handle_request_defer():170] handle defer: 8
2025-07-11 22:20:12,908 DEBUG   SenderThread:2831850 [sender.py:send_request():396] send_request: defer
2025-07-11 22:20:12,908 INFO    SenderThread:2831850 [sender.py:send_request_defer():598] handle sender defer: 8
2025-07-11 22:20:12,908 INFO    SenderThread:2831850 [job_builder.py:build():232] Attempting to build job artifact
2025-07-11 22:20:12,908 INFO    SenderThread:2831850 [job_builder.py:build():256] is repo sourced job
2025-07-11 22:20:12,910 INFO    SenderThread:2831850 [job_builder.py:build():297] adding wandb-job metadata file
2025-07-11 22:20:12,924 INFO    SenderThread:2831850 [sender.py:transition_state():602] send defer: 9
2025-07-11 22:20:12,924 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: defer
2025-07-11 22:20:12,924 DEBUG   SenderThread:2831850 [sender.py:send():369] send: artifact
2025-07-11 22:20:12,925 INFO    HandlerThread:2831850 [handler.py:handle_request_defer():170] handle defer: 9
2025-07-11 22:20:13,416 INFO    Thread-12 :2831850 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-ifsfg35l/files/output.log
2025-07-11 22:20:13,566 INFO    wandb-upload_1:2831850 [upload_job.py:push():91] Uploaded file /gel/usr/akath/.local/share/wandb/artifacts/staging/tmpeoz4kqib
2025-07-11 22:20:13,573 INFO    wandb-upload_2:2831850 [upload_job.py:push():91] Uploaded file /gel/usr/akath/.local/share/wandb/artifacts/staging/tmpmfjn88nw
2025-07-11 22:20:14,405 INFO    SenderThread:2831850 [sender.py:send_artifact():1450] sent artifact job-https___github.com_Shrinidhibhat87_codingchallenge_sereact.git_main.py - {'id': 'QXJ0aWZhY3Q6MTg3NDAzMjEwMA==', 'digest': '4f4ba9767a27b00621d65a0eba015245', 'state': 'PENDING', 'aliases': [], 'artifactSequence': {'id': 'QXJ0aWZhY3RDb2xsZWN0aW9uOjY4NDgwMzQ0Ng==', 'latestArtifact': None}, 'version': 'latest'}
2025-07-11 22:20:14,405 DEBUG   SenderThread:2831850 [sender.py:send_request():396] send_request: defer
2025-07-11 22:20:14,405 INFO    SenderThread:2831850 [sender.py:send_request_defer():598] handle sender defer: 9
2025-07-11 22:20:14,405 INFO    SenderThread:2831850 [dir_watcher.py:finish():359] shutting down directory watcher
2025-07-11 22:20:14,417 INFO    SenderThread:2831850 [dir_watcher.py:finish():389] scan: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-ifsfg35l/files
2025-07-11 22:20:14,417 INFO    SenderThread:2831850 [dir_watcher.py:finish():403] scan save: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-ifsfg35l/files/config.yaml config.yaml
2025-07-11 22:20:14,417 INFO    SenderThread:2831850 [dir_watcher.py:finish():403] scan save: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-ifsfg35l/files/wandb-summary.json wandb-summary.json
2025-07-11 22:20:14,423 INFO    SenderThread:2831850 [dir_watcher.py:finish():403] scan save: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-ifsfg35l/files/requirements.txt requirements.txt
2025-07-11 22:20:14,424 INFO    SenderThread:2831850 [dir_watcher.py:finish():403] scan save: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-ifsfg35l/files/conda-environment.yaml conda-environment.yaml
2025-07-11 22:20:14,439 INFO    SenderThread:2831850 [dir_watcher.py:finish():403] scan save: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-ifsfg35l/files/wandb-metadata.json wandb-metadata.json
2025-07-11 22:20:14,439 INFO    SenderThread:2831850 [dir_watcher.py:finish():403] scan save: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-ifsfg35l/files/output.log output.log
2025-07-11 22:20:14,444 INFO    SenderThread:2831850 [sender.py:transition_state():602] send defer: 10
2025-07-11 22:20:14,444 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: defer
2025-07-11 22:20:14,450 INFO    HandlerThread:2831850 [handler.py:handle_request_defer():170] handle defer: 10
2025-07-11 22:20:14,451 DEBUG   SenderThread:2831850 [sender.py:send_request():396] send_request: defer
2025-07-11 22:20:14,452 INFO    SenderThread:2831850 [sender.py:send_request_defer():598] handle sender defer: 10
2025-07-11 22:20:14,452 INFO    SenderThread:2831850 [file_pusher.py:finish():159] shutting down file pusher
2025-07-11 22:20:14,676 INFO    wandb-upload_2:2831850 [upload_job.py:push():133] Uploaded file /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-ifsfg35l/files/wandb-summary.json
2025-07-11 22:20:14,719 INFO    wandb-upload_0:2831850 [upload_job.py:push():133] Uploaded file /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-ifsfg35l/files/config.yaml
2025-07-11 22:20:14,729 INFO    wandb-upload_5:2831850 [upload_job.py:push():133] Uploaded file /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-ifsfg35l/files/requirements.txt
2025-07-11 22:20:14,745 INFO    wandb-upload_7:2831850 [upload_job.py:push():133] Uploaded file /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-ifsfg35l/files/output.log
2025-07-11 22:20:14,793 INFO    wandb-upload_6:2831850 [upload_job.py:push():133] Uploaded file /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-ifsfg35l/files/conda-environment.yaml
2025-07-11 22:20:14,993 INFO    Thread-11 :2831850 [sender.py:transition_state():602] send defer: 11
2025-07-11 22:20:14,994 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: defer
2025-07-11 22:20:14,994 INFO    HandlerThread:2831850 [handler.py:handle_request_defer():170] handle defer: 11
2025-07-11 22:20:14,994 DEBUG   SenderThread:2831850 [sender.py:send_request():396] send_request: defer
2025-07-11 22:20:14,994 INFO    SenderThread:2831850 [sender.py:send_request_defer():598] handle sender defer: 11
2025-07-11 22:20:14,995 INFO    SenderThread:2831850 [file_pusher.py:join():164] waiting for file pusher
2025-07-11 22:20:14,995 INFO    SenderThread:2831850 [sender.py:transition_state():602] send defer: 12
2025-07-11 22:20:14,995 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: defer
2025-07-11 22:20:14,995 INFO    HandlerThread:2831850 [handler.py:handle_request_defer():170] handle defer: 12
2025-07-11 22:20:14,995 DEBUG   SenderThread:2831850 [sender.py:send_request():396] send_request: defer
2025-07-11 22:20:14,995 INFO    SenderThread:2831850 [sender.py:send_request_defer():598] handle sender defer: 12
2025-07-11 22:20:15,340 INFO    SenderThread:2831850 [sender.py:transition_state():602] send defer: 13
2025-07-11 22:20:15,340 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: defer
2025-07-11 22:20:15,340 INFO    HandlerThread:2831850 [handler.py:handle_request_defer():170] handle defer: 13
2025-07-11 22:20:15,341 DEBUG   SenderThread:2831850 [sender.py:send_request():396] send_request: defer
2025-07-11 22:20:15,341 INFO    SenderThread:2831850 [sender.py:send_request_defer():598] handle sender defer: 13
2025-07-11 22:20:15,341 INFO    SenderThread:2831850 [sender.py:transition_state():602] send defer: 14
2025-07-11 22:20:15,341 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: defer
2025-07-11 22:20:15,341 DEBUG   SenderThread:2831850 [sender.py:send():369] send: final
2025-07-11 22:20:15,341 INFO    HandlerThread:2831850 [handler.py:handle_request_defer():170] handle defer: 14
2025-07-11 22:20:15,341 DEBUG   SenderThread:2831850 [sender.py:send():369] send: footer
2025-07-11 22:20:15,341 DEBUG   SenderThread:2831850 [sender.py:send_request():396] send_request: defer
2025-07-11 22:20:15,342 INFO    SenderThread:2831850 [sender.py:send_request_defer():598] handle sender defer: 14
2025-07-11 22:20:15,342 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: poll_exit
2025-07-11 22:20:15,343 DEBUG   SenderThread:2831850 [sender.py:send_request():396] send_request: poll_exit
2025-07-11 22:20:15,343 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: server_info
2025-07-11 22:20:15,344 DEBUG   SenderThread:2831850 [sender.py:send_request():396] send_request: server_info
2025-07-11 22:20:15,346 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: get_summary
2025-07-11 22:20:15,347 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: sampled_history
2025-07-11 22:20:15,402 INFO    MainThread:2831850 [wandb_run.py:_footer_history_summary_info():3467] rendering history
2025-07-11 22:20:15,403 INFO    MainThread:2831850 [wandb_run.py:_footer_history_summary_info():3499] rendering summary
2025-07-11 22:20:15,403 INFO    MainThread:2831850 [wandb_run.py:_footer_sync_info():3426] logging synced files
2025-07-11 22:20:15,403 DEBUG   HandlerThread:2831850 [handler.py:handle_request():144] handle_request: shutdown
2025-07-11 22:20:15,403 INFO    HandlerThread:2831850 [handler.py:finish():854] shutting down handler
2025-07-11 22:20:16,344 INFO    WriterThread:2831850 [datastore.py:close():298] close: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-ifsfg35l/run-ifsfg35l.wandb
2025-07-11 22:20:16,402 INFO    SenderThread:2831850 [sender.py:finish():1526] shutting down sender
2025-07-11 22:20:16,402 INFO    SenderThread:2831850 [file_pusher.py:finish():159] shutting down file pusher
2025-07-11 22:20:16,402 INFO    SenderThread:2831850 [file_pusher.py:join():164] waiting for file pusher
