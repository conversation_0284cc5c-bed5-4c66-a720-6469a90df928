2025-07-13 16:07:09,476 INFO    MainThread:2917502 [wandb_setup.py:_flush():76] Current SDK version is 0.15.4
2025-07-13 16:07:09,476 INFO    MainThread:2917502 [wandb_setup.py:_flush():76] Configure stats pid to 2917502
2025-07-13 16:07:09,476 INFO    MainThread:2917502 [wandb_setup.py:_flush():76] Loading settings from /gel/usr/akath/.config/wandb/settings
2025-07-13 16:07:09,476 INFO    MainThread:2917502 [wandb_setup.py:_flush():76] Loading settings from /home-local/akath.nobkp/sereact/wandb/settings
2025-07-13 16:07:09,477 INFO    MainThread:2917502 [wandb_setup.py:_flush():76] Loading settings from environment variables: {}
2025-07-13 16:07:09,477 INFO    MainThread:2917502 [wandb_setup.py:_flush():76] Applying setup settings: {'_disable_service': False}
2025-07-13 16:07:09,477 INFO    MainThread:2917502 [wandb_setup.py:_flush():76] Inferring run settings from compute environment: {'program_relpath': 'main.py', 'program': 'main.py'}
2025-07-13 16:07:09,477 INFO    MainThread:2917502 [wandb_init.py:_log_setup():507] Logging user logs to /home-local/akath.nobkp/sereact/wandb/run-20250713_160709-8rziddh2/logs/debug.log
2025-07-13 16:07:09,477 INFO    MainThread:2917502 [wandb_init.py:_log_setup():508] Logging internal logs to /home-local/akath.nobkp/sereact/wandb/run-20250713_160709-8rziddh2/logs/debug-internal.log
2025-07-13 16:07:09,477 INFO    MainThread:2917502 [wandb_init.py:init():547] calling init triggers
2025-07-13 16:07:09,477 INFO    MainThread:2917502 [wandb_init.py:init():555] wandb.init called with sweep_config: {}
config: {}
2025-07-13 16:07:09,477 INFO    MainThread:2917502 [wandb_init.py:init():596] starting backend
2025-07-13 16:07:09,477 INFO    MainThread:2917502 [wandb_init.py:init():600] setting up manager
2025-07-13 16:07:09,487 INFO    MainThread:2917502 [backend.py:_multiprocessing_setup():108] multiprocessing start_methods=fork,spawn,forkserver, using: spawn
2025-07-13 16:07:09,491 INFO    MainThread:2917502 [wandb_init.py:init():606] backend started and connected
2025-07-13 16:07:09,496 INFO    MainThread:2917502 [wandb_init.py:init():703] updated telemetry
2025-07-13 16:07:09,518 INFO    MainThread:2917502 [wandb_init.py:init():736] communicating run to backend with 60.0 second timeout
2025-07-13 16:07:09,785 INFO    MainThread:2917502 [wandb_run.py:_on_init():2176] communicating current version
2025-07-13 16:07:09,838 INFO    MainThread:2917502 [wandb_run.py:_on_init():2185] got version response upgrade_message: "wandb version 0.21.0 is available!  To upgrade, please run:\n $ pip install wandb --upgrade"

2025-07-13 16:07:09,838 INFO    MainThread:2917502 [wandb_init.py:init():787] starting run threads in backend
