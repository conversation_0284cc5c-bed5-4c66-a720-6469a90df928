2025-07-13 16:07:09,493 INFO    StreamThr :2917542 [internal.py:wandb_internal():89] W&B internal server running at pid: 2917542, started at: 2025-07-13 16:07:09.492990
2025-07-13 16:07:09,495 DEBUG   HandlerThread:2917542 [handler.py:handle_request():144] handle_request: status
2025-07-13 16:07:09,497 INFO    WriterThread:2917542 [datastore.py:open_for_write():85] open: /home-local/akath.nobkp/sereact/wandb/run-20250713_160709-8rziddh2/run-8rziddh2.wandb
2025-07-13 16:07:09,498 DEBUG   SenderThread:2917542 [sender.py:send():369] send: header
2025-07-13 16:07:09,519 DEBUG   SenderThread:2917542 [sender.py:send():369] send: run
2025-07-13 16:07:09,773 INFO    SenderThread:2917542 [dir_watcher.py:__init__():211] watching files in: /home-local/akath.nobkp/sereact/wandb/run-20250713_160709-8rziddh2/files
2025-07-13 16:07:09,774 INFO    SenderThread:2917542 [sender.py:_start_run_threads():1103] run started: 8rziddh2 with start time 1752437229.491327
2025-07-13 16:07:09,777 DEBUG   SenderThread:2917542 [sender.py:send_request():396] send_request: summary_record
2025-07-13 16:07:09,777 INFO    SenderThread:2917542 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 16:07:09,785 DEBUG   HandlerThread:2917542 [handler.py:handle_request():144] handle_request: check_version
2025-07-13 16:07:09,786 DEBUG   SenderThread:2917542 [sender.py:send_request():396] send_request: check_version
2025-07-13 16:07:09,848 DEBUG   HandlerThread:2917542 [handler.py:handle_request():144] handle_request: run_start
2025-07-13 16:07:09,853 DEBUG   HandlerThread:2917542 [system_info.py:__init__():31] System info init
2025-07-13 16:07:09,853 DEBUG   HandlerThread:2917542 [system_info.py:__init__():46] System info init done
2025-07-13 16:07:09,853 INFO    HandlerThread:2917542 [system_monitor.py:start():181] Starting system monitor
2025-07-13 16:07:09,854 INFO    SystemMonitor:2917542 [system_monitor.py:_start():145] Starting system asset monitoring threads
2025-07-13 16:07:09,854 INFO    HandlerThread:2917542 [system_monitor.py:probe():201] Collecting system info
2025-07-13 16:07:09,855 INFO    SystemMonitor:2917542 [interfaces.py:start():190] Started cpu monitoring
2025-07-13 16:07:09,856 INFO    SystemMonitor:2917542 [interfaces.py:start():190] Started disk monitoring
2025-07-13 16:07:09,858 INFO    SystemMonitor:2917542 [interfaces.py:start():190] Started gpu monitoring
2025-07-13 16:07:09,858 INFO    SystemMonitor:2917542 [interfaces.py:start():190] Started memory monitoring
2025-07-13 16:07:09,859 INFO    SystemMonitor:2917542 [interfaces.py:start():190] Started network monitoring
2025-07-13 16:07:09,900 DEBUG   HandlerThread:2917542 [system_info.py:probe():195] Probing system
2025-07-13 16:07:09,909 DEBUG   HandlerThread:2917542 [system_info.py:_probe_git():180] Probing git
2025-07-13 16:07:09,926 DEBUG   HandlerThread:2917542 [system_info.py:_probe_git():188] Probing git done
2025-07-13 16:07:09,927 DEBUG   HandlerThread:2917542 [system_info.py:probe():240] Probing system done
2025-07-13 16:07:09,927 DEBUG   HandlerThread:2917542 [system_monitor.py:probe():210] {'os': 'Linux-5.4.0-77-generic-x86_64-with-debian-bullseye-sid', 'python': '3.7.16', 'heartbeatAt': '2025-07-13T20:07:09.900639', 'startedAt': '2025-07-13T20:07:09.473723', 'docker': None, 'cuda': None, 'args': ('--local_rank=0', '--cfg', 'config/base_train.yaml', '--output', '/home-local2/akath.extra.nobkp/sereact', '--data-path', '/home-local2/akath.extra.nobkp/dl_challenge', '--batch-size', '1'), 'state': 'running', 'program': 'main.py', 'codePath': 'main.py', 'git': {'remote': 'https://github.com/Shrinidhibhat87/codingchallenge_sereact.git', 'commit': '667b7ded061f079ea281aa2193d47b61df08fec6'}, 'email': '<EMAIL>', 'root': '/home-local/akath.nobkp/sereact', 'host': 'lv3-32190', 'username': 'akath', 'executable': '/gel/usr/akath/.conda/envs/swin/bin/python', 'cpu_count': 6, 'cpu_count_logical': 12, 'cpu_freq': {'current': 1.4103333333333332, 'min': 1200.0, 'max': 4000.0}, 'cpu_freq_per_core': [{'current': 1.555, 'min': 1200.0, 'max': 4000.0}, {'current': 1.199, 'min': 1200.0, 'max': 4000.0}, {'current': 1.199, 'min': 1200.0, 'max': 4000.0}, {'current': 1.319, 'min': 1200.0, 'max': 4000.0}, {'current': 1.199, 'min': 1200.0, 'max': 4000.0}, {'current': 1.969, 'min': 1200.0, 'max': 4000.0}, {'current': 1.595, 'min': 1200.0, 'max': 4000.0}, {'current': 1.199, 'min': 1200.0, 'max': 4000.0}, {'current': 1.201, 'min': 1200.0, 'max': 4000.0}, {'current': 1.331, 'min': 1200.0, 'max': 4000.0}, {'current': 1.204, 'min': 1200.0, 'max': 4000.0}, {'current': 1.954, 'min': 1200.0, 'max': 4000.0}], 'disk': {'total': 111.2200813293457, 'used': 53.117218017578125}, 'gpu': 'TITAN Xp', 'gpu_count': 3, 'gpu_devices': [{'name': 'TITAN Xp', 'memory_total': 12787122176}, {'name': 'TITAN Xp', 'memory_total': 12788498432}, {'name': 'TITAN Xp', 'memory_total': 12788498432}], 'memory': {'total': 62.72977066040039}}
2025-07-13 16:07:09,927 INFO    HandlerThread:2917542 [system_monitor.py:probe():211] Finished collecting system info
2025-07-13 16:07:09,927 INFO    HandlerThread:2917542 [system_monitor.py:probe():214] Publishing system info
2025-07-13 16:07:09,927 DEBUG   HandlerThread:2917542 [system_info.py:_save_pip():52] Saving list of pip packages installed into the current environment
2025-07-13 16:07:09,928 DEBUG   HandlerThread:2917542 [system_info.py:_save_pip():67] Saving pip packages done
2025-07-13 16:07:09,928 DEBUG   HandlerThread:2917542 [system_info.py:_save_conda():75] Saving list of conda packages installed into the current environment
2025-07-13 16:07:10,776 INFO    Thread-12 :2917542 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/sereact/wandb/run-20250713_160709-8rziddh2/files/wandb-summary.json
2025-07-13 16:07:10,777 INFO    Thread-12 :2917542 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/sereact/wandb/run-20250713_160709-8rziddh2/files/conda-environment.yaml
2025-07-13 16:07:10,777 INFO    Thread-12 :2917542 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/sereact/wandb/run-20250713_160709-8rziddh2/files/requirements.txt
2025-07-13 16:07:11,862 INFO    cpu       :2917542 [interfaces.py:monitor():140] Process cpu has exited.
2025-07-13 16:07:11,863 DEBUG   SystemMonitor:2917542 [system_monitor.py:_start():159] Starting system metrics aggregation loop
2025-07-13 16:07:11,863 DEBUG   SystemMonitor:2917542 [system_monitor.py:_start():166] Finished system metrics aggregation loop
2025-07-13 16:07:11,863 DEBUG   SystemMonitor:2917542 [system_monitor.py:_start():170] Publishing last batch of metrics
2025-07-13 16:07:11,865 INFO    memory    :2917542 [interfaces.py:monitor():140] Process proc.memory.rssMB has exited.
2025-07-13 16:07:12,849 INFO    MainThread:2917542 [internal.py:handle_exit():76] Internal process exited
