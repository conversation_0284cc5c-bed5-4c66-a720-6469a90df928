=> merge config from config/base_train.yaml
RANK and WORLD_SIZE in environ: 0/3
[32m[2025-07-13 18:18:41 3DDETR.yaml][33m(main.py 552)[39m: INFO Full config saved to /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/config.json
[32m[2025-07-13 18:18:41 3DDETR.yaml][33m(main.py 555)[39m: INFO AMP_ENABLE: true
TAG: default
amp_opt_level: ''
base:
- ''
data:
  augment: false
  batch_size: 2
  cache_mode: part
  data_path: /home-local2/akath.extra.nobkp/dl_challenge
  dataset: Sereact_dataset
  debug: false
  num_workers: 4
  pin_memory: true
  transform: null
  zip_mode: false
eval_mode: false
local_rank: 0
loss:
  matcher_costs:
    cost_box_corners: 1.0
    giou: 5.0
    l1: 2.0
  weights:
    box_corners: 1.0
    giou: 1.0
    size: 1.0
    size_reg: 1.0
model:
  decoder:
    dim: 256
    dropout: 0.1
    ffn_dim: 256
    nhead: 4
    num_layers: 3
  encoder:
    activation: relu
    dim: 256
    dropout: 0.1
    ffn_dim: 128
    nheads: 4
    num_layers: 3
    preencoder_npoints: 2048
    type: vanilla
    use_color: false
  export_model: false
  mlp_dropout: 0.3
  name: 3DDETR.yaml
  num_angular_bins: 12
  num_queries: 256
  position_embedding: fourier
  pretrained: null
  pretrained_weights_path: /home/<USER>/Coding/Pre_trained_Weights/3detr/scannet_ep1080.pth
  resume: ''
  training: true
  unit_test: false
output: /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123
print_freq: 10
save_freq: 1
seed: 40
tag: '123'
train:
  accumulation_steps: 1
  auto_resume: false
  base_lr: 0.0001
  clip_grad: 0.1
  filter_biases_wd: true
  final_lr: 1.0e-06
  lr_scheduler: cosine
  max_epoch: 200
  start_epoch: 0
  unit_test_epoch: 100
  use_checkpoint: false
  warm_lr: 5.0e-06
  warm_lr_epochs: 9
  weight_decay: 0.01
unit_test: false
[32m[2025-07-13 18:18:41 3DDETR.yaml][33m(main.py 556)[39m: INFO {"cfg": "config/base_train.yaml", "opts": null, "batch_size": 2, "data_path": "/home-local2/akath.extra.nobkp/dl_challenge", "pretrained": null, "resume": null, "accumulation_steps": null, "use_checkpoint": false, "disable_amp": false, "amp_opt_level": null, "output": "/home-local2/akath.extra.nobkp/sereact", "tag": null, "eval": false, "unit_test": false, "base_lr": null, "local_rank": 0}
local rank 0 / global rank 0 successfully build train dataset
local rank 0 / global rank 0 successfully build val dataset
[32m[2025-07-13 18:18:42 3DDETR.yaml][33m(main.py 101)[39m: INFO Model3DDETR(
  (pre_encoder): PointnetSAModuleVotes(
    (grouper): QueryAndGroup()
    (mlp_module): SharedMLP(
      (layer0): Conv2d(
        (conv): Conv2d(3, 64, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn): BatchNorm2d(
          (bn): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        )
        (activation): ReLU(inplace=True)
      )
      (layer1): Conv2d(
        (conv): Conv2d(64, 128, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn): BatchNorm2d(
          (bn): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        )
        (activation): ReLU(inplace=True)
      )
      (layer2): Conv2d(
        (conv): Conv2d(128, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn): BatchNorm2d(
          (bn): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        )
        (activation): ReLU(inplace=True)
      )
    )
  )
  (encoder): TransformerEncoder(
    (layers): ModuleList(
      (0): TransformerEncoderLayer(
        attn_dr=0.1
        (self_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (linear1): Linear(in_features=256, out_features=128, bias=True)
        (dropout): Dropout(p=0.1, inplace=False)
        (linear2): Linear(in_features=128, out_features=256, bias=True)
        (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout2): Dropout(p=0.1, inplace=False)
        (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout1): Dropout(p=0.1, inplace=False)
        (activation): ReLU()
      )
      (1): TransformerEncoderLayer(
        attn_dr=0.1
        (self_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (linear1): Linear(in_features=256, out_features=128, bias=True)
        (dropout): Dropout(p=0.1, inplace=False)
        (linear2): Linear(in_features=128, out_features=256, bias=True)
        (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout2): Dropout(p=0.1, inplace=False)
        (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout1): Dropout(p=0.1, inplace=False)
        (activation): ReLU()
      )
      (2): TransformerEncoderLayer(
        attn_dr=0.1
        (self_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (linear1): Linear(in_features=256, out_features=128, bias=True)
        (dropout): Dropout(p=0.1, inplace=False)
        (linear2): Linear(in_features=128, out_features=256, bias=True)
        (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout2): Dropout(p=0.1, inplace=False)
        (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout1): Dropout(p=0.1, inplace=False)
        (activation): ReLU()
      )
    )
  )
  (encoder_decoder_projection): GenericMLP(
    (layers): Sequential(
      (0): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
      (1): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (2): ReLU()
      (3): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
      (4): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (5): ReLU()
    )
  )
  (positional_embedding): PositionEmbeddingCoordsSine(type=fourier, scale=6.283185307179586, normalize=True, gaussB_shape=torch.Size([3, 128]), gaussB_sum=-17.944507598876953)
  (query_projection): GenericMLP(
    (layers): Sequential(
      (0): Conv1d(256, 256, kernel_size=(1,), stride=(1,))
      (1): ReLU()
      (2): Conv1d(256, 256, kernel_size=(1,), stride=(1,))
      (3): ReLU()
    )
  )
  (rgb_backbone): Sequential(
    (0): Conv2d(3, 64, kernel_size=(7, 7), stride=(2, 2), padding=(3, 3), bias=False)
    (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
    (2): ReLU(inplace=True)
    (3): MaxPool2d(kernel_size=3, stride=2, padding=1, dilation=1, ceil_mode=False)
    (4): Sequential(
      (0): BasicBlock(
        (conv1): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
        (conv2): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      )
      (1): BasicBlock(
        (conv1): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
        (conv2): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      )
    )
    (5): Sequential(
      (0): BasicBlock(
        (conv1): Conv2d(64, 128, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1), bias=False)
        (bn1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
        (conv2): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (downsample): Sequential(
          (0): Conv2d(64, 128, kernel_size=(1, 1), stride=(2, 2), bias=False)
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        )
      )
      (1): BasicBlock(
        (conv1): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
        (conv2): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      )
    )
  )
  (rgb_proj): Conv1d(128, 3, kernel_size=(1,), stride=(1,))
  (decoder): TransformerDecoder(
    (layers): ModuleList(
      (0): TransformerDecoderLayer(
        (self_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (multihead_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm3): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout1): Dropout(p=0.1, inplace=False)
        (dropout2): Dropout(p=0.1, inplace=False)
        (dropout3): Dropout(p=0.1, inplace=False)
        (linear1): Linear(in_features=256, out_features=256, bias=True)
        (dropout): Dropout(p=0.1, inplace=False)
        (linear2): Linear(in_features=256, out_features=256, bias=True)
        (activation): ReLU()
      )
      (1): TransformerDecoderLayer(
        (self_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (multihead_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm3): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout1): Dropout(p=0.1, inplace=False)
        (dropout2): Dropout(p=0.1, inplace=False)
        (dropout3): Dropout(p=0.1, inplace=False)
        (linear1): Linear(in_features=256, out_features=256, bias=True)
        (dropout): Dropout(p=0.1, inplace=False)
        (linear2): Linear(in_features=256, out_features=256, bias=True)
        (activation): ReLU()
      )
      (2): TransformerDecoderLayer(
        (self_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (multihead_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm3): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout1): Dropout(p=0.1, inplace=False)
        (dropout2): Dropout(p=0.1, inplace=False)
        (dropout3): Dropout(p=0.1, inplace=False)
        (linear1): Linear(in_features=256, out_features=256, bias=True)
        (dropout): Dropout(p=0.1, inplace=False)
        (linear2): Linear(in_features=256, out_features=256, bias=True)
        (activation): ReLU()
      )
    )
    (norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (mlp_heads): ModuleDict(
    (center_head): GenericMLP(
      (layers): Sequential(
        (0): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
        (1): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU()
        (3): Dropout(p=0.3, inplace=False)
        (4): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
        (5): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (6): ReLU()
        (7): Dropout(p=0.3, inplace=False)
        (8): Conv1d(256, 3, kernel_size=(1,), stride=(1,))
      )
    )
    (size_head): GenericMLP(
      (layers): Sequential(
        (0): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
        (1): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU()
        (3): Dropout(p=0.3, inplace=False)
        (4): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
        (5): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (6): ReLU()
        (7): Dropout(p=0.3, inplace=False)
        (8): Conv1d(256, 3, kernel_size=(1,), stride=(1,))
      )
    )
    (angle_cls_head): GenericMLP(
      (layers): Sequential(
        (0): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
        (1): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU()
        (3): Dropout(p=0.3, inplace=False)
        (4): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
        (5): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (6): ReLU()
        (7): Dropout(p=0.3, inplace=False)
        (8): Conv1d(256, 12, kernel_size=(1,), stride=(1,))
      )
    )
    (angle_residual_head): GenericMLP(
      (layers): Sequential(
        (0): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
        (1): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU()
        (3): Dropout(p=0.3, inplace=False)
        (4): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
        (5): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (6): ReLU()
        (7): Dropout(p=0.3, inplace=False)
        (8): Conv1d(256, 12, kernel_size=(1,), stride=(1,))
      )
    )
  )
)
[32m[2025-07-13 18:18:42 3DDETR.yaml][33m(main.py 103)[39m: INFO number of params: 4494497
[32m[2025-07-13 18:18:42 3DDETR.yaml][33m(main.py 151)[39m: INFO Start training
[32m[2025-07-13 18:18:48 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [0/200][0/27]	eta 0:02:35 lr 0.000100	time 5.7515 (5.7515)	loss 45.0498 (45.0498)	miou 0.0223	grad_norm 462.3711 (462.3711)	loss_scale 65536.0000 (65536.0000)	mem 4934MB
[32m[2025-07-13 18:19:22 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [0/200][10/27]	eta 0:01:02 lr 0.000099	time 3.3641 (3.6511)	loss 17.9470 (24.3482)	miou 0.1086	grad_norm 242.2663 (602.6478)	loss_scale 65536.0000 (65536.0000)	mem 5742MB
[32m[2025-07-13 18:19:52 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [0/200][20/27]	eta 0:00:23 lr 0.000097	time 3.2368 (3.3613)	loss 15.4929 (23.1041)	miou 0.1114	grad_norm 238.3264 (754.3000)	loss_scale 65536.0000 (65536.0000)	mem 5742MB
[32m[2025-07-13 18:20:11 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 0 training takes 0:01:29
[32m[2025-07-13 18:20:11 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
0
[32m[2025-07-13 18:20:25 3DDETR.yaml][33m(utils_help.py 85)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saving......
[32m[2025-07-13 18:20:25 3DDETR.yaml][33m(utils_help.py 87)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saved !!!
[32m[2025-07-13 18:20:25 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.0875%
[32m[2025-07-13 18:20:25 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.0875%
[32m[2025-07-13 18:20:29 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [1/200][0/27]	eta 0:01:52 lr 0.000095	time 4.1560 (4.1560)	loss 17.8668 (17.8668)	miou 0.0870	grad_norm 302.3055 (302.3055)	loss_scale 65536.0000 (65536.0000)	mem 5742MB
[32m[2025-07-13 18:20:55 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [1/200][10/27]	eta 0:00:46 lr 0.000091	time 2.4158 (2.7182)	loss 13.2496 (16.1596)	miou 0.1202	grad_norm 116.5758 (137.5979)	loss_scale 65536.0000 (65536.0000)	mem 5742MB
[32m[2025-07-13 18:21:18 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [1/200][20/27]	eta 0:00:17 lr 0.000086	time 2.7759 (2.5115)	loss 18.1563 (14.9474)	miou 0.1490	grad_norm 664.6860 (173.7224)	loss_scale 65536.0000 (65536.0000)	mem 5742MB
[32m[2025-07-13 18:21:33 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 1 training takes 0:01:08
[32m[2025-07-13 18:21:33 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 18:21:44 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.0813%
[32m[2025-07-13 18:21:44 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.0875%
[32m[2025-07-13 18:21:47 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [2/200][0/27]	eta 0:01:28 lr 0.000082	time 3.2948 (3.2948)	loss 13.0393 (13.0393)	miou 0.0825	grad_norm 107.3022 (107.3022)	loss_scale 65536.0000 (65536.0000)	mem 5742MB
[32m[2025-07-13 18:22:09 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [2/200][10/27]	eta 0:00:39 lr 0.000076	time 1.7845 (2.3230)	loss 11.1094 (12.9373)	miou 0.1335	grad_norm 110.2609 (124.6203)	loss_scale 65536.0000 (65536.0000)	mem 5742MB
[32m[2025-07-13 18:22:31 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [2/200][20/27]	eta 0:00:15 lr 0.000069	time 2.5479 (2.2469)	loss 16.5643 (14.7617)	miou 0.1442	grad_norm 160.7113 (172.1409)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:22:44 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 2 training takes 0:01:00
[32m[2025-07-13 18:22:44 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
2
[32m[2025-07-13 18:22:54 3DDETR.yaml][33m(utils_help.py 85)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saving......
[32m[2025-07-13 18:22:54 3DDETR.yaml][33m(utils_help.py 87)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saved !!!
[32m[2025-07-13 18:22:54 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1573%
[32m[2025-07-13 18:22:54 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.1573%
[32m[2025-07-13 18:22:57 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [3/200][0/27]	eta 0:01:15 lr 0.000064	time 2.7928 (2.7928)	loss 13.6395 (13.6395)	miou 0.1559	grad_norm 193.8773 (193.8773)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:23:19 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [3/200][10/27]	eta 0:00:37 lr 0.000056	time 1.6326 (2.1878)	loss 9.6141 (14.3418)	miou 0.1478	grad_norm 51.6288 (190.3671)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:23:40 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [3/200][20/27]	eta 0:00:15 lr 0.000048	time 2.0587 (2.1641)	loss 11.6115 (13.5747)	miou 0.1484	grad_norm 80.5423 (188.1857)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:23:53 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 3 training takes 0:00:58
[32m[2025-07-13 18:23:53 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
3
[32m[2025-07-13 18:24:03 3DDETR.yaml][33m(utils_help.py 85)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saving......
[32m[2025-07-13 18:24:04 3DDETR.yaml][33m(utils_help.py 87)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saved !!!
[32m[2025-07-13 18:24:04 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.2273%
[32m[2025-07-13 18:24:04 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2273%
[32m[2025-07-13 18:24:06 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [4/200][0/27]	eta 0:00:59 lr 0.000043	time 2.1952 (2.1952)	loss 9.6860 (9.6860)	miou 0.2219	grad_norm 173.0682 (173.0682)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:24:26 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [4/200][10/27]	eta 0:00:34 lr 0.000035	time 1.8969 (2.0073)	loss 13.4606 (12.2257)	miou 0.1881	grad_norm 91.9127 (200.2218)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:24:45 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [4/200][20/27]	eta 0:00:13 lr 0.000028	time 1.8850 (1.9747)	loss 10.6687 (11.8149)	miou 0.1861	grad_norm 86.5123 (252.5495)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:24:58 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 4 training takes 0:00:54
[32m[2025-07-13 18:24:58 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 18:25:08 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1805%
[32m[2025-07-13 18:25:08 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2273%
[32m[2025-07-13 18:25:11 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [5/200][0/27]	eta 0:01:27 lr 0.000023	time 3.2401 (3.2401)	loss 8.5380 (8.5380)	miou 0.1781	grad_norm 24377.3359 (24377.3359)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:25:30 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [5/200][10/27]	eta 0:00:33 lr 0.000017	time 1.3555 (1.9842)	loss 11.3585 (11.2631)	miou 0.1773	grad_norm 106.4192 (2717.5833)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:25:50 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [5/200][20/27]	eta 0:00:13 lr 0.000011	time 1.8368 (1.9856)	loss 13.4748 (11.0750)	miou 0.1808	grad_norm 152.9448 (1585.3793)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:26:02 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 5 training takes 0:00:53
[32m[2025-07-13 18:26:02 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 18:26:12 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.2109%
[32m[2025-07-13 18:26:12 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2273%
[32m[2025-07-13 18:26:15 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [6/200][0/27]	eta 0:01:08 lr 0.000008	time 2.5299 (2.5299)	loss 9.2778 (9.2778)	miou 0.2113	grad_norm 191.1545 (191.1545)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:26:34 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [6/200][10/27]	eta 0:00:33 lr 0.000004	time 1.7323 (1.9705)	loss 14.5835 (10.9834)	miou 0.1933	grad_norm 164.3467 (913.9500)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:26:53 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [6/200][20/27]	eta 0:00:13 lr 0.000002	time 1.9350 (1.9278)	loss 9.0991 (11.4988)	miou 0.1914	grad_norm 268.9030 (712.7175)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:27:04 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 6 training takes 0:00:52
[32m[2025-07-13 18:27:04 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 18:27:14 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.2264%
[32m[2025-07-13 18:27:14 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2273%
[32m[2025-07-13 18:27:17 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [7/200][0/27]	eta 0:01:10 lr 0.000001	time 2.6197 (2.6197)	loss 9.7105 (9.7105)	miou 0.2145	grad_norm 125.8730 (125.8730)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:27:36 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [7/200][10/27]	eta 0:00:33 lr 0.000000	time 1.5581 (1.9629)	loss 10.8598 (11.9250)	miou 0.1950	grad_norm 106.6795 (481.1198)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:27:55 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [7/200][20/27]	eta 0:00:13 lr 0.000001	time 1.6926 (1.9446)	loss 8.8985 (11.6622)	miou 0.1779	grad_norm 297.1133 (606.3327)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:28:07 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 7 training takes 0:00:52
[32m[2025-07-13 18:28:07 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 18:28:17 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1899%
[32m[2025-07-13 18:28:17 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2273%
[32m[2025-07-13 18:28:19 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [8/200][0/27]	eta 0:01:09 lr 0.000002	time 2.5706 (2.5706)	loss 10.8680 (10.8680)	miou 0.1911	grad_norm 367.7371 (367.7371)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:28:39 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [8/200][10/27]	eta 0:00:34 lr 0.000004	time 2.0121 (2.0118)	loss 8.3076 (11.0159)	miou 0.1655	grad_norm 174.5788 (233.4137)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:28:58 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [8/200][20/27]	eta 0:00:13 lr 0.000008	time 1.6579 (1.9500)	loss 9.4125 (11.4648)	miou 0.1675	grad_norm 137.6780 (303.8268)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:29:09 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 8 training takes 0:00:52
[32m[2025-07-13 18:29:09 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 18:29:19 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1808%
[32m[2025-07-13 18:29:19 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2273%
[32m[2025-07-13 18:29:22 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [9/200][0/27]	eta 0:01:02 lr 0.000011	time 2.3021 (2.3021)	loss 11.6071 (11.6071)	miou 0.1802	grad_norm 314.5930 (314.5930)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:29:41 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [9/200][10/27]	eta 0:00:33 lr 0.000017	time 2.1033 (1.9422)	loss 6.5654 (10.3481)	miou 0.1793	grad_norm 44.2239 (379.0677)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:30:01 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [9/200][20/27]	eta 0:00:13 lr 0.000023	time 2.4769 (1.9726)	loss 10.6382 (10.3507)	miou 0.1726	grad_norm 326.8742 (373.4622)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:30:12 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 9 training takes 0:00:52
[32m[2025-07-13 18:30:12 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
9
[32m[2025-07-13 18:30:22 3DDETR.yaml][33m(utils_help.py 85)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saving......
[32m[2025-07-13 18:30:23 3DDETR.yaml][33m(utils_help.py 87)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saved !!!
[32m[2025-07-13 18:30:23 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.2477%
[32m[2025-07-13 18:30:23 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2477%
[32m[2025-07-13 18:30:25 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [10/200][0/27]	eta 0:01:08 lr 0.000028	time 2.5452 (2.5452)	loss 9.5635 (9.5635)	miou 0.2354	grad_norm 598.1960 (598.1960)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:30:45 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [10/200][10/27]	eta 0:00:33 lr 0.000035	time 1.6048 (1.9679)	loss 14.9017 (10.5528)	miou 0.2002	grad_norm 241.6849 (334.7465)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:31:03 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [10/200][20/27]	eta 0:00:13 lr 0.000043	time 1.6340 (1.9270)	loss 7.7260 (10.6688)	miou 0.1836	grad_norm 275.1156 (527.1048)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:31:16 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 10 training takes 0:00:53
[32m[2025-07-13 18:31:16 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 18:31:26 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1797%
[32m[2025-07-13 18:31:26 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2477%
[32m[2025-07-13 18:31:29 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [11/200][0/27]	eta 0:01:04 lr 0.000048	time 2.4074 (2.4074)	loss 15.1555 (15.1555)	miou 0.1660	grad_norm 295.7665 (295.7665)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:31:47 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [11/200][10/27]	eta 0:00:31 lr 0.000056	time 2.0336 (1.8755)	loss 9.9368 (11.6991)	miou 0.1467	grad_norm 308.7213 (492.6458)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:32:06 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [11/200][20/27]	eta 0:00:13 lr 0.000064	time 1.5864 (1.8822)	loss 8.7900 (10.8325)	miou 0.1425	grad_norm 221.1468 (372.3610)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:32:17 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 11 training takes 0:00:51
[32m[2025-07-13 18:32:17 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 18:32:28 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.2265%
[32m[2025-07-13 18:32:28 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2477%
[32m[2025-07-13 18:32:30 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [12/200][0/27]	eta 0:01:18 lr 0.000069	time 2.8915 (2.8915)	loss 11.6254 (11.6254)	miou 0.1931	grad_norm 2723.5935 (2723.5935)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:32:48 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [12/200][10/27]	eta 0:00:32 lr 0.000076	time 1.7758 (1.9010)	loss 8.9625 (10.8686)	miou 0.1534	grad_norm 48.7103 (560.5447)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:33:08 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [12/200][20/27]	eta 0:00:13 lr 0.000082	time 2.5222 (1.9325)	loss 9.6555 (10.5387)	miou 0.1508	grad_norm 212.3971 (535.5075)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:33:20 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 12 training takes 0:00:52
[32m[2025-07-13 18:33:20 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 18:33:30 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.2194%
[32m[2025-07-13 18:33:30 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2477%
[32m[2025-07-13 18:33:33 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [13/200][0/27]	eta 0:01:10 lr 0.000086	time 2.6040 (2.6040)	loss 8.7781 (8.7781)	miou 0.2123	grad_norm 820.8179 (820.8179)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:33:52 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [13/200][10/27]	eta 0:00:33 lr 0.000091	time 1.8175 (1.9731)	loss 12.6604 (10.7364)	miou 0.1861	grad_norm 97.4423 (1246.6455)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:34:11 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [13/200][20/27]	eta 0:00:13 lr 0.000095	time 1.5343 (1.9511)	loss 8.6211 (10.0479)	miou 0.1711	grad_norm 868.1520 (875.2585)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:34:23 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 13 training takes 0:00:52
[32m[2025-07-13 18:34:23 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 18:34:33 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1413%
[32m[2025-07-13 18:34:33 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2477%
[32m[2025-07-13 18:34:35 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [14/200][0/27]	eta 0:00:55 lr 0.000097	time 2.0638 (2.0638)	loss 9.0977 (9.0977)	miou 0.1481	grad_norm 204.4265 (204.4265)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:34:54 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [14/200][10/27]	eta 0:00:32 lr 0.000099	time 2.1201 (1.8887)	loss 10.6071 (9.5808)	miou 0.1489	grad_norm 52.8810 (123.8273)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:35:12 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [14/200][20/27]	eta 0:00:13 lr 0.000100	time 1.8746 (1.8842)	loss 9.4964 (9.8191)	miou 0.1460	grad_norm 472.0407 (185.3275)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:35:25 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 14 training takes 0:00:51
[32m[2025-07-13 18:35:25 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 18:35:35 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.2293%
[32m[2025-07-13 18:35:35 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2477%
[32m[2025-07-13 18:35:37 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [15/200][0/27]	eta 0:01:06 lr 0.000100	time 2.4488 (2.4488)	loss 6.6551 (6.6551)	miou 0.2294	grad_norm 188.7554 (188.7554)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:35:57 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [15/200][10/27]	eta 0:00:33 lr 0.000098	time 1.7032 (1.9873)	loss 11.4354 (8.4044)	miou 0.1973	grad_norm 79.4691 (279.9983)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:36:17 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [15/200][20/27]	eta 0:00:14 lr 0.000096	time 2.3015 (2.0006)	loss 9.1644 (8.8338)	miou 0.1865	grad_norm 651.7649 (298.6591)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:36:28 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 15 training takes 0:00:53
[32m[2025-07-13 18:36:28 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 18:36:39 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.2064%
[32m[2025-07-13 18:36:39 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2477%
[32m[2025-07-13 18:36:41 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [16/200][0/27]	eta 0:01:02 lr 0.000093	time 2.2987 (2.2987)	loss 7.6775 (7.6775)	miou 0.2085	grad_norm 1737.9954 (1737.9954)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:37:00 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [16/200][10/27]	eta 0:00:33 lr 0.000089	time 1.7459 (1.9627)	loss 11.0885 (9.0683)	miou 0.2014	grad_norm 50.0172 (327.4244)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:37:19 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [16/200][20/27]	eta 0:00:13 lr 0.000084	time 1.9999 (1.9456)	loss 9.2653 (9.0359)	miou 0.1920	grad_norm 204.9695 (307.0705)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:37:31 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 16 training takes 0:00:52
[32m[2025-07-13 18:37:31 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 18:37:41 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1522%
[32m[2025-07-13 18:37:41 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2477%
[32m[2025-07-13 18:37:43 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [17/200][0/27]	eta 0:00:59 lr 0.000079	time 2.1923 (2.1923)	loss 5.2619 (5.2619)	miou 0.1584	grad_norm 224.8732 (224.8732)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:38:01 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [17/200][10/27]	eta 0:00:31 lr 0.000073	time 2.0403 (1.8671)	loss 11.0328 (8.0372)	miou 0.1764	grad_norm 82.9990 (179.3281)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:38:20 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [17/200][20/27]	eta 0:00:13 lr 0.000065	time 2.0998 (1.8621)	loss 9.0503 (9.5705)	miou 0.1718	grad_norm 69.4425 (167.5993)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:38:32 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 17 training takes 0:00:50
[32m[2025-07-13 18:38:32 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 18:38:42 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1822%
[32m[2025-07-13 18:38:42 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2477%
[32m[2025-07-13 18:38:45 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [18/200][0/27]	eta 0:01:12 lr 0.000060	time 2.6864 (2.6864)	loss 8.8889 (8.8889)	miou 0.1846	grad_norm 448.8732 (448.8732)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:39:03 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [18/200][10/27]	eta 0:00:33 lr 0.000052	time 1.5783 (1.9500)	loss 13.3754 (8.8306)	miou 0.1943	grad_norm 59.9352 (215.1144)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:39:22 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [18/200][20/27]	eta 0:00:13 lr 0.000045	time 1.4658 (1.9008)	loss 9.0610 (9.2875)	miou 0.2001	grad_norm 283.5776 (179.0957)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:39:35 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 18 training takes 0:00:53
[32m[2025-07-13 18:39:35 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 18:39:45 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.0826%
[32m[2025-07-13 18:39:45 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2477%
[32m[2025-07-13 18:39:48 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [19/200][0/27]	eta 0:01:01 lr 0.000039	time 2.2873 (2.2873)	loss 13.2413 (13.2413)	miou 0.0840	grad_norm 109.9646 (109.9646)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:40:08 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [19/200][10/27]	eta 0:00:34 lr 0.000032	time 1.9433 (2.0124)	loss 9.7277 (9.8691)	miou 0.1183	grad_norm 119.1634 (217.7383)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:40:27 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [19/200][20/27]	eta 0:00:13 lr 0.000025	time 1.9682 (1.9588)	loss 11.8447 (9.2488)	miou 0.1467	grad_norm 37.5503 (250.7983)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:40:39 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 19 training takes 0:00:53
[32m[2025-07-13 18:40:39 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 18:40:49 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1178%
[32m[2025-07-13 18:40:49 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2477%
[32m[2025-07-13 18:40:51 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [20/200][0/27]	eta 0:01:04 lr 0.000020	time 2.3883 (2.3883)	loss 7.9046 (7.9046)	miou 0.1184	grad_norm 101.1668 (101.1668)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:41:11 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [20/200][10/27]	eta 0:00:33 lr 0.000014	time 1.6092 (1.9608)	loss 14.1924 (10.2798)	miou 0.1480	grad_norm 96.7064 (110.4990)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:41:30 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [20/200][20/27]	eta 0:00:13 lr 0.000009	time 2.0746 (1.9539)	loss 5.5475 (9.4339)	miou 0.1598	grad_norm 63.7682 (121.2146)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:41:42 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 20 training takes 0:00:52
[32m[2025-07-13 18:41:42 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 18:41:52 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1042%
[32m[2025-07-13 18:41:52 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2477%
[32m[2025-07-13 18:41:54 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [21/200][0/27]	eta 0:01:09 lr 0.000006	time 2.5847 (2.5847)	loss 9.7047 (9.7047)	miou 0.1101	grad_norm 396.1635 (396.1635)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:42:14 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [21/200][10/27]	eta 0:00:34 lr 0.000003	time 1.7294 (2.0029)	loss 9.4668 (9.0508)	miou 0.1789	grad_norm 32.5596 (148.9885)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:42:32 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [21/200][20/27]	eta 0:00:13 lr 0.000001	time 2.0329 (1.9265)	loss 6.0411 (8.7741)	miou 0.1901	grad_norm 33.0907 (122.7009)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:42:44 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 21 training takes 0:00:52
[32m[2025-07-13 18:42:44 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 18:42:54 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1086%
[32m[2025-07-13 18:42:54 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2477%
[32m[2025-07-13 18:42:56 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [22/200][0/27]	eta 0:01:07 lr 0.000000	time 2.4862 (2.4862)	loss 9.9270 (9.9270)	miou 0.1175	grad_norm 165.8193 (165.8193)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:43:16 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [22/200][10/27]	eta 0:00:34 lr 0.000000	time 1.8166 (2.0313)	loss 9.0197 (8.8220)	miou 0.1486	grad_norm 87.7725 (115.5888)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:43:36 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [22/200][20/27]	eta 0:00:13 lr 0.000001	time 2.0003 (1.9932)	loss 6.9896 (8.6504)	miou 0.1624	grad_norm 45.3756 (102.5144)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:43:47 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 22 training takes 0:00:53
[32m[2025-07-13 18:43:47 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 18:43:57 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1090%
[32m[2025-07-13 18:43:57 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2477%
[32m[2025-07-13 18:44:00 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [23/200][0/27]	eta 0:01:21 lr 0.000003	time 3.0091 (3.0091)	loss 7.8185 (7.8185)	miou 0.1440	grad_norm 269.2756 (269.2756)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:44:19 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [23/200][10/27]	eta 0:00:34 lr 0.000006	time 1.8340 (2.0013)	loss 9.2375 (8.3893)	miou 0.1718	grad_norm 59.2491 (93.8505)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:44:37 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [23/200][20/27]	eta 0:00:13 lr 0.000010	time 2.1119 (1.9046)	loss 8.6109 (8.9772)	miou 0.1805	grad_norm 204.9084 (123.9288)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:44:49 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 23 training takes 0:00:51
[32m[2025-07-13 18:44:49 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 18:44:59 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.0876%
[32m[2025-07-13 18:44:59 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2477%
[32m[2025-07-13 18:45:02 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [24/200][0/27]	eta 0:01:04 lr 0.000014	time 2.3982 (2.3982)	loss 7.4576 (7.4576)	miou 0.1216	grad_norm 330.7142 (330.7142)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:45:21 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [24/200][10/27]	eta 0:00:33 lr 0.000020	time 2.0692 (1.9752)	loss 9.9010 (9.1938)	miou 0.1495	grad_norm 93.1148 (179.2025)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:45:39 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [24/200][20/27]	eta 0:00:13 lr 0.000027	time 1.8089 (1.8923)	loss 10.3939 (8.9663)	miou 0.1512	grad_norm 117.1729 (190.7570)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:45:51 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 24 training takes 0:00:52
[32m[2025-07-13 18:45:51 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 18:46:01 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1315%
[32m[2025-07-13 18:46:01 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2477%
[32m[2025-07-13 18:46:05 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [25/200][0/27]	eta 0:01:38 lr 0.000032	time 3.6358 (3.6358)	loss 6.7558 (6.7558)	miou 0.1352	grad_norm 73.9066 (73.9066)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:46:24 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [25/200][10/27]	eta 0:00:34 lr 0.000039	time 1.7550 (2.0321)	loss 12.5118 (10.0970)	miou 0.1686	grad_norm 363.0718 (148.3205)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:46:44 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [25/200][20/27]	eta 0:00:14 lr 0.000047	time 2.0393 (2.0185)	loss 8.7438 (9.9007)	miou 0.1904	grad_norm 158.2867 (575.4148)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:46:56 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 25 training takes 0:00:54
[32m[2025-07-13 18:46:56 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 18:47:06 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1123%
[32m[2025-07-13 18:47:06 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2477%
[32m[2025-07-13 18:47:09 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [26/200][0/27]	eta 0:01:13 lr 0.000052	time 2.7050 (2.7050)	loss 8.0018 (8.0018)	miou 0.1269	grad_norm 147.8978 (147.8978)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:47:28 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [26/200][10/27]	eta 0:00:34 lr 0.000060	time 1.9461 (2.0422)	loss 10.3128 (9.0326)	miou 0.1485	grad_norm 47.8045 (90.3270)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:47:46 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [26/200][20/27]	eta 0:00:13 lr 0.000068	time 1.7085 (1.9144)	loss 12.1329 (9.0989)	miou 0.1651	grad_norm 162.7803 (126.7016)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:47:58 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 26 training takes 0:00:51
[32m[2025-07-13 18:47:58 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 18:48:08 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1563%
[32m[2025-07-13 18:48:08 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2477%
[32m[2025-07-13 18:48:11 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [27/200][0/27]	eta 0:01:22 lr 0.000073	time 3.0657 (3.0657)	loss 4.4077 (4.4077)	miou 0.1593	grad_norm 536.9620 (536.9620)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:48:31 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [27/200][10/27]	eta 0:00:36 lr 0.000079	time 2.7849 (2.1397)	loss 8.4109 (7.8969)	miou 0.1974	grad_norm 51.1968 (168.2893)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:48:50 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [27/200][20/27]	eta 0:00:14 lr 0.000085	time 2.1265 (2.0257)	loss 5.4299 (7.9696)	miou 0.1968	grad_norm 619.2233 (170.4147)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:49:01 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 27 training takes 0:00:53
[32m[2025-07-13 18:49:01 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 18:49:12 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1031%
[32m[2025-07-13 18:49:12 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2477%
[32m[2025-07-13 18:49:15 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [28/200][0/27]	eta 0:01:19 lr 0.000089	time 2.9394 (2.9394)	loss 13.3045 (13.3045)	miou 0.1225	grad_norm 204.0400 (204.0400)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:49:33 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [28/200][10/27]	eta 0:00:33 lr 0.000093	time 1.8934 (1.9586)	loss 7.6623 (10.7485)	miou 0.1501	grad_norm 49.6572 (149.6808)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:49:53 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [28/200][20/27]	eta 0:00:13 lr 0.000097	time 2.2620 (1.9663)	loss 8.2892 (9.2369)	miou 0.1774	grad_norm 66.8252 (202.3924)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:50:04 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 28 training takes 0:00:52
[32m[2025-07-13 18:50:04 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 18:50:14 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1228%
[32m[2025-07-13 18:50:14 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2477%
[32m[2025-07-13 18:50:17 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [29/200][0/27]	eta 0:01:05 lr 0.000098	time 2.4272 (2.4272)	loss 9.6827 (9.6827)	miou 0.1375	grad_norm 78.8362 (78.8362)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:50:36 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [29/200][10/27]	eta 0:00:33 lr 0.000100	time 1.9406 (1.9927)	loss 8.8661 (10.0305)	miou 0.1745	grad_norm 20.4755 (87.7638)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:50:55 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [29/200][20/27]	eta 0:00:13 lr 0.000100	time 1.4872 (1.9563)	loss 9.4526 (9.0605)	miou 0.1912	grad_norm 123.1134 (118.5845)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:51:07 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 29 training takes 0:00:53
[32m[2025-07-13 18:51:07 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 18:51:17 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1396%
[32m[2025-07-13 18:51:17 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2477%
[32m[2025-07-13 18:51:20 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [30/200][0/27]	eta 0:01:03 lr 0.000099	time 2.3368 (2.3368)	loss 11.6529 (11.6529)	miou 0.1377	grad_norm 88.2710 (88.2710)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:51:39 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [30/200][10/27]	eta 0:00:32 lr 0.000097	time 1.5821 (1.9264)	loss 6.3732 (8.6928)	miou 0.1770	grad_norm 17.0862 (83.4523)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:51:59 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [30/200][20/27]	eta 0:00:13 lr 0.000094	time 2.2189 (1.9686)	loss 9.1210 (8.8991)	miou 0.1801	grad_norm 56.4905 (113.0907)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:52:11 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 30 training takes 0:00:53
[32m[2025-07-13 18:52:11 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 18:52:21 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1775%
[32m[2025-07-13 18:52:21 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2477%
[32m[2025-07-13 18:52:24 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [31/200][0/27]	eta 0:01:17 lr 0.000091	time 2.8820 (2.8820)	loss 7.1995 (7.1995)	miou 0.1860	grad_norm 195.3710 (195.3710)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:52:43 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [31/200][10/27]	eta 0:00:34 lr 0.000086	time 1.4456 (2.0005)	loss 5.6868 (7.3714)	miou 0.2042	grad_norm 26.8315 (128.0538)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:53:01 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [31/200][20/27]	eta 0:00:13 lr 0.000081	time 1.8750 (1.9172)	loss 6.2630 (7.7959)	miou 0.2206	grad_norm 41.4154 (177.5217)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:53:13 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 31 training takes 0:00:51
[32m[2025-07-13 18:53:13 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 18:53:23 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1813%
[32m[2025-07-13 18:53:23 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2477%
[32m[2025-07-13 18:53:26 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [32/200][0/27]	eta 0:01:23 lr 0.000076	time 3.0869 (3.0869)	loss 6.5642 (6.5642)	miou 0.1934	grad_norm 162.5368 (162.5368)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:53:46 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [32/200][10/27]	eta 0:00:36 lr 0.000069	time 2.2769 (2.1396)	loss 10.7449 (7.9466)	miou 0.1916	grad_norm 27.7349 (84.0585)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:54:05 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [32/200][20/27]	eta 0:00:13 lr 0.000062	time 2.0482 (1.9923)	loss 8.6363 (8.4406)	miou 0.1924	grad_norm 33.9154 (90.8372)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:54:17 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 32 training takes 0:00:53
[32m[2025-07-13 18:54:17 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 18:54:27 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1961%
[32m[2025-07-13 18:54:27 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2477%
[32m[2025-07-13 18:54:29 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [33/200][0/27]	eta 0:00:59 lr 0.000056	time 2.2084 (2.2084)	loss 8.3644 (8.3644)	miou 0.1817	grad_norm 185.0782 (185.0782)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:54:48 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [33/200][10/27]	eta 0:00:32 lr 0.000048	time 1.6997 (1.9179)	loss 8.7604 (8.2072)	miou 0.1814	grad_norm 71.9617 (71.5679)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:55:08 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [33/200][20/27]	eta 0:00:13 lr 0.000041	time 1.7163 (1.9817)	loss 8.0093 (8.0158)	miou 0.1875	grad_norm 63.1840 (93.7322)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:55:20 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 33 training takes 0:00:53
[32m[2025-07-13 18:55:20 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 18:55:30 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1827%
[32m[2025-07-13 18:55:30 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2477%
[32m[2025-07-13 18:55:32 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [34/200][0/27]	eta 0:00:55 lr 0.000035	time 2.0460 (2.0460)	loss 8.9841 (8.9841)	miou 0.1811	grad_norm 1656.4083 (1656.4083)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:55:51 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [34/200][10/27]	eta 0:00:32 lr 0.000028	time 1.7057 (1.8883)	loss 14.1452 (8.9045)	miou 0.1988	grad_norm 57.0423 (238.8464)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:56:09 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [34/200][20/27]	eta 0:00:12 lr 0.000021	time 1.7656 (1.8362)	loss 8.8540 (8.6194)	miou 0.2003	grad_norm 140.8799 (166.6319)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:56:20 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 34 training takes 0:00:49
[32m[2025-07-13 18:56:20 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 18:56:30 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1697%
[32m[2025-07-13 18:56:30 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2477%
[32m[2025-07-13 18:56:33 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [35/200][0/27]	eta 0:01:12 lr 0.000017	time 2.6933 (2.6933)	loss 7.5339 (7.5339)	miou 0.1905	grad_norm 107.5607 (107.5607)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:56:53 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [35/200][10/27]	eta 0:00:34 lr 0.000011	time 1.5956 (2.0537)	loss 8.7978 (7.6287)	miou 0.2153	grad_norm 30.7426 (125.0417)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:57:11 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [35/200][20/27]	eta 0:00:13 lr 0.000007	time 1.7497 (1.9366)	loss 8.3759 (7.8383)	miou 0.2193	grad_norm 30.8434 (91.4994)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:57:23 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 35 training takes 0:00:53
[32m[2025-07-13 18:57:23 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 18:57:33 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1785%
[32m[2025-07-13 18:57:33 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2477%
[32m[2025-07-13 18:57:36 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [36/200][0/27]	eta 0:01:10 lr 0.000004	time 2.6147 (2.6147)	loss 11.1594 (11.1594)	miou 0.1759	grad_norm 49.6588 (49.6588)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:57:54 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [36/200][10/27]	eta 0:00:32 lr 0.000002	time 1.6537 (1.9015)	loss 7.3261 (7.7791)	miou 0.1997	grad_norm 56.8688 (73.5418)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:58:14 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [36/200][20/27]	eta 0:00:13 lr 0.000000	time 2.2835 (1.9420)	loss 6.6490 (8.2162)	miou 0.2080	grad_norm 49.8930 (70.3800)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:58:25 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 36 training takes 0:00:51
[32m[2025-07-13 18:58:25 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 18:58:35 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1637%
[32m[2025-07-13 18:58:35 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2477%
[32m[2025-07-13 18:58:38 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [37/200][0/27]	eta 0:01:10 lr 0.000000	time 2.5967 (2.5967)	loss 7.6352 (7.6352)	miou 0.1587	grad_norm 253.5548 (253.5548)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:58:57 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [37/200][10/27]	eta 0:00:33 lr 0.000001	time 2.1563 (1.9558)	loss 10.8047 (7.7595)	miou 0.1960	grad_norm 99.0969 (128.6096)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:59:18 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [37/200][20/27]	eta 0:00:14 lr 0.000002	time 1.7377 (2.0106)	loss 7.4484 (7.8486)	miou 0.2135	grad_norm 41.8649 (111.9428)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 18:59:30 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 37 training takes 0:00:54
[32m[2025-07-13 18:59:30 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 18:59:40 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1647%
[32m[2025-07-13 18:59:40 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2477%
[32m[2025-07-13 18:59:42 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [38/200][0/27]	eta 0:01:06 lr 0.000004	time 2.4686 (2.4686)	loss 6.5899 (6.5899)	miou 0.1676	grad_norm 83.4207 (83.4207)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:00:02 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [38/200][10/27]	eta 0:00:34 lr 0.000008	time 2.0822 (2.0201)	loss 9.4543 (7.8577)	miou 0.1781	grad_norm 31.7485 (64.2801)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:00:22 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [38/200][20/27]	eta 0:00:13 lr 0.000013	time 1.9949 (1.9914)	loss 11.0220 (8.0991)	miou 0.1811	grad_norm 33.6753 (81.7998)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:00:33 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 38 training takes 0:00:53
[32m[2025-07-13 19:00:33 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 19:00:43 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1399%
[32m[2025-07-13 19:00:43 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2477%
[32m[2025-07-13 19:00:46 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [39/200][0/27]	eta 0:01:10 lr 0.000017	time 2.6081 (2.6081)	loss 6.9962 (6.9962)	miou 0.1458	grad_norm 132.9167 (132.9167)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:01:04 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [39/200][10/27]	eta 0:00:32 lr 0.000023	time 1.8397 (1.9007)	loss 7.5012 (7.9262)	miou 0.1712	grad_norm 40.6425 (58.0417)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:01:23 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [39/200][20/27]	eta 0:00:13 lr 0.000030	time 2.1766 (1.9088)	loss 10.6590 (7.7552)	miou 0.1843	grad_norm 30.9279 (74.3784)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:01:34 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 39 training takes 0:00:51
[32m[2025-07-13 19:01:34 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 19:01:44 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1601%
[32m[2025-07-13 19:01:44 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2477%
[32m[2025-07-13 19:01:47 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [40/200][0/27]	eta 0:01:12 lr 0.000035	time 2.6760 (2.6760)	loss 9.7703 (9.7703)	miou 0.1528	grad_norm 45.6569 (45.6569)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:02:06 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [40/200][10/27]	eta 0:00:33 lr 0.000043	time 1.7319 (1.9587)	loss 8.7234 (8.4069)	miou 0.1967	grad_norm 185.9179 (98.3138)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:02:25 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [40/200][20/27]	eta 0:00:13 lr 0.000051	time 2.0189 (1.9376)	loss 8.1578 (7.9709)	miou 0.2041	grad_norm 56.7527 (111.4154)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:02:36 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 40 training takes 0:00:52
[32m[2025-07-13 19:02:36 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 19:02:47 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1538%
[32m[2025-07-13 19:02:47 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2477%
[32m[2025-07-13 19:02:49 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [41/200][0/27]	eta 0:01:01 lr 0.000056	time 2.2817 (2.2817)	loss 5.3554 (5.3554)	miou 0.1666	grad_norm 179.9544 (179.9544)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:03:08 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [41/200][10/27]	eta 0:00:33 lr 0.000064	time 2.0384 (1.9662)	loss 8.2075 (8.2594)	miou 0.1841	grad_norm 47.3183 (72.8280)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:03:28 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [41/200][20/27]	eta 0:00:13 lr 0.000071	time 2.1183 (1.9524)	loss 9.7212 (8.3658)	miou 0.2003	grad_norm 30.7607 (84.7214)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:03:40 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 41 training takes 0:00:53
[32m[2025-07-13 19:03:40 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 19:03:50 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1607%
[32m[2025-07-13 19:03:50 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2477%
[32m[2025-07-13 19:03:53 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [42/200][0/27]	eta 0:01:05 lr 0.000076	time 2.4105 (2.4105)	loss 5.9753 (5.9753)	miou 0.1747	grad_norm 44.9880 (44.9880)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:04:10 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [42/200][10/27]	eta 0:00:30 lr 0.000082	time 1.6925 (1.7950)	loss 11.1883 (8.4683)	miou 0.1905	grad_norm 44.6744 (60.8195)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:04:30 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [42/200][20/27]	eta 0:00:13 lr 0.000088	time 1.7011 (1.8818)	loss 8.8745 (8.4831)	miou 0.1947	grad_norm 88.1313 (82.4203)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:04:41 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 42 training takes 0:00:51
[32m[2025-07-13 19:04:41 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 19:04:52 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1942%
[32m[2025-07-13 19:04:52 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2477%
[32m[2025-07-13 19:04:54 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [43/200][0/27]	eta 0:01:04 lr 0.000091	time 2.3984 (2.3984)	loss 7.3071 (7.3071)	miou 0.1968	grad_norm 67.8756 (67.8756)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:05:14 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [43/200][10/27]	eta 0:00:35 lr 0.000095	time 2.3009 (2.0746)	loss 9.3066 (8.7468)	miou 0.1833	grad_norm 38.7074 (100.9494)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:05:34 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [43/200][20/27]	eta 0:00:14 lr 0.000098	time 2.0641 (2.0224)	loss 6.2193 (8.6875)	miou 0.1927	grad_norm 97.9627 (84.9124)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:05:45 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 43 training takes 0:00:53
[32m[2025-07-13 19:05:45 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 19:05:56 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1795%
[32m[2025-07-13 19:05:56 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2477%
[32m[2025-07-13 19:05:59 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [44/200][0/27]	eta 0:01:23 lr 0.000099	time 3.0828 (3.0828)	loss 6.6498 (6.6498)	miou 0.1820	grad_norm 30.0860 (30.0860)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:06:18 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [44/200][10/27]	eta 0:00:34 lr 0.000100	time 1.8609 (2.0210)	loss 10.9248 (8.4008)	miou 0.1808	grad_norm 34.3419 (87.6412)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:06:36 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [44/200][20/27]	eta 0:00:13 lr 0.000100	time 1.6395 (1.9268)	loss 10.4714 (8.6494)	miou 0.1878	grad_norm 74.3446 (83.9491)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:06:48 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 44 training takes 0:00:52
[32m[2025-07-13 19:06:48 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 19:06:58 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1444%
[32m[2025-07-13 19:06:58 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2477%
[32m[2025-07-13 19:07:01 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [45/200][0/27]	eta 0:01:06 lr 0.000098	time 2.4503 (2.4503)	loss 6.7086 (6.7086)	miou 0.1531	grad_norm 38.7119 (38.7119)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:07:21 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [45/200][10/27]	eta 0:00:34 lr 0.000096	time 2.1659 (2.0391)	loss 8.8364 (7.6881)	miou 0.1763	grad_norm 17.7211 (67.6788)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:07:40 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [45/200][20/27]	eta 0:00:13 lr 0.000092	time 2.4227 (1.9627)	loss 6.9084 (7.5468)	miou 0.2014	grad_norm 382.4458 (79.5409)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:07:51 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 45 training takes 0:00:52
[32m[2025-07-13 19:07:51 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 19:08:01 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.2251%
[32m[2025-07-13 19:08:01 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2477%
[32m[2025-07-13 19:08:04 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [46/200][0/27]	eta 0:01:14 lr 0.000089	time 2.7711 (2.7711)	loss 6.0156 (6.0156)	miou 0.2118	grad_norm 96.4051 (96.4051)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:08:22 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [46/200][10/27]	eta 0:00:32 lr 0.000084	time 1.6150 (1.8891)	loss 10.1278 (8.0664)	miou 0.2206	grad_norm 56.5129 (63.6636)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:08:42 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [46/200][20/27]	eta 0:00:13 lr 0.000077	time 1.6921 (1.9377)	loss 6.1169 (7.9338)	miou 0.2152	grad_norm 107.7262 (88.2144)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:08:53 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 46 training takes 0:00:52
[32m[2025-07-13 19:08:53 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 19:09:03 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1651%
[32m[2025-07-13 19:09:03 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2477%
[32m[2025-07-13 19:09:06 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [47/200][0/27]	eta 0:01:07 lr 0.000073	time 2.5067 (2.5067)	loss 9.0293 (9.0293)	miou 0.1628	grad_norm 44.8949 (44.8949)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:09:26 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [47/200][10/27]	eta 0:00:34 lr 0.000065	time 1.4486 (2.0323)	loss 14.2639 (8.6248)	miou 0.1919	grad_norm 71.0545 (61.6108)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:09:44 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [47/200][20/27]	eta 0:00:13 lr 0.000058	time 1.9307 (1.9555)	loss 9.3204 (8.8985)	miou 0.2086	grad_norm 197.4158 (81.4816)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:09:55 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 47 training takes 0:00:52
[32m[2025-07-13 19:09:55 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 19:10:06 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1320%
[32m[2025-07-13 19:10:06 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2477%
[32m[2025-07-13 19:10:08 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [48/200][0/27]	eta 0:00:57 lr 0.000052	time 2.1246 (2.1246)	loss 8.5325 (8.5325)	miou 0.1362	grad_norm 84.5702 (84.5702)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:10:26 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [48/200][10/27]	eta 0:00:32 lr 0.000045	time 2.0067 (1.8981)	loss 10.3494 (9.1054)	miou 0.1749	grad_norm 58.4741 (68.3616)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:10:45 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [48/200][20/27]	eta 0:00:13 lr 0.000037	time 1.8865 (1.8927)	loss 6.3934 (8.2762)	miou 0.1862	grad_norm 55.5367 (86.6114)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:10:58 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 48 training takes 0:00:52
[32m[2025-07-13 19:10:58 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 19:11:08 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1299%
[32m[2025-07-13 19:11:08 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2477%
[32m[2025-07-13 19:11:11 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [49/200][0/27]	eta 0:01:09 lr 0.000032	time 2.5872 (2.5872)	loss 7.0601 (7.0601)	miou 0.1459	grad_norm 91.5716 (91.5716)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:11:30 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [49/200][10/27]	eta 0:00:33 lr 0.000025	time 1.6128 (1.9841)	loss 9.5805 (8.0818)	miou 0.1692	grad_norm 50.4950 (62.8380)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:11:48 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [49/200][20/27]	eta 0:00:13 lr 0.000018	time 1.8986 (1.9078)	loss 6.8828 (7.6214)	miou 0.1961	grad_norm 209.3890 (81.8611)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:12:00 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 49 training takes 0:00:52
[32m[2025-07-13 19:12:00 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 19:12:10 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1510%
[32m[2025-07-13 19:12:10 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2477%
[32m[2025-07-13 19:12:12 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [50/200][0/27]	eta 0:00:52 lr 0.000014	time 1.9524 (1.9524)	loss 5.7000 (5.7000)	miou 0.1575	grad_norm 45.4969 (45.4969)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:12:31 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [50/200][10/27]	eta 0:00:32 lr 0.000009	time 1.8363 (1.8899)	loss 8.2340 (7.4193)	miou 0.2099	grad_norm 23.4899 (53.9724)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:12:51 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [50/200][20/27]	eta 0:00:13 lr 0.000005	time 1.8877 (1.9205)	loss 10.5976 (7.7129)	miou 0.2143	grad_norm 185.7861 (56.5052)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:13:02 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 50 training takes 0:00:51
[32m[2025-07-13 19:13:02 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 19:13:12 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1506%
[32m[2025-07-13 19:13:12 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2477%
[32m[2025-07-13 19:13:14 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [51/200][0/27]	eta 0:01:01 lr 0.000003	time 2.2622 (2.2622)	loss 9.8612 (9.8612)	miou 0.1552	grad_norm 399.3839 (399.3839)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:13:34 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [51/200][10/27]	eta 0:00:32 lr 0.000001	time 1.9542 (1.9381)	loss 9.1309 (8.7752)	miou 0.1822	grad_norm 31.2686 (72.8610)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:13:53 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [51/200][20/27]	eta 0:00:13 lr 0.000000	time 1.7082 (1.9330)	loss 8.8409 (8.3621)	miou 0.2064	grad_norm 29.5235 (64.4221)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:14:04 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 51 training takes 0:00:51
[32m[2025-07-13 19:14:04 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 19:14:14 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1616%
[32m[2025-07-13 19:14:14 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2477%
[32m[2025-07-13 19:14:17 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [52/200][0/27]	eta 0:01:27 lr 0.000000	time 3.2328 (3.2328)	loss 6.8626 (6.8626)	miou 0.1584	grad_norm 148.4946 (148.4946)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:14:36 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [52/200][10/27]	eta 0:00:33 lr 0.000001	time 1.5303 (1.9708)	loss 8.9081 (7.8469)	miou 0.1951	grad_norm 78.2608 (85.1866)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:14:55 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [52/200][20/27]	eta 0:00:13 lr 0.000004	time 1.7439 (1.9528)	loss 6.2847 (8.1018)	miou 0.2304	grad_norm 57.3681 (66.0071)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:15:08 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 52 training takes 0:00:53
[32m[2025-07-13 19:15:08 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 19:15:18 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.0948%
[32m[2025-07-13 19:15:18 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2477%
[32m[2025-07-13 19:15:20 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [53/200][0/27]	eta 0:01:03 lr 0.000006	time 2.3418 (2.3418)	loss 9.2605 (9.2605)	miou 0.0982	grad_norm 55.5349 (55.5349)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:15:40 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [53/200][10/27]	eta 0:00:34 lr 0.000010	time 1.4921 (2.0003)	loss 8.4757 (7.7788)	miou 0.1680	grad_norm 68.5683 (54.1154)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:15:59 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [53/200][20/27]	eta 0:00:13 lr 0.000016	time 2.0499 (1.9576)	loss 8.7308 (7.5873)	miou 0.1901	grad_norm 44.7757 (773.2732)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:16:10 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 53 training takes 0:00:52
[32m[2025-07-13 19:16:10 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 19:16:20 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1204%
[32m[2025-07-13 19:16:20 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2477%
[32m[2025-07-13 19:16:23 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [54/200][0/27]	eta 0:01:15 lr 0.000020	time 2.8103 (2.8103)	loss 7.1325 (7.1325)	miou 0.1200	grad_norm 644.7662 (644.7662)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:16:42 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [54/200][10/27]	eta 0:00:34 lr 0.000027	time 2.0742 (2.0120)	loss 11.0299 (8.4910)	miou 0.1554	grad_norm 56.2857 (107.8470)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:17:02 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [54/200][20/27]	eta 0:00:13 lr 0.000034	time 1.7650 (1.9910)	loss 9.4727 (8.3866)	miou 0.1828	grad_norm 17.7738 (121.2652)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:17:13 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 54 training takes 0:00:53
[32m[2025-07-13 19:17:13 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 19:17:23 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1283%
[32m[2025-07-13 19:17:23 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2477%
[32m[2025-07-13 19:17:26 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [55/200][0/27]	eta 0:01:04 lr 0.000039	time 2.3826 (2.3826)	loss 7.6282 (7.6282)	miou 0.1475	grad_norm 40.5341 (40.5341)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:17:44 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [55/200][10/27]	eta 0:00:32 lr 0.000047	time 1.6965 (1.9259)	loss 10.0753 (8.4626)	miou 0.1830	grad_norm 106.2600 (50.7891)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:18:04 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [55/200][20/27]	eta 0:00:13 lr 0.000055	time 1.9401 (1.9304)	loss 9.2308 (8.4751)	miou 0.1901	grad_norm 29.0961 (60.1989)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:18:16 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 55 training takes 0:00:52
[32m[2025-07-13 19:18:16 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 19:18:26 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1096%
[32m[2025-07-13 19:18:26 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2477%
[32m[2025-07-13 19:18:29 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [56/200][0/27]	eta 0:01:19 lr 0.000060	time 2.9281 (2.9281)	loss 7.0903 (7.0903)	miou 0.1210	grad_norm 41.7315 (41.7315)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:18:48 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [56/200][10/27]	eta 0:00:33 lr 0.000068	time 1.5863 (1.9668)	loss 7.4699 (8.7521)	miou 0.1608	grad_norm 45.9234 (51.6005)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:19:07 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [56/200][20/27]	eta 0:00:13 lr 0.000075	time 2.0262 (1.9551)	loss 9.8705 (8.1926)	miou 0.1815	grad_norm 91.4544 (70.0234)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:19:19 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 56 training takes 0:00:52
[32m[2025-07-13 19:19:19 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 19:19:29 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1833%
[32m[2025-07-13 19:19:29 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2477%
[32m[2025-07-13 19:19:32 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [57/200][0/27]	eta 0:01:11 lr 0.000079	time 2.6585 (2.6585)	loss 8.3456 (8.3456)	miou 0.1826	grad_norm 50.1396 (50.1396)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:19:51 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [57/200][10/27]	eta 0:00:33 lr 0.000085	time 1.8456 (1.9620)	loss 8.9009 (7.3432)	miou 0.2115	grad_norm 22.0802 (42.8237)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:20:11 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [57/200][20/27]	eta 0:00:13 lr 0.000090	time 1.6130 (1.9779)	loss 10.3699 (7.7809)	miou 0.2211	grad_norm 42.8991 (44.9251)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:20:22 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 57 training takes 0:00:53
[32m[2025-07-13 19:20:22 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 19:20:33 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.0930%
[32m[2025-07-13 19:20:33 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2477%
[32m[2025-07-13 19:20:35 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [58/200][0/27]	eta 0:01:11 lr 0.000093	time 2.6643 (2.6643)	loss 7.7772 (7.7772)	miou 0.1008	grad_norm 51.1740 (51.1740)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:20:55 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [58/200][10/27]	eta 0:00:34 lr 0.000097	time 1.9407 (2.0211)	loss 6.0660 (8.7942)	miou 0.1359	grad_norm 45.4629 (68.1392)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:21:14 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [58/200][20/27]	eta 0:00:13 lr 0.000099	time 2.0480 (1.9870)	loss 10.6289 (8.7748)	miou 0.1779	grad_norm 80.5531 (75.9975)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:21:27 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 58 training takes 0:00:54
[32m[2025-07-13 19:21:27 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 19:21:37 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.0754%
[32m[2025-07-13 19:21:37 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2477%
[32m[2025-07-13 19:21:40 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [59/200][0/27]	eta 0:01:03 lr 0.000100	time 2.3423 (2.3423)	loss 6.2242 (6.2242)	miou 0.0894	grad_norm 112.0553 (112.0553)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:21:59 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [59/200][10/27]	eta 0:00:34 lr 0.000100	time 2.0885 (2.0038)	loss 10.4281 (9.0890)	miou 0.1223	grad_norm 45.7150 (89.1268)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:22:17 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [59/200][20/27]	eta 0:00:13 lr 0.000099	time 1.9519 (1.9096)	loss 11.7942 (8.8904)	miou 0.1601	grad_norm 62.2837 (78.9984)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:22:29 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 59 training takes 0:00:51
[32m[2025-07-13 19:22:29 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 19:22:39 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1075%
[32m[2025-07-13 19:22:39 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2477%
[32m[2025-07-13 19:22:42 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [60/200][0/27]	eta 0:01:28 lr 0.000097	time 3.2875 (3.2875)	loss 6.0777 (6.0777)	miou 0.1409	grad_norm 126.7935 (126.7935)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:23:01 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [60/200][10/27]	eta 0:00:34 lr 0.000094	time 2.1109 (2.0267)	loss 6.5172 (7.7764)	miou 0.1708	grad_norm 58.9737 (52.6450)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:23:21 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [60/200][20/27]	eta 0:00:14 lr 0.000090	time 1.7509 (2.0093)	loss 10.8870 (8.0248)	miou 0.1786	grad_norm 208.4306 (63.2306)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:23:33 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 60 training takes 0:00:53
[32m[2025-07-13 19:23:33 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 19:23:43 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1909%
[32m[2025-07-13 19:23:43 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2477%
[32m[2025-07-13 19:23:46 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [61/200][0/27]	eta 0:01:19 lr 0.000086	time 2.9381 (2.9381)	loss 8.0714 (8.0714)	miou 0.1930	grad_norm 74.1566 (74.1566)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:24:05 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [61/200][10/27]	eta 0:00:34 lr 0.000081	time 2.2575 (2.0280)	loss 10.6541 (8.3215)	miou 0.2008	grad_norm 57.2746 (60.0815)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:24:24 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [61/200][20/27]	eta 0:00:13 lr 0.000074	time 1.6731 (1.9528)	loss 13.1247 (8.8658)	miou 0.2029	grad_norm 44.6033 (63.6661)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:24:35 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 61 training takes 0:00:52
[32m[2025-07-13 19:24:35 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 19:24:45 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1030%
[32m[2025-07-13 19:24:45 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2477%
[32m[2025-07-13 19:24:48 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [62/200][0/27]	eta 0:01:23 lr 0.000069	time 3.0809 (3.0809)	loss 8.2216 (8.2216)	miou 0.1080	grad_norm 37.6233 (37.6233)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:25:08 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [62/200][10/27]	eta 0:00:34 lr 0.000062	time 1.9759 (2.0201)	loss 6.2620 (7.6726)	miou 0.1580	grad_norm 73.6727 (70.3727)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:25:26 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [62/200][20/27]	eta 0:00:13 lr 0.000054	time 1.7718 (1.9498)	loss 8.1939 (8.0826)	miou 0.1964	grad_norm 29.9629 (168.1260)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:25:37 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 62 training takes 0:00:51
[32m[2025-07-13 19:25:37 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 19:25:47 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.0796%
[32m[2025-07-13 19:25:47 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2477%
[32m[2025-07-13 19:25:50 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [63/200][0/27]	eta 0:01:14 lr 0.000048	time 2.7595 (2.7595)	loss 8.3040 (8.3040)	miou 0.0967	grad_norm 141.0125 (141.0125)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
[32m[2025-07-13 19:26:09 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [63/200][10/27]	eta 0:00:33 lr 0.000041	time 1.6599 (1.9527)	loss 11.3597 (8.1232)	miou 0.1213	grad_norm 56.2258 (74.7391)	loss_scale 65536.0000 (65536.0000)	mem 5937MB
