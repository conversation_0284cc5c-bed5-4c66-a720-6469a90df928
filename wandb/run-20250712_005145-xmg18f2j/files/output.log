=> merge config from config/base_train.yaml
RANK and WORLD_SIZE in environ: 0/3
[32m[2025-07-12 00:51:51 3DDETR.yaml][33m(main.py 549)[39m: INFO Full config saved to /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/config.json
[32m[2025-07-12 00:51:51 3DDETR.yaml][33m(main.py 552)[39m: INFO AMP_ENABLE: true
TAG: default
amp_opt_level: ''
base:
- ''
data:
  augment: false
  batch_size: 1
  cache_mode: part
  data_path: /home-local2/akath.extra.nobkp/dl_challenge
  dataset: Sereact_dataset
  debug: false
  num_workers: 4
  pin_memory: true
  transform: null
  zip_mode: false
eval_mode: false
local_rank: 0
loss:
  matcher_costs:
    cost_box_corners: 1.0
    giou: 5.0
    l1: 2.0
  weights:
    box_corners: 1.0
    giou: 1.0
    size: 1.0
    size_reg: 1.0
model:
  decoder:
    dim: 256
    dropout: 0.1
    ffn_dim: 256
    nhead: 4
    num_layers: 3
  encoder:
    activation: relu
    dim: 256
    dropout: 0.1
    ffn_dim: 128
    nheads: 4
    num_layers: 3
    preencoder_npoints: 2048
    type: vanilla
    use_color: false
  export_model: false
  mlp_dropout: 0.3
  name: 3DDETR.yaml
  num_angular_bins: 12
  num_queries: 256
  position_embedding: fourier
  pretrained: null
  pretrained_weights_path: /home/<USER>/Coding/Pre_trained_Weights/3detr/scannet_ep1080.pth
  resume: ''
  training: true
  unit_test: false
output: /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123
print_freq: 10
save_freq: 1
seed: 40
tag: '123'
train:
  accumulation_steps: 1
  auto_resume: false
  base_lr: 0.0001
  clip_grad: 0.1
  filter_biases_wd: true
  final_lr: 1.0e-06
  lr_scheduler: cosine
  max_epoch: 200
  start_epoch: 0
  unit_test_epoch: 100
  use_checkpoint: false
  warm_lr: 5.0e-06
  warm_lr_epochs: 9
  weight_decay: 0.01
unit_test: false
[32m[2025-07-12 00:51:51 3DDETR.yaml][33m(main.py 553)[39m: INFO {"cfg": "config/base_train.yaml", "opts": null, "batch_size": 1, "data_path": "/home-local2/akath.extra.nobkp/dl_challenge", "pretrained": null, "resume": null, "accumulation_steps": null, "use_checkpoint": false, "disable_amp": false, "amp_opt_level": null, "output": "/home-local2/akath.extra.nobkp/sereact", "tag": null, "eval": false, "unit_test": false, "base_lr": null, "local_rank": 0}
local rank 0 / global rank 0 successfully build train dataset
local rank 0 / global rank 0 successfully build val dataset
[32m[2025-07-12 00:51:51 3DDETR.yaml][33m(main.py 102)[39m: INFO Model3DDETR(
  (pre_encoder): PointnetSAModuleVotes(
    (grouper): QueryAndGroup()
    (mlp_module): SharedMLP(
      (layer0): Conv2d(
        (conv): Conv2d(3, 64, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn): BatchNorm2d(
          (bn): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        )
        (activation): ReLU(inplace=True)
      )
      (layer1): Conv2d(
        (conv): Conv2d(64, 128, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn): BatchNorm2d(
          (bn): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        )
        (activation): ReLU(inplace=True)
      )
      (layer2): Conv2d(
        (conv): Conv2d(128, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn): BatchNorm2d(
          (bn): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        )
        (activation): ReLU(inplace=True)
      )
    )
  )
  (encoder): TransformerEncoder(
    (layers): ModuleList(
      (0): TransformerEncoderLayer(
        attn_dr=0.1
        (self_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (linear1): Linear(in_features=256, out_features=128, bias=True)
        (dropout): Dropout(p=0.1, inplace=False)
        (linear2): Linear(in_features=128, out_features=256, bias=True)
        (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout2): Dropout(p=0.1, inplace=False)
        (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout1): Dropout(p=0.1, inplace=False)
        (activation): ReLU()
      )
      (1): TransformerEncoderLayer(
        attn_dr=0.1
        (self_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (linear1): Linear(in_features=256, out_features=128, bias=True)
        (dropout): Dropout(p=0.1, inplace=False)
        (linear2): Linear(in_features=128, out_features=256, bias=True)
        (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout2): Dropout(p=0.1, inplace=False)
        (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout1): Dropout(p=0.1, inplace=False)
        (activation): ReLU()
      )
      (2): TransformerEncoderLayer(
        attn_dr=0.1
        (self_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (linear1): Linear(in_features=256, out_features=128, bias=True)
        (dropout): Dropout(p=0.1, inplace=False)
        (linear2): Linear(in_features=128, out_features=256, bias=True)
        (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout2): Dropout(p=0.1, inplace=False)
        (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout1): Dropout(p=0.1, inplace=False)
        (activation): ReLU()
      )
    )
  )
  (encoder_decoder_projection): GenericMLP(
    (layers): Sequential(
      (0): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
      (1): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (2): ReLU()
      (3): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
      (4): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (5): ReLU()
    )
  )
  (positional_embedding): PositionEmbeddingCoordsSine(type=fourier, scale=6.283185307179586, normalize=True, gaussB_shape=torch.Size([3, 128]), gaussB_sum=-17.944507598876953)
  (query_projection): GenericMLP(
    (layers): Sequential(
      (0): Conv1d(256, 256, kernel_size=(1,), stride=(1,))
      (1): ReLU()
      (2): Conv1d(256, 256, kernel_size=(1,), stride=(1,))
      (3): ReLU()
    )
  )
  (decoder): TransformerDecoder(
    (layers): ModuleList(
      (0): TransformerDecoderLayer(
        (self_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (multihead_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm3): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout1): Dropout(p=0.1, inplace=False)
        (dropout2): Dropout(p=0.1, inplace=False)
        (dropout3): Dropout(p=0.1, inplace=False)
        (linear1): Linear(in_features=256, out_features=256, bias=True)
        (dropout): Dropout(p=0.1, inplace=False)
        (linear2): Linear(in_features=256, out_features=256, bias=True)
        (activation): ReLU()
      )
      (1): TransformerDecoderLayer(
        (self_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (multihead_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm3): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout1): Dropout(p=0.1, inplace=False)
        (dropout2): Dropout(p=0.1, inplace=False)
        (dropout3): Dropout(p=0.1, inplace=False)
        (linear1): Linear(in_features=256, out_features=256, bias=True)
        (dropout): Dropout(p=0.1, inplace=False)
        (linear2): Linear(in_features=256, out_features=256, bias=True)
        (activation): ReLU()
      )
      (2): TransformerDecoderLayer(
        (self_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (multihead_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm3): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout1): Dropout(p=0.1, inplace=False)
        (dropout2): Dropout(p=0.1, inplace=False)
        (dropout3): Dropout(p=0.1, inplace=False)
        (linear1): Linear(in_features=256, out_features=256, bias=True)
        (dropout): Dropout(p=0.1, inplace=False)
        (linear2): Linear(in_features=256, out_features=256, bias=True)
        (activation): ReLU()
      )
    )
    (norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (mlp_heads): ModuleDict(
    (center_head): GenericMLP(
      (layers): Sequential(
        (0): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
        (1): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU()
        (3): Dropout(p=0.3, inplace=False)
        (4): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
        (5): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (6): ReLU()
        (7): Dropout(p=0.3, inplace=False)
        (8): Conv1d(256, 3, kernel_size=(1,), stride=(1,))
      )
    )
    (size_head): GenericMLP(
      (layers): Sequential(
        (0): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
        (1): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU()
        (3): Dropout(p=0.3, inplace=False)
        (4): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
        (5): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (6): ReLU()
        (7): Dropout(p=0.3, inplace=False)
        (8): Conv1d(256, 3, kernel_size=(1,), stride=(1,))
      )
    )
    (angle_cls_head): GenericMLP(
      (layers): Sequential(
        (0): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
        (1): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU()
        (3): Dropout(p=0.3, inplace=False)
        (4): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
        (5): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (6): ReLU()
        (7): Dropout(p=0.3, inplace=False)
        (8): Conv1d(256, 12, kernel_size=(1,), stride=(1,))
      )
    )
    (angle_residual_head): GenericMLP(
      (layers): Sequential(
        (0): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
        (1): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU()
        (3): Dropout(p=0.3, inplace=False)
        (4): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
        (5): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (6): ReLU()
        (7): Dropout(p=0.3, inplace=False)
        (8): Conv1d(256, 12, kernel_size=(1,), stride=(1,))
      )
    )
  )
)
[32m[2025-07-12 00:51:51 3DDETR.yaml][33m(main.py 104)[39m: INFO number of params: 3811038
[32m[2025-07-12 00:51:51 3DDETR.yaml][33m(main.py 153)[39m: INFO Start training
[32m[2025-07-12 00:51:55 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [0/200][0/54]	eta 0:03:32 lr 0.000100	time 3.9335 (3.9335)	loss 38.3961 (38.3961)	miou 0.0295	grad_norm 184.7570 (184.7570)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 00:52:16 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [0/200][10/54]	eta 0:01:40 lr 0.000099	time 2.4911 (2.2922)	loss 10.9717 (19.7316)	miou 0.1866	grad_norm 433.6414 (129.1212)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 00:52:35 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [0/200][20/54]	eta 0:01:10 lr 0.000097	time 2.4032 (2.0742)	loss 13.3186 (18.4467)	miou 0.1736	grad_norm 38.3370 (98.7213)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 00:52:54 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [0/200][30/54]	eta 0:00:48 lr 0.000094	time 2.2236 (2.0265)	loss 20.4815 (18.6381)	miou 0.1717	grad_norm 46.1647 (86.9686)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 00:53:09 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [0/200][40/54]	eta 0:00:26 lr 0.000090	time 0.9860 (1.9052)	loss 10.3756 (17.6221)	miou 0.1638	grad_norm 35.8179 (75.2828)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 00:53:22 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [0/200][50/54]	eta 0:00:07 lr 0.000085	time 1.7769 (1.7840)	loss 11.3997 (16.7474)	miou 0.1622	grad_norm 46.7402 (73.8405)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 00:53:26 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 0 training takes 0:01:34
[32m[2025-07-12 00:53:26 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
0
[32m[2025-07-12 00:53:40 3DDETR.yaml][33m(utils_help.py 202)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saving......
[32m[2025-07-12 00:53:40 3DDETR.yaml][33m(utils_help.py 204)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saved !!!
[32m[2025-07-12 00:53:40 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1573%
[32m[2025-07-12 00:53:40 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.1573%
[32m[2025-07-12 00:53:43 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [1/200][0/54]	eta 0:02:00 lr 0.000082	time 2.2249 (2.2249)	loss 16.3428 (16.3428)	miou 0.1565	grad_norm 159.3008 (159.3008)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 00:53:55 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [1/200][10/54]	eta 0:00:57 lr 0.000076	time 0.9169 (1.3069)	loss 10.3212 (12.1645)	miou 0.1594	grad_norm 26.0345 (51.7440)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 00:54:06 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [1/200][20/54]	eta 0:00:41 lr 0.000069	time 1.3379 (1.2239)	loss 9.2426 (11.8926)	miou 0.1593	grad_norm 61.2550 (49.6776)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 00:54:17 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [1/200][30/54]	eta 0:00:28 lr 0.000062	time 1.0394 (1.1686)	loss 10.8352 (12.2835)	miou 0.1509	grad_norm 67.9232 (47.9051)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 00:54:27 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [1/200][40/54]	eta 0:00:15 lr 0.000054	time 1.0569 (1.1297)	loss 12.0171 (11.7875)	miou 0.1567	grad_norm 73.2346 (45.6555)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 00:54:39 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [1/200][50/54]	eta 0:00:04 lr 0.000046	time 1.2646 (1.1480)	loss 12.2862 (11.6932)	miou 0.1554	grad_norm 22.2219 (44.2433)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 00:54:42 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 1 training takes 0:01:01
[32m[2025-07-12 00:54:42 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 00:54:53 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1404%
[32m[2025-07-12 00:54:53 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.1573%
[32m[2025-07-12 00:54:54 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [2/200][0/54]	eta 0:01:08 lr 0.000043	time 1.2600 (1.2600)	loss 9.7180 (9.7180)	miou 0.1352	grad_norm 20.8186 (20.8186)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 00:55:04 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [2/200][10/54]	eta 0:00:46 lr 0.000035	time 0.9927 (1.0650)	loss 9.6724 (10.3811)	miou 0.1364	grad_norm 23.9726 (31.9946)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 00:55:15 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [2/200][20/54]	eta 0:00:37 lr 0.000028	time 1.2447 (1.0920)	loss 9.5590 (10.1409)	miou 0.1437	grad_norm 28.1660 (30.0008)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 00:55:27 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [2/200][30/54]	eta 0:00:26 lr 0.000021	time 1.3506 (1.0957)	loss 8.9116 (10.8856)	miou 0.1437	grad_norm 35.6799 (35.9324)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 00:55:36 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [2/200][40/54]	eta 0:00:14 lr 0.000015	time 1.0452 (1.0703)	loss 10.1284 (10.7931)	miou 0.1434	grad_norm 33.1443 (36.2522)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 00:55:48 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [2/200][50/54]	eta 0:00:04 lr 0.000010	time 1.0024 (1.0817)	loss 10.7362 (10.7113)	miou 0.1479	grad_norm 94.9705 (39.9756)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 00:55:51 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 2 training takes 0:00:58
[32m[2025-07-12 00:55:51 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
2
[32m[2025-07-12 00:56:01 3DDETR.yaml][33m(utils_help.py 202)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saving......
[32m[2025-07-12 00:56:01 3DDETR.yaml][33m(utils_help.py 204)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saved !!!
[32m[2025-07-12 00:56:01 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1591%
[32m[2025-07-12 00:56:01 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.1591%
[32m[2025-07-12 00:56:03 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [3/200][0/54]	eta 0:01:36 lr 0.000008	time 1.7849 (1.7849)	loss 9.6860 (9.6860)	miou 0.1596	grad_norm 62.2676 (62.2676)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 00:56:14 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [3/200][10/54]	eta 0:00:51 lr 0.000004	time 0.9617 (1.1672)	loss 10.5973 (11.1906)	miou 0.1481	grad_norm 20.1857 (41.1106)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 00:56:24 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [3/200][20/54]	eta 0:00:37 lr 0.000002	time 0.9250 (1.1037)	loss 9.6808 (11.7156)	miou 0.1538	grad_norm 29.7916 (42.1919)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 00:56:35 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [3/200][30/54]	eta 0:00:26 lr 0.000000	time 1.0967 (1.1116)	loss 9.7774 (11.2362)	miou 0.1598	grad_norm 19.9460 (43.9643)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 00:56:47 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [3/200][40/54]	eta 0:00:15 lr 0.000000	time 1.4806 (1.1175)	loss 13.2140 (10.8376)	miou 0.1586	grad_norm 47.5820 (42.4338)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 00:56:57 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [3/200][50/54]	eta 0:00:04 lr 0.000001	time 0.9586 (1.1039)	loss 10.0374 (10.9226)	miou 0.1563	grad_norm 54.9419 (42.2246)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 00:57:01 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 3 training takes 0:00:59
[32m[2025-07-12 00:57:01 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
3
[32m[2025-07-12 00:57:11 3DDETR.yaml][33m(utils_help.py 202)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saving......
[32m[2025-07-12 00:57:11 3DDETR.yaml][33m(utils_help.py 204)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saved !!!
[32m[2025-07-12 00:57:11 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1652%
[32m[2025-07-12 00:57:11 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.1652%
[32m[2025-07-12 00:57:13 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [4/200][0/54]	eta 0:01:12 lr 0.000002	time 1.3507 (1.3507)	loss 9.8678 (9.8678)	miou 0.1629	grad_norm 51.8279 (51.8279)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 00:57:23 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [4/200][10/54]	eta 0:00:45 lr 0.000004	time 1.0398 (1.0387)	loss 11.6296 (12.5715)	miou 0.1527	grad_norm 48.5194 (49.9513)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 00:57:33 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [4/200][20/54]	eta 0:00:35 lr 0.000008	time 0.9254 (1.0472)	loss 12.3908 (11.5145)	miou 0.1508	grad_norm 47.0255 (48.3983)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 00:57:44 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [4/200][30/54]	eta 0:00:25 lr 0.000013	time 0.9535 (1.0614)	loss 8.8855 (11.6213)	miou 0.1486	grad_norm 20.7656 (47.9855)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 00:57:54 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [4/200][40/54]	eta 0:00:14 lr 0.000019	time 1.0434 (1.0471)	loss 10.3736 (11.6087)	miou 0.1527	grad_norm 22.9698 (47.8264)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 00:58:05 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [4/200][50/54]	eta 0:00:04 lr 0.000025	time 1.2751 (1.0626)	loss 11.0605 (11.5221)	miou 0.1508	grad_norm 35.7558 (46.6748)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 00:58:09 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 4 training takes 0:00:57
[32m[2025-07-12 00:58:09 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
4
[32m[2025-07-12 00:58:19 3DDETR.yaml][33m(utils_help.py 202)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saving......
[32m[2025-07-12 00:58:19 3DDETR.yaml][33m(utils_help.py 204)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saved !!!
[32m[2025-07-12 00:58:19 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1688%
[32m[2025-07-12 00:58:19 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.1688%
[32m[2025-07-12 00:58:21 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [5/200][0/54]	eta 0:01:08 lr 0.000028	time 1.2667 (1.2667)	loss 7.9865 (7.9865)	miou 0.1703	grad_norm 178.9871 (178.9871)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 00:58:32 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [5/200][10/54]	eta 0:00:49 lr 0.000035	time 1.0609 (1.1196)	loss 9.3838 (9.3711)	miou 0.1716	grad_norm 41.4147 (57.3807)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 00:58:42 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [5/200][20/54]	eta 0:00:37 lr 0.000043	time 1.2229 (1.0945)	loss 12.0534 (9.9991)	miou 0.1730	grad_norm 65.0884 (53.3676)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 00:58:53 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [5/200][30/54]	eta 0:00:25 lr 0.000051	time 1.1458 (1.0798)	loss 10.0958 (10.5253)	miou 0.1700	grad_norm 104.9004 (56.8591)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 00:59:04 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [5/200][40/54]	eta 0:00:15 lr 0.000059	time 1.0638 (1.0994)	loss 11.8280 (10.3108)	miou 0.1673	grad_norm 36.1793 (61.4309)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 00:59:15 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [5/200][50/54]	eta 0:00:04 lr 0.000066	time 1.1746 (1.0992)	loss 9.8998 (10.1730)	miou 0.1652	grad_norm 84.6713 (58.8164)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 00:59:19 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 5 training takes 0:00:59
[32m[2025-07-12 00:59:19 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 00:59:29 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1367%
[32m[2025-07-12 00:59:29 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.1688%
[32m[2025-07-12 00:59:30 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [6/200][0/54]	eta 0:01:12 lr 0.000069	time 1.3463 (1.3463)	loss 8.4754 (8.4754)	miou 0.1397	grad_norm 131.5013 (131.5013)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 00:59:41 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [6/200][10/54]	eta 0:00:49 lr 0.000076	time 0.8205 (1.1344)	loss 7.9535 (9.3475)	miou 0.1381	grad_norm 63.3453 (70.2662)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 00:59:51 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [6/200][20/54]	eta 0:00:36 lr 0.000082	time 1.0477 (1.0701)	loss 12.7600 (9.8992)	miou 0.1452	grad_norm 37.0357 (60.4977)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:00:02 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [6/200][30/54]	eta 0:00:25 lr 0.000088	time 1.1471 (1.0696)	loss 8.4682 (9.6306)	miou 0.1620	grad_norm 48.5691 (56.7340)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:00:12 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [6/200][40/54]	eta 0:00:14 lr 0.000093	time 1.1510 (1.0636)	loss 8.6048 (9.8708)	miou 0.1579	grad_norm 38.3677 (61.1211)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:00:23 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [6/200][50/54]	eta 0:00:04 lr 0.000096	time 1.0829 (1.0624)	loss 12.9546 (9.7746)	miou 0.1589	grad_norm 182.1302 (63.9975)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:00:26 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 6 training takes 0:00:57
[32m[2025-07-12 01:00:26 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
6
[32m[2025-07-12 01:00:36 3DDETR.yaml][33m(utils_help.py 202)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saving......
[32m[2025-07-12 01:00:36 3DDETR.yaml][33m(utils_help.py 204)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saved !!!
[32m[2025-07-12 01:00:36 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2156%
[32m[2025-07-12 01:00:36 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2156%
[32m[2025-07-12 01:00:38 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [7/200][0/54]	eta 0:01:09 lr 0.000097	time 1.2931 (1.2931)	loss 8.7043 (8.7043)	miou 0.2085	grad_norm 77.2554 (77.2554)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:00:49 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [7/200][10/54]	eta 0:00:49 lr 0.000099	time 1.1164 (1.1334)	loss 7.3804 (9.2142)	miou 0.1968	grad_norm 72.3004 (62.0707)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:00:58 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [7/200][20/54]	eta 0:00:35 lr 0.000100	time 0.9368 (1.0499)	loss 9.6360 (9.2182)	miou 0.1859	grad_norm 81.4164 (56.8650)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:01:09 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [7/200][30/54]	eta 0:00:25 lr 0.000100	time 0.9522 (1.0480)	loss 7.6697 (9.3749)	miou 0.1850	grad_norm 96.7886 (61.9709)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:01:20 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [7/200][40/54]	eta 0:00:15 lr 0.000098	time 1.0469 (1.0730)	loss 7.9463 (9.2490)	miou 0.1916	grad_norm 34.7074 (57.6795)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:01:31 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [7/200][50/54]	eta 0:00:04 lr 0.000095	time 1.1465 (1.0765)	loss 7.0736 (9.3788)	miou 0.1933	grad_norm 24.7812 (59.1998)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:01:34 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 7 training takes 0:00:57
[32m[2025-07-12 01:01:34 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
7
[32m[2025-07-12 01:01:44 3DDETR.yaml][33m(utils_help.py 202)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saving......
[32m[2025-07-12 01:01:45 3DDETR.yaml][33m(utils_help.py 204)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saved !!!
[32m[2025-07-12 01:01:45 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2168%
[32m[2025-07-12 01:01:45 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2168%
[32m[2025-07-12 01:01:46 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [8/200][0/54]	eta 0:01:12 lr 0.000093	time 1.3353 (1.3353)	loss 5.8002 (5.8002)	miou 0.2165	grad_norm 83.0652 (83.0652)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:01:57 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [8/200][10/54]	eta 0:00:50 lr 0.000089	time 1.1754 (1.1414)	loss 9.6410 (9.3862)	miou 0.1960	grad_norm 42.5496 (49.0516)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:02:08 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [8/200][20/54]	eta 0:00:37 lr 0.000084	time 1.3181 (1.1127)	loss 5.8431 (9.0639)	miou 0.1979	grad_norm 55.7388 (49.2736)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:02:19 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [8/200][30/54]	eta 0:00:26 lr 0.000077	time 1.2466 (1.1040)	loss 9.5452 (9.1770)	miou 0.1928	grad_norm 19.9753 (45.2505)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:02:30 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [8/200][40/54]	eta 0:00:15 lr 0.000071	time 0.9870 (1.1069)	loss 9.6071 (9.2898)	miou 0.1878	grad_norm 17.7218 (47.5813)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:02:40 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [8/200][50/54]	eta 0:00:04 lr 0.000063	time 1.0184 (1.0948)	loss 8.1121 (9.2235)	miou 0.1933	grad_norm 29.8898 (46.8575)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:02:44 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 8 training takes 0:00:59
[32m[2025-07-12 01:02:44 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 01:02:54 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1951%
[32m[2025-07-12 01:02:54 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2168%
[32m[2025-07-12 01:02:55 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [9/200][0/54]	eta 0:01:05 lr 0.000060	time 1.2040 (1.2040)	loss 11.9611 (11.9611)	miou 0.1935	grad_norm 40.6033 (40.6033)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:03:06 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [9/200][10/54]	eta 0:00:47 lr 0.000052	time 0.9960 (1.0766)	loss 11.3399 (9.9324)	miou 0.1936	grad_norm 27.5562 (43.9212)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:03:16 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [9/200][20/54]	eta 0:00:36 lr 0.000045	time 1.0309 (1.0691)	loss 6.6269 (9.1750)	miou 0.1888	grad_norm 27.1284 (41.3697)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:03:26 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [9/200][30/54]	eta 0:00:25 lr 0.000037	time 0.9577 (1.0578)	loss 12.5379 (8.9069)	miou 0.1905	grad_norm 64.7701 (41.4762)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:03:39 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [9/200][40/54]	eta 0:00:15 lr 0.000029	time 1.4028 (1.0963)	loss 8.1995 (8.8384)	miou 0.1914	grad_norm 39.9774 (43.9924)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:03:50 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [9/200][50/54]	eta 0:00:04 lr 0.000023	time 0.9787 (1.0971)	loss 8.4299 (8.9069)	miou 0.1924	grad_norm 110.0767 (44.8867)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:03:52 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 9 training takes 0:00:58
[32m[2025-07-12 01:03:52 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 01:04:03 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1952%
[32m[2025-07-12 01:04:03 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2168%
[32m[2025-07-12 01:04:04 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [10/200][0/54]	eta 0:01:31 lr 0.000020	time 1.6862 (1.6862)	loss 8.3795 (8.3795)	miou 0.1940	grad_norm 56.7924 (56.7924)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:04:14 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [10/200][10/54]	eta 0:00:45 lr 0.000014	time 0.9087 (1.0275)	loss 9.7475 (9.3514)	miou 0.1745	grad_norm 22.0346 (46.2218)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:04:25 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [10/200][20/54]	eta 0:00:35 lr 0.000009	time 0.9080 (1.0583)	loss 6.0887 (8.6590)	miou 0.1833	grad_norm 36.9466 (46.3787)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:04:36 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [10/200][30/54]	eta 0:00:25 lr 0.000005	time 0.8784 (1.0766)	loss 8.3394 (8.9273)	miou 0.1775	grad_norm 18.5817 (45.4481)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:04:47 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [10/200][40/54]	eta 0:00:15 lr 0.000002	time 1.2515 (1.0744)	loss 8.0790 (8.7792)	miou 0.1840	grad_norm 39.3074 (44.8502)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:04:58 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [10/200][50/54]	eta 0:00:04 lr 0.000000	time 1.1292 (1.0866)	loss 6.5972 (8.6864)	miou 0.1865	grad_norm 26.9419 (44.6450)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:05:02 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 10 training takes 0:00:59
[32m[2025-07-12 01:05:02 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 01:05:12 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1915%
[32m[2025-07-12 01:05:12 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2168%
[32m[2025-07-12 01:05:13 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [11/200][0/54]	eta 0:01:21 lr 0.000000	time 1.5075 (1.5075)	loss 7.8732 (7.8732)	miou 0.1901	grad_norm 35.9791 (35.9791)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:05:23 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [11/200][10/54]	eta 0:00:44 lr 0.000000	time 0.8177 (1.0121)	loss 10.8074 (9.0573)	miou 0.1831	grad_norm 21.5099 (57.7596)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:05:33 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [11/200][20/54]	eta 0:00:34 lr 0.000001	time 0.9874 (1.0107)	loss 9.8774 (8.9135)	miou 0.1841	grad_norm 35.6861 (47.6786)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:05:44 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [11/200][30/54]	eta 0:00:25 lr 0.000004	time 1.0346 (1.0528)	loss 11.6153 (9.0206)	miou 0.1845	grad_norm 27.7132 (42.2326)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:05:55 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [11/200][40/54]	eta 0:00:14 lr 0.000007	time 0.8637 (1.0474)	loss 8.8822 (8.8707)	miou 0.1805	grad_norm 37.7802 (40.1983)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:06:05 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [11/200][50/54]	eta 0:00:04 lr 0.000012	time 1.0018 (1.0464)	loss 13.9806 (8.9972)	miou 0.1835	grad_norm 32.8815 (40.3719)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:06:09 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 11 training takes 0:00:57
[32m[2025-07-12 01:06:09 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 01:06:19 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2004%
[32m[2025-07-12 01:06:19 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2168%
[32m[2025-07-12 01:06:20 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [12/200][0/54]	eta 0:01:14 lr 0.000014	time 1.3804 (1.3804)	loss 8.0070 (8.0070)	miou 0.1960	grad_norm 120.7323 (120.7323)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:06:30 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [12/200][10/54]	eta 0:00:42 lr 0.000020	time 0.6055 (0.9710)	loss 10.0886 (9.0854)	miou 0.1850	grad_norm 23.1151 (35.1106)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:06:41 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [12/200][20/54]	eta 0:00:35 lr 0.000027	time 1.2119 (1.0586)	loss 6.5190 (8.7014)	miou 0.1887	grad_norm 34.8300 (37.3838)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:06:52 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [12/200][30/54]	eta 0:00:25 lr 0.000034	time 1.1750 (1.0606)	loss 9.1683 (8.7838)	miou 0.1857	grad_norm 18.2372 (34.6745)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:07:03 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [12/200][40/54]	eta 0:00:14 lr 0.000041	time 1.1071 (1.0712)	loss 6.6441 (8.6776)	miou 0.1873	grad_norm 56.6321 (39.8425)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:07:14 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [12/200][50/54]	eta 0:00:04 lr 0.000049	time 0.9478 (1.0728)	loss 7.4212 (8.7753)	miou 0.1860	grad_norm 41.1560 (41.6163)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:07:17 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 12 training takes 0:00:57
[32m[2025-07-12 01:07:17 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 01:07:27 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1921%
[32m[2025-07-12 01:07:27 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2168%
[32m[2025-07-12 01:07:28 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [13/200][0/54]	eta 0:00:57 lr 0.000052	time 1.0573 (1.0573)	loss 8.6426 (8.6426)	miou 0.1928	grad_norm 31.1072 (31.1072)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:07:38 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [13/200][10/54]	eta 0:00:47 lr 0.000060	time 1.0377 (1.0717)	loss 7.8022 (8.0269)	miou 0.1864	grad_norm 16.7153 (37.2993)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:07:49 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [13/200][20/54]	eta 0:00:35 lr 0.000068	time 1.0025 (1.0444)	loss 10.9186 (8.1838)	miou 0.1824	grad_norm 31.9035 (40.3831)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:07:59 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [13/200][30/54]	eta 0:00:25 lr 0.000075	time 1.2637 (1.0512)	loss 7.7906 (8.1850)	miou 0.1809	grad_norm 80.6036 (42.7077)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:08:09 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [13/200][40/54]	eta 0:00:14 lr 0.000081	time 0.7334 (1.0270)	loss 9.0410 (8.2222)	miou 0.1801	grad_norm 40.6062 (44.9426)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:08:19 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [13/200][50/54]	eta 0:00:04 lr 0.000087	time 0.9356 (1.0317)	loss 10.7658 (8.4482)	miou 0.1901	grad_norm 33.7012 (45.3042)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:08:23 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 13 training takes 0:00:56
[32m[2025-07-12 01:08:23 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 01:08:33 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1945%
[32m[2025-07-12 01:08:33 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2168%
[32m[2025-07-12 01:08:34 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [14/200][0/54]	eta 0:01:01 lr 0.000089	time 1.1460 (1.1460)	loss 8.7177 (8.7177)	miou 0.1909	grad_norm 66.6234 (66.6234)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:08:45 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [14/200][10/54]	eta 0:00:47 lr 0.000093	time 0.9436 (1.0773)	loss 6.7829 (8.8689)	miou 0.1850	grad_norm 34.3790 (64.9286)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:08:55 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [14/200][20/54]	eta 0:00:36 lr 0.000097	time 1.0666 (1.0724)	loss 10.2911 (8.9110)	miou 0.1821	grad_norm 62.6918 (51.6317)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:09:06 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [14/200][30/54]	eta 0:00:25 lr 0.000099	time 0.8309 (1.0784)	loss 10.2877 (8.8798)	miou 0.1851	grad_norm 65.3282 (47.0395)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:09:17 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [14/200][40/54]	eta 0:00:15 lr 0.000100	time 1.0536 (1.0899)	loss 10.3544 (8.7553)	miou 0.1759	grad_norm 95.4126 (45.9900)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:09:28 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [14/200][50/54]	eta 0:00:04 lr 0.000100	time 0.9856 (1.0852)	loss 6.9937 (8.8332)	miou 0.1793	grad_norm 40.3220 (45.8397)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:09:32 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 14 training takes 0:00:58
[32m[2025-07-12 01:09:32 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 01:09:42 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1497%
[32m[2025-07-12 01:09:42 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2168%
[32m[2025-07-12 01:09:43 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [15/200][0/54]	eta 0:01:17 lr 0.000099	time 1.4288 (1.4288)	loss 7.3595 (7.3595)	miou 0.1531	grad_norm 18.7900 (18.7900)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:09:53 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [15/200][10/54]	eta 0:00:46 lr 0.000097	time 1.3317 (1.0578)	loss 6.0134 (8.2214)	miou 0.1739	grad_norm 131.3060 (41.5947)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:10:03 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [15/200][20/54]	eta 0:00:35 lr 0.000094	time 0.9999 (1.0470)	loss 10.0077 (8.3142)	miou 0.1859	grad_norm 18.3728 (32.2539)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:10:14 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [15/200][30/54]	eta 0:00:25 lr 0.000090	time 0.8905 (1.0432)	loss 7.3444 (8.2734)	miou 0.1905	grad_norm 45.6780 (30.2194)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:10:26 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [15/200][40/54]	eta 0:00:15 lr 0.000085	time 1.3843 (1.0789)	loss 7.6346 (8.2146)	miou 0.1949	grad_norm 52.3552 (33.0252)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:10:36 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [15/200][50/54]	eta 0:00:04 lr 0.000079	time 1.2732 (1.0780)	loss 5.9176 (8.2472)	miou 0.1996	grad_norm 32.5988 (33.3025)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:10:40 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 15 training takes 0:00:58
[32m[2025-07-12 01:10:40 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 01:10:50 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1804%
[32m[2025-07-12 01:10:50 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2168%
[32m[2025-07-12 01:10:51 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [16/200][0/54]	eta 0:00:56 lr 0.000076	time 1.0475 (1.0475)	loss 6.8801 (6.8801)	miou 0.1753	grad_norm 55.3980 (55.3980)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:11:01 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [16/200][10/54]	eta 0:00:42 lr 0.000069	time 0.8439 (0.9704)	loss 4.9099 (8.2653)	miou 0.1903	grad_norm 47.7807 (33.9874)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:11:11 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [16/200][20/54]	eta 0:00:34 lr 0.000062	time 1.0077 (1.0222)	loss 10.0243 (8.5286)	miou 0.2055	grad_norm 18.8765 (31.6428)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:11:23 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [16/200][30/54]	eta 0:00:25 lr 0.000054	time 1.3130 (1.0543)	loss 10.3224 (8.5105)	miou 0.2088	grad_norm 57.6892 (32.9199)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:11:33 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [16/200][40/54]	eta 0:00:14 lr 0.000046	time 1.1672 (1.0602)	loss 15.5073 (8.7614)	miou 0.2132	grad_norm 34.6746 (35.3119)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:11:44 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [16/200][50/54]	eta 0:00:04 lr 0.000038	time 1.0895 (1.0624)	loss 8.0114 (8.6790)	miou 0.2110	grad_norm 30.8702 (35.7603)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:11:47 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 16 training takes 0:00:56
[32m[2025-07-12 01:11:47 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 01:11:57 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1913%
[32m[2025-07-12 01:11:57 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2168%
[32m[2025-07-12 01:11:58 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [17/200][0/54]	eta 0:01:20 lr 0.000035	time 1.4867 (1.4867)	loss 6.3455 (6.3455)	miou 0.1923	grad_norm 28.4482 (28.4482)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:12:08 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [17/200][10/54]	eta 0:00:45 lr 0.000028	time 0.9276 (1.0310)	loss 7.4550 (8.6966)	miou 0.2114	grad_norm 23.5710 (38.4966)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:12:19 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [17/200][20/54]	eta 0:00:36 lr 0.000021	time 1.0810 (1.0761)	loss 10.3644 (8.0914)	miou 0.2115	grad_norm 15.7735 (37.8658)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:12:29 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [17/200][30/54]	eta 0:00:25 lr 0.000015	time 0.8504 (1.0522)	loss 6.6073 (8.0799)	miou 0.2070	grad_norm 45.6200 (37.2268)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:12:40 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [17/200][40/54]	eta 0:00:14 lr 0.000010	time 1.2225 (1.0644)	loss 8.8173 (8.0997)	miou 0.2062	grad_norm 20.8999 (37.2978)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:12:50 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [17/200][50/54]	eta 0:00:04 lr 0.000006	time 1.0483 (1.0533)	loss 6.6399 (8.2630)	miou 0.2064	grad_norm 28.6251 (37.0835)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:12:54 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 17 training takes 0:00:57
[32m[2025-07-12 01:12:54 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 01:13:04 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2046%
[32m[2025-07-12 01:13:04 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2168%
[32m[2025-07-12 01:13:06 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [18/200][0/54]	eta 0:01:42 lr 0.000004	time 1.8975 (1.8975)	loss 7.1673 (7.1673)	miou 0.2034	grad_norm 45.2342 (45.2342)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:13:17 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [18/200][10/54]	eta 0:00:50 lr 0.000002	time 0.9372 (1.1402)	loss 6.1904 (8.9453)	miou 0.1978	grad_norm 28.2036 (39.1376)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:13:26 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [18/200][20/54]	eta 0:00:35 lr 0.000000	time 0.9969 (1.0575)	loss 10.0955 (8.3034)	miou 0.1990	grad_norm 25.3762 (37.5546)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:13:37 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [18/200][30/54]	eta 0:00:25 lr 0.000000	time 1.3574 (1.0738)	loss 6.9765 (8.2262)	miou 0.2013	grad_norm 85.2904 (39.9314)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:13:48 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [18/200][40/54]	eta 0:00:14 lr 0.000001	time 0.8728 (1.0634)	loss 7.7381 (8.4545)	miou 0.2024	grad_norm 51.9978 (39.2044)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:13:59 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [18/200][50/54]	eta 0:00:04 lr 0.000003	time 1.1685 (1.0675)	loss 10.5045 (8.4527)	miou 0.2024	grad_norm 67.2479 (38.9003)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:14:02 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 18 training takes 0:00:58
[32m[2025-07-12 01:14:02 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 01:14:12 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1948%
[32m[2025-07-12 01:14:12 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2168%
[32m[2025-07-12 01:14:14 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [19/200][0/54]	eta 0:01:17 lr 0.000004	time 1.4395 (1.4395)	loss 9.4982 (9.4982)	miou 0.1958	grad_norm 37.2935 (37.2935)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:14:24 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [19/200][10/54]	eta 0:00:47 lr 0.000008	time 0.9840 (1.0772)	loss 13.0280 (8.6179)	miou 0.1900	grad_norm 30.9986 (30.6187)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:14:35 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [19/200][20/54]	eta 0:00:36 lr 0.000013	time 1.5120 (1.0795)	loss 10.7294 (8.5760)	miou 0.1978	grad_norm 26.0175 (33.8278)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:14:45 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [19/200][30/54]	eta 0:00:25 lr 0.000019	time 0.8584 (1.0515)	loss 6.6316 (7.9957)	miou 0.2081	grad_norm 55.0464 (35.2595)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:14:55 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [19/200][40/54]	eta 0:00:14 lr 0.000025	time 1.1482 (1.0454)	loss 11.0821 (7.9746)	miou 0.2128	grad_norm 25.3068 (34.5763)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:15:06 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [19/200][50/54]	eta 0:00:04 lr 0.000032	time 1.2872 (1.0637)	loss 7.4998 (8.0918)	miou 0.2143	grad_norm 26.1445 (35.9923)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:15:10 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 19 training takes 0:00:58
[32m[2025-07-12 01:15:10 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 01:15:20 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1835%
[32m[2025-07-12 01:15:20 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2168%
[32m[2025-07-12 01:15:22 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [20/200][0/54]	eta 0:01:09 lr 0.000035	time 1.2903 (1.2903)	loss 6.8414 (6.8414)	miou 0.1856	grad_norm 23.4590 (23.4590)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:15:32 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [20/200][10/54]	eta 0:00:46 lr 0.000043	time 0.8887 (1.0563)	loss 9.0198 (8.1900)	miou 0.1852	grad_norm 17.7252 (31.4554)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:15:43 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [20/200][20/54]	eta 0:00:36 lr 0.000051	time 0.9457 (1.0652)	loss 10.0286 (8.3640)	miou 0.1954	grad_norm 27.2357 (31.0584)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:15:53 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [20/200][30/54]	eta 0:00:25 lr 0.000059	time 0.7667 (1.0522)	loss 9.4629 (8.1700)	miou 0.1985	grad_norm 19.7513 (32.2584)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:16:04 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [20/200][40/54]	eta 0:00:15 lr 0.000066	time 1.1267 (1.0716)	loss 5.9699 (8.2751)	miou 0.2015	grad_norm 31.9401 (32.4259)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:16:15 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [20/200][50/54]	eta 0:00:04 lr 0.000073	time 1.0144 (1.0757)	loss 9.5486 (8.4908)	miou 0.2010	grad_norm 16.6222 (32.0363)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:16:18 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 20 training takes 0:00:57
[32m[2025-07-12 01:16:18 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 01:16:28 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1836%
[32m[2025-07-12 01:16:28 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2168%
[32m[2025-07-12 01:16:30 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [21/200][0/54]	eta 0:01:10 lr 0.000076	time 1.2968 (1.2968)	loss 6.5443 (6.5443)	miou 0.1904	grad_norm 19.1162 (19.1162)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:16:40 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [21/200][10/54]	eta 0:00:45 lr 0.000082	time 1.1613 (1.0353)	loss 6.3127 (8.9441)	miou 0.1998	grad_norm 25.0312 (28.5642)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:16:51 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [21/200][20/54]	eta 0:00:37 lr 0.000088	time 1.3681 (1.0973)	loss 10.3513 (9.5368)	miou 0.1999	grad_norm 14.4264 (29.8230)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:17:02 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [21/200][30/54]	eta 0:00:25 lr 0.000093	time 0.6950 (1.0788)	loss 7.0256 (8.9822)	miou 0.2028	grad_norm 43.0338 (29.5349)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:17:12 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [21/200][40/54]	eta 0:00:15 lr 0.000096	time 1.0993 (1.0759)	loss 8.3529 (8.7339)	miou 0.2051	grad_norm 13.3292 (32.6021)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:17:23 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [21/200][50/54]	eta 0:00:04 lr 0.000099	time 0.7858 (1.0716)	loss 8.3099 (8.6046)	miou 0.2059	grad_norm 30.0859 (32.5810)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:17:26 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 21 training takes 0:00:57
[32m[2025-07-12 01:17:26 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 01:17:36 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1172%
[32m[2025-07-12 01:17:36 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2168%
[32m[2025-07-12 01:17:38 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [22/200][0/54]	eta 0:01:16 lr 0.000099	time 1.4192 (1.4192)	loss 6.3566 (6.3566)	miou 0.1305	grad_norm 33.3902 (33.3902)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:17:48 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [22/200][10/54]	eta 0:00:48 lr 0.000100	time 1.2592 (1.1084)	loss 6.3106 (8.9886)	miou 0.1511	grad_norm 32.7189 (32.2382)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:17:59 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [22/200][20/54]	eta 0:00:36 lr 0.000100	time 0.7312 (1.0866)	loss 8.2962 (8.5638)	miou 0.1693	grad_norm 34.4084 (30.1354)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:18:09 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [22/200][30/54]	eta 0:00:25 lr 0.000098	time 1.0521 (1.0638)	loss 15.7363 (8.4654)	miou 0.1778	grad_norm 20.6296 (31.4102)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:18:20 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [22/200][40/54]	eta 0:00:15 lr 0.000095	time 1.3982 (1.0812)	loss 7.3934 (8.3025)	miou 0.1835	grad_norm 21.9315 (30.2820)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:18:31 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [22/200][50/54]	eta 0:00:04 lr 0.000091	time 0.9878 (1.0783)	loss 12.2608 (8.3378)	miou 0.1839	grad_norm 36.5783 (31.1160)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:18:35 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 22 training takes 0:00:58
[32m[2025-07-12 01:18:35 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 01:18:45 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2162%
[32m[2025-07-12 01:18:45 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2168%
[32m[2025-07-12 01:18:47 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [23/200][0/54]	eta 0:01:34 lr 0.000089	time 1.7489 (1.7489)	loss 8.0906 (8.0906)	miou 0.2109	grad_norm 56.9491 (56.9491)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:18:56 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [23/200][10/54]	eta 0:00:46 lr 0.000084	time 0.9610 (1.0571)	loss 9.2554 (8.0902)	miou 0.2131	grad_norm 25.0066 (25.4833)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:19:07 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [23/200][20/54]	eta 0:00:36 lr 0.000077	time 0.8561 (1.0665)	loss 8.9627 (7.7317)	miou 0.2154	grad_norm 26.1247 (25.9435)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:19:18 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [23/200][30/54]	eta 0:00:25 lr 0.000071	time 1.2081 (1.0679)	loss 10.5736 (7.7564)	miou 0.2079	grad_norm 23.3806 (26.2129)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:19:28 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [23/200][40/54]	eta 0:00:14 lr 0.000063	time 1.2407 (1.0642)	loss 8.6773 (8.0387)	miou 0.2116	grad_norm 20.1626 (26.4540)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:19:39 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [23/200][50/54]	eta 0:00:04 lr 0.000055	time 1.2186 (1.0714)	loss 8.0104 (7.9679)	miou 0.2114	grad_norm 27.6692 (26.3094)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:19:43 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 23 training takes 0:00:58
[32m[2025-07-12 01:19:43 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 01:19:53 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1455%
[32m[2025-07-12 01:19:53 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2168%
[32m[2025-07-12 01:19:55 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [24/200][0/54]	eta 0:01:26 lr 0.000052	time 1.5982 (1.5982)	loss 7.2548 (7.2548)	miou 0.1630	grad_norm 23.2228 (23.2228)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:20:05 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [24/200][10/54]	eta 0:00:46 lr 0.000045	time 1.3753 (1.0609)	loss 6.8594 (7.7827)	miou 0.1973	grad_norm 21.5983 (24.9287)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:20:17 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [24/200][20/54]	eta 0:00:37 lr 0.000037	time 1.4159 (1.1144)	loss 10.6400 (8.2826)	miou 0.2012	grad_norm 30.2256 (26.5023)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:20:26 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [24/200][30/54]	eta 0:00:25 lr 0.000029	time 0.8885 (1.0568)	loss 16.6781 (8.5088)	miou 0.2059	grad_norm 57.4464 (26.4859)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:20:37 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [24/200][40/54]	eta 0:00:14 lr 0.000023	time 0.8935 (1.0621)	loss 7.8310 (8.3315)	miou 0.2083	grad_norm 33.8654 (26.3813)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:20:49 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [24/200][50/54]	eta 0:00:04 lr 0.000016	time 1.2126 (1.0862)	loss 8.0286 (8.2901)	miou 0.2049	grad_norm 46.2093 (26.6392)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:20:52 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 24 training takes 0:00:58
[32m[2025-07-12 01:20:52 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 01:21:02 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1469%
[32m[2025-07-12 01:21:02 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2168%
[32m[2025-07-12 01:21:03 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [25/200][0/54]	eta 0:01:18 lr 0.000014	time 1.4497 (1.4497)	loss 5.9060 (5.9060)	miou 0.1459	grad_norm 17.9864 (17.9864)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:21:14 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [25/200][10/54]	eta 0:00:48 lr 0.000009	time 1.0894 (1.1048)	loss 9.1797 (8.5207)	miou 0.1618	grad_norm 28.4506 (24.0215)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:21:24 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [25/200][20/54]	eta 0:00:35 lr 0.000005	time 1.0754 (1.0575)	loss 14.3614 (8.8209)	miou 0.1738	grad_norm 47.0759 (24.0940)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:21:35 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [25/200][30/54]	eta 0:00:25 lr 0.000002	time 0.9021 (1.0575)	loss 10.0480 (8.5556)	miou 0.1859	grad_norm 53.2768 (24.3705)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:21:46 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [25/200][40/54]	eta 0:00:15 lr 0.000000	time 0.9212 (1.0763)	loss 11.6018 (8.6124)	miou 0.2008	grad_norm 26.9701 (24.2944)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:21:58 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [25/200][50/54]	eta 0:00:04 lr 0.000000	time 1.2300 (1.1077)	loss 5.6304 (8.3133)	miou 0.2094	grad_norm 64.6161 (25.1803)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:22:02 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 25 training takes 0:00:59
[32m[2025-07-12 01:22:02 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 01:22:12 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1740%
[32m[2025-07-12 01:22:12 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2168%
[32m[2025-07-12 01:22:13 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [26/200][0/54]	eta 0:01:21 lr 0.000000	time 1.5031 (1.5031)	loss 6.4557 (6.4557)	miou 0.1743	grad_norm 25.0849 (25.0849)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:22:24 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [26/200][10/54]	eta 0:00:47 lr 0.000001	time 0.8780 (1.0832)	loss 5.5592 (7.5844)	miou 0.2002	grad_norm 27.7091 (25.7575)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:22:34 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [26/200][20/54]	eta 0:00:36 lr 0.000004	time 0.9825 (1.0717)	loss 6.1863 (7.5640)	miou 0.2271	grad_norm 47.3645 (27.4908)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:22:46 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [26/200][30/54]	eta 0:00:26 lr 0.000007	time 1.0672 (1.0950)	loss 10.6148 (7.5794)	miou 0.2265	grad_norm 17.7674 (24.3976)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:22:56 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [26/200][40/54]	eta 0:00:15 lr 0.000012	time 0.8284 (1.0725)	loss 10.1255 (7.9186)	miou 0.2242	grad_norm 21.4623 (24.5018)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:23:07 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [26/200][50/54]	eta 0:00:04 lr 0.000018	time 1.0662 (1.0915)	loss 5.7932 (7.8972)	miou 0.2276	grad_norm 13.0773 (25.1126)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:23:10 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 26 training takes 0:00:58
[32m[2025-07-12 01:23:10 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 01:23:20 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1709%
[32m[2025-07-12 01:23:20 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2168%
[32m[2025-07-12 01:23:22 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [27/200][0/54]	eta 0:01:34 lr 0.000020	time 1.7588 (1.7588)	loss 5.5308 (5.5308)	miou 0.1716	grad_norm 21.5274 (21.5274)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:23:33 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [27/200][10/54]	eta 0:00:50 lr 0.000027	time 1.0867 (1.1495)	loss 6.9740 (7.7758)	miou 0.2134	grad_norm 18.0112 (23.8108)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:23:44 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [27/200][20/54]	eta 0:00:37 lr 0.000034	time 1.2402 (1.1016)	loss 9.6166 (7.8355)	miou 0.2205	grad_norm 19.3819 (22.5104)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:23:55 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [27/200][30/54]	eta 0:00:26 lr 0.000041	time 1.1277 (1.1197)	loss 6.4325 (7.9121)	miou 0.2329	grad_norm 12.8073 (22.1764)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:24:05 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [27/200][40/54]	eta 0:00:15 lr 0.000049	time 0.9038 (1.0966)	loss 4.9148 (7.8888)	miou 0.2331	grad_norm 58.3886 (24.0883)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:24:16 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [27/200][50/54]	eta 0:00:04 lr 0.000057	time 0.9600 (1.0813)	loss 7.2559 (7.8637)	miou 0.2315	grad_norm 27.6537 (24.3183)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:24:19 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 27 training takes 0:00:58
[32m[2025-07-12 01:24:19 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 01:24:29 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1440%
[32m[2025-07-12 01:24:29 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2168%
[32m[2025-07-12 01:24:31 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [28/200][0/54]	eta 0:01:32 lr 0.000060	time 1.7172 (1.7172)	loss 6.3115 (6.3115)	miou 0.1486	grad_norm 16.6137 (16.6137)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:24:41 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [28/200][10/54]	eta 0:00:47 lr 0.000068	time 0.9841 (1.0753)	loss 7.3362 (8.3940)	miou 0.1749	grad_norm 37.7203 (28.3628)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:24:51 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [28/200][20/54]	eta 0:00:35 lr 0.000075	time 1.2421 (1.0547)	loss 7.2466 (7.9858)	miou 0.1948	grad_norm 17.0380 (26.1715)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:25:02 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [28/200][30/54]	eta 0:00:25 lr 0.000081	time 0.8930 (1.0753)	loss 6.3609 (7.6791)	miou 0.2067	grad_norm 32.5009 (25.8950)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:25:13 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [28/200][40/54]	eta 0:00:14 lr 0.000087	time 0.9294 (1.0673)	loss 9.0509 (7.8364)	miou 0.2101	grad_norm 23.9871 (25.7655)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:25:24 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [28/200][50/54]	eta 0:00:04 lr 0.000092	time 0.9581 (1.0691)	loss 4.8225 (7.8125)	miou 0.2143	grad_norm 27.4505 (26.1385)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:25:27 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 28 training takes 0:00:58
[32m[2025-07-12 01:25:27 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 01:25:37 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1988%
[32m[2025-07-12 01:25:37 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2168%
[32m[2025-07-12 01:25:39 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [29/200][0/54]	eta 0:01:25 lr 0.000093	time 1.5865 (1.5865)	loss 6.8923 (6.8923)	miou 0.2003	grad_norm 13.6974 (13.6974)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:25:49 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [29/200][10/54]	eta 0:00:45 lr 0.000097	time 0.8939 (1.0363)	loss 7.7023 (8.6175)	miou 0.2027	grad_norm 49.4162 (25.4999)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:26:00 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [29/200][20/54]	eta 0:00:36 lr 0.000099	time 0.9048 (1.0694)	loss 7.4156 (8.2716)	miou 0.2150	grad_norm 15.5075 (22.5334)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:26:10 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [29/200][30/54]	eta 0:00:25 lr 0.000100	time 0.9221 (1.0421)	loss 6.0981 (7.8495)	miou 0.2184	grad_norm 50.5133 (24.8477)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:26:21 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [29/200][40/54]	eta 0:00:14 lr 0.000100	time 0.9469 (1.0544)	loss 6.4495 (7.9372)	miou 0.2266	grad_norm 24.9221 (24.2487)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:26:32 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [29/200][50/54]	eta 0:00:04 lr 0.000098	time 1.1049 (1.0680)	loss 10.4843 (7.9772)	miou 0.2301	grad_norm 34.7180 (24.9360)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:26:35 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 29 training takes 0:00:57
[32m[2025-07-12 01:26:35 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 01:26:45 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1550%
[32m[2025-07-12 01:26:45 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2168%
[32m[2025-07-12 01:26:46 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [30/200][0/54]	eta 0:01:15 lr 0.000097	time 1.3916 (1.3916)	loss 10.7843 (10.7843)	miou 0.1540	grad_norm 34.1863 (34.1863)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:26:56 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [30/200][10/54]	eta 0:00:45 lr 0.000094	time 1.0572 (1.0415)	loss 10.6530 (8.3942)	miou 0.1781	grad_norm 25.5956 (23.9589)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:27:08 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [30/200][20/54]	eta 0:00:36 lr 0.000090	time 1.2349 (1.0763)	loss 6.1177 (7.7718)	miou 0.1907	grad_norm 20.5615 (26.8071)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:27:18 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [30/200][30/54]	eta 0:00:25 lr 0.000085	time 0.9177 (1.0660)	loss 10.1323 (7.5897)	miou 0.2002	grad_norm 41.2301 (27.7573)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:27:29 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [30/200][40/54]	eta 0:00:15 lr 0.000079	time 0.9108 (1.0740)	loss 12.0987 (7.8223)	miou 0.2005	grad_norm 24.3122 (26.4881)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:27:41 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [30/200][50/54]	eta 0:00:04 lr 0.000072	time 1.1651 (1.0976)	loss 9.6416 (7.8640)	miou 0.2020	grad_norm 30.5305 (26.0218)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:27:44 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 30 training takes 0:00:59
[32m[2025-07-12 01:27:44 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 01:27:55 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1992%
[32m[2025-07-12 01:27:55 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2168%
[32m[2025-07-12 01:27:56 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [31/200][0/54]	eta 0:01:16 lr 0.000069	time 1.4196 (1.4196)	loss 6.3540 (6.3540)	miou 0.2061	grad_norm 86.2877 (86.2877)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:28:07 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [31/200][10/54]	eta 0:00:50 lr 0.000062	time 1.3149 (1.1372)	loss 8.0075 (7.9663)	miou 0.2096	grad_norm 16.5947 (28.3062)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:28:18 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [31/200][20/54]	eta 0:00:37 lr 0.000054	time 1.0483 (1.0924)	loss 7.9446 (7.2977)	miou 0.2211	grad_norm 9.5467 (25.3299)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:28:28 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [31/200][30/54]	eta 0:00:25 lr 0.000046	time 0.7947 (1.0616)	loss 8.2429 (7.2545)	miou 0.2348	grad_norm 13.2422 (22.9455)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:28:38 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [31/200][40/54]	eta 0:00:14 lr 0.000038	time 0.8624 (1.0566)	loss 5.8732 (7.4303)	miou 0.2296	grad_norm 13.2615 (22.5773)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:28:49 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [31/200][50/54]	eta 0:00:04 lr 0.000031	time 1.0410 (1.0641)	loss 7.3933 (7.6044)	miou 0.2243	grad_norm 26.3427 (21.4406)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:28:53 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 31 training takes 0:00:58
[32m[2025-07-12 01:28:53 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 01:29:03 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1964%
[32m[2025-07-12 01:29:03 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2168%
[32m[2025-07-12 01:29:05 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [32/200][0/54]	eta 0:01:29 lr 0.000028	time 1.6527 (1.6527)	loss 6.3887 (6.3887)	miou 0.1992	grad_norm 169.0876 (169.0876)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:29:16 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [32/200][10/54]	eta 0:00:52 lr 0.000021	time 1.1351 (1.1852)	loss 6.2879 (7.9071)	miou 0.2217	grad_norm 21.0419 (34.8767)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:29:27 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [32/200][20/54]	eta 0:00:38 lr 0.000015	time 1.2406 (1.1299)	loss 10.7290 (8.1043)	miou 0.2212	grad_norm 15.2645 (28.1792)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:29:37 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [32/200][30/54]	eta 0:00:26 lr 0.000010	time 0.9801 (1.0994)	loss 4.7184 (7.7746)	miou 0.2310	grad_norm 29.7712 (26.1089)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:29:48 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [32/200][40/54]	eta 0:00:15 lr 0.000006	time 1.0198 (1.0966)	loss 6.7441 (7.6613)	miou 0.2335	grad_norm 23.0098 (24.6047)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:29:59 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [32/200][50/54]	eta 0:00:04 lr 0.000003	time 1.2053 (1.1071)	loss 9.2876 (7.5324)	miou 0.2328	grad_norm 23.0992 (24.5887)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:30:03 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 32 training takes 0:00:59
[32m[2025-07-12 01:30:03 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 01:30:13 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1536%
[32m[2025-07-12 01:30:13 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2168%
[32m[2025-07-12 01:30:14 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [33/200][0/54]	eta 0:01:12 lr 0.000002	time 1.3463 (1.3463)	loss 5.8980 (5.8980)	miou 0.1657	grad_norm 22.4769 (22.4769)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:30:24 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [33/200][10/54]	eta 0:00:46 lr 0.000000	time 0.9828 (1.0671)	loss 7.1477 (7.8066)	miou 0.1884	grad_norm 33.3633 (21.9395)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:30:35 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [33/200][20/54]	eta 0:00:36 lr 0.000000	time 0.9275 (1.0836)	loss 8.9271 (8.0845)	miou 0.2104	grad_norm 25.1142 (20.9768)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:30:47 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [33/200][30/54]	eta 0:00:26 lr 0.000001	time 0.9652 (1.0976)	loss 9.8516 (7.7547)	miou 0.2183	grad_norm 12.8835 (22.0694)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:30:59 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [33/200][40/54]	eta 0:00:15 lr 0.000003	time 1.0234 (1.1234)	loss 8.3918 (7.7462)	miou 0.2219	grad_norm 15.0147 (22.4022)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:31:09 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [33/200][50/54]	eta 0:00:04 lr 0.000007	time 1.1115 (1.1009)	loss 5.4447 (7.9315)	miou 0.2224	grad_norm 40.5706 (22.4415)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:31:12 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 33 training takes 0:00:59
[32m[2025-07-12 01:31:12 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 01:31:22 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1496%
[32m[2025-07-12 01:31:22 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2168%
[32m[2025-07-12 01:31:23 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [34/200][0/54]	eta 0:01:22 lr 0.000008	time 1.5235 (1.5235)	loss 9.3840 (9.3840)	miou 0.1533	grad_norm 21.4088 (21.4088)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:31:35 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [34/200][10/54]	eta 0:00:51 lr 0.000013	time 0.9619 (1.1627)	loss 11.5605 (7.7913)	miou 0.1729	grad_norm 25.6330 (22.1418)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:31:45 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [34/200][20/54]	eta 0:00:37 lr 0.000019	time 0.9338 (1.0945)	loss 9.5068 (7.4862)	miou 0.1902	grad_norm 30.9909 (23.9267)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:31:56 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [34/200][30/54]	eta 0:00:26 lr 0.000025	time 0.8212 (1.0887)	loss 6.7740 (7.1819)	miou 0.2088	grad_norm 20.4960 (22.9038)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:32:06 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [34/200][40/54]	eta 0:00:15 lr 0.000032	time 1.1809 (1.0811)	loss 9.9968 (7.5980)	miou 0.2118	grad_norm 14.5781 (23.2386)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:32:17 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [34/200][50/54]	eta 0:00:04 lr 0.000040	time 1.0405 (1.0825)	loss 6.0848 (7.5981)	miou 0.2182	grad_norm 13.3052 (22.4896)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:32:20 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 34 training takes 0:00:58
[32m[2025-07-12 01:32:20 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 01:32:30 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1404%
[32m[2025-07-12 01:32:30 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2168%
[32m[2025-07-12 01:32:32 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [35/200][0/54]	eta 0:01:28 lr 0.000043	time 1.6432 (1.6432)	loss 7.2931 (7.2931)	miou 0.1695	grad_norm 18.3407 (18.3407)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:32:42 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [35/200][10/54]	eta 0:00:46 lr 0.000051	time 0.9239 (1.0459)	loss 7.8637 (7.8619)	miou 0.1904	grad_norm 20.2010 (22.7789)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:32:53 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [35/200][20/54]	eta 0:00:36 lr 0.000059	time 1.0939 (1.0873)	loss 8.8208 (7.4985)	miou 0.2133	grad_norm 12.8379 (23.9663)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:33:03 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [35/200][30/54]	eta 0:00:25 lr 0.000066	time 0.8009 (1.0649)	loss 9.1763 (7.5077)	miou 0.2181	grad_norm 34.1171 (23.0436)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:33:14 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [35/200][40/54]	eta 0:00:14 lr 0.000073	time 0.7692 (1.0605)	loss 7.4904 (7.6156)	miou 0.2260	grad_norm 12.2398 (22.0671)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:33:25 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [35/200][50/54]	eta 0:00:04 lr 0.000080	time 1.0630 (1.0792)	loss 8.8162 (7.6153)	miou 0.2221	grad_norm 17.6334 (22.6241)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:33:29 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 35 training takes 0:00:58
[32m[2025-07-12 01:33:29 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 01:33:39 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1328%
[32m[2025-07-12 01:33:39 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2168%
[32m[2025-07-12 01:33:40 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [36/200][0/54]	eta 0:01:20 lr 0.000082	time 1.4935 (1.4935)	loss 10.4640 (10.4640)	miou 0.1330	grad_norm 19.4485 (19.4485)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 01:33:50 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [36/200][10/54]	eta 0:00:45 lr 0.000088	time 0.9934 (1.0317)	loss 8.2377 (7.8929)	miou 0.1611	grad_norm 13.5737 (19.3603)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
