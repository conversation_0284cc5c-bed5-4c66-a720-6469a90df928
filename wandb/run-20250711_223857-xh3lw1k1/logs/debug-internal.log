2025-07-11 22:38:57,610 INFO    StreamThr :2842104 [internal.py:wandb_internal():89] W&B internal server running at pid: 2842104, started at: 2025-07-11 22:38:57.609672
2025-07-11 22:38:57,613 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status
2025-07-11 22:38:57,617 INFO    WriterThread:2842104 [datastore.py:open_for_write():85] open: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/run-xh3lw1k1.wandb
2025-07-11 22:38:57,618 DEBUG   SenderThread:2842104 [sender.py:send():369] send: header
2025-07-11 22:38:57,635 DEBUG   SenderThread:2842104 [sender.py:send():369] send: run
2025-07-11 22:38:57,889 INFO    SenderThread:2842104 [dir_watcher.py:__init__():211] watching files in: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files
2025-07-11 22:38:57,890 INFO    SenderThread:2842104 [sender.py:_start_run_threads():1103] run started: xh3lw1k1 with start time 1752287937.607756
2025-07-11 22:38:57,892 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:38:57,893 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:38:57,903 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: check_version
2025-07-11 22:38:57,903 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: check_version
2025-07-11 22:38:57,974 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: run_start
2025-07-11 22:38:57,978 DEBUG   HandlerThread:2842104 [system_info.py:__init__():31] System info init
2025-07-11 22:38:57,978 DEBUG   HandlerThread:2842104 [system_info.py:__init__():46] System info init done
2025-07-11 22:38:57,978 INFO    HandlerThread:2842104 [system_monitor.py:start():181] Starting system monitor
2025-07-11 22:38:57,978 INFO    SystemMonitor:2842104 [system_monitor.py:_start():145] Starting system asset monitoring threads
2025-07-11 22:38:57,979 INFO    HandlerThread:2842104 [system_monitor.py:probe():201] Collecting system info
2025-07-11 22:38:57,980 INFO    SystemMonitor:2842104 [interfaces.py:start():190] Started cpu monitoring
2025-07-11 22:38:57,980 INFO    SystemMonitor:2842104 [interfaces.py:start():190] Started disk monitoring
2025-07-11 22:38:57,981 INFO    SystemMonitor:2842104 [interfaces.py:start():190] Started gpu monitoring
2025-07-11 22:38:57,982 INFO    SystemMonitor:2842104 [interfaces.py:start():190] Started memory monitoring
2025-07-11 22:38:57,983 INFO    SystemMonitor:2842104 [interfaces.py:start():190] Started network monitoring
2025-07-11 22:38:58,000 DEBUG   HandlerThread:2842104 [system_info.py:probe():195] Probing system
2025-07-11 22:38:58,007 DEBUG   HandlerThread:2842104 [system_info.py:_probe_git():180] Probing git
2025-07-11 22:38:58,020 DEBUG   HandlerThread:2842104 [system_info.py:_probe_git():188] Probing git done
2025-07-11 22:38:58,021 DEBUG   HandlerThread:2842104 [system_info.py:probe():240] Probing system done
2025-07-11 22:38:58,021 DEBUG   HandlerThread:2842104 [system_monitor.py:probe():210] {'os': 'Linux-5.4.0-77-generic-x86_64-with-debian-bullseye-sid', 'python': '3.7.16', 'heartbeatAt': '2025-07-12T02:38:58.000469', 'startedAt': '2025-07-12T02:38:57.590163', 'docker': None, 'cuda': None, 'args': ('--local_rank=2', '--cfg', 'config/base_train.yaml', '--output', '/home-local2/akath.extra.nobkp/sereact', '--data-path', '/home-local2/akath.extra.nobkp/dl_challenge', '--batch-size', '2'), 'state': 'running', 'program': 'main.py', 'codePath': 'main.py', 'git': {'remote': 'https://github.com/Shrinidhibhat87/codingchallenge_sereact.git', 'commit': 'a3ff60e13dfced588b500c8a1de00f36fdf22d49'}, 'email': '<EMAIL>', 'root': '/home-local/akath.nobkp/codingchallenge_sereact', 'host': 'lv3-32190', 'username': 'akath', 'executable': '/gel/usr/akath/.conda/envs/swin/bin/python', 'cpu_count': 6, 'cpu_count_logical': 12, 'cpu_freq': {'current': 2.06475, 'min': 1200.0, 'max': 4000.0}, 'cpu_freq_per_core': [{'current': 1.615, 'min': 1200.0, 'max': 4000.0}, {'current': 1.199, 'min': 1200.0, 'max': 4000.0}, {'current': 1.199, 'min': 1200.0, 'max': 4000.0}, {'current': 2.226, 'min': 1200.0, 'max': 4000.0}, {'current': 2.241, 'min': 1200.0, 'max': 4000.0}, {'current': 2.933, 'min': 1200.0, 'max': 4000.0}, {'current': 2.042, 'min': 1200.0, 'max': 4000.0}, {'current': 1.199, 'min': 1200.0, 'max': 4000.0}, {'current': 1.199, 'min': 1200.0, 'max': 4000.0}, {'current': 3.044, 'min': 1200.0, 'max': 4000.0}, {'current': 3.271, 'min': 1200.0, 'max': 4000.0}, {'current': 2.609, 'min': 1200.0, 'max': 4000.0}], 'disk': {'total': 111.2200813293457, 'used': 53.244991302490234}, 'gpu': 'TITAN Xp', 'gpu_count': 3, 'gpu_devices': [{'name': 'TITAN Xp', 'memory_total': 12787122176}, {'name': 'TITAN Xp', 'memory_total': 12788498432}, {'name': 'TITAN Xp', 'memory_total': 12788498432}], 'memory': {'total': 62.72977066040039}}
2025-07-11 22:38:58,021 INFO    HandlerThread:2842104 [system_monitor.py:probe():211] Finished collecting system info
2025-07-11 22:38:58,021 INFO    HandlerThread:2842104 [system_monitor.py:probe():214] Publishing system info
2025-07-11 22:38:58,021 DEBUG   HandlerThread:2842104 [system_info.py:_save_pip():52] Saving list of pip packages installed into the current environment
2025-07-11 22:38:58,022 DEBUG   HandlerThread:2842104 [system_info.py:_save_pip():67] Saving pip packages done
2025-07-11 22:38:58,022 DEBUG   HandlerThread:2842104 [system_info.py:_save_conda():75] Saving list of conda packages installed into the current environment
2025-07-11 22:38:58,893 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:38:58,894 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/conda-environment.yaml
2025-07-11 22:38:58,894 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/requirements.txt
2025-07-11 22:39:00,503 DEBUG   HandlerThread:2842104 [system_info.py:_save_conda():86] Saving conda packages done
2025-07-11 22:39:00,504 INFO    HandlerThread:2842104 [system_monitor.py:probe():216] Finished publishing system info
2025-07-11 22:39:00,510 DEBUG   SenderThread:2842104 [sender.py:send():369] send: files
2025-07-11 22:39:00,511 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-metadata.json with policy now
2025-07-11 22:39:00,521 DEBUG   SenderThread:2842104 [sender.py:send():369] send: telemetry
2025-07-11 22:39:00,529 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:39:00,531 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:39:00,827 INFO    wandb-upload_0:2842104 [upload_job.py:push():133] Uploaded file /tmp/tmpjjbtdweywandb/r1u9rslz-wandb-metadata.json
2025-07-11 22:39:00,890 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/conda-environment.yaml
2025-07-11 22:39:00,891 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-metadata.json
2025-07-11 22:39:00,891 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/output.log
2025-07-11 22:39:02,667 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:39:02,891 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/output.log
2025-07-11 22:39:04,892 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/output.log
2025-07-11 22:39:07,691 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:39:08,824 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:39:08,826 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:39:08,827 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:39:08,831 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:39:08,893 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:39:11,608 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:39:11,609 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:39:11,609 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:39:11,609 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:39:11,894 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:39:13,612 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:39:14,453 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:39:14,455 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:39:14,455 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:39:14,455 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:39:14,895 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:39:15,520 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:39:15,521 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:39:17,119 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:39:17,120 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:39:17,120 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:39:17,121 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:39:17,896 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:39:19,123 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:39:19,587 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:39:19,588 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:39:19,588 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:39:19,589 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:39:19,897 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:39:22,571 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:39:22,572 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:39:22,572 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:39:22,572 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:39:22,898 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:39:24,575 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:39:25,007 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:39:25,008 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:39:25,008 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:39:25,008 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:39:25,899 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:39:27,614 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:39:27,615 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:39:27,615 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:39:27,615 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:39:27,902 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:39:29,624 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:39:29,903 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/config.yaml
2025-07-11 22:39:30,256 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:39:30,257 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:39:30,257 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:39:30,258 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:39:30,520 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:39:30,520 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:39:30,903 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:39:33,196 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:39:33,197 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:39:33,198 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:39:33,198 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:39:33,904 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:39:35,200 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:39:36,091 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:39:36,092 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:39:36,092 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:39:36,093 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:39:36,906 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:39:38,813 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:39:38,815 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:39:38,815 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:39:38,815 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:39:38,906 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:39:40,410 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:39:40,411 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:39:40,411 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:39:40,412 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:39:40,413 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:39:40,907 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:39:43,005 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:39:43,006 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:39:43,006 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:39:43,006 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:39:43,908 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:39:45,520 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:39:45,521 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:39:45,679 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:39:45,896 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:39:45,897 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:39:45,898 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:39:45,898 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:39:45,909 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:39:48,295 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:39:48,297 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:39:48,297 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:39:48,297 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:39:48,909 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:39:50,862 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:39:50,863 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:39:50,863 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:39:50,863 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:39:50,864 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:39:50,910 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:39:53,224 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:39:53,225 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:39:53,225 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:39:53,225 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:39:53,911 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:39:55,830 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:39:55,831 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:39:55,831 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:39:55,831 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:39:55,912 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:39:56,832 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:39:57,850 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:39:57,851 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:39:57,851 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:39:57,852 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:39:57,912 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:39:57,984 DEBUG   SystemMonitor:2842104 [system_monitor.py:_start():159] Starting system metrics aggregation loop
2025-07-11 22:39:57,986 DEBUG   SenderThread:2842104 [sender.py:send():369] send: stats
2025-07-11 22:40:00,325 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:00,326 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:40:00,326 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:00,327 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:00,520 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:40:00,520 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:40:00,913 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:40:02,671 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:40:03,091 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:03,092 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:40:03,092 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:03,092 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:03,914 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:40:05,659 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:05,660 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:40:05,660 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:05,662 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:05,915 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:40:07,438 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:07,439 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:40:07,439 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:07,440 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:07,915 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:40:08,440 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:40:09,797 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:09,798 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:40:09,798 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:09,798 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:09,916 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:40:11,194 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:11,195 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:40:11,195 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:11,195 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:11,916 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:40:12,603 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:12,605 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:40:12,605 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:12,606 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:12,917 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:40:13,607 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:40:15,521 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:40:15,522 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:40:18,628 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:40:23,629 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:40:23,777 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:23,778 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:40:23,778 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:23,778 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:23,920 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:40:24,921 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/output.log
2025-07-11 22:40:27,988 DEBUG   SenderThread:2842104 [sender.py:send():369] send: stats
2025-07-11 22:40:28,692 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:28,693 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:40:28,693 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:28,693 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:40:28,694 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:28,922 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:40:30,471 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:30,472 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:40:30,472 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:30,473 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:30,521 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:40:30,521 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:40:30,923 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:40:32,818 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:32,819 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:40:32,820 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:32,822 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:32,923 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:40:33,823 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:40:35,637 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:35,638 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:40:35,639 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:35,639 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:35,924 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:40:37,139 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:37,140 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:40:37,140 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:37,140 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:37,925 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:40:39,141 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:40:39,748 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:39,749 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:40:39,749 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:39,750 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:39,925 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:40:41,478 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:41,478 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:40:41,479 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:41,479 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:41,926 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:40:43,154 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:43,155 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:40:43,155 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:43,158 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:43,926 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:40:44,159 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:40:44,733 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:44,734 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:40:44,734 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:44,734 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:44,927 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:40:45,521 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:40:45,521 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:40:47,815 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:47,816 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:40:47,816 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:47,817 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:47,928 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:40:49,818 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:40:49,886 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:49,887 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:40:49,888 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:49,888 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:49,928 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:40:52,499 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:52,500 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:40:52,500 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:52,501 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:52,929 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:40:55,100 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:55,101 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:40:55,102 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:55,104 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:40:55,105 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:55,930 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:40:56,917 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:56,918 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:40:56,918 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:56,918 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:56,931 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:40:57,989 DEBUG   SenderThread:2842104 [sender.py:send():369] send: stats
2025-07-11 22:40:58,258 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:58,259 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:40:58,259 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:58,260 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:58,931 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:41:00,044 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:00,045 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:41:00,045 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:00,045 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:00,520 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:41:00,521 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:41:00,606 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:41:00,932 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:41:01,134 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:01,136 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:41:01,136 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:01,137 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:01,932 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:41:02,319 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:02,320 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:41:02,321 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:02,321 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:02,932 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:41:04,202 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:04,204 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:41:04,204 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:04,204 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:04,933 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:41:05,488 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:05,489 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:41:05,490 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:05,492 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:05,933 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:41:06,492 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:41:07,490 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:07,491 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:41:07,491 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:07,491 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:07,934 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:41:09,751 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:09,752 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:41:09,752 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:09,753 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:09,934 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:41:11,671 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:11,672 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:41:11,673 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:11,673 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:41:11,673 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:11,935 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:41:13,462 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:13,463 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:41:13,463 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:13,464 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:13,936 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:41:15,520 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:41:15,521 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:41:15,596 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:15,644 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:41:15,645 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:15,647 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:15,936 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:41:17,058 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:17,058 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:41:17,059 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:17,059 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:41:17,060 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:17,937 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:41:19,663 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:19,664 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:41:19,665 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:19,665 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:19,937 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:41:22,666 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:41:27,666 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:41:27,991 DEBUG   SenderThread:2842104 [sender.py:send():369] send: stats
2025-07-11 22:41:29,649 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:29,650 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:41:29,650 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:29,651 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:29,940 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:41:30,521 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:41:30,522 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:41:30,940 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/output.log
2025-07-11 22:41:33,482 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:33,483 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:41:33,483 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:33,483 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:41:33,484 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:33,941 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:41:34,681 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:34,682 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:41:34,683 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:34,683 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:34,942 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:41:36,250 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:36,251 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:41:36,251 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:36,251 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:36,942 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:41:38,264 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:38,264 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:41:38,265 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:38,267 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:38,943 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:41:39,267 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:41:41,069 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:41,071 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:41:41,071 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:41,071 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:41,944 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:41:42,635 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:42,636 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:41:42,637 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:42,638 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:42,944 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:41:44,639 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:41:45,091 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:45,092 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:41:45,092 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:45,093 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:45,521 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:41:45,521 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:41:45,945 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:41:46,676 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:46,677 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:41:46,677 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:46,677 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:46,946 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:41:48,100 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:48,101 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:41:48,101 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:48,102 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:48,946 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:41:49,578 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:49,579 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:41:49,579 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:49,582 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:49,947 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:41:50,583 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:41:51,014 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:51,015 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:41:51,015 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:51,016 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:51,947 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:41:52,809 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:52,810 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:41:52,810 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:52,810 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:52,948 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:41:54,043 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:54,043 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:41:54,044 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:54,044 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:54,948 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:41:55,838 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:55,839 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:41:55,839 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:55,839 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:41:55,840 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:55,949 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:41:57,720 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:57,722 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:41:57,722 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:57,723 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:57,949 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:41:57,992 DEBUG   SenderThread:2842104 [sender.py:send():369] send: stats
2025-07-11 22:41:59,726 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:59,727 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:41:59,727 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:59,730 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:59,950 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:42:00,521 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:42:00,522 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:42:00,900 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:00,901 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:42:00,901 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:00,902 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:42:00,902 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:00,950 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:42:02,102 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:02,103 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:42:02,103 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:02,104 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:02,951 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:42:03,333 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:03,334 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:42:03,334 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:03,335 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:03,951 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:42:05,811 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:05,812 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:42:05,812 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:05,812 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:05,952 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:42:06,813 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:42:07,938 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:07,939 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:42:07,939 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:07,940 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:07,953 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:42:09,566 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:09,567 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:42:09,567 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:09,567 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:09,954 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:42:11,564 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:11,566 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:42:11,566 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:11,568 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:11,954 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:42:12,569 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:42:13,675 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:13,676 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:42:13,677 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:13,677 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:13,955 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:42:15,231 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:15,232 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:42:15,233 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:15,233 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:15,521 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:42:15,522 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:42:15,956 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:42:16,716 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:16,716 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:42:16,716 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:16,717 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:16,956 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:42:17,717 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:42:18,554 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:18,555 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:42:18,555 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:18,556 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:18,957 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:42:23,557 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:42:27,994 DEBUG   SenderThread:2842104 [sender.py:send():369] send: stats
2025-07-11 22:42:28,112 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:28,113 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:42:28,113 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:28,114 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:28,960 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:42:29,114 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:42:30,521 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:42:30,522 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:42:31,745 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:31,746 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:42:31,746 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:31,746 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:31,961 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:42:33,121 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:33,122 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:42:33,122 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:33,123 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:33,962 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:42:34,124 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:42:34,798 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:34,799 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:42:34,800 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:34,800 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:34,963 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:42:36,736 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:36,737 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:42:36,737 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:36,737 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:36,963 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:42:38,331 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:38,331 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:42:38,332 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:38,334 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:38,964 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:42:39,334 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:42:39,684 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:39,685 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:42:39,685 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:39,685 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:39,964 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:42:41,643 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:41,645 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:42:41,645 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:41,645 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:41,965 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:42:43,480 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:43,481 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:42:43,481 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:43,482 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:43,965 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:42:44,482 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:42:45,061 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:45,061 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:42:45,062 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:45,062 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:45,521 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:42:45,522 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:42:45,966 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:42:47,928 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:47,930 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:42:47,930 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:47,931 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:47,967 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:42:49,340 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:49,341 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:42:49,341 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:49,346 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:49,967 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:42:50,346 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:42:51,167 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:51,168 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:42:51,168 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:51,169 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:51,968 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:42:52,746 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:52,747 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:42:52,747 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:52,748 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:52,968 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:42:54,253 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:54,254 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:42:54,254 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:54,255 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:54,969 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:42:55,687 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:55,688 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:42:55,689 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:55,689 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:42:55,689 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:55,969 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:42:56,915 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:56,916 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:42:56,917 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:56,917 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:56,969 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:42:57,995 DEBUG   SenderThread:2842104 [sender.py:send():369] send: stats
2025-07-11 22:42:58,893 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:58,894 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:42:58,895 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:58,895 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:58,970 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:43:00,522 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:43:00,523 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:43:01,388 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:01,389 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:43:01,389 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:01,390 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:43:01,391 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:01,971 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:43:02,857 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:02,858 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:43:02,859 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:02,859 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:02,971 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:43:04,794 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:04,795 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:43:04,795 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:04,796 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:04,972 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:43:06,322 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:06,324 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:43:06,324 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:06,324 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:06,972 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:43:07,324 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:43:07,933 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:07,934 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:43:07,934 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:07,934 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:07,973 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:43:09,320 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:09,321 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:43:09,321 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:09,321 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:09,973 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:43:10,835 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:10,836 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:43:10,836 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:10,836 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:10,973 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:43:12,132 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:12,147 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:43:12,148 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:12,150 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:12,974 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:43:13,150 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:43:13,584 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:13,586 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:43:13,586 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:13,586 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:13,974 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:43:15,522 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:43:15,522 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:43:15,663 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:15,693 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:43:15,693 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:15,694 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:15,975 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:43:18,695 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:43:23,695 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:43:25,256 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:25,257 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:43:25,257 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:25,259 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:25,978 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:43:27,996 DEBUG   SenderThread:2842104 [sender.py:send():369] send: stats
2025-07-11 22:43:28,409 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:28,410 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:43:28,410 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:28,411 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:28,979 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:43:29,411 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:43:30,213 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:30,215 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:43:30,215 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:30,215 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:30,522 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:43:30,522 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:43:30,979 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:43:31,676 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:31,677 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:43:31,677 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:31,677 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:31,980 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:43:33,284 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:33,285 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:43:33,285 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:33,286 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:33,980 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:43:35,287 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:43:35,424 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:35,425 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:43:35,425 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:35,428 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:35,981 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:43:37,339 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:37,340 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:43:37,340 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:37,341 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:37,981 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:43:39,039 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:39,040 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:43:39,040 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:39,040 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:39,982 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:43:40,683 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:40,684 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:43:40,684 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:40,684 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:43:40,685 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:40,982 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:43:42,226 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:42,227 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:43:42,228 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:42,228 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:42,982 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:43:43,811 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:43,812 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:43:43,812 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:43,812 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:43,983 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:43:45,522 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:43:45,522 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:43:45,582 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:45,678 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:43:45,679 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:45,681 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:45,983 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:43:46,682 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:43:47,241 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:47,243 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:43:47,243 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:47,243 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:47,984 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:43:48,736 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:48,738 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:43:48,738 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:48,739 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:48,984 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:43:50,387 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:50,388 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:43:50,389 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:50,389 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:50,985 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:43:52,324 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:52,325 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:43:52,325 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:52,326 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:43:52,326 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:52,986 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:43:54,228 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:54,229 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:43:54,230 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:54,230 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:54,986 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:43:55,713 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:55,714 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:43:55,714 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:55,717 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:55,987 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:43:57,287 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:57,287 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:43:57,288 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:57,288 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:57,987 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:43:57,997 DEBUG   SenderThread:2842104 [sender.py:send():369] send: stats
2025-07-11 22:43:57,998 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:43:58,971 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:58,972 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:43:58,972 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:58,973 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:58,988 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:44:00,522 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:44:00,523 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:44:00,832 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:00,833 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:44:00,833 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:00,833 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:00,988 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:44:01,973 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:01,974 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:44:01,974 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:01,975 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:01,989 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:44:03,667 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:03,668 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:44:03,668 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:03,668 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:44:03,669 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:03,989 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:44:05,418 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:05,419 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:44:05,419 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:05,420 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:05,990 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:44:06,939 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:06,940 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:44:06,940 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:06,941 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:06,990 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:44:08,941 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:44:09,036 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:09,036 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:44:09,037 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:09,037 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:09,991 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:44:10,641 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:10,642 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:44:10,642 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:10,643 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:10,992 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:44:13,199 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:13,200 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:44:13,200 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:13,200 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:13,993 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:44:14,201 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:44:15,523 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:44:15,524 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:44:19,649 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:44:22,798 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:22,799 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:44:22,799 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:22,800 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:22,996 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:44:24,800 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:44:25,767 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:25,768 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:44:25,768 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:25,770 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:25,997 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:44:27,338 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:27,339 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:44:27,339 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:27,340 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:27,999 DEBUG   SenderThread:2842104 [sender.py:send():369] send: stats
2025-07-11 22:44:28,000 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:44:29,214 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:29,215 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:44:29,216 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:29,216 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:30,000 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:44:30,217 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:44:30,523 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:44:30,523 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:44:30,933 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:30,934 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:44:30,934 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:30,935 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:31,001 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:44:32,340 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:32,341 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:44:32,341 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:32,342 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:33,001 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:44:33,889 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:33,890 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:44:33,891 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:33,891 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:34,002 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:44:35,870 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:35,871 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:44:35,871 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:35,871 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:44:35,872 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:36,002 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:44:37,116 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:37,117 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:44:37,117 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:37,117 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:38,003 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:44:38,721 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:38,722 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:44:38,723 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:38,723 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:39,003 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:44:40,227 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:40,228 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:44:40,228 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:40,228 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:41,004 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:44:41,229 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:44:41,622 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:41,623 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:44:41,623 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:41,623 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:42,004 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:44:43,381 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:43,382 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:44:43,383 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:43,384 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:44,004 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:44:44,855 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:44,856 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:44:44,856 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:44,857 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:45,005 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:44:45,522 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:44:45,523 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:44:46,596 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:44:46,711 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:46,712 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:44:46,712 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:46,713 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:47,005 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:44:47,894 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:47,895 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:44:47,896 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:47,896 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:48,006 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:44:49,489 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:49,490 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:44:49,491 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:49,492 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:50,006 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:44:50,994 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:50,995 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:44:50,996 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:50,996 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:51,007 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:44:51,997 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:44:52,559 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:52,560 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:44:52,560 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:52,561 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:53,007 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:44:54,226 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:54,227 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:44:54,227 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:54,228 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:55,008 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:44:56,247 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:56,248 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:44:56,248 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:56,250 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:57,009 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:44:57,251 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:44:57,793 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:57,794 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:44:57,794 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:57,795 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:58,000 DEBUG   SenderThread:2842104 [sender.py:send():369] send: stats
2025-07-11 22:44:58,009 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:44:59,239 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:59,240 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:44:59,241 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:59,241 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:00,010 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:45:00,522 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:45:00,523 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:45:00,900 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:00,901 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:45:00,901 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:00,902 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:01,010 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:45:02,298 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:02,299 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:45:02,299 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:02,300 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:45:02,300 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:03,011 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:45:03,609 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:03,611 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:45:03,611 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:03,611 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:04,011 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:45:05,759 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:05,760 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:45:05,760 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:05,760 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:06,012 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:45:07,227 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:07,228 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:45:07,229 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:07,229 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:08,013 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:45:08,230 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:45:13,230 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:45:15,523 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:45:15,523 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:45:16,853 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:16,854 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:45:16,854 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:16,855 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:17,016 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:45:18,855 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:45:19,694 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:19,695 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:45:19,695 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:19,695 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:20,017 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:45:21,265 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:21,267 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:45:21,267 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:21,268 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:22,017 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:45:23,196 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:23,197 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:45:23,197 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:23,198 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:24,018 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:45:24,199 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:45:24,805 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:24,805 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:45:24,806 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:24,806 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:25,018 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:45:26,452 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:26,453 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:45:26,453 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:26,456 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:27,019 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:45:27,631 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:27,632 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:45:27,632 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:27,632 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:28,002 DEBUG   SenderThread:2842104 [sender.py:send():369] send: stats
2025-07-11 22:45:28,019 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:45:29,472 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:29,473 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:45:29,473 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:29,474 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:45:29,474 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:30,020 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:45:30,523 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:45:30,523 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:45:30,934 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:30,936 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:45:30,936 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:30,936 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:31,020 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:45:32,223 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:32,224 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:45:32,224 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:32,225 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:33,021 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:45:33,891 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:33,892 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:45:33,893 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:33,894 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:34,021 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:45:34,895 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:45:35,552 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:35,553 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:45:35,553 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:35,553 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:36,022 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:45:37,192 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:37,193 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:45:37,193 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:37,194 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:38,023 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:45:38,797 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:38,798 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:45:38,798 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:38,799 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:39,023 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:45:40,785 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:40,786 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:45:40,786 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:40,786 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:45:40,787 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:41,024 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:45:42,014 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:42,015 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:45:42,015 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:42,015 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:42,024 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:45:43,380 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:43,382 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:45:43,382 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:43,383 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:44,025 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:45:45,095 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:45,096 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:45:45,096 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:45,097 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:45,523 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:45:45,523 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:45:46,025 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:45:46,620 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:46,620 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:45:46,621 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:46,621 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:45:46,622 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:47,026 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:45:48,186 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:48,187 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:45:48,187 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:48,188 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:49,026 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:45:49,717 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:49,718 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:45:49,718 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:49,718 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:50,027 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:45:51,719 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:45:51,771 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:51,772 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:45:51,772 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:51,772 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:52,027 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:45:53,264 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:53,265 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:45:53,265 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:53,266 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:54,028 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:45:54,711 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:54,712 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:45:54,712 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:54,713 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:55,028 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:45:55,886 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:55,887 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:45:55,887 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:55,887 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:56,028 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:45:56,888 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:45:57,647 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:57,648 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:45:57,648 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:57,651 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:58,003 DEBUG   SenderThread:2842104 [sender.py:send():369] send: stats
2025-07-11 22:45:58,029 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:45:59,142 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:59,143 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:45:59,143 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:59,144 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:00,029 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:46:00,523 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:46:00,524 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:46:00,783 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:00,783 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:46:00,784 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:00,784 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:01,030 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:46:02,785 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:46:07,785 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:46:10,351 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:10,353 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:46:10,353 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:10,353 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:11,033 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:46:11,033 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/output.log
2025-07-11 22:46:12,718 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:12,720 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:46:12,720 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:12,720 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:13,034 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:46:13,721 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:46:14,557 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:14,559 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:46:14,559 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:14,560 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:15,034 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:46:15,523 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:46:15,524 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:46:16,602 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:16,603 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:46:16,603 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:16,604 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:17,035 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:46:18,228 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:18,229 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:46:18,229 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:18,230 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:19,036 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:46:19,231 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:46:19,560 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:19,560 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:46:19,560 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:19,561 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:20,036 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:46:21,003 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:21,005 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:46:21,005 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:21,005 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:21,036 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:46:22,812 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:22,813 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:46:22,814 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:22,814 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:23,037 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:46:24,181 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:24,182 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:46:24,182 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:24,183 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:25,037 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:46:25,183 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:46:26,137 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:26,138 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:46:26,138 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:26,139 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:27,038 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:46:27,462 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:27,463 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:46:27,463 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:27,464 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:28,005 DEBUG   SenderThread:2842104 [sender.py:send():369] send: stats
2025-07-11 22:46:28,038 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:46:28,759 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:28,761 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:46:28,761 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:28,762 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:29,039 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:46:30,346 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:30,347 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:46:30,347 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:30,347 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:46:30,347 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:30,523 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:46:30,523 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:46:31,039 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:46:31,926 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:31,927 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:46:31,927 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:31,927 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:32,040 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:46:33,474 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:33,475 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:46:33,475 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:33,476 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:34,040 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:46:35,310 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:35,311 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:46:35,311 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:35,311 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:36,041 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:46:36,312 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:46:36,889 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:36,890 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:46:36,890 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:36,891 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:37,041 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:46:39,011 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:39,012 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:46:39,012 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:39,012 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:39,042 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:46:40,330 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:40,330 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:46:40,330 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:40,331 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:41,043 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:46:41,331 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:46:42,303 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:42,303 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:46:42,304 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:42,304 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:43,044 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:46:44,276 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:44,277 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:46:44,277 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:44,277 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:45,044 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:46:45,523 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:46:45,523 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:46:45,958 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:45,959 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:46:45,959 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:45,960 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:46,045 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:46:46,960 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:46:48,117 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:48,118 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:46:48,118 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:48,118 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:49,046 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:46:49,425 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:49,426 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:46:49,426 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:49,427 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:50,046 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:46:50,973 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:50,974 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:46:50,974 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:50,974 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:51,046 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:46:51,975 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:46:52,842 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:52,843 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:46:52,843 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:52,843 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:53,047 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:46:54,182 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:54,183 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:46:54,183 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:54,183 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:55,048 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:46:55,504 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:55,514 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:46:55,515 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:55,515 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:56,048 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:46:57,516 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:46:58,006 DEBUG   SenderThread:2842104 [sender.py:send():369] send: stats
2025-07-11 22:47:00,524 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:47:00,524 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:47:02,715 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:47:05,088 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:47:05,089 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:47:05,090 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:47:05,090 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:47:06,050 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:47:07,667 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:47:07,668 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:47:07,669 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:47:07,669 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:47:08,051 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:47:08,670 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:47:09,500 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:47:09,501 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:47:09,501 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:47:09,501 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:47:10,052 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:47:11,073 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:47:11,074 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:47:11,074 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:47:11,074 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:47:12,052 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:47:12,916 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:47:12,916 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:47:12,917 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:47:12,917 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:47:13,053 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:47:13,917 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:47:14,979 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:47:14,980 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:47:14,980 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:47:14,981 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:47:15,053 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:47:15,523 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:47:15,524 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:47:17,139 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:47:17,140 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:47:17,140 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:47:17,141 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:47:18,054 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:47:18,587 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:47:18,589 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:47:18,589 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:47:18,590 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:47:19,054 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:47:19,590 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:47:20,166 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:47:20,168 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:47:20,168 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:47:20,169 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:47:21,055 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:47:21,726 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:47:21,727 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:47:21,728 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:47:21,728 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:47:22,055 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:47:23,296 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:47:23,297 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:47:23,297 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:47:23,298 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:47:24,056 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:47:24,991 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:47:24,992 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:47:24,992 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:47:24,992 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:47:24,992 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:47:25,057 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:47:26,484 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:47:26,485 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:47:26,485 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:47:26,486 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:47:27,057 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:47:27,867 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:47:27,868 DEBUG   SenderThread:2842104 [sender.py:send():369] send: history
2025-07-11 22:47:27,868 DEBUG   SenderThread:2842104 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:47:27,869 INFO    SenderThread:2842104 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:47:28,007 DEBUG   SenderThread:2842104 [sender.py:send():369] send: stats
2025-07-11 22:47:28,058 INFO    Thread-12 :2842104 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-xh3lw1k1/files/wandb-summary.json
2025-07-11 22:47:30,008 DEBUG   HandlerThread:2842104 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:47:32,151 INFO    memory    :2842104 [interfaces.py:monitor():140] Process proc.memory.rssMB has exited.
2025-07-11 22:47:32,151 DEBUG   SystemMonitor:2842104 [system_monitor.py:_start():166] Finished system metrics aggregation loop
2025-07-11 22:47:32,152 DEBUG   SystemMonitor:2842104 [system_monitor.py:_start():170] Publishing last batch of metrics
2025-07-11 22:47:32,153 DEBUG   SenderThread:2842104 [sender.py:send():369] send: stats
2025-07-11 22:47:33,053 INFO    MainThread:2842104 [internal.py:handle_exit():76] Internal process exited
