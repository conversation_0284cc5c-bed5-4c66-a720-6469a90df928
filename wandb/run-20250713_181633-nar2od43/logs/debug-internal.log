2025-07-13 18:16:33,388 INFO    StreamThr :2941651 [internal.py:wandb_internal():89] W&B internal server running at pid: 2941651, started at: 2025-07-13 18:16:33.387168
2025-07-13 18:16:33,389 DEBUG   HandlerThread:2941651 [handler.py:handle_request():144] handle_request: status
2025-07-13 18:16:33,395 INFO    WriterThread:2941651 [datastore.py:open_for_write():85] open: /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-nar2od43/run-nar2od43.wandb
2025-07-13 18:16:33,396 DEBUG   SenderThread:2941651 [sender.py:send():369] send: header
2025-07-13 18:16:33,410 DEBUG   SenderThread:2941651 [sender.py:send():369] send: run
2025-07-13 18:16:33,801 INFO    SenderThread:2941651 [dir_watcher.py:__init__():211] watching files in: /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-nar2od43/files
2025-07-13 18:16:33,802 INFO    SenderThread:2941651 [sender.py:_start_run_threads():1103] run started: nar2od43 with start time 1752444993.385889
2025-07-13 18:16:33,805 DEBUG   SenderThread:2941651 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:16:33,805 INFO    SenderThread:2941651 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:16:33,813 DEBUG   HandlerThread:2941651 [handler.py:handle_request():144] handle_request: check_version
2025-07-13 18:16:33,813 DEBUG   SenderThread:2941651 [sender.py:send_request():396] send_request: check_version
2025-07-13 18:16:33,881 DEBUG   HandlerThread:2941651 [handler.py:handle_request():144] handle_request: run_start
2025-07-13 18:16:33,887 DEBUG   HandlerThread:2941651 [system_info.py:__init__():31] System info init
2025-07-13 18:16:33,887 DEBUG   HandlerThread:2941651 [system_info.py:__init__():46] System info init done
2025-07-13 18:16:33,887 INFO    HandlerThread:2941651 [system_monitor.py:start():181] Starting system monitor
2025-07-13 18:16:33,888 INFO    SystemMonitor:2941651 [system_monitor.py:_start():145] Starting system asset monitoring threads
2025-07-13 18:16:33,888 INFO    HandlerThread:2941651 [system_monitor.py:probe():201] Collecting system info
2025-07-13 18:16:33,889 INFO    SystemMonitor:2941651 [interfaces.py:start():190] Started cpu monitoring
2025-07-13 18:16:33,890 INFO    SystemMonitor:2941651 [interfaces.py:start():190] Started disk monitoring
2025-07-13 18:16:33,891 INFO    SystemMonitor:2941651 [interfaces.py:start():190] Started gpu monitoring
2025-07-13 18:16:33,892 INFO    SystemMonitor:2941651 [interfaces.py:start():190] Started memory monitoring
2025-07-13 18:16:33,893 INFO    SystemMonitor:2941651 [interfaces.py:start():190] Started network monitoring
2025-07-13 18:16:33,910 DEBUG   HandlerThread:2941651 [system_info.py:probe():195] Probing system
2025-07-13 18:16:33,919 DEBUG   HandlerThread:2941651 [system_info.py:_probe_git():180] Probing git
2025-07-13 18:16:33,937 DEBUG   HandlerThread:2941651 [system_info.py:_probe_git():188] Probing git done
2025-07-13 18:16:33,938 DEBUG   HandlerThread:2941651 [system_info.py:probe():240] Probing system done
2025-07-13 18:16:33,938 DEBUG   HandlerThread:2941651 [system_monitor.py:probe():210] {'os': 'Linux-5.4.0-77-generic-x86_64-with-debian-bullseye-sid', 'python': '3.7.16', 'heartbeatAt': '2025-07-13T22:16:33.910814', 'startedAt': '2025-07-13T22:16:33.368515', 'docker': None, 'cuda': None, 'args': ('--local_rank=2', '--cfg', 'config/base_train.yaml', '--output', '/home-local2/akath.extra.nobkp/sereact', '--data-path', '/home-local2/akath.extra.nobkp/dl_challenge', '--batch-size', '2'), 'state': 'running', 'program': 'main.py', 'codePath': 'main.py', 'git': {'remote': 'https://github.com/Shrinidhibhat87/codingchallenge_sereact.git', 'commit': '667b7ded061f079ea281aa2193d47b61df08fec6'}, 'email': '<EMAIL>', 'root': '/home-local/akath.nobkp/sereact', 'host': 'lv3-32190', 'username': 'akath', 'executable': '/gel/usr/akath/.conda/envs/swin/bin/python', 'cpu_count': 6, 'cpu_count_logical': 12, 'cpu_freq': {'current': 2.118916666666667, 'min': 1200.0, 'max': 4000.0}, 'cpu_freq_per_core': [{'current': 1.817, 'min': 1200.0, 'max': 4000.0}, {'current': 1.766, 'min': 1200.0, 'max': 4000.0}, {'current': 1.289, 'min': 1200.0, 'max': 4000.0}, {'current': 3.137, 'min': 1200.0, 'max': 4000.0}, {'current': 1.435, 'min': 1200.0, 'max': 4000.0}, {'current': 3.319, 'min': 1200.0, 'max': 4000.0}, {'current': 1.608, 'min': 1200.0, 'max': 4000.0}, {'current': 1.447, 'min': 1200.0, 'max': 4000.0}, {'current': 2.159, 'min': 1200.0, 'max': 4000.0}, {'current': 2.655, 'min': 1200.0, 'max': 4000.0}, {'current': 1.334, 'min': 1200.0, 'max': 4000.0}, {'current': 3.461, 'min': 1200.0, 'max': 4000.0}], 'disk': {'total': 111.2200813293457, 'used': 53.132808685302734}, 'gpu': 'TITAN Xp', 'gpu_count': 3, 'gpu_devices': [{'name': 'TITAN Xp', 'memory_total': 12787122176}, {'name': 'TITAN Xp', 'memory_total': 12788498432}, {'name': 'TITAN Xp', 'memory_total': 12788498432}], 'memory': {'total': 62.72977066040039}}
2025-07-13 18:16:33,938 INFO    HandlerThread:2941651 [system_monitor.py:probe():211] Finished collecting system info
2025-07-13 18:16:33,938 INFO    HandlerThread:2941651 [system_monitor.py:probe():214] Publishing system info
2025-07-13 18:16:33,938 DEBUG   HandlerThread:2941651 [system_info.py:_save_pip():52] Saving list of pip packages installed into the current environment
2025-07-13 18:16:33,939 DEBUG   HandlerThread:2941651 [system_info.py:_save_pip():67] Saving pip packages done
2025-07-13 18:16:33,939 DEBUG   HandlerThread:2941651 [system_info.py:_save_conda():75] Saving list of conda packages installed into the current environment
2025-07-13 18:16:34,804 INFO    Thread-12 :2941651 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-nar2od43/files/requirements.txt
2025-07-13 18:16:34,804 INFO    Thread-12 :2941651 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-nar2od43/files/conda-environment.yaml
2025-07-13 18:16:34,805 INFO    Thread-12 :2941651 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-nar2od43/files/wandb-summary.json
2025-07-13 18:16:40,371 DEBUG   HandlerThread:2941651 [system_info.py:_save_conda():86] Saving conda packages done
2025-07-13 18:16:40,373 INFO    HandlerThread:2941651 [system_monitor.py:probe():216] Finished publishing system info
2025-07-13 18:16:40,378 DEBUG   HandlerThread:2941651 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:16:40,378 DEBUG   HandlerThread:2941651 [handler.py:handle_request():144] handle_request: keepalive
2025-07-13 18:16:40,379 DEBUG   SenderThread:2941651 [sender.py:send():369] send: files
2025-07-13 18:16:40,379 INFO    SenderThread:2941651 [sender.py:_save_file():1354] saving file wandb-metadata.json with policy now
2025-07-13 18:16:40,389 DEBUG   HandlerThread:2941651 [handler.py:handle_request():144] handle_request: stop_status
2025-07-13 18:16:40,390 DEBUG   SenderThread:2941651 [sender.py:send_request():396] send_request: stop_status
2025-07-13 18:16:40,491 DEBUG   SenderThread:2941651 [sender.py:send():369] send: telemetry
2025-07-13 18:16:40,804 INFO    Thread-12 :2941651 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-nar2od43/files/conda-environment.yaml
2025-07-13 18:16:40,804 INFO    Thread-12 :2941651 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-nar2od43/files/output.log
2025-07-13 18:16:40,804 INFO    Thread-12 :2941651 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-nar2od43/files/wandb-metadata.json
2025-07-13 18:16:40,893 INFO    wandb-upload_0:2941651 [upload_job.py:push():133] Uploaded file /tmp/tmp44jld5opwandb/xlv7jiyv-wandb-metadata.json
2025-07-13 18:16:42,805 INFO    Thread-12 :2941651 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-nar2od43/files/output.log
2025-07-13 18:16:44,013 DEBUG   HandlerThread:2941651 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:16:44,014 DEBUG   SenderThread:2941651 [sender.py:send():369] send: exit
2025-07-13 18:16:44,014 INFO    SenderThread:2941651 [sender.py:send_exit():574] handling exit code: 1
2025-07-13 18:16:44,014 INFO    SenderThread:2941651 [sender.py:send_exit():576] handling runtime: 10
2025-07-13 18:16:44,020 INFO    SenderThread:2941651 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:16:44,021 INFO    SenderThread:2941651 [sender.py:send_exit():582] send defer
2025-07-13 18:16:44,021 DEBUG   HandlerThread:2941651 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:16:44,021 INFO    HandlerThread:2941651 [handler.py:handle_request_defer():170] handle defer: 0
2025-07-13 18:16:44,021 DEBUG   SenderThread:2941651 [sender.py:send_request():396] send_request: defer
2025-07-13 18:16:44,021 INFO    SenderThread:2941651 [sender.py:send_request_defer():598] handle sender defer: 0
2025-07-13 18:16:44,021 INFO    SenderThread:2941651 [sender.py:transition_state():602] send defer: 1
2025-07-13 18:16:44,021 DEBUG   HandlerThread:2941651 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:16:44,021 INFO    HandlerThread:2941651 [handler.py:handle_request_defer():170] handle defer: 1
2025-07-13 18:16:44,022 DEBUG   SenderThread:2941651 [sender.py:send_request():396] send_request: defer
2025-07-13 18:16:44,022 INFO    SenderThread:2941651 [sender.py:send_request_defer():598] handle sender defer: 1
2025-07-13 18:16:44,022 INFO    SenderThread:2941651 [sender.py:transition_state():602] send defer: 2
2025-07-13 18:16:44,022 DEBUG   HandlerThread:2941651 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:16:44,022 INFO    HandlerThread:2941651 [handler.py:handle_request_defer():170] handle defer: 2
2025-07-13 18:16:44,022 INFO    HandlerThread:2941651 [system_monitor.py:finish():190] Stopping system monitor
2025-07-13 18:16:44,022 DEBUG   SystemMonitor:2941651 [system_monitor.py:_start():159] Starting system metrics aggregation loop
2025-07-13 18:16:44,023 INFO    HandlerThread:2941651 [interfaces.py:finish():202] Joined cpu monitor
2025-07-13 18:16:44,023 DEBUG   SystemMonitor:2941651 [system_monitor.py:_start():166] Finished system metrics aggregation loop
2025-07-13 18:16:44,023 INFO    HandlerThread:2941651 [interfaces.py:finish():202] Joined disk monitor
2025-07-13 18:16:44,023 DEBUG   SystemMonitor:2941651 [system_monitor.py:_start():170] Publishing last batch of metrics
2025-07-13 18:16:44,188 INFO    HandlerThread:2941651 [interfaces.py:finish():202] Joined gpu monitor
2025-07-13 18:16:44,189 INFO    HandlerThread:2941651 [interfaces.py:finish():202] Joined memory monitor
2025-07-13 18:16:44,189 INFO    HandlerThread:2941651 [interfaces.py:finish():202] Joined network monitor
2025-07-13 18:16:44,190 DEBUG   SenderThread:2941651 [sender.py:send_request():396] send_request: defer
2025-07-13 18:16:44,190 INFO    SenderThread:2941651 [sender.py:send_request_defer():598] handle sender defer: 2
2025-07-13 18:16:44,190 INFO    SenderThread:2941651 [sender.py:transition_state():602] send defer: 3
2025-07-13 18:16:44,190 DEBUG   SenderThread:2941651 [sender.py:send():369] send: stats
2025-07-13 18:16:44,190 DEBUG   HandlerThread:2941651 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:16:44,191 INFO    HandlerThread:2941651 [handler.py:handle_request_defer():170] handle defer: 3
2025-07-13 18:16:44,191 DEBUG   SenderThread:2941651 [sender.py:send_request():396] send_request: defer
2025-07-13 18:16:44,192 INFO    SenderThread:2941651 [sender.py:send_request_defer():598] handle sender defer: 3
2025-07-13 18:16:44,192 INFO    SenderThread:2941651 [sender.py:transition_state():602] send defer: 4
2025-07-13 18:16:44,192 DEBUG   HandlerThread:2941651 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:16:44,192 INFO    HandlerThread:2941651 [handler.py:handle_request_defer():170] handle defer: 4
2025-07-13 18:16:44,192 DEBUG   SenderThread:2941651 [sender.py:send_request():396] send_request: defer
2025-07-13 18:16:44,193 INFO    SenderThread:2941651 [sender.py:send_request_defer():598] handle sender defer: 4
2025-07-13 18:16:44,193 INFO    SenderThread:2941651 [sender.py:transition_state():602] send defer: 5
2025-07-13 18:16:44,193 DEBUG   HandlerThread:2941651 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:16:44,193 INFO    HandlerThread:2941651 [handler.py:handle_request_defer():170] handle defer: 5
2025-07-13 18:16:44,193 DEBUG   SenderThread:2941651 [sender.py:send():369] send: summary
2025-07-13 18:16:44,194 INFO    SenderThread:2941651 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:16:44,194 DEBUG   SenderThread:2941651 [sender.py:send_request():396] send_request: defer
2025-07-13 18:16:44,195 INFO    SenderThread:2941651 [sender.py:send_request_defer():598] handle sender defer: 5
2025-07-13 18:16:44,195 INFO    SenderThread:2941651 [sender.py:transition_state():602] send defer: 6
2025-07-13 18:16:44,195 DEBUG   HandlerThread:2941651 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:16:44,195 INFO    HandlerThread:2941651 [handler.py:handle_request_defer():170] handle defer: 6
2025-07-13 18:16:44,195 DEBUG   SenderThread:2941651 [sender.py:send_request():396] send_request: defer
2025-07-13 18:16:44,195 INFO    SenderThread:2941651 [sender.py:send_request_defer():598] handle sender defer: 6
2025-07-13 18:16:44,201 DEBUG   HandlerThread:2941651 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:16:44,298 INFO    SenderThread:2941651 [sender.py:transition_state():602] send defer: 7
2025-07-13 18:16:44,299 DEBUG   HandlerThread:2941651 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:16:44,299 INFO    HandlerThread:2941651 [handler.py:handle_request_defer():170] handle defer: 7
2025-07-13 18:16:44,299 DEBUG   SenderThread:2941651 [sender.py:send_request():396] send_request: defer
2025-07-13 18:16:44,299 INFO    SenderThread:2941651 [sender.py:send_request_defer():598] handle sender defer: 7
2025-07-13 18:16:44,806 INFO    Thread-12 :2941651 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-nar2od43/files/output.log
2025-07-13 18:16:44,806 INFO    Thread-12 :2941651 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-nar2od43/files/wandb-summary.json
2025-07-13 18:16:44,807 INFO    Thread-12 :2941651 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-nar2od43/files/config.yaml
2025-07-13 18:16:45,015 DEBUG   HandlerThread:2941651 [handler.py:handle_request():144] handle_request: poll_exit
2025-07-13 18:16:46,017 INFO    SenderThread:2941651 [sender.py:transition_state():602] send defer: 8
2025-07-13 18:16:46,018 DEBUG   SenderThread:2941651 [sender.py:send_request():396] send_request: poll_exit
2025-07-13 18:16:46,018 DEBUG   HandlerThread:2941651 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:16:46,019 INFO    HandlerThread:2941651 [handler.py:handle_request_defer():170] handle defer: 8
2025-07-13 18:16:46,019 DEBUG   SenderThread:2941651 [sender.py:send_request():396] send_request: defer
2025-07-13 18:16:46,019 INFO    SenderThread:2941651 [sender.py:send_request_defer():598] handle sender defer: 8
2025-07-13 18:16:46,019 INFO    SenderThread:2941651 [job_builder.py:build():232] Attempting to build job artifact
2025-07-13 18:16:46,020 INFO    SenderThread:2941651 [job_builder.py:build():256] is repo sourced job
2025-07-13 18:16:46,024 INFO    SenderThread:2941651 [job_builder.py:build():297] adding wandb-job metadata file
2025-07-13 18:16:46,037 INFO    SenderThread:2941651 [sender.py:transition_state():602] send defer: 9
2025-07-13 18:16:46,038 DEBUG   HandlerThread:2941651 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:16:46,038 INFO    HandlerThread:2941651 [handler.py:handle_request_defer():170] handle defer: 9
2025-07-13 18:16:46,038 DEBUG   SenderThread:2941651 [sender.py:send():369] send: artifact
2025-07-13 18:16:46,621 INFO    wandb-upload_0:2941651 [upload_job.py:push():88] Skipped uploading /gel/usr/akath/.local/share/wandb/artifacts/staging/tmpv0lebltx
2025-07-13 18:16:46,775 INFO    wandb-upload_2:2941651 [upload_job.py:push():91] Uploaded file /gel/usr/akath/.local/share/wandb/artifacts/staging/tmpv33qb1zc
2025-07-13 18:16:46,807 INFO    Thread-12 :2941651 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-nar2od43/files/output.log
2025-07-13 18:16:47,463 INFO    SenderThread:2941651 [sender.py:send_artifact():1450] sent artifact job-https___github.com_Shrinidhibhat87_codingchallenge_sereact.git_main.py - {'id': 'QXJ0aWZhY3Q6MTg3NzkyNTU2Mw==', 'digest': '24022c04cd416a726867eb3e3cc8a3ff', 'state': 'PENDING', 'aliases': [], 'artifactSequence': {'id': 'QXJ0aWZhY3RDb2xsZWN0aW9uOjY4NDgwMzQ0Ng==', 'latestArtifact': {'id': 'QXJ0aWZhY3Q6MTg3NzkyMzY4NQ==', 'versionIndex': 8}}, 'version': 'latest'}
2025-07-13 18:16:47,463 DEBUG   SenderThread:2941651 [sender.py:send_request():396] send_request: defer
2025-07-13 18:16:47,463 INFO    SenderThread:2941651 [sender.py:send_request_defer():598] handle sender defer: 9
2025-07-13 18:16:47,463 INFO    SenderThread:2941651 [dir_watcher.py:finish():359] shutting down directory watcher
2025-07-13 18:16:47,808 INFO    SenderThread:2941651 [dir_watcher.py:finish():389] scan: /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-nar2od43/files
2025-07-13 18:16:47,808 INFO    SenderThread:2941651 [dir_watcher.py:finish():403] scan save: /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-nar2od43/files/config.yaml config.yaml
2025-07-13 18:16:47,808 INFO    SenderThread:2941651 [dir_watcher.py:finish():403] scan save: /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-nar2od43/files/wandb-summary.json wandb-summary.json
2025-07-13 18:16:47,808 INFO    SenderThread:2941651 [dir_watcher.py:finish():403] scan save: /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-nar2od43/files/requirements.txt requirements.txt
2025-07-13 18:16:47,815 INFO    SenderThread:2941651 [dir_watcher.py:finish():403] scan save: /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-nar2od43/files/conda-environment.yaml conda-environment.yaml
2025-07-13 18:16:47,815 INFO    SenderThread:2941651 [dir_watcher.py:finish():403] scan save: /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-nar2od43/files/wandb-metadata.json wandb-metadata.json
2025-07-13 18:16:47,815 INFO    SenderThread:2941651 [dir_watcher.py:finish():403] scan save: /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-nar2od43/files/output.log output.log
2025-07-13 18:16:47,821 INFO    SenderThread:2941651 [sender.py:transition_state():602] send defer: 10
2025-07-13 18:16:47,822 DEBUG   HandlerThread:2941651 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:16:47,825 INFO    HandlerThread:2941651 [handler.py:handle_request_defer():170] handle defer: 10
2025-07-13 18:16:47,829 DEBUG   SenderThread:2941651 [sender.py:send_request():396] send_request: defer
2025-07-13 18:16:47,829 INFO    SenderThread:2941651 [sender.py:send_request_defer():598] handle sender defer: 10
2025-07-13 18:16:47,829 INFO    SenderThread:2941651 [file_pusher.py:finish():159] shutting down file pusher
2025-07-13 18:16:48,110 INFO    wandb-upload_2:2941651 [upload_job.py:push():133] Uploaded file /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-nar2od43/files/wandb-summary.json
2025-07-13 18:16:48,142 INFO    wandb-upload_1:2941651 [upload_job.py:push():133] Uploaded file /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-nar2od43/files/config.yaml
2025-07-13 18:16:48,149 INFO    wandb-upload_0:2941651 [upload_job.py:push():133] Uploaded file /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-nar2od43/files/requirements.txt
2025-07-13 18:16:48,150 INFO    wandb-upload_5:2941651 [upload_job.py:push():133] Uploaded file /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-nar2od43/files/output.log
2025-07-13 18:16:48,172 INFO    wandb-upload_3:2941651 [upload_job.py:push():133] Uploaded file /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-nar2od43/files/conda-environment.yaml
2025-07-13 18:16:48,372 INFO    Thread-11 :2941651 [sender.py:transition_state():602] send defer: 11
2025-07-13 18:16:48,372 DEBUG   HandlerThread:2941651 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:16:48,373 INFO    HandlerThread:2941651 [handler.py:handle_request_defer():170] handle defer: 11
2025-07-13 18:16:48,373 DEBUG   SenderThread:2941651 [sender.py:send_request():396] send_request: defer
2025-07-13 18:16:48,373 INFO    SenderThread:2941651 [sender.py:send_request_defer():598] handle sender defer: 11
2025-07-13 18:16:48,374 INFO    SenderThread:2941651 [file_pusher.py:join():164] waiting for file pusher
2025-07-13 18:16:48,374 INFO    SenderThread:2941651 [sender.py:transition_state():602] send defer: 12
2025-07-13 18:16:48,374 DEBUG   HandlerThread:2941651 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:16:48,374 INFO    HandlerThread:2941651 [handler.py:handle_request_defer():170] handle defer: 12
2025-07-13 18:16:48,374 DEBUG   SenderThread:2941651 [sender.py:send_request():396] send_request: defer
2025-07-13 18:16:48,374 INFO    SenderThread:2941651 [sender.py:send_request_defer():598] handle sender defer: 12
2025-07-13 18:16:48,451 INFO    SenderThread:2941651 [sender.py:transition_state():602] send defer: 13
2025-07-13 18:16:48,451 DEBUG   HandlerThread:2941651 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:16:48,451 INFO    HandlerThread:2941651 [handler.py:handle_request_defer():170] handle defer: 13
2025-07-13 18:16:48,452 DEBUG   SenderThread:2941651 [sender.py:send_request():396] send_request: defer
2025-07-13 18:16:48,452 INFO    SenderThread:2941651 [sender.py:send_request_defer():598] handle sender defer: 13
2025-07-13 18:16:48,452 INFO    SenderThread:2941651 [sender.py:transition_state():602] send defer: 14
2025-07-13 18:16:48,452 DEBUG   HandlerThread:2941651 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:16:48,453 DEBUG   SenderThread:2941651 [sender.py:send():369] send: final
2025-07-13 18:16:48,453 INFO    HandlerThread:2941651 [handler.py:handle_request_defer():170] handle defer: 14
2025-07-13 18:16:48,453 DEBUG   SenderThread:2941651 [sender.py:send():369] send: footer
2025-07-13 18:16:48,453 DEBUG   SenderThread:2941651 [sender.py:send_request():396] send_request: defer
2025-07-13 18:16:48,453 INFO    SenderThread:2941651 [sender.py:send_request_defer():598] handle sender defer: 14
2025-07-13 18:16:48,455 DEBUG   HandlerThread:2941651 [handler.py:handle_request():144] handle_request: poll_exit
2025-07-13 18:16:48,455 DEBUG   HandlerThread:2941651 [handler.py:handle_request():144] handle_request: server_info
2025-07-13 18:16:48,456 DEBUG   HandlerThread:2941651 [handler.py:handle_request():144] handle_request: get_summary
2025-07-13 18:16:48,456 DEBUG   SenderThread:2941651 [sender.py:send_request():396] send_request: poll_exit
2025-07-13 18:16:48,456 DEBUG   HandlerThread:2941651 [handler.py:handle_request():144] handle_request: sampled_history
2025-07-13 18:16:48,457 DEBUG   SenderThread:2941651 [sender.py:send_request():396] send_request: server_info
2025-07-13 18:16:48,507 INFO    MainThread:2941651 [wandb_run.py:_footer_history_summary_info():3467] rendering history
2025-07-13 18:16:48,507 INFO    MainThread:2941651 [wandb_run.py:_footer_history_summary_info():3499] rendering summary
2025-07-13 18:16:48,508 INFO    MainThread:2941651 [wandb_run.py:_footer_sync_info():3426] logging synced files
2025-07-13 18:16:48,508 DEBUG   HandlerThread:2941651 [handler.py:handle_request():144] handle_request: shutdown
2025-07-13 18:16:48,508 INFO    HandlerThread:2941651 [handler.py:finish():854] shutting down handler
2025-07-13 18:16:49,456 INFO    WriterThread:2941651 [datastore.py:close():298] close: /home-local/akath.nobkp/sereact/wandb/run-20250713_181633-nar2od43/run-nar2od43.wandb
2025-07-13 18:16:49,507 INFO    SenderThread:2941651 [sender.py:finish():1526] shutting down sender
2025-07-13 18:16:49,507 INFO    SenderThread:2941651 [file_pusher.py:finish():159] shutting down file pusher
2025-07-13 18:16:49,507 INFO    SenderThread:2941651 [file_pusher.py:join():164] waiting for file pusher
