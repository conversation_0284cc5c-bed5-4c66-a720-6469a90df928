2025-07-12 01:34:52,547 INFO    MainThread:2867975 [wandb_setup.py:_flush():76] Current SDK version is 0.15.4
2025-07-12 01:34:52,547 INFO    MainThread:2867975 [wandb_setup.py:_flush():76] Configure stats pid to 2867975
2025-07-12 01:34:52,547 INFO    MainThread:2867975 [wandb_setup.py:_flush():76] Loading settings from /gel/usr/akath/.config/wandb/settings
2025-07-12 01:34:52,547 INFO    MainThread:2867975 [wandb_setup.py:_flush():76] Loading settings from /home-local/akath.nobkp/codingchallenge_sereact/wandb/settings
2025-07-12 01:34:52,547 INFO    MainThread:2867975 [wandb_setup.py:_flush():76] Loading settings from environment variables: {}
2025-07-12 01:34:52,547 INFO    MainThread:2867975 [wandb_setup.py:_flush():76] Applying setup settings: {'_disable_service': False}
2025-07-12 01:34:52,547 INFO    MainThread:2867975 [wandb_setup.py:_flush():76] Inferring run settings from compute environment: {'program_relpath': 'main.py', 'program': 'main.py'}
2025-07-12 01:34:52,547 INFO    MainThread:2867975 [wandb_init.py:_log_setup():507] Logging user logs to /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_013452-s59rg7fz/logs/debug.log
2025-07-12 01:34:52,547 INFO    MainThread:2867975 [wandb_init.py:_log_setup():508] Logging internal logs to /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_013452-s59rg7fz/logs/debug-internal.log
2025-07-12 01:34:52,547 INFO    MainThread:2867975 [wandb_init.py:init():547] calling init triggers
2025-07-12 01:34:52,548 INFO    MainThread:2867975 [wandb_init.py:init():555] wandb.init called with sweep_config: {}
config: {}
2025-07-12 01:34:52,548 INFO    MainThread:2867975 [wandb_init.py:init():596] starting backend
2025-07-12 01:34:52,548 INFO    MainThread:2867975 [wandb_init.py:init():600] setting up manager
2025-07-12 01:34:52,552 INFO    MainThread:2867975 [backend.py:_multiprocessing_setup():108] multiprocessing start_methods=fork,spawn,forkserver, using: spawn
2025-07-12 01:34:52,557 INFO    MainThread:2867975 [wandb_init.py:init():606] backend started and connected
2025-07-12 01:34:52,562 INFO    MainThread:2867975 [wandb_init.py:init():703] updated telemetry
2025-07-12 01:34:52,578 INFO    MainThread:2867975 [wandb_init.py:init():736] communicating run to backend with 60.0 second timeout
2025-07-12 01:34:53,019 INFO    MainThread:2867975 [wandb_run.py:_on_init():2176] communicating current version
2025-07-12 01:34:53,082 INFO    MainThread:2867975 [wandb_run.py:_on_init():2185] got version response upgrade_message: "wandb version 0.21.0 is available!  To upgrade, please run:\n $ pip install wandb --upgrade"

2025-07-12 01:34:53,082 INFO    MainThread:2867975 [wandb_init.py:init():787] starting run threads in backend
2025-07-12 01:34:55,660 INFO    MainThread:2867975 [wandb_run.py:_console_start():2155] atexit reg
2025-07-12 01:34:55,661 INFO    MainThread:2867975 [wandb_run.py:_redirect():2010] redirect: SettingsConsole.WRAP_RAW
2025-07-12 01:34:55,661 INFO    MainThread:2867975 [wandb_run.py:_redirect():2075] Wrapping output streams.
2025-07-12 01:34:55,661 INFO    MainThread:2867975 [wandb_run.py:_redirect():2100] Redirects installed.
2025-07-12 01:34:55,663 INFO    MainThread:2867975 [wandb_init.py:init():828] run started, returning control to user process
2025-07-12 05:31:40,719 WARNING MsgRouterThr:2867975 [router.py:message_loop():77] message_loop has been closed
