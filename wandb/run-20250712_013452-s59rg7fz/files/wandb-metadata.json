{"os": "Linux-5.4.0-77-generic-x86_64-with-debian-bullseye-sid", "python": "3.7.16", "heartbeatAt": "2025-07-12T05:34:53.134457", "startedAt": "2025-07-12T05:34:52.544036", "docker": null, "cuda": null, "args": ["--local_rank=2", "--cfg", "config/base_train.yaml", "--output", "/home-local2/akath.extra.nobkp/sereact", "--data-path", "/home-local2/akath.extra.nobkp/dl_challenge", "--batch-size", "1"], "state": "running", "program": "main.py", "codePath": "main.py", "git": {"remote": "https://github.com/Shrinidhibhat87/codingchallenge_sereact.git", "commit": "a3ff60e13dfced588b500c8a1de00f36fdf22d49"}, "email": "<EMAIL>", "root": "/home-local/akath.nobkp/codingchallenge_sereact", "host": "lv3-32190", "username": "akath", "executable": "/gel/usr/akath/.conda/envs/swin/bin/python", "cpu_count": 6, "cpu_count_logical": 12, "cpu_freq": {"current": 2.2290833333333335, "min": 1200.0, "max": 4000.0}, "cpu_freq_per_core": [{"current": 1.995, "min": 1200.0, "max": 4000.0}, {"current": 1.969, "min": 1200.0, "max": 4000.0}, {"current": 3.114, "min": 1200.0, "max": 4000.0}, {"current": 1.911, "min": 1200.0, "max": 4000.0}, {"current": 1.241, "min": 1200.0, "max": 4000.0}, {"current": 3.52, "min": 1200.0, "max": 4000.0}, {"current": 2.144, "min": 1200.0, "max": 4000.0}, {"current": 1.603, "min": 1200.0, "max": 4000.0}, {"current": 1.68, "min": 1200.0, "max": 4000.0}, {"current": 2.754, "min": 1200.0, "max": 4000.0}, {"current": 1.231, "min": 1200.0, "max": 4000.0}, {"current": 3.587, "min": 1200.0, "max": 4000.0}], "disk": {"total": 111.2200813293457, "used": 53.232173919677734}, "gpu": "TITAN Xp", "gpu_count": 3, "gpu_devices": [{"name": "TITAN Xp", "memory_total": 12787122176}, {"name": "TITAN Xp", "memory_total": 12788498432}, {"name": "TITAN Xp", "memory_total": 12788498432}], "memory": {"total": 62.72977066040039}}