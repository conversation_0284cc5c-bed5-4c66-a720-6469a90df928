{"os": "Linux-5.4.0-77-generic-x86_64-with-debian-bullseye-sid", "python": "3.7.16", "heartbeatAt": "2025-07-12T16:56:11.457609", "startedAt": "2025-07-12T16:56:10.890384", "docker": null, "cuda": null, "args": ["--local_rank=0", "--cfg", "config/base_train.yaml", "--output", "/home-local2/akath.extra.nobkp/sereact", "--data-path", "/home-local2/akath.extra.nobkp/dl_challenge", "--batch-size", "1"], "state": "running", "program": "main.py", "codePath": "main.py", "git": {"remote": "https://github.com/Shrinidhibhat87/codingchallenge_sereact.git", "commit": "a3ff60e13dfced588b500c8a1de00f36fdf22d49"}, "email": "<EMAIL>", "root": "/home-local/akath.nobkp/codingchallenge_sereact", "host": "lv3-32190", "username": "akath", "executable": "/gel/usr/akath/.conda/envs/swin/bin/python", "cpu_count": 6, "cpu_count_logical": 12, "cpu_freq": {"current": 1.7644166666666665, "min": 1200.0, "max": 4000.0}, "cpu_freq_per_core": [{"current": 2.291, "min": 1200.0, "max": 4000.0}, {"current": 1.299, "min": 1200.0, "max": 4000.0}, {"current": 1.204, "min": 1200.0, "max": 4000.0}, {"current": 2.498, "min": 1200.0, "max": 4000.0}, {"current": 2.398, "min": 1200.0, "max": 4000.0}, {"current": 3.241, "min": 1200.0, "max": 4000.0}, {"current": 2.008, "min": 1200.0, "max": 4000.0}, {"current": 1.417, "min": 1200.0, "max": 4000.0}, {"current": 1.199, "min": 1200.0, "max": 4000.0}, {"current": 2.495, "min": 1200.0, "max": 4000.0}, {"current": 2.396, "min": 1200.0, "max": 4000.0}, {"current": 3.549, "min": 1200.0, "max": 4000.0}], "disk": {"total": 111.2200813293457, "used": 53.25860595703125}, "gpu": "TITAN Xp", "gpu_count": 3, "gpu_devices": [{"name": "TITAN Xp", "memory_total": 12787122176}, {"name": "TITAN Xp", "memory_total": 12788498432}, {"name": "TITAN Xp", "memory_total": 12788498432}], "memory": {"total": 62.72977066040039}}