=> merge config from config/base_train.yaml
RANK and WORLD_SIZE in environ: 0/3
[32m[2025-07-12 12:56:16 3DDETR.yaml][33m(main.py 549)[39m: INFO Full config saved to /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/config.json
[32m[2025-07-12 12:56:16 3DDETR.yaml][33m(main.py 552)[39m: INFO AMP_ENABLE: true
TAG: default
amp_opt_level: ''
base:
- ''
data:
  augment: false
  batch_size: 1
  cache_mode: part
  data_path: /home-local2/akath.extra.nobkp/dl_challenge
  dataset: Sereact_dataset
  debug: false
  num_workers: 4
  pin_memory: true
  transform: null
  zip_mode: false
eval_mode: false
local_rank: 0
loss:
  matcher_costs:
    cost_box_corners: 1.0
    giou: 5.0
    l1: 2.0
  weights:
    box_corners: 1.0
    giou: 1.0
    size: 1.0
    size_reg: 1.0
model:
  decoder:
    dim: 256
    dropout: 0.1
    ffn_dim: 256
    nhead: 4
    num_layers: 3
  encoder:
    activation: relu
    dim: 256
    dropout: 0.1
    ffn_dim: 128
    nheads: 4
    num_layers: 3
    preencoder_npoints: 2048
    type: vanilla
    use_color: false
  export_model: false
  mlp_dropout: 0.3
  name: 3DDETR.yaml
  num_angular_bins: 12
  num_queries: 256
  position_embedding: fourier
  pretrained: null
  pretrained_weights_path: /home/<USER>/Coding/Pre_trained_Weights/3detr/scannet_ep1080.pth
  resume: ''
  training: true
  unit_test: false
output: /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123
print_freq: 10
save_freq: 1
seed: 40
tag: '123'
train:
  accumulation_steps: 1
  auto_resume: false
  base_lr: 0.0001
  clip_grad: 0.1
  filter_biases_wd: true
  final_lr: 1.0e-06
  lr_scheduler: cosine
  max_epoch: 200
  start_epoch: 0
  unit_test_epoch: 100
  use_checkpoint: false
  warm_lr: 5.0e-06
  warm_lr_epochs: 9
  weight_decay: 0.01
unit_test: false
[32m[2025-07-12 12:56:16 3DDETR.yaml][33m(main.py 553)[39m: INFO {"cfg": "config/base_train.yaml", "opts": null, "batch_size": 1, "data_path": "/home-local2/akath.extra.nobkp/dl_challenge", "pretrained": null, "resume": null, "accumulation_steps": null, "use_checkpoint": false, "disable_amp": false, "amp_opt_level": null, "output": "/home-local2/akath.extra.nobkp/sereact", "tag": null, "eval": false, "unit_test": false, "base_lr": null, "local_rank": 0}
local rank 0 / global rank 0 successfully build train dataset
local rank 0 / global rank 0 successfully build val dataset
[32m[2025-07-12 12:56:16 3DDETR.yaml][33m(main.py 102)[39m: INFO Model3DDETR(
  (pre_encoder): PointnetSAModuleVotes(
    (grouper): QueryAndGroup()
    (mlp_module): SharedMLP(
      (layer0): Conv2d(
        (conv): Conv2d(3, 64, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn): BatchNorm2d(
          (bn): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        )
        (activation): ReLU(inplace=True)
      )
      (layer1): Conv2d(
        (conv): Conv2d(64, 128, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn): BatchNorm2d(
          (bn): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        )
        (activation): ReLU(inplace=True)
      )
      (layer2): Conv2d(
        (conv): Conv2d(128, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn): BatchNorm2d(
          (bn): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        )
        (activation): ReLU(inplace=True)
      )
    )
  )
  (encoder): TransformerEncoder(
    (layers): ModuleList(
      (0): TransformerEncoderLayer(
        attn_dr=0.1
        (self_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (linear1): Linear(in_features=256, out_features=128, bias=True)
        (dropout): Dropout(p=0.1, inplace=False)
        (linear2): Linear(in_features=128, out_features=256, bias=True)
        (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout2): Dropout(p=0.1, inplace=False)
        (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout1): Dropout(p=0.1, inplace=False)
        (activation): ReLU()
      )
      (1): TransformerEncoderLayer(
        attn_dr=0.1
        (self_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (linear1): Linear(in_features=256, out_features=128, bias=True)
        (dropout): Dropout(p=0.1, inplace=False)
        (linear2): Linear(in_features=128, out_features=256, bias=True)
        (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout2): Dropout(p=0.1, inplace=False)
        (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout1): Dropout(p=0.1, inplace=False)
        (activation): ReLU()
      )
      (2): TransformerEncoderLayer(
        attn_dr=0.1
        (self_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (linear1): Linear(in_features=256, out_features=128, bias=True)
        (dropout): Dropout(p=0.1, inplace=False)
        (linear2): Linear(in_features=128, out_features=256, bias=True)
        (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout2): Dropout(p=0.1, inplace=False)
        (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout1): Dropout(p=0.1, inplace=False)
        (activation): ReLU()
      )
    )
  )
  (encoder_decoder_projection): GenericMLP(
    (layers): Sequential(
      (0): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
      (1): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (2): ReLU()
      (3): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
      (4): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (5): ReLU()
    )
  )
  (positional_embedding): PositionEmbeddingCoordsSine(type=fourier, scale=6.283185307179586, normalize=True, gaussB_shape=torch.Size([3, 128]), gaussB_sum=-17.944507598876953)
  (query_projection): GenericMLP(
    (layers): Sequential(
      (0): Conv1d(256, 256, kernel_size=(1,), stride=(1,))
      (1): ReLU()
      (2): Conv1d(256, 256, kernel_size=(1,), stride=(1,))
      (3): ReLU()
    )
  )
  (decoder): TransformerDecoder(
    (layers): ModuleList(
      (0): TransformerDecoderLayer(
        (self_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (multihead_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm3): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout1): Dropout(p=0.1, inplace=False)
        (dropout2): Dropout(p=0.1, inplace=False)
        (dropout3): Dropout(p=0.1, inplace=False)
        (linear1): Linear(in_features=256, out_features=256, bias=True)
        (dropout): Dropout(p=0.1, inplace=False)
        (linear2): Linear(in_features=256, out_features=256, bias=True)
        (activation): ReLU()
      )
      (1): TransformerDecoderLayer(
        (self_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (multihead_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm3): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout1): Dropout(p=0.1, inplace=False)
        (dropout2): Dropout(p=0.1, inplace=False)
        (dropout3): Dropout(p=0.1, inplace=False)
        (linear1): Linear(in_features=256, out_features=256, bias=True)
        (dropout): Dropout(p=0.1, inplace=False)
        (linear2): Linear(in_features=256, out_features=256, bias=True)
        (activation): ReLU()
      )
      (2): TransformerDecoderLayer(
        (self_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (multihead_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm3): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout1): Dropout(p=0.1, inplace=False)
        (dropout2): Dropout(p=0.1, inplace=False)
        (dropout3): Dropout(p=0.1, inplace=False)
        (linear1): Linear(in_features=256, out_features=256, bias=True)
        (dropout): Dropout(p=0.1, inplace=False)
        (linear2): Linear(in_features=256, out_features=256, bias=True)
        (activation): ReLU()
      )
    )
    (norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (mlp_heads): ModuleDict(
    (center_head): GenericMLP(
      (layers): Sequential(
        (0): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
        (1): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU()
        (3): Dropout(p=0.3, inplace=False)
        (4): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
        (5): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (6): ReLU()
        (7): Dropout(p=0.3, inplace=False)
        (8): Conv1d(256, 3, kernel_size=(1,), stride=(1,))
      )
    )
    (size_head): GenericMLP(
      (layers): Sequential(
        (0): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
        (1): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU()
        (3): Dropout(p=0.3, inplace=False)
        (4): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
        (5): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (6): ReLU()
        (7): Dropout(p=0.3, inplace=False)
        (8): Conv1d(256, 3, kernel_size=(1,), stride=(1,))
      )
    )
    (angle_cls_head): GenericMLP(
      (layers): Sequential(
        (0): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
        (1): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU()
        (3): Dropout(p=0.3, inplace=False)
        (4): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
        (5): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (6): ReLU()
        (7): Dropout(p=0.3, inplace=False)
        (8): Conv1d(256, 12, kernel_size=(1,), stride=(1,))
      )
    )
    (angle_residual_head): GenericMLP(
      (layers): Sequential(
        (0): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
        (1): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU()
        (3): Dropout(p=0.3, inplace=False)
        (4): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
        (5): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (6): ReLU()
        (7): Dropout(p=0.3, inplace=False)
        (8): Conv1d(256, 12, kernel_size=(1,), stride=(1,))
      )
    )
  )
)
[32m[2025-07-12 12:56:16 3DDETR.yaml][33m(main.py 104)[39m: INFO number of params: 3811038
[32m[2025-07-12 12:56:16 3DDETR.yaml][33m(main.py 153)[39m: INFO Start training
[32m[2025-07-12 12:56:20 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [0/200][0/54]	eta 0:03:29 lr 0.000100	time 3.8731 (3.8731)	loss 38.3961 (38.3961)	miou 0.0295	grad_norm 184.7570 (184.7570)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 12:56:41 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [0/200][10/54]	eta 0:01:40 lr 0.000099	time 2.4636 (2.2780)	loss 10.9715 (19.7317)	miou 0.1866	grad_norm 430.4720 (128.8305)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 12:56:59 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [0/200][20/54]	eta 0:01:10 lr 0.000097	time 2.3748 (2.0599)	loss 12.1377 (18.4472)	miou 0.1731	grad_norm 34.8592 (98.5001)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 12:57:18 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [0/200][30/54]	eta 0:00:48 lr 0.000094	time 2.2032 (2.0111)	loss 20.6212 (18.7335)	miou 0.1724	grad_norm 46.5702 (87.3981)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 12:57:33 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [0/200][40/54]	eta 0:00:26 lr 0.000090	time 1.0007 (1.8925)	loss 10.4343 (17.6611)	miou 0.1640	grad_norm 32.8181 (75.5728)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 12:57:46 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [0/200][50/54]	eta 0:00:07 lr 0.000085	time 1.7665 (1.7706)	loss 11.9002 (16.8096)	miou 0.1599	grad_norm 39.4094 (73.6836)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 12:57:50 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 0 training takes 0:01:33
[32m[2025-07-12 12:57:50 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
0
[32m[2025-07-12 12:58:04 3DDETR.yaml][33m(utils_help.py 202)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saving......
[32m[2025-07-12 12:58:04 3DDETR.yaml][33m(utils_help.py 204)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saved !!!
[32m[2025-07-12 12:58:04 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1368%
[32m[2025-07-12 12:58:04 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.1368%
[32m[2025-07-12 12:58:07 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [1/200][0/54]	eta 0:02:07 lr 0.000082	time 2.3563 (2.3563)	loss 13.8673 (13.8673)	miou 0.1348	grad_norm 166.8256 (166.8256)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 12:58:19 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [1/200][10/54]	eta 0:00:57 lr 0.000076	time 0.9431 (1.3099)	loss 10.6418 (11.7439)	miou 0.1384	grad_norm 29.1311 (53.6242)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 12:58:30 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [1/200][20/54]	eta 0:00:41 lr 0.000069	time 1.3213 (1.2201)	loss 9.7795 (11.7568)	miou 0.1369	grad_norm 61.1262 (50.1074)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 12:58:40 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [1/200][30/54]	eta 0:00:27 lr 0.000062	time 1.0344 (1.1616)	loss 10.8032 (12.1530)	miou 0.1326	grad_norm 61.5958 (49.3921)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 12:58:50 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [1/200][40/54]	eta 0:00:15 lr 0.000054	time 1.0343 (1.1212)	loss 12.0997 (11.7366)	miou 0.1422	grad_norm 99.0459 (48.5574)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 12:59:02 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [1/200][50/54]	eta 0:00:04 lr 0.000046	time 1.2337 (1.1371)	loss 11.2505 (11.5986)	miou 0.1466	grad_norm 24.0252 (48.8623)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 12:59:05 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 1 training takes 0:01:01
[32m[2025-07-12 12:59:05 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 12:59:16 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1315%
[32m[2025-07-12 12:59:16 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.1368%
[32m[2025-07-12 12:59:17 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [2/200][0/54]	eta 0:01:10 lr 0.000043	time 1.3088 (1.3088)	loss 9.9125 (9.9125)	miou 0.1284	grad_norm 27.5529 (27.5529)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 12:59:27 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [2/200][10/54]	eta 0:00:46 lr 0.000035	time 1.0062 (1.0522)	loss 9.4996 (10.4544)	miou 0.1420	grad_norm 24.6562 (39.7183)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 12:59:38 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [2/200][20/54]	eta 0:00:36 lr 0.000028	time 1.2611 (1.0803)	loss 9.7038 (10.1867)	miou 0.1478	grad_norm 30.6256 (35.7157)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 12:59:49 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [2/200][30/54]	eta 0:00:26 lr 0.000021	time 1.4310 (1.0834)	loss 9.3430 (10.8512)	miou 0.1495	grad_norm 60.7756 (46.5339)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 12:59:59 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [2/200][40/54]	eta 0:00:14 lr 0.000015	time 1.0264 (1.0576)	loss 10.2353 (10.7495)	miou 0.1504	grad_norm 35.0532 (47.2790)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:00:10 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [2/200][50/54]	eta 0:00:04 lr 0.000010	time 0.9888 (1.0692)	loss 18.6194 (10.9133)	miou 0.1494	grad_norm 136.9053 (57.3387)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:00:13 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 2 training takes 0:00:57
[32m[2025-07-12 13:00:13 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
2
[32m[2025-07-12 13:00:23 3DDETR.yaml][33m(utils_help.py 202)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saving......
[32m[2025-07-12 13:00:23 3DDETR.yaml][33m(utils_help.py 204)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saved !!!
[32m[2025-07-12 13:00:23 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1406%
[32m[2025-07-12 13:00:23 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.1406%
[32m[2025-07-12 13:00:25 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [3/200][0/54]	eta 0:01:39 lr 0.000008	time 1.8457 (1.8457)	loss 8.2599 (8.2599)	miou 0.1418	grad_norm 60.2777 (60.2777)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:00:36 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [3/200][10/54]	eta 0:00:51 lr 0.000004	time 0.9623 (1.1632)	loss 10.6451 (10.5978)	miou 0.1348	grad_norm 22.2166 (57.5675)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:00:46 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [3/200][20/54]	eta 0:00:37 lr 0.000002	time 0.9305 (1.1032)	loss 8.3234 (11.1669)	miou 0.1416	grad_norm 39.1917 (60.6604)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:00:58 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [3/200][30/54]	eta 0:00:26 lr 0.000000	time 1.0924 (1.1084)	loss 10.6980 (10.8951)	miou 0.1432	grad_norm 29.4090 (68.2380)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:01:09 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [3/200][40/54]	eta 0:00:15 lr 0.000000	time 1.4944 (1.1096)	loss 15.0638 (10.7565)	miou 0.1472	grad_norm 63.2126 (64.8947)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:01:19 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [3/200][50/54]	eta 0:00:04 lr 0.000001	time 0.9164 (1.0952)	loss 9.2741 (10.7709)	miou 0.1490	grad_norm 73.7254 (63.5650)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:01:23 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 3 training takes 0:00:59
[32m[2025-07-12 13:01:23 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 13:01:32 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1338%
[32m[2025-07-12 13:01:32 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.1406%
[32m[2025-07-12 13:01:34 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [4/200][0/54]	eta 0:01:39 lr 0.000002	time 1.8445 (1.8445)	loss 10.1117 (10.1117)	miou 0.1337	grad_norm 65.5376 (65.5376)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:01:44 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [4/200][10/54]	eta 0:00:47 lr 0.000004	time 1.0340 (1.0708)	loss 11.4386 (11.2914)	miou 0.1414	grad_norm 67.9695 (66.0447)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:01:55 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [4/200][20/54]	eta 0:00:35 lr 0.000008	time 0.9346 (1.0576)	loss 11.9589 (10.7617)	miou 0.1465	grad_norm 124.8253 (70.9429)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:02:05 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [4/200][30/54]	eta 0:00:25 lr 0.000013	time 0.9365 (1.0625)	loss 8.9013 (11.3475)	miou 0.1422	grad_norm 23.5404 (70.1275)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:02:15 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [4/200][40/54]	eta 0:00:14 lr 0.000019	time 1.0473 (1.0428)	loss 10.7603 (11.2675)	miou 0.1466	grad_norm 25.7894 (67.1085)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:02:26 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [4/200][50/54]	eta 0:00:04 lr 0.000025	time 1.2480 (1.0583)	loss 11.0627 (11.1979)	miou 0.1472	grad_norm 41.3835 (66.0697)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:02:30 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 4 training takes 0:00:57
[32m[2025-07-12 13:02:30 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
4
[32m[2025-07-12 13:02:40 3DDETR.yaml][33m(utils_help.py 202)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saving......
[32m[2025-07-12 13:02:40 3DDETR.yaml][33m(utils_help.py 204)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saved !!!
[32m[2025-07-12 13:02:40 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1704%
[32m[2025-07-12 13:02:40 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.1704%
[32m[2025-07-12 13:02:41 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [5/200][0/54]	eta 0:01:08 lr 0.000028	time 1.2777 (1.2777)	loss 8.3364 (8.3364)	miou 0.1712	grad_norm 267.6622 (267.6622)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:02:52 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [5/200][10/54]	eta 0:00:48 lr 0.000035	time 0.9375 (1.0961)	loss 9.3341 (9.5542)	miou 0.1807	grad_norm 33.5564 (94.8807)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:03:03 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [5/200][20/54]	eta 0:00:36 lr 0.000043	time 1.2309 (1.0812)	loss 10.9648 (10.1170)	miou 0.1757	grad_norm 54.0706 (76.8970)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:03:13 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [5/200][30/54]	eta 0:00:25 lr 0.000051	time 1.1520 (1.0642)	loss 9.4903 (10.4979)	miou 0.1703	grad_norm 134.5473 (77.8561)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:03:25 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [5/200][40/54]	eta 0:00:15 lr 0.000059	time 1.0639 (1.0843)	loss 13.5722 (10.3019)	miou 0.1748	grad_norm 96.0900 (90.2038)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:03:36 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [5/200][50/54]	eta 0:00:04 lr 0.000066	time 1.1613 (1.0859)	loss 10.6551 (10.1512)	miou 0.1741	grad_norm 59.4260 (82.4398)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:03:39 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 5 training takes 0:00:58
[32m[2025-07-12 13:03:39 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
5
[32m[2025-07-12 13:03:48 3DDETR.yaml][33m(utils_help.py 202)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saving......
[32m[2025-07-12 13:03:49 3DDETR.yaml][33m(utils_help.py 204)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saved !!!
[32m[2025-07-12 13:03:49 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1920%
[32m[2025-07-12 13:03:49 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.1920%
[32m[2025-07-12 13:03:50 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [6/200][0/54]	eta 0:01:24 lr 0.000069	time 1.5692 (1.5692)	loss 8.5786 (8.5786)	miou 0.1917	grad_norm 84.5411 (84.5411)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:04:02 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [6/200][10/54]	eta 0:00:50 lr 0.000076	time 0.8185 (1.1571)	loss 8.2621 (9.3560)	miou 0.1838	grad_norm 40.9671 (72.0421)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:04:11 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [6/200][20/54]	eta 0:00:36 lr 0.000082	time 1.0617 (1.0745)	loss 11.9410 (9.8471)	miou 0.1811	grad_norm 51.3524 (92.5712)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:04:22 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [6/200][30/54]	eta 0:00:25 lr 0.000088	time 1.0258 (1.0652)	loss 7.2842 (9.5910)	miou 0.1871	grad_norm 30.5336 (79.8260)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:04:32 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [6/200][40/54]	eta 0:00:14 lr 0.000093	time 1.0982 (1.0533)	loss 9.1050 (9.9918)	miou 0.1789	grad_norm 50.0852 (83.4867)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:04:42 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [6/200][50/54]	eta 0:00:04 lr 0.000096	time 1.0646 (1.0524)	loss 13.2637 (9.9028)	miou 0.1814	grad_norm 47.5043 (79.0396)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:04:46 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 6 training takes 0:00:56
[32m[2025-07-12 13:04:46 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
6
[32m[2025-07-12 13:04:56 3DDETR.yaml][33m(utils_help.py 202)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saving......
[32m[2025-07-12 13:04:56 3DDETR.yaml][33m(utils_help.py 204)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saved !!!
[32m[2025-07-12 13:04:56 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2036%
[32m[2025-07-12 13:04:56 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2036%
[32m[2025-07-12 13:04:57 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [7/200][0/54]	eta 0:01:21 lr 0.000097	time 1.5146 (1.5146)	loss 9.0320 (9.0320)	miou 0.1971	grad_norm 31.6463 (31.6463)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:05:09 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [7/200][10/54]	eta 0:00:50 lr 0.000099	time 1.0845 (1.1454)	loss 7.3217 (9.2344)	miou 0.1841	grad_norm 164.6986 (80.5938)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:05:18 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [7/200][20/54]	eta 0:00:35 lr 0.000100	time 0.9411 (1.0531)	loss 10.3237 (9.2903)	miou 0.1789	grad_norm 77.5779 (84.1536)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:05:28 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [7/200][30/54]	eta 0:00:25 lr 0.000100	time 0.9305 (1.0421)	loss 8.7899 (9.4694)	miou 0.1799	grad_norm 74.2894 (75.1420)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:05:40 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [7/200][40/54]	eta 0:00:14 lr 0.000098	time 1.0550 (1.0660)	loss 8.2369 (9.2616)	miou 0.1851	grad_norm 22.2762 (74.7650)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:05:50 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [7/200][50/54]	eta 0:00:04 lr 0.000095	time 1.1353 (1.0686)	loss 6.3470 (9.3338)	miou 0.1897	grad_norm 25.8982 (69.9125)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:05:53 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 7 training takes 0:00:57
[32m[2025-07-12 13:05:53 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
7
[32m[2025-07-12 13:06:03 3DDETR.yaml][33m(utils_help.py 202)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saving......
[32m[2025-07-12 13:06:04 3DDETR.yaml][33m(utils_help.py 204)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saved !!!
[32m[2025-07-12 13:06:04 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2223%
[32m[2025-07-12 13:06:04 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2223%
[32m[2025-07-12 13:06:05 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [8/200][0/54]	eta 0:01:15 lr 0.000093	time 1.3915 (1.3915)	loss 7.2373 (7.2373)	miou 0.2235	grad_norm 68.5014 (68.5014)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:06:16 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [8/200][10/54]	eta 0:00:49 lr 0.000089	time 1.1382 (1.1322)	loss 10.4102 (9.3935)	miou 0.2027	grad_norm 36.0904 (54.7405)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:06:27 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [8/200][20/54]	eta 0:00:37 lr 0.000084	time 1.3058 (1.1001)	loss 6.8745 (9.2138)	miou 0.1982	grad_norm 53.0479 (47.4354)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:06:38 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [8/200][30/54]	eta 0:00:26 lr 0.000077	time 1.2174 (1.0960)	loss 9.2849 (9.1794)	miou 0.2034	grad_norm 16.3897 (41.3078)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:06:49 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [8/200][40/54]	eta 0:00:15 lr 0.000071	time 0.9791 (1.0979)	loss 9.3558 (9.4157)	miou 0.1932	grad_norm 25.4554 (44.0438)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:06:59 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [8/200][50/54]	eta 0:00:04 lr 0.000063	time 1.0275 (1.0846)	loss 8.1721 (9.3395)	miou 0.1968	grad_norm 31.2334 (43.3556)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:07:02 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 8 training takes 0:00:58
[32m[2025-07-12 13:07:02 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 13:07:12 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1634%
[32m[2025-07-12 13:07:12 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2223%
[32m[2025-07-12 13:07:13 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [9/200][0/54]	eta 0:01:01 lr 0.000060	time 1.1466 (1.1466)	loss 11.0970 (11.0970)	miou 0.1717	grad_norm 37.5175 (37.5175)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:07:23 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [9/200][10/54]	eta 0:00:46 lr 0.000052	time 0.9781 (1.0503)	loss 11.5737 (9.5158)	miou 0.1744	grad_norm 21.4801 (40.2003)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:07:34 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [9/200][20/54]	eta 0:00:35 lr 0.000045	time 1.0219 (1.0464)	loss 6.4909 (8.9482)	miou 0.1763	grad_norm 28.6857 (38.0464)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:07:44 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [9/200][30/54]	eta 0:00:24 lr 0.000037	time 0.9375 (1.0351)	loss 12.5372 (8.7776)	miou 0.1792	grad_norm 46.4079 (42.0298)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:07:56 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [9/200][40/54]	eta 0:00:15 lr 0.000029	time 1.3931 (1.0770)	loss 8.5812 (8.7238)	miou 0.1844	grad_norm 58.6762 (43.9221)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:08:07 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [9/200][50/54]	eta 0:00:04 lr 0.000023	time 0.9392 (1.0782)	loss 8.3839 (8.7955)	miou 0.1846	grad_norm 75.1292 (42.8137)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:08:10 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 9 training takes 0:00:57
[32m[2025-07-12 13:08:10 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 13:08:19 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2176%
[32m[2025-07-12 13:08:19 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2223%
[32m[2025-07-12 13:08:21 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [10/200][0/54]	eta 0:01:30 lr 0.000020	time 1.6779 (1.6779)	loss 8.2039 (8.2039)	miou 0.2259	grad_norm 34.9236 (34.9236)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:08:31 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [10/200][10/54]	eta 0:00:44 lr 0.000014	time 0.8755 (1.0168)	loss 9.9081 (9.4096)	miou 0.2079	grad_norm 22.0130 (33.6525)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:08:41 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [10/200][20/54]	eta 0:00:35 lr 0.000009	time 0.9283 (1.0484)	loss 6.0072 (8.8521)	miou 0.1947	grad_norm 47.5027 (38.1905)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:08:52 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [10/200][30/54]	eta 0:00:25 lr 0.000005	time 0.8936 (1.0645)	loss 8.2596 (9.1616)	miou 0.1895	grad_norm 21.2615 (48.5499)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:09:03 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [10/200][40/54]	eta 0:00:14 lr 0.000002	time 1.2429 (1.0607)	loss 6.9077 (8.9949)	miou 0.1935	grad_norm 36.7093 (53.2022)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:09:14 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [10/200][50/54]	eta 0:00:04 lr 0.000000	time 1.1127 (1.0730)	loss 6.8816 (8.9169)	miou 0.2002	grad_norm 27.5377 (56.5582)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:09:18 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 10 training takes 0:00:58
[32m[2025-07-12 13:09:18 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 13:09:28 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2052%
[32m[2025-07-12 13:09:28 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2223%
[32m[2025-07-12 13:09:29 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [11/200][0/54]	eta 0:01:19 lr 0.000000	time 1.4766 (1.4766)	loss 8.2619 (8.2619)	miou 0.1980	grad_norm 35.5075 (35.5075)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:09:38 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [11/200][10/54]	eta 0:00:42 lr 0.000000	time 0.8134 (0.9772)	loss 10.7385 (9.3592)	miou 0.1840	grad_norm 19.1746 (54.6072)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:09:48 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [11/200][20/54]	eta 0:00:33 lr 0.000001	time 0.9806 (0.9887)	loss 8.2605 (9.0679)	miou 0.1907	grad_norm 56.7827 (53.5402)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:10:00 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [11/200][30/54]	eta 0:00:24 lr 0.000004	time 1.0359 (1.0367)	loss 10.9599 (9.2141)	miou 0.1855	grad_norm 15.1807 (47.4706)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:10:10 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [11/200][40/54]	eta 0:00:14 lr 0.000007	time 0.8510 (1.0343)	loss 9.0357 (8.9867)	miou 0.1899	grad_norm 35.7027 (47.1121)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:10:20 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [11/200][50/54]	eta 0:00:04 lr 0.000012	time 0.9949 (1.0327)	loss 15.4977 (9.1585)	miou 0.1877	grad_norm 42.9580 (47.2458)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:10:24 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 11 training takes 0:00:56
[32m[2025-07-12 13:10:24 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 13:10:34 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2021%
[32m[2025-07-12 13:10:34 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2223%
[32m[2025-07-12 13:10:35 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [12/200][0/54]	eta 0:01:13 lr 0.000014	time 1.3652 (1.3652)	loss 8.3505 (8.3505)	miou 0.2024	grad_norm 98.7457 (98.7457)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:10:44 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [12/200][10/54]	eta 0:00:41 lr 0.000020	time 0.6185 (0.9530)	loss 11.1052 (9.1955)	miou 0.1878	grad_norm 23.3581 (40.2448)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:10:56 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [12/200][20/54]	eta 0:00:35 lr 0.000027	time 1.2209 (1.0509)	loss 6.0163 (8.7826)	miou 0.2029	grad_norm 16.7240 (43.0134)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:11:07 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [12/200][30/54]	eta 0:00:25 lr 0.000034	time 1.1517 (1.0541)	loss 9.5910 (8.8739)	miou 0.1929	grad_norm 18.4403 (40.7907)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:11:18 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [12/200][40/54]	eta 0:00:14 lr 0.000041	time 1.0908 (1.0649)	loss 6.0033 (8.7583)	miou 0.1927	grad_norm 34.5788 (44.6171)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:11:28 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [12/200][50/54]	eta 0:00:04 lr 0.000049	time 0.9428 (1.0674)	loss 7.5774 (8.7607)	miou 0.1917	grad_norm 45.6117 (47.6869)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:11:31 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 12 training takes 0:00:57
[32m[2025-07-12 13:11:31 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 13:11:41 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2180%
[32m[2025-07-12 13:11:41 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2223%
[32m[2025-07-12 13:11:42 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [13/200][0/54]	eta 0:01:10 lr 0.000052	time 1.3041 (1.3041)	loss 8.9220 (8.9220)	miou 0.2131	grad_norm 30.5495 (30.5495)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:11:53 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [13/200][10/54]	eta 0:00:47 lr 0.000060	time 1.0345 (1.0881)	loss 7.3457 (8.2920)	miou 0.2224	grad_norm 20.8476 (37.0037)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:12:03 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [13/200][20/54]	eta 0:00:35 lr 0.000068	time 0.9939 (1.0414)	loss 11.3743 (8.4851)	miou 0.2096	grad_norm 32.9351 (54.1531)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:12:14 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [13/200][30/54]	eta 0:00:25 lr 0.000075	time 1.2325 (1.0448)	loss 6.2008 (8.4614)	miou 0.2053	grad_norm 51.8129 (50.2756)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:12:23 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [13/200][40/54]	eta 0:00:14 lr 0.000081	time 0.7335 (1.0188)	loss 8.2985 (8.4146)	miou 0.1998	grad_norm 27.3184 (45.4822)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:12:33 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [13/200][50/54]	eta 0:00:04 lr 0.000087	time 0.8958 (1.0242)	loss 10.1607 (8.6162)	miou 0.1965	grad_norm 31.9699 (45.2789)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:12:37 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 13 training takes 0:00:55
[32m[2025-07-12 13:12:37 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 13:12:47 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2142%
[32m[2025-07-12 13:12:47 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2223%
[32m[2025-07-12 13:12:48 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [14/200][0/54]	eta 0:01:05 lr 0.000089	time 1.2137 (1.2137)	loss 9.2585 (9.2585)	miou 0.2094	grad_norm 99.3175 (99.3175)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:12:58 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [14/200][10/54]	eta 0:00:47 lr 0.000093	time 0.9489 (1.0724)	loss 7.6742 (9.3746)	miou 0.2045	grad_norm 38.9801 (94.5706)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:13:09 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [14/200][20/54]	eta 0:00:36 lr 0.000097	time 1.0841 (1.0662)	loss 9.7479 (9.3298)	miou 0.1867	grad_norm 50.0728 (70.1130)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:13:20 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [14/200][30/54]	eta 0:00:25 lr 0.000099	time 0.8912 (1.0739)	loss 9.3407 (9.1836)	miou 0.1879	grad_norm 69.2794 (61.4635)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:13:31 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [14/200][40/54]	eta 0:00:15 lr 0.000100	time 1.0523 (1.0812)	loss 10.1750 (8.9018)	miou 0.1887	grad_norm 19.5010 (58.9104)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:13:41 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [14/200][50/54]	eta 0:00:04 lr 0.000100	time 0.9753 (1.0746)	loss 6.6831 (8.9294)	miou 0.1886	grad_norm 63.9429 (54.6628)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:13:45 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 14 training takes 0:00:58
[32m[2025-07-12 13:13:45 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 13:13:55 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1906%
[32m[2025-07-12 13:13:55 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2223%
[32m[2025-07-12 13:13:56 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [15/200][0/54]	eta 0:01:16 lr 0.000099	time 1.4092 (1.4092)	loss 9.2168 (9.2168)	miou 0.1829	grad_norm 27.0188 (27.0188)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:14:06 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [15/200][10/54]	eta 0:00:46 lr 0.000097	time 1.3251 (1.0534)	loss 6.2343 (8.7540)	miou 0.1706	grad_norm 87.8834 (33.7051)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:14:16 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [15/200][20/54]	eta 0:00:35 lr 0.000094	time 1.0196 (1.0374)	loss 9.9448 (8.7514)	miou 0.1783	grad_norm 20.2786 (31.9031)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:14:27 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [15/200][30/54]	eta 0:00:24 lr 0.000090	time 0.8983 (1.0363)	loss 7.9121 (8.5816)	miou 0.1811	grad_norm 65.2433 (31.1661)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:14:38 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [15/200][40/54]	eta 0:00:14 lr 0.000085	time 1.3360 (1.0702)	loss 8.9376 (8.5746)	miou 0.1827	grad_norm 69.2293 (54.7729)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:14:49 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [15/200][50/54]	eta 0:00:04 lr 0.000079	time 1.2505 (1.0697)	loss 6.4716 (8.5227)	miou 0.1840	grad_norm 32.2576 (49.7258)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:14:52 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 15 training takes 0:00:57
[32m[2025-07-12 13:14:52 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
15
[32m[2025-07-12 13:15:02 3DDETR.yaml][33m(utils_help.py 202)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saving......
[32m[2025-07-12 13:15:02 3DDETR.yaml][33m(utils_help.py 204)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saved !!!
[32m[2025-07-12 13:15:02 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2249%
[32m[2025-07-12 13:15:02 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2249%
[32m[2025-07-12 13:15:04 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [16/200][0/54]	eta 0:00:56 lr 0.000076	time 1.0471 (1.0471)	loss 8.3470 (8.3470)	miou 0.2208	grad_norm 73.4034 (73.4034)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:15:13 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [16/200][10/54]	eta 0:00:42 lr 0.000069	time 0.8600 (0.9560)	loss 6.6620 (8.5452)	miou 0.2184	grad_norm 83.4977 (38.0686)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:15:24 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [16/200][20/54]	eta 0:00:34 lr 0.000062	time 0.9943 (1.0061)	loss 9.4829 (8.6632)	miou 0.2217	grad_norm 14.9369 (33.2411)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:15:35 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [16/200][30/54]	eta 0:00:24 lr 0.000054	time 1.2891 (1.0377)	loss 10.5320 (8.5713)	miou 0.2224	grad_norm 25.6310 (33.0980)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:15:45 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [16/200][40/54]	eta 0:00:14 lr 0.000046	time 1.1585 (1.0450)	loss 14.6126 (8.7943)	miou 0.2207	grad_norm 25.7691 (37.9590)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:15:56 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [16/200][50/54]	eta 0:00:04 lr 0.000038	time 1.0809 (1.0481)	loss 8.0292 (8.6937)	miou 0.2160	grad_norm 41.2151 (37.0669)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:15:59 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 16 training takes 0:00:56
[32m[2025-07-12 13:15:59 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
16
[32m[2025-07-12 13:16:08 3DDETR.yaml][33m(utils_help.py 202)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saving......
[32m[2025-07-12 13:16:09 3DDETR.yaml][33m(utils_help.py 204)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saved !!!
[32m[2025-07-12 13:16:09 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2392%
[32m[2025-07-12 13:16:09 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2392%
[32m[2025-07-12 13:16:10 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [17/200][0/54]	eta 0:01:02 lr 0.000035	time 1.1498 (1.1498)	loss 5.5591 (5.5591)	miou 0.2396	grad_norm 85.2866 (85.2866)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:16:20 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [17/200][10/54]	eta 0:00:43 lr 0.000028	time 0.9284 (0.9926)	loss 7.5755 (8.2121)	miou 0.2278	grad_norm 25.6937 (38.0225)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:16:31 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [17/200][20/54]	eta 0:00:35 lr 0.000021	time 1.0792 (1.0460)	loss 10.0392 (7.9414)	miou 0.2242	grad_norm 20.9559 (38.8999)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:16:41 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [17/200][30/54]	eta 0:00:24 lr 0.000015	time 0.8670 (1.0302)	loss 6.1952 (7.9612)	miou 0.2180	grad_norm 47.0631 (39.4606)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:16:52 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [17/200][40/54]	eta 0:00:14 lr 0.000010	time 1.2249 (1.0462)	loss 9.2549 (8.0478)	miou 0.2129	grad_norm 40.4249 (44.3778)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:17:02 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [17/200][50/54]	eta 0:00:04 lr 0.000006	time 1.0596 (1.0359)	loss 6.3245 (8.1897)	miou 0.2111	grad_norm 22.5805 (42.7435)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:17:05 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 17 training takes 0:00:56
[32m[2025-07-12 13:17:05 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 13:17:15 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2357%
[32m[2025-07-12 13:17:15 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2392%
[32m[2025-07-12 13:17:17 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [18/200][0/54]	eta 0:01:41 lr 0.000004	time 1.8784 (1.8784)	loss 5.3162 (5.3162)	miou 0.2364	grad_norm 75.3933 (75.3933)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:17:27 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [18/200][10/54]	eta 0:00:49 lr 0.000002	time 0.9413 (1.1206)	loss 6.4564 (8.2829)	miou 0.2106	grad_norm 47.3062 (55.7513)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:17:37 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [18/200][20/54]	eta 0:00:35 lr 0.000000	time 1.0018 (1.0415)	loss 10.7296 (8.0545)	miou 0.2109	grad_norm 23.5199 (53.8093)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:17:48 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [18/200][30/54]	eta 0:00:25 lr 0.000000	time 1.3451 (1.0593)	loss 6.2529 (8.0818)	miou 0.2070	grad_norm 77.9253 (49.2027)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:17:58 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [18/200][40/54]	eta 0:00:14 lr 0.000001	time 0.8759 (1.0487)	loss 8.1386 (8.3013)	miou 0.2104	grad_norm 68.7941 (45.4838)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:18:09 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [18/200][50/54]	eta 0:00:04 lr 0.000003	time 1.1467 (1.0526)	loss 9.4356 (8.3343)	miou 0.2109	grad_norm 152.5402 (46.6977)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:18:12 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 18 training takes 0:00:57
[32m[2025-07-12 13:18:12 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 13:18:22 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2309%
[32m[2025-07-12 13:18:22 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2392%
[32m[2025-07-12 13:18:24 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [19/200][0/54]	eta 0:01:21 lr 0.000004	time 1.5133 (1.5133)	loss 7.8652 (7.8652)	miou 0.2267	grad_norm 60.1463 (60.1463)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:18:34 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [19/200][10/54]	eta 0:00:47 lr 0.000008	time 0.9583 (1.0715)	loss 14.0155 (8.5176)	miou 0.2207	grad_norm 41.0581 (41.1539)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:18:45 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [19/200][20/54]	eta 0:00:36 lr 0.000013	time 1.4668 (1.0704)	loss 9.8064 (8.7526)	miou 0.2117	grad_norm 24.7333 (53.4048)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:18:54 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [19/200][30/54]	eta 0:00:25 lr 0.000019	time 0.8639 (1.0421)	loss 6.2738 (8.1348)	miou 0.2153	grad_norm 38.2998 (53.7902)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:19:05 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [19/200][40/54]	eta 0:00:14 lr 0.000025	time 1.1426 (1.0358)	loss 10.5527 (8.0446)	miou 0.2170	grad_norm 18.5623 (50.4396)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:19:16 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [19/200][50/54]	eta 0:00:04 lr 0.000032	time 1.2468 (1.0520)	loss 7.1717 (8.1903)	miou 0.2217	grad_norm 21.4046 (49.4931)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:19:20 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 19 training takes 0:00:57
[32m[2025-07-12 13:19:20 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
19
[32m[2025-07-12 13:19:29 3DDETR.yaml][33m(utils_help.py 202)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saving......
[32m[2025-07-12 13:19:30 3DDETR.yaml][33m(utils_help.py 204)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saved !!!
[32m[2025-07-12 13:19:30 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2415%
[32m[2025-07-12 13:19:30 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2415%
[32m[2025-07-12 13:19:31 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [20/200][0/54]	eta 0:01:08 lr 0.000035	time 1.2694 (1.2694)	loss 6.9662 (6.9662)	miou 0.2420	grad_norm 40.2916 (40.2916)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:19:41 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [20/200][10/54]	eta 0:00:45 lr 0.000043	time 0.8803 (1.0436)	loss 8.8389 (8.0017)	miou 0.2353	grad_norm 19.1341 (46.9412)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:19:52 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [20/200][20/54]	eta 0:00:35 lr 0.000051	time 0.9395 (1.0524)	loss 9.3558 (8.2045)	miou 0.2282	grad_norm 23.5120 (36.3545)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:20:02 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [20/200][30/54]	eta 0:00:24 lr 0.000059	time 0.7940 (1.0375)	loss 9.3288 (7.8558)	miou 0.2279	grad_norm 20.7511 (34.0177)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:20:13 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [20/200][40/54]	eta 0:00:14 lr 0.000066	time 1.1083 (1.0566)	loss 4.7917 (7.9651)	miou 0.2203	grad_norm 23.5067 (32.1232)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:20:24 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [20/200][50/54]	eta 0:00:04 lr 0.000073	time 0.9919 (1.0611)	loss 9.6357 (8.2146)	miou 0.2215	grad_norm 24.9373 (31.3397)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:20:27 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 20 training takes 0:00:57
[32m[2025-07-12 13:20:27 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 13:20:37 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1703%
[32m[2025-07-12 13:20:37 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2415%
[32m[2025-07-12 13:20:38 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [21/200][0/54]	eta 0:01:16 lr 0.000076	time 1.4144 (1.4144)	loss 6.8687 (6.8687)	miou 0.1698	grad_norm 76.5100 (76.5100)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:20:48 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [21/200][10/54]	eta 0:00:45 lr 0.000082	time 1.1166 (1.0405)	loss 6.0091 (9.0122)	miou 0.1988	grad_norm 37.9047 (37.7958)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:20:59 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [21/200][20/54]	eta 0:00:37 lr 0.000088	time 1.3584 (1.0906)	loss 9.7554 (9.5555)	miou 0.1976	grad_norm 14.8220 (34.6281)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:21:10 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [21/200][30/54]	eta 0:00:25 lr 0.000093	time 0.6545 (1.0690)	loss 6.5620 (8.8924)	miou 0.2073	grad_norm 29.2655 (31.0802)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:21:20 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [21/200][40/54]	eta 0:00:14 lr 0.000096	time 1.1018 (1.0618)	loss 8.6896 (8.7115)	miou 0.2100	grad_norm 14.3089 (30.6170)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:21:30 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [21/200][50/54]	eta 0:00:04 lr 0.000099	time 0.7587 (1.0572)	loss 8.2680 (8.5621)	miou 0.2142	grad_norm 39.5933 (31.2273)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:21:34 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 21 training takes 0:00:57
[32m[2025-07-12 13:21:34 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 13:21:43 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1685%
[32m[2025-07-12 13:21:43 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2415%
[32m[2025-07-12 13:21:45 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [22/200][0/54]	eta 0:01:20 lr 0.000099	time 1.4882 (1.4882)	loss 7.6027 (7.6027)	miou 0.1688	grad_norm 70.5590 (70.5590)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:21:56 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [22/200][10/54]	eta 0:00:48 lr 0.000100	time 1.3508 (1.1129)	loss 6.3520 (8.7766)	miou 0.1847	grad_norm 19.4573 (40.0123)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:22:06 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [22/200][20/54]	eta 0:00:36 lr 0.000100	time 0.7204 (1.0807)	loss 8.7816 (8.3801)	miou 0.1998	grad_norm 34.4464 (34.4717)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:22:16 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [22/200][30/54]	eta 0:00:25 lr 0.000098	time 1.0534 (1.0519)	loss 15.1866 (8.3814)	miou 0.1998	grad_norm 22.3819 (37.7338)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:22:27 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [22/200][40/54]	eta 0:00:14 lr 0.000095	time 1.3803 (1.0685)	loss 7.1637 (8.2502)	miou 0.2016	grad_norm 17.1069 (35.1246)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:22:38 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [22/200][50/54]	eta 0:00:04 lr 0.000091	time 0.9717 (1.0661)	loss 12.8377 (8.2830)	miou 0.2032	grad_norm 33.7575 (33.6206)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:22:41 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 22 training takes 0:00:57
[32m[2025-07-12 13:22:41 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 13:22:51 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2341%
[32m[2025-07-12 13:22:51 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2415%
[32m[2025-07-12 13:22:53 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [23/200][0/54]	eta 0:01:36 lr 0.000089	time 1.7885 (1.7885)	loss 7.8389 (7.8389)	miou 0.2339	grad_norm 66.0619 (66.0619)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:23:03 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [23/200][10/54]	eta 0:00:46 lr 0.000084	time 0.9652 (1.0541)	loss 9.3353 (8.0599)	miou 0.2345	grad_norm 16.8323 (23.9438)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:23:13 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [23/200][20/54]	eta 0:00:35 lr 0.000077	time 0.8624 (1.0531)	loss 9.0719 (7.7562)	miou 0.2339	grad_norm 33.0152 (24.9264)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:23:24 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [23/200][30/54]	eta 0:00:25 lr 0.000071	time 1.1762 (1.0539)	loss 11.0649 (7.8358)	miou 0.2234	grad_norm 15.9229 (24.8518)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:23:34 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [23/200][40/54]	eta 0:00:14 lr 0.000063	time 1.2229 (1.0462)	loss 8.9183 (8.0533)	miou 0.2205	grad_norm 33.2667 (25.1054)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:23:45 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [23/200][50/54]	eta 0:00:04 lr 0.000055	time 1.2173 (1.0562)	loss 7.7034 (8.0142)	miou 0.2203	grad_norm 44.4544 (29.0523)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:23:49 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 23 training takes 0:00:57
[32m[2025-07-12 13:23:49 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
23
[32m[2025-07-12 13:23:58 3DDETR.yaml][33m(utils_help.py 202)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saving......
[32m[2025-07-12 13:23:59 3DDETR.yaml][33m(utils_help.py 204)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saved !!!
[32m[2025-07-12 13:23:59 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2484%
[32m[2025-07-12 13:23:59 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 13:24:00 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [24/200][0/54]	eta 0:01:24 lr 0.000052	time 1.5694 (1.5694)	loss 7.0573 (7.0573)	miou 0.2556	grad_norm 23.8991 (23.8991)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:24:10 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [24/200][10/54]	eta 0:00:46 lr 0.000045	time 1.3408 (1.0564)	loss 7.3800 (7.8506)	miou 0.2406	grad_norm 18.0240 (21.3279)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:24:22 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [24/200][20/54]	eta 0:00:37 lr 0.000037	time 1.4093 (1.1042)	loss 9.7585 (8.1827)	miou 0.2345	grad_norm 37.3130 (23.3801)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:24:31 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [24/200][30/54]	eta 0:00:25 lr 0.000029	time 0.9053 (1.0466)	loss 16.8298 (8.3984)	miou 0.2312	grad_norm 46.4257 (23.1312)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:24:42 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [24/200][40/54]	eta 0:00:14 lr 0.000023	time 0.8840 (1.0504)	loss 8.1592 (8.2413)	miou 0.2341	grad_norm 27.4963 (24.3881)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:24:53 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [24/200][50/54]	eta 0:00:04 lr 0.000016	time 1.1834 (1.0725)	loss 7.8596 (8.1448)	miou 0.2324	grad_norm 38.3485 (24.9642)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:24:57 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 24 training takes 0:00:58
[32m[2025-07-12 13:24:57 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 13:25:07 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2375%
[32m[2025-07-12 13:25:07 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 13:25:08 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [25/200][0/54]	eta 0:01:29 lr 0.000014	time 1.6651 (1.6651)	loss 5.9878 (5.9878)	miou 0.2413	grad_norm 19.0226 (19.0226)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:25:19 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [25/200][10/54]	eta 0:00:48 lr 0.000009	time 1.0562 (1.1097)	loss 8.4293 (8.4590)	miou 0.2319	grad_norm 22.2454 (23.0646)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:25:29 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [25/200][20/54]	eta 0:00:35 lr 0.000005	time 1.0785 (1.0466)	loss 12.6056 (8.8490)	miou 0.2112	grad_norm 30.0426 (22.5384)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:25:39 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [25/200][30/54]	eta 0:00:25 lr 0.000002	time 0.9027 (1.0447)	loss 9.6829 (8.6474)	miou 0.2093	grad_norm 50.3716 (23.0728)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:25:50 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [25/200][40/54]	eta 0:00:14 lr 0.000000	time 0.9209 (1.0611)	loss 11.0378 (8.6312)	miou 0.2168	grad_norm 32.7338 (22.7350)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:26:02 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [25/200][50/54]	eta 0:00:04 lr 0.000000	time 1.2143 (1.0936)	loss 5.4713 (8.3056)	miou 0.2166	grad_norm 109.1166 (25.3884)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:26:06 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 25 training takes 0:00:58
[32m[2025-07-12 13:26:06 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 13:26:15 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2277%
[32m[2025-07-12 13:26:15 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 13:26:17 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [26/200][0/54]	eta 0:01:29 lr 0.000000	time 1.6616 (1.6616)	loss 6.0378 (6.0378)	miou 0.2228	grad_norm 19.9743 (19.9743)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:26:27 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [26/200][10/54]	eta 0:00:47 lr 0.000001	time 0.8562 (1.0720)	loss 6.0889 (7.5930)	miou 0.2215	grad_norm 44.6942 (26.9321)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:26:38 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [26/200][20/54]	eta 0:00:36 lr 0.000004	time 0.9660 (1.0632)	loss 6.3343 (7.5179)	miou 0.2312	grad_norm 46.2872 (28.2685)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:26:49 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [26/200][30/54]	eta 0:00:25 lr 0.000007	time 1.0712 (1.0832)	loss 9.4055 (7.5073)	miou 0.2280	grad_norm 19.9895 (25.3997)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:26:59 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [26/200][40/54]	eta 0:00:14 lr 0.000012	time 0.8466 (1.0617)	loss 10.6787 (7.9194)	miou 0.2269	grad_norm 18.6075 (25.6923)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:27:10 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [26/200][50/54]	eta 0:00:04 lr 0.000018	time 1.0482 (1.0804)	loss 5.9896 (7.8790)	miou 0.2319	grad_norm 12.3498 (24.9669)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:27:14 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 26 training takes 0:00:58
[32m[2025-07-12 13:27:14 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 13:27:23 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2191%
[32m[2025-07-12 13:27:23 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 13:27:25 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [27/200][0/54]	eta 0:01:46 lr 0.000020	time 1.9726 (1.9726)	loss 5.3135 (5.3135)	miou 0.2211	grad_norm 26.9630 (26.9630)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:27:36 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [27/200][10/54]	eta 0:00:51 lr 0.000027	time 1.0747 (1.1647)	loss 6.9834 (7.9322)	miou 0.2239	grad_norm 17.2987 (28.1677)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:27:47 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [27/200][20/54]	eta 0:00:37 lr 0.000034	time 1.2301 (1.1042)	loss 8.7462 (7.9398)	miou 0.2261	grad_norm 22.9034 (24.7617)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:27:58 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [27/200][30/54]	eta 0:00:26 lr 0.000041	time 1.0961 (1.1163)	loss 6.3774 (7.9775)	miou 0.2331	grad_norm 15.3562 (24.6315)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:28:08 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [27/200][40/54]	eta 0:00:15 lr 0.000049	time 0.9043 (1.0888)	loss 4.3752 (7.9275)	miou 0.2311	grad_norm 48.3064 (25.9782)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:28:18 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [27/200][50/54]	eta 0:00:04 lr 0.000057	time 0.9439 (1.0734)	loss 6.9123 (7.8991)	miou 0.2334	grad_norm 20.7982 (26.7400)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:28:22 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 27 training takes 0:00:58
[32m[2025-07-12 13:28:22 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 13:28:31 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2371%
[32m[2025-07-12 13:28:31 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 13:28:33 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [28/200][0/54]	eta 0:01:25 lr 0.000060	time 1.5790 (1.5790)	loss 6.4932 (6.4932)	miou 0.2388	grad_norm 18.2208 (18.2208)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:28:43 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [28/200][10/54]	eta 0:00:45 lr 0.000068	time 0.9231 (1.0358)	loss 8.1482 (8.4555)	miou 0.2298	grad_norm 20.3472 (25.9729)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:28:53 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [28/200][20/54]	eta 0:00:34 lr 0.000075	time 1.2307 (1.0283)	loss 7.3089 (8.0429)	miou 0.2305	grad_norm 21.6120 (23.5672)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:29:04 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [28/200][30/54]	eta 0:00:25 lr 0.000081	time 0.8930 (1.0541)	loss 6.7423 (7.7116)	miou 0.2321	grad_norm 39.1986 (23.5719)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:29:14 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [28/200][40/54]	eta 0:00:14 lr 0.000087	time 0.9291 (1.0461)	loss 8.8633 (7.8470)	miou 0.2344	grad_norm 15.9283 (23.5438)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:29:25 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [28/200][50/54]	eta 0:00:04 lr 0.000092	time 0.9557 (1.0496)	loss 4.2103 (7.8996)	miou 0.2315	grad_norm 21.9434 (23.7940)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:29:28 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 28 training takes 0:00:57
[32m[2025-07-12 13:29:28 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 13:29:38 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1907%
[32m[2025-07-12 13:29:38 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 13:29:40 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [29/200][0/54]	eta 0:01:35 lr 0.000093	time 1.7739 (1.7739)	loss 6.5741 (6.5741)	miou 0.1956	grad_norm 13.4212 (13.4212)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:29:50 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [29/200][10/54]	eta 0:00:46 lr 0.000097	time 0.9069 (1.0467)	loss 7.3965 (8.7556)	miou 0.1986	grad_norm 45.8066 (22.7355)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:30:01 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [29/200][20/54]	eta 0:00:36 lr 0.000099	time 0.8919 (1.0711)	loss 7.4153 (8.4236)	miou 0.1987	grad_norm 14.7669 (22.7131)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:30:11 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [29/200][30/54]	eta 0:00:25 lr 0.000100	time 0.9104 (1.0421)	loss 6.0118 (7.9641)	miou 0.2061	grad_norm 47.5682 (25.0965)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:30:21 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [29/200][40/54]	eta 0:00:14 lr 0.000100	time 0.9204 (1.0488)	loss 5.9614 (8.0365)	miou 0.2153	grad_norm 40.2265 (24.5581)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:30:32 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [29/200][50/54]	eta 0:00:04 lr 0.000098	time 1.1046 (1.0604)	loss 10.7493 (8.0383)	miou 0.2201	grad_norm 30.1899 (25.1125)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:30:35 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 29 training takes 0:00:57
[32m[2025-07-12 13:30:35 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 13:30:45 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1949%
[32m[2025-07-12 13:30:45 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 13:30:47 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [30/200][0/54]	eta 0:01:15 lr 0.000097	time 1.4044 (1.4044)	loss 10.5961 (10.5961)	miou 0.1934	grad_norm 22.1337 (22.1337)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:30:57 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [30/200][10/54]	eta 0:00:45 lr 0.000094	time 1.0502 (1.0244)	loss 11.7981 (8.5120)	miou 0.2058	grad_norm 16.2755 (35.1737)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:31:07 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [30/200][20/54]	eta 0:00:35 lr 0.000090	time 1.2253 (1.0502)	loss 5.8820 (7.7296)	miou 0.2215	grad_norm 11.6094 (30.4851)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:31:18 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [30/200][30/54]	eta 0:00:25 lr 0.000085	time 0.9054 (1.0451)	loss 9.2097 (7.6834)	miou 0.2222	grad_norm 20.1499 (31.3491)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:31:28 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [30/200][40/54]	eta 0:00:14 lr 0.000079	time 0.8968 (1.0539)	loss 12.2950 (7.8219)	miou 0.2227	grad_norm 22.0443 (28.6526)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:31:40 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [30/200][50/54]	eta 0:00:04 lr 0.000072	time 1.1491 (1.0796)	loss 8.5005 (7.8463)	miou 0.2203	grad_norm 19.9642 (26.7992)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:31:44 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 30 training takes 0:00:58
[32m[2025-07-12 13:31:44 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 13:31:54 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1712%
[32m[2025-07-12 13:31:54 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 13:31:55 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [31/200][0/54]	eta 0:01:41 lr 0.000069	time 1.8818 (1.8818)	loss 6.4008 (6.4008)	miou 0.1847	grad_norm 68.6987 (68.6987)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:32:06 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [31/200][10/54]	eta 0:00:51 lr 0.000062	time 1.3070 (1.1618)	loss 8.3844 (8.2065)	miou 0.2089	grad_norm 20.3484 (32.4107)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:32:17 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [31/200][20/54]	eta 0:00:37 lr 0.000054	time 1.0331 (1.1056)	loss 7.7032 (7.5333)	miou 0.2184	grad_norm 11.6440 (26.7845)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:32:27 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [31/200][30/54]	eta 0:00:25 lr 0.000046	time 0.8009 (1.0646)	loss 8.3335 (7.4715)	miou 0.2273	grad_norm 16.2625 (24.4607)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:32:37 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [31/200][40/54]	eta 0:00:14 lr 0.000038	time 0.8428 (1.0554)	loss 5.8199 (7.5317)	miou 0.2360	grad_norm 12.0761 (23.6568)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:32:48 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [31/200][50/54]	eta 0:00:04 lr 0.000031	time 1.0255 (1.0610)	loss 6.7786 (7.6435)	miou 0.2335	grad_norm 19.2496 (22.4650)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:32:52 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 31 training takes 0:00:57
[32m[2025-07-12 13:32:52 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 13:33:01 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2029%
[32m[2025-07-12 13:33:01 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 13:33:03 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [32/200][0/54]	eta 0:01:44 lr 0.000028	time 1.9385 (1.9385)	loss 5.2288 (5.2288)	miou 0.2223	grad_norm 46.5850 (46.5850)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:33:15 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [32/200][10/54]	eta 0:00:53 lr 0.000021	time 1.1327 (1.2064)	loss 6.4660 (7.7319)	miou 0.2406	grad_norm 19.0259 (25.1139)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:33:25 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [32/200][20/54]	eta 0:00:38 lr 0.000015	time 1.2420 (1.1394)	loss 10.7486 (7.9496)	miou 0.2415	grad_norm 18.9489 (22.8122)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:33:35 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [32/200][30/54]	eta 0:00:26 lr 0.000010	time 0.9899 (1.1006)	loss 5.1511 (7.6737)	miou 0.2480	grad_norm 23.1634 (21.1768)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:33:46 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [32/200][40/54]	eta 0:00:15 lr 0.000006	time 1.0078 (1.0920)	loss 7.1531 (7.5680)	miou 0.2502	grad_norm 14.8655 (20.7571)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:33:57 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [32/200][50/54]	eta 0:00:04 lr 0.000003	time 1.2134 (1.1003)	loss 9.3224 (7.4451)	miou 0.2517	grad_norm 20.8222 (20.8820)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:34:01 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 32 training takes 0:00:59
[32m[2025-07-12 13:34:01 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 13:34:11 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1752%
[32m[2025-07-12 13:34:11 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 13:34:12 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [33/200][0/54]	eta 0:01:13 lr 0.000002	time 1.3546 (1.3546)	loss 6.2369 (6.2369)	miou 0.1835	grad_norm 13.9742 (13.9742)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:34:22 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [33/200][10/54]	eta 0:00:46 lr 0.000000	time 0.9874 (1.0539)	loss 7.0080 (7.7763)	miou 0.2100	grad_norm 28.2999 (20.1719)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:34:33 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [33/200][20/54]	eta 0:00:36 lr 0.000000	time 0.9136 (1.0684)	loss 8.7840 (7.9694)	miou 0.2200	grad_norm 26.2951 (19.6052)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:34:44 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [33/200][30/54]	eta 0:00:25 lr 0.000001	time 0.9443 (1.0813)	loss 9.9901 (7.6376)	miou 0.2281	grad_norm 10.9387 (20.8868)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:34:56 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [33/200][40/54]	eta 0:00:15 lr 0.000003	time 1.0301 (1.1084)	loss 8.2566 (7.6906)	miou 0.2320	grad_norm 16.2856 (21.3236)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:35:06 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [33/200][50/54]	eta 0:00:04 lr 0.000007	time 1.0919 (1.0843)	loss 5.9167 (7.9032)	miou 0.2318	grad_norm 25.6803 (21.6115)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:35:09 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 33 training takes 0:00:58
[32m[2025-07-12 13:35:09 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 13:35:18 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1983%
[32m[2025-07-12 13:35:18 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 13:35:20 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [34/200][0/54]	eta 0:01:18 lr 0.000008	time 1.4583 (1.4583)	loss 8.4525 (8.4525)	miou 0.2032	grad_norm 18.5747 (18.5747)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:35:31 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [34/200][10/54]	eta 0:00:50 lr 0.000013	time 0.8542 (1.1517)	loss 11.4786 (7.8346)	miou 0.2194	grad_norm 17.4269 (25.0848)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:35:41 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [34/200][20/54]	eta 0:00:36 lr 0.000019	time 0.9472 (1.0809)	loss 9.6363 (7.6858)	miou 0.2225	grad_norm 13.5524 (23.8996)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:35:52 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [34/200][30/54]	eta 0:00:25 lr 0.000025	time 0.7760 (1.0727)	loss 6.6651 (7.3518)	miou 0.2382	grad_norm 19.9056 (23.2416)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:36:02 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [34/200][40/54]	eta 0:00:14 lr 0.000032	time 1.1821 (1.0648)	loss 10.0671 (7.7818)	miou 0.2430	grad_norm 14.6704 (23.2690)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:36:13 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [34/200][50/54]	eta 0:00:04 lr 0.000040	time 1.0496 (1.0694)	loss 6.5442 (7.7744)	miou 0.2430	grad_norm 18.5722 (22.4077)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:36:16 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 34 training takes 0:00:57
[32m[2025-07-12 13:36:16 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 13:36:26 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1695%
[32m[2025-07-12 13:36:26 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 13:36:28 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [35/200][0/54]	eta 0:01:29 lr 0.000043	time 1.6666 (1.6666)	loss 7.3505 (7.3505)	miou 0.1864	grad_norm 19.2021 (19.2021)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:36:38 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [35/200][10/54]	eta 0:00:45 lr 0.000051	time 0.9073 (1.0427)	loss 7.2569 (7.8025)	miou 0.1973	grad_norm 23.9024 (22.0103)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:36:49 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [35/200][20/54]	eta 0:00:36 lr 0.000059	time 1.0952 (1.0801)	loss 9.2463 (7.4944)	miou 0.2132	grad_norm 12.4574 (23.2820)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:36:59 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [35/200][30/54]	eta 0:00:25 lr 0.000066	time 0.7892 (1.0576)	loss 9.0689 (7.4156)	miou 0.2265	grad_norm 29.5726 (22.8365)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:37:09 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [35/200][40/54]	eta 0:00:14 lr 0.000073	time 0.7498 (1.0533)	loss 7.3695 (7.5399)	miou 0.2348	grad_norm 12.4664 (21.6420)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:37:21 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [35/200][50/54]	eta 0:00:04 lr 0.000080	time 1.0674 (1.0724)	loss 8.7419 (7.5572)	miou 0.2345	grad_norm 15.1909 (22.0718)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:37:24 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 35 training takes 0:00:58
[32m[2025-07-12 13:37:24 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 13:37:34 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1541%
[32m[2025-07-12 13:37:34 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 13:37:35 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [36/200][0/54]	eta 0:01:14 lr 0.000082	time 1.3834 (1.3834)	loss 10.5696 (10.5696)	miou 0.1550	grad_norm 17.8836 (17.8836)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:37:45 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [36/200][10/54]	eta 0:00:44 lr 0.000088	time 0.9877 (1.0053)	loss 8.1586 (7.9471)	miou 0.1931	grad_norm 12.4629 (24.8133)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:37:55 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [36/200][20/54]	eta 0:00:34 lr 0.000093	time 0.9243 (1.0133)	loss 6.5818 (8.1771)	miou 0.2087	grad_norm 21.1103 (23.1531)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:38:06 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [36/200][30/54]	eta 0:00:25 lr 0.000096	time 0.9788 (1.0425)	loss 9.2501 (8.2462)	miou 0.2094	grad_norm 16.6917 (22.1465)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:38:17 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [36/200][40/54]	eta 0:00:14 lr 0.000099	time 1.0528 (1.0543)	loss 5.2425 (7.9130)	miou 0.2217	grad_norm 18.2743 (21.9855)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:38:27 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [36/200][50/54]	eta 0:00:04 lr 0.000100	time 0.8140 (1.0301)	loss 8.8005 (7.9162)	miou 0.2221	grad_norm 21.4706 (22.3171)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:38:30 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 36 training takes 0:00:55
[32m[2025-07-12 13:38:30 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 13:38:40 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1605%
[32m[2025-07-12 13:38:40 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 13:38:41 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [37/200][0/54]	eta 0:01:02 lr 0.000100	time 1.1653 (1.1653)	loss 6.4275 (6.4275)	miou 0.1631	grad_norm 34.7475 (34.7475)	loss_scale 65536.0000 (65536.0000)	mem 3265MB
[32m[2025-07-12 13:38:51 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [37/200][10/54]	eta 0:00:43 lr 0.000100	time 1.0439 (0.9990)	loss 8.7785 (7.6977)	miou 0.1937	grad_norm 14.1059 (21.8245)	loss_scale 131072.0000 (125114.1818)	mem 3265MB
[32m[2025-07-12 13:39:02 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [37/200][20/54]	eta 0:00:35 lr 0.000098	time 1.1224 (1.0302)	loss 6.9189 (7.5740)	miou 0.2182	grad_norm 23.5155 (20.0346)	loss_scale 131072.0000 (127951.2381)	mem 3265MB
[32m[2025-07-12 13:39:13 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [37/200][30/54]	eta 0:00:25 lr 0.000095	time 1.1407 (1.0551)	loss 10.3257 (7.6492)	miou 0.2218	grad_norm 35.2248 (22.5452)	loss_scale 131072.0000 (128957.9355)	mem 3265MB
[32m[2025-07-12 13:39:23 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [37/200][40/54]	eta 0:00:14 lr 0.000091	time 1.0510 (1.0588)	loss 6.6089 (7.7262)	miou 0.2305	grad_norm 24.2412 (22.4559)	loss_scale 131072.0000 (129473.5610)	mem 3265MB
[32m[2025-07-12 13:39:34 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [37/200][50/54]	eta 0:00:04 lr 0.000086	time 1.4988 (1.0570)	loss 7.0886 (7.8958)	miou 0.2361	grad_norm 22.7240 (21.4667)	loss_scale 131072.0000 (129786.9804)	mem 3265MB
[32m[2025-07-12 13:39:37 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 37 training takes 0:00:56
[32m[2025-07-12 13:39:37 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 13:39:46 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1856%
[32m[2025-07-12 13:39:46 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 13:39:48 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [38/200][0/54]	eta 0:01:34 lr 0.000084	time 1.7447 (1.7447)	loss 6.1964 (6.1964)	miou 0.1908	grad_norm 16.4415 (16.4415)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:39:59 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [38/200][10/54]	eta 0:00:50 lr 0.000077	time 1.1483 (1.1444)	loss 9.3763 (7.4868)	miou 0.2114	grad_norm 35.2980 (27.4712)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:40:10 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [38/200][20/54]	eta 0:00:37 lr 0.000071	time 1.0925 (1.1135)	loss 6.8030 (7.6918)	miou 0.2232	grad_norm 12.2425 (24.0324)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:40:20 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [38/200][30/54]	eta 0:00:25 lr 0.000063	time 0.9487 (1.0796)	loss 7.5078 (7.6849)	miou 0.2233	grad_norm 26.0819 (22.8071)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:40:31 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [38/200][40/54]	eta 0:00:15 lr 0.000055	time 1.3984 (1.0971)	loss 9.5673 (7.6829)	miou 0.2222	grad_norm 12.2993 (24.1576)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:40:42 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [38/200][50/54]	eta 0:00:04 lr 0.000048	time 1.0103 (1.0920)	loss 7.8537 (7.7707)	miou 0.2301	grad_norm 43.8881 (24.1195)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:40:46 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 38 training takes 0:00:59
[32m[2025-07-12 13:40:46 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 13:40:55 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1824%
[32m[2025-07-12 13:40:55 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 13:40:57 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [39/200][0/54]	eta 0:01:21 lr 0.000045	time 1.5111 (1.5111)	loss 6.6390 (6.6390)	miou 0.1799	grad_norm 30.0935 (30.0935)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:41:07 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [39/200][10/54]	eta 0:00:44 lr 0.000037	time 1.1243 (1.0065)	loss 7.8229 (7.6236)	miou 0.2192	grad_norm 21.5238 (25.3125)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:41:17 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [39/200][20/54]	eta 0:00:34 lr 0.000029	time 1.2188 (1.0134)	loss 7.2280 (8.0735)	miou 0.2395	grad_norm 12.2357 (21.4101)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:41:27 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [39/200][30/54]	eta 0:00:24 lr 0.000023	time 0.8902 (1.0214)	loss 6.0608 (7.7984)	miou 0.2464	grad_norm 37.1265 (21.5177)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:41:39 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [39/200][40/54]	eta 0:00:14 lr 0.000016	time 1.2473 (1.0557)	loss 8.5361 (7.6433)	miou 0.2526	grad_norm 10.9859 (21.7437)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:41:49 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [39/200][50/54]	eta 0:00:04 lr 0.000011	time 0.9525 (1.0465)	loss 5.3058 (7.5352)	miou 0.2487	grad_norm 29.4753 (21.6254)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:41:52 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 39 training takes 0:00:56
[32m[2025-07-12 13:41:52 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 13:42:02 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2012%
[32m[2025-07-12 13:42:02 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 13:42:03 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [40/200][0/54]	eta 0:01:12 lr 0.000009	time 1.3469 (1.3469)	loss 8.2464 (8.2464)	miou 0.2048	grad_norm 17.9521 (17.9521)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:42:15 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [40/200][10/54]	eta 0:00:49 lr 0.000005	time 1.3768 (1.1340)	loss 8.7731 (8.6935)	miou 0.2082	grad_norm 24.5712 (27.7310)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:42:24 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [40/200][20/54]	eta 0:00:35 lr 0.000002	time 0.9621 (1.0509)	loss 9.4613 (7.9876)	miou 0.2285	grad_norm 26.7346 (22.4565)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:42:35 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [40/200][30/54]	eta 0:00:25 lr 0.000000	time 1.0645 (1.0573)	loss 6.3543 (8.0199)	miou 0.2374	grad_norm 11.5540 (20.8391)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:42:45 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [40/200][40/54]	eta 0:00:14 lr 0.000000	time 0.9911 (1.0521)	loss 7.3199 (7.9297)	miou 0.2420	grad_norm 14.0551 (20.9277)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:42:56 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [40/200][50/54]	eta 0:00:04 lr 0.000001	time 1.1984 (1.0658)	loss 5.3988 (7.8204)	miou 0.2440	grad_norm 14.1973 (20.9790)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:43:00 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 40 training takes 0:00:57
[32m[2025-07-12 13:43:00 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 13:43:10 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1813%
[32m[2025-07-12 13:43:10 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 13:43:11 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [41/200][0/54]	eta 0:01:17 lr 0.000001	time 1.4426 (1.4426)	loss 6.2197 (6.2197)	miou 0.1851	grad_norm 21.8757 (21.8757)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:43:22 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [41/200][10/54]	eta 0:00:48 lr 0.000004	time 1.1028 (1.1116)	loss 15.0516 (8.8057)	miou 0.2214	grad_norm 24.6074 (16.8987)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:43:32 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [41/200][20/54]	eta 0:00:36 lr 0.000007	time 1.3684 (1.0637)	loss 8.6032 (8.2198)	miou 0.2350	grad_norm 19.7650 (19.0607)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:43:42 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [41/200][30/54]	eta 0:00:25 lr 0.000012	time 0.7903 (1.0491)	loss 8.8354 (7.9498)	miou 0.2430	grad_norm 25.0315 (19.8918)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:43:52 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [41/200][40/54]	eta 0:00:14 lr 0.000018	time 0.9410 (1.0460)	loss 10.2265 (8.2614)	miou 0.2458	grad_norm 20.8508 (20.2158)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:44:03 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [41/200][50/54]	eta 0:00:04 lr 0.000024	time 0.9486 (1.0449)	loss 6.0096 (8.1931)	miou 0.2394	grad_norm 26.3060 (21.1177)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:44:07 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 41 training takes 0:00:57
[32m[2025-07-12 13:44:07 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 13:44:17 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1988%
[32m[2025-07-12 13:44:17 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 13:44:18 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [42/200][0/54]	eta 0:01:05 lr 0.000027	time 1.2166 (1.2166)	loss 5.5283 (5.5283)	miou 0.2150	grad_norm 14.5245 (14.5245)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:44:28 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [42/200][10/54]	eta 0:00:43 lr 0.000034	time 0.9281 (0.9998)	loss 4.8698 (7.3505)	miou 0.2257	grad_norm 26.1123 (18.4403)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:44:38 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [42/200][20/54]	eta 0:00:34 lr 0.000041	time 0.9880 (1.0286)	loss 8.7708 (7.6777)	miou 0.2398	grad_norm 12.5006 (17.3774)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:44:49 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [42/200][30/54]	eta 0:00:25 lr 0.000049	time 0.8719 (1.0470)	loss 7.0073 (7.6179)	miou 0.2506	grad_norm 19.2585 (20.6772)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:45:00 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [42/200][40/54]	eta 0:00:14 lr 0.000057	time 0.9841 (1.0607)	loss 6.9400 (7.6754)	miou 0.2478	grad_norm 20.0020 (20.4335)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:45:11 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [42/200][50/54]	eta 0:00:04 lr 0.000065	time 0.9260 (1.0670)	loss 7.1325 (7.5916)	miou 0.2490	grad_norm 18.6011 (21.1055)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:45:14 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 42 training takes 0:00:57
[32m[2025-07-12 13:45:14 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 13:45:24 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2359%
[32m[2025-07-12 13:45:24 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 13:45:26 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [43/200][0/54]	eta 0:01:31 lr 0.000068	time 1.6995 (1.6995)	loss 5.6162 (5.6162)	miou 0.2434	grad_norm 15.3207 (15.3207)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:45:37 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [43/200][10/54]	eta 0:00:49 lr 0.000075	time 1.0811 (1.1233)	loss 10.1353 (9.1531)	miou 0.2375	grad_norm 15.0772 (20.3058)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:45:48 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [43/200][20/54]	eta 0:00:38 lr 0.000081	time 1.3623 (1.1202)	loss 7.7331 (8.5657)	miou 0.2481	grad_norm 15.5142 (19.7446)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:45:58 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [43/200][30/54]	eta 0:00:26 lr 0.000087	time 0.8909 (1.0852)	loss 13.3709 (8.4194)	miou 0.2453	grad_norm 23.7345 (21.9452)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:46:08 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [43/200][40/54]	eta 0:00:15 lr 0.000092	time 1.1061 (1.0721)	loss 6.1528 (8.1641)	miou 0.2423	grad_norm 70.0568 (22.3781)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:46:19 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [43/200][50/54]	eta 0:00:04 lr 0.000096	time 0.9849 (1.0654)	loss 5.6564 (7.9817)	miou 0.2406	grad_norm 12.6522 (27.7832)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:46:22 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 43 training takes 0:00:57
[32m[2025-07-12 13:46:22 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 13:46:32 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2126%
[32m[2025-07-12 13:46:32 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 13:46:33 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [44/200][0/54]	eta 0:01:28 lr 0.000097	time 1.6462 (1.6462)	loss 6.5344 (6.5344)	miou 0.2129	grad_norm 13.9538 (13.9538)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:46:44 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [44/200][10/54]	eta 0:00:48 lr 0.000099	time 0.6393 (1.0989)	loss 12.2171 (7.4724)	miou 0.2184	grad_norm 23.5938 (21.3203)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:46:54 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [44/200][20/54]	eta 0:00:35 lr 0.000100	time 0.9121 (1.0477)	loss 8.7086 (7.2856)	miou 0.2372	grad_norm 31.0284 (21.8217)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:47:04 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [44/200][30/54]	eta 0:00:24 lr 0.000100	time 1.1610 (1.0308)	loss 7.6572 (7.6402)	miou 0.2374	grad_norm 34.9788 (21.4261)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:47:13 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [44/200][40/54]	eta 0:00:14 lr 0.000098	time 0.8369 (1.0146)	loss 8.7555 (7.7348)	miou 0.2330	grad_norm 27.8853 (21.4412)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:47:24 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [44/200][50/54]	eta 0:00:04 lr 0.000096	time 1.2774 (1.0307)	loss 7.1346 (7.6764)	miou 0.2423	grad_norm 10.5663 (21.0345)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:47:28 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 44 training takes 0:00:56
[32m[2025-07-12 13:47:28 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 13:47:38 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1890%
[32m[2025-07-12 13:47:38 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 13:47:40 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [45/200][0/54]	eta 0:01:29 lr 0.000094	time 1.6562 (1.6562)	loss 5.8614 (5.8614)	miou 0.1920	grad_norm 11.4949 (11.4949)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:47:51 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [45/200][10/54]	eta 0:00:50 lr 0.000090	time 1.3734 (1.1445)	loss 6.4772 (7.0556)	miou 0.1988	grad_norm 21.2463 (19.2807)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:48:01 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [45/200][20/54]	eta 0:00:37 lr 0.000085	time 1.2415 (1.1167)	loss 9.3945 (7.4588)	miou 0.2169	grad_norm 12.1305 (20.2517)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:48:12 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [45/200][30/54]	eta 0:00:25 lr 0.000079	time 0.9320 (1.0809)	loss 8.0419 (7.5620)	miou 0.2236	grad_norm 33.0656 (19.4129)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:48:22 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [45/200][40/54]	eta 0:00:14 lr 0.000072	time 1.1304 (1.0653)	loss 7.1571 (7.4468)	miou 0.2240	grad_norm 20.8576 (19.0970)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:48:32 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [45/200][50/54]	eta 0:00:04 lr 0.000065	time 0.8934 (1.0638)	loss 10.3340 (7.6490)	miou 0.2208	grad_norm 18.0601 (19.9656)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:48:35 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 45 training takes 0:00:57
[32m[2025-07-12 13:48:35 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 13:48:45 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2022%
[32m[2025-07-12 13:48:45 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 13:48:48 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [46/200][0/54]	eta 0:02:02 lr 0.000062	time 2.2670 (2.2670)	loss 5.1611 (5.1611)	miou 0.2071	grad_norm 10.4962 (10.4962)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:48:58 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [46/200][10/54]	eta 0:00:51 lr 0.000054	time 1.3415 (1.1620)	loss 6.1212 (7.0708)	miou 0.2406	grad_norm 17.7318 (19.4546)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:49:08 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [46/200][20/54]	eta 0:00:36 lr 0.000046	time 1.1359 (1.0782)	loss 15.2006 (7.7438)	miou 0.2350	grad_norm 14.0739 (19.8536)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:49:18 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [46/200][30/54]	eta 0:00:25 lr 0.000038	time 0.7341 (1.0647)	loss 5.8819 (7.5381)	miou 0.2346	grad_norm 14.9585 (19.7346)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:49:29 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [46/200][40/54]	eta 0:00:15 lr 0.000031	time 0.9615 (1.0722)	loss 7.0608 (7.6853)	miou 0.2300	grad_norm 15.9497 (19.6825)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:49:40 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [46/200][50/54]	eta 0:00:04 lr 0.000024	time 0.9524 (1.0779)	loss 7.0615 (7.7237)	miou 0.2283	grad_norm 31.1547 (19.9960)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:49:44 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 46 training takes 0:00:58
[32m[2025-07-12 13:49:44 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 13:49:53 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1850%
[32m[2025-07-12 13:49:53 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 13:49:55 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [47/200][0/54]	eta 0:01:11 lr 0.000021	time 1.3247 (1.3247)	loss 8.9856 (8.9856)	miou 0.1842	grad_norm 15.5988 (15.5988)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:50:04 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [47/200][10/54]	eta 0:00:43 lr 0.000015	time 0.9824 (0.9863)	loss 8.0727 (8.2477)	miou 0.2209	grad_norm 15.4790 (22.4738)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:50:15 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [47/200][20/54]	eta 0:00:35 lr 0.000010	time 0.9410 (1.0516)	loss 11.8686 (7.9290)	miou 0.2335	grad_norm 17.2855 (18.6246)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:50:26 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [47/200][30/54]	eta 0:00:25 lr 0.000006	time 0.8906 (1.0599)	loss 10.3124 (7.8329)	miou 0.2352	grad_norm 24.0673 (18.4258)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:50:37 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [47/200][40/54]	eta 0:00:14 lr 0.000003	time 1.2953 (1.0711)	loss 7.7912 (7.6367)	miou 0.2461	grad_norm 25.0605 (18.4890)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:50:48 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [47/200][50/54]	eta 0:00:04 lr 0.000001	time 1.1140 (1.0726)	loss 5.7213 (7.4970)	miou 0.2456	grad_norm 10.1895 (18.7863)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:50:51 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 47 training takes 0:00:57
[32m[2025-07-12 13:50:51 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 13:51:01 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1885%
[32m[2025-07-12 13:51:01 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 13:51:02 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [48/200][0/54]	eta 0:01:17 lr 0.000000	time 1.4413 (1.4413)	loss 8.4117 (8.4117)	miou 0.1891	grad_norm 35.3627 (35.3627)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:51:12 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [48/200][10/54]	eta 0:00:44 lr 0.000000	time 0.8719 (1.0148)	loss 9.2928 (7.3562)	miou 0.2199	grad_norm 14.9977 (20.1609)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:51:23 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [48/200][20/54]	eta 0:00:35 lr 0.000001	time 1.0327 (1.0437)	loss 5.9752 (7.7850)	miou 0.2277	grad_norm 14.8832 (18.9914)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:51:34 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [48/200][30/54]	eta 0:00:25 lr 0.000003	time 1.3548 (1.0629)	loss 6.4942 (7.5499)	miou 0.2336	grad_norm 21.0475 (19.5293)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:51:44 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [48/200][40/54]	eta 0:00:14 lr 0.000007	time 0.8825 (1.0522)	loss 5.7208 (7.3976)	miou 0.2476	grad_norm 21.1346 (20.0987)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:51:55 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [48/200][50/54]	eta 0:00:04 lr 0.000011	time 1.2301 (1.0683)	loss 5.2172 (7.3612)	miou 0.2444	grad_norm 22.9738 (19.8298)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:51:58 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 48 training takes 0:00:57
[32m[2025-07-12 13:51:58 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 13:52:08 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1756%
[32m[2025-07-12 13:52:08 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 13:52:09 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [49/200][0/54]	eta 0:01:22 lr 0.000013	time 1.5201 (1.5201)	loss 6.8221 (6.8221)	miou 0.1782	grad_norm 19.5010 (19.5010)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:52:21 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [49/200][10/54]	eta 0:00:50 lr 0.000019	time 1.3841 (1.1469)	loss 8.6407 (7.9771)	miou 0.2218	grad_norm 23.1262 (18.7633)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:52:31 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [49/200][20/54]	eta 0:00:36 lr 0.000025	time 0.9986 (1.0872)	loss 6.7416 (7.8299)	miou 0.2333	grad_norm 14.2149 (18.5464)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:52:41 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [49/200][30/54]	eta 0:00:25 lr 0.000032	time 0.9345 (1.0833)	loss 5.7110 (7.4681)	miou 0.2368	grad_norm 24.0064 (18.2359)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:52:52 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [49/200][40/54]	eta 0:00:15 lr 0.000040	time 1.2554 (1.0754)	loss 6.4787 (7.3580)	miou 0.2457	grad_norm 38.7155 (18.8050)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:53:03 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [49/200][50/54]	eta 0:00:04 lr 0.000048	time 1.2716 (1.0771)	loss 5.9043 (7.3106)	miou 0.2524	grad_norm 18.5076 (18.2034)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:53:06 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 49 training takes 0:00:58
[32m[2025-07-12 13:53:06 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 13:53:16 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2028%
[32m[2025-07-12 13:53:16 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 13:53:17 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [50/200][0/54]	eta 0:01:07 lr 0.000051	time 1.2476 (1.2476)	loss 5.5389 (5.5389)	miou 0.2065	grad_norm 21.5694 (21.5694)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:53:27 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [50/200][10/54]	eta 0:00:41 lr 0.000059	time 0.9527 (0.9543)	loss 9.9833 (8.3141)	miou 0.2137	grad_norm 13.4953 (19.0276)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:53:38 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [50/200][20/54]	eta 0:00:34 lr 0.000066	time 0.9912 (1.0229)	loss 8.1829 (7.8364)	miou 0.2331	grad_norm 14.4551 (20.0160)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:53:48 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [50/200][30/54]	eta 0:00:24 lr 0.000073	time 0.8985 (1.0394)	loss 6.6526 (7.9414)	miou 0.2388	grad_norm 22.6586 (19.6015)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:53:59 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [50/200][40/54]	eta 0:00:14 lr 0.000080	time 0.8580 (1.0465)	loss 6.9885 (7.7159)	miou 0.2456	grad_norm 20.7324 (20.1440)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:54:10 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [50/200][50/54]	eta 0:00:04 lr 0.000086	time 1.0339 (1.0557)	loss 7.0570 (7.6871)	miou 0.2436	grad_norm 12.4073 (20.2737)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:54:13 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 50 training takes 0:00:56
[32m[2025-07-12 13:54:13 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 13:54:23 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2048%
[32m[2025-07-12 13:54:23 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 13:54:24 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [51/200][0/54]	eta 0:01:15 lr 0.000088	time 1.3950 (1.3950)	loss 9.9089 (9.9089)	miou 0.2164	grad_norm 34.4937 (34.4937)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:54:34 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [51/200][10/54]	eta 0:00:45 lr 0.000093	time 0.8466 (1.0388)	loss 16.7687 (8.7314)	miou 0.2333	grad_norm 25.8437 (22.3734)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:54:45 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [51/200][20/54]	eta 0:00:35 lr 0.000096	time 1.2152 (1.0336)	loss 8.3235 (8.1889)	miou 0.2452	grad_norm 13.0720 (18.5908)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:54:56 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [51/200][30/54]	eta 0:00:25 lr 0.000099	time 0.9399 (1.0782)	loss 6.4532 (7.9269)	miou 0.2476	grad_norm 17.6237 (19.9537)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:55:07 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [51/200][40/54]	eta 0:00:14 lr 0.000100	time 0.9937 (1.0672)	loss 7.2987 (7.7182)	miou 0.2491	grad_norm 15.3263 (19.3604)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:55:17 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [51/200][50/54]	eta 0:00:04 lr 0.000100	time 1.0419 (1.0637)	loss 8.8526 (8.0348)	miou 0.2391	grad_norm 33.9480 (19.4459)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:55:20 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 51 training takes 0:00:57
[32m[2025-07-12 13:55:20 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 13:55:30 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1963%
[32m[2025-07-12 13:55:30 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 13:55:32 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [52/200][0/54]	eta 0:01:31 lr 0.000100	time 1.6966 (1.6966)	loss 6.4757 (6.4757)	miou 0.1925	grad_norm 28.1070 (28.1070)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:55:43 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [52/200][10/54]	eta 0:00:50 lr 0.000098	time 0.9432 (1.1455)	loss 5.8220 (9.4408)	miou 0.2080	grad_norm 14.6484 (20.6746)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:55:53 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [52/200][20/54]	eta 0:00:36 lr 0.000095	time 0.9297 (1.0614)	loss 6.4193 (8.3429)	miou 0.2202	grad_norm 13.7939 (21.2776)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:56:03 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [52/200][30/54]	eta 0:00:25 lr 0.000091	time 0.8481 (1.0503)	loss 10.5334 (7.9871)	miou 0.2276	grad_norm 17.5574 (20.0565)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:56:14 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [52/200][40/54]	eta 0:00:14 lr 0.000086	time 1.0200 (1.0568)	loss 6.6628 (7.9797)	miou 0.2351	grad_norm 169.3446 (23.4229)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:56:24 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [52/200][50/54]	eta 0:00:04 lr 0.000080	time 0.8990 (1.0465)	loss 7.7345 (7.9617)	miou 0.2342	grad_norm 19.5489 (22.7806)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:56:27 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 52 training takes 0:00:56
[32m[2025-07-12 13:56:27 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 13:56:36 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1738%
[32m[2025-07-12 13:56:36 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 13:56:38 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [53/200][0/54]	eta 0:01:03 lr 0.000077	time 1.1676 (1.1676)	loss 9.5793 (9.5793)	miou 0.1826	grad_norm 35.3299 (35.3299)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:56:49 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [53/200][10/54]	eta 0:00:49 lr 0.000071	time 0.7757 (1.1274)	loss 10.7136 (7.9910)	miou 0.1987	grad_norm 17.2987 (23.7959)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:57:00 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [53/200][20/54]	eta 0:00:37 lr 0.000063	time 1.2643 (1.1122)	loss 6.6220 (7.6772)	miou 0.2128	grad_norm 29.7147 (24.7991)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:57:10 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [53/200][30/54]	eta 0:00:25 lr 0.000055	time 1.2380 (1.0781)	loss 7.1843 (7.1211)	miou 0.2297	grad_norm 9.7748 (23.1873)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:57:21 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [53/200][40/54]	eta 0:00:15 lr 0.000048	time 1.3495 (1.0753)	loss 8.5818 (7.2346)	miou 0.2374	grad_norm 18.8081 (21.1974)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:57:31 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [53/200][50/54]	eta 0:00:04 lr 0.000040	time 0.8069 (1.0754)	loss 7.6137 (7.2988)	miou 0.2384	grad_norm 14.0231 (20.1893)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:57:34 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 53 training takes 0:00:57
[32m[2025-07-12 13:57:34 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 13:57:44 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1801%
[32m[2025-07-12 13:57:44 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 13:57:46 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [54/200][0/54]	eta 0:01:17 lr 0.000037	time 1.4279 (1.4279)	loss 5.9323 (5.9323)	miou 0.1782	grad_norm 19.5486 (19.5486)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:57:56 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [54/200][10/54]	eta 0:00:45 lr 0.000029	time 0.9652 (1.0398)	loss 10.2182 (7.9289)	miou 0.1975	grad_norm 20.1035 (19.7311)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:58:07 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [54/200][20/54]	eta 0:00:36 lr 0.000023	time 1.4880 (1.0824)	loss 9.0257 (7.7137)	miou 0.2081	grad_norm 12.1036 (18.9101)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:58:17 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [54/200][30/54]	eta 0:00:25 lr 0.000016	time 0.8320 (1.0565)	loss 6.1714 (7.6567)	miou 0.2158	grad_norm 17.1620 (18.1583)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:58:28 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [54/200][40/54]	eta 0:00:14 lr 0.000011	time 1.1023 (1.0642)	loss 8.5072 (7.3614)	miou 0.2418	grad_norm 8.7213 (17.4482)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:58:38 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [54/200][50/54]	eta 0:00:04 lr 0.000007	time 1.0256 (1.0652)	loss 7.1818 (7.4550)	miou 0.2446	grad_norm 26.8754 (19.0239)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:58:41 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 54 training takes 0:00:57
[32m[2025-07-12 13:58:41 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 13:58:51 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1885%
[32m[2025-07-12 13:58:51 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 13:58:53 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [55/200][0/54]	eta 0:01:21 lr 0.000005	time 1.5013 (1.5013)	loss 6.7184 (6.7184)	miou 0.1953	grad_norm 17.3939 (17.3939)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:59:03 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [55/200][10/54]	eta 0:00:45 lr 0.000002	time 0.9537 (1.0345)	loss 9.1698 (8.3791)	miou 0.2193	grad_norm 21.3853 (25.4120)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:59:13 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [55/200][20/54]	eta 0:00:35 lr 0.000000	time 0.9465 (1.0454)	loss 10.0427 (7.9796)	miou 0.2199	grad_norm 25.9443 (22.8873)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:59:24 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [55/200][30/54]	eta 0:00:25 lr 0.000000	time 1.0560 (1.0509)	loss 7.1909 (7.5603)	miou 0.2320	grad_norm 20.2801 (21.5807)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:59:35 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [55/200][40/54]	eta 0:00:14 lr 0.000001	time 1.0637 (1.0625)	loss 8.3319 (7.8794)	miou 0.2331	grad_norm 13.1324 (20.6353)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:59:46 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [55/200][50/54]	eta 0:00:04 lr 0.000003	time 1.1742 (1.0799)	loss 8.4986 (7.8461)	miou 0.2489	grad_norm 11.3595 (20.1495)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 13:59:50 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 55 training takes 0:00:58
[32m[2025-07-12 13:59:50 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 14:00:00 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1809%
[32m[2025-07-12 14:00:00 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 14:00:01 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [56/200][0/54]	eta 0:01:18 lr 0.000004	time 1.4512 (1.4512)	loss 6.1622 (6.1622)	miou 0.1871	grad_norm 22.1726 (22.1726)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:00:11 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [56/200][10/54]	eta 0:00:46 lr 0.000007	time 1.0408 (1.0494)	loss 9.8425 (7.9320)	miou 0.2046	grad_norm 15.7558 (15.5562)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:00:21 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [56/200][20/54]	eta 0:00:34 lr 0.000012	time 0.9339 (1.0248)	loss 7.8549 (8.3793)	miou 0.2173	grad_norm 16.2637 (15.5158)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:00:31 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [56/200][30/54]	eta 0:00:24 lr 0.000018	time 0.8292 (1.0264)	loss 5.9374 (7.8697)	miou 0.2307	grad_norm 10.8380 (17.0120)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:00:42 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [56/200][40/54]	eta 0:00:14 lr 0.000024	time 1.3980 (1.0339)	loss 8.7061 (7.7736)	miou 0.2288	grad_norm 25.5326 (17.6741)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:00:52 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [56/200][50/54]	eta 0:00:04 lr 0.000031	time 1.0816 (1.0350)	loss 6.5941 (7.6660)	miou 0.2440	grad_norm 21.3087 (17.8312)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:00:56 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 56 training takes 0:00:56
[32m[2025-07-12 14:00:56 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 14:01:06 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1669%
[32m[2025-07-12 14:01:06 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 14:01:07 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [57/200][0/54]	eta 0:01:31 lr 0.000034	time 1.6853 (1.6853)	loss 8.1837 (8.1837)	miou 0.1857	grad_norm 25.1157 (25.1157)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:01:18 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [57/200][10/54]	eta 0:00:48 lr 0.000041	time 0.9822 (1.0976)	loss 11.7636 (7.2846)	miou 0.2410	grad_norm 21.7246 (17.3026)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:01:28 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [57/200][20/54]	eta 0:00:36 lr 0.000049	time 1.0082 (1.0836)	loss 10.8010 (7.4202)	miou 0.2522	grad_norm 17.1306 (18.5262)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:01:38 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [57/200][30/54]	eta 0:00:25 lr 0.000057	time 0.8048 (1.0449)	loss 6.5509 (7.3272)	miou 0.2601	grad_norm 17.9193 (17.8894)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:01:49 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [57/200][40/54]	eta 0:00:14 lr 0.000065	time 1.0152 (1.0702)	loss 7.4000 (7.2403)	miou 0.2635	grad_norm 23.3254 (18.5063)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:01:59 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [57/200][50/54]	eta 0:00:04 lr 0.000072	time 1.3919 (1.0496)	loss 6.6368 (7.3730)	miou 0.2616	grad_norm 26.5926 (18.6173)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:02:02 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 57 training takes 0:00:56
[32m[2025-07-12 14:02:02 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 14:02:12 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1725%
[32m[2025-07-12 14:02:12 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 14:02:14 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [58/200][0/54]	eta 0:01:11 lr 0.000075	time 1.3331 (1.3331)	loss 7.2515 (7.2515)	miou 0.1770	grad_norm 8.5668 (8.5668)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:02:23 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [58/200][10/54]	eta 0:00:42 lr 0.000081	time 0.6892 (0.9762)	loss 15.1721 (7.8041)	miou 0.2147	grad_norm 16.4059 (13.9350)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:02:34 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [58/200][20/54]	eta 0:00:34 lr 0.000087	time 1.0638 (1.0148)	loss 4.9995 (7.6850)	miou 0.2300	grad_norm 9.0938 (14.8979)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:02:44 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [58/200][30/54]	eta 0:00:24 lr 0.000092	time 1.1197 (1.0235)	loss 8.7037 (7.4764)	miou 0.2371	grad_norm 17.3019 (16.6949)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:02:55 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [58/200][40/54]	eta 0:00:14 lr 0.000096	time 1.0065 (1.0429)	loss 8.0738 (7.5881)	miou 0.2384	grad_norm 12.7701 (16.9778)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:03:06 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [58/200][50/54]	eta 0:00:04 lr 0.000098	time 1.1846 (1.0524)	loss 6.0971 (7.4498)	miou 0.2359	grad_norm 13.3790 (18.0085)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:03:09 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 58 training takes 0:00:56
[32m[2025-07-12 14:03:09 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 14:03:19 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1741%
[32m[2025-07-12 14:03:19 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 14:03:20 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [59/200][0/54]	eta 0:01:23 lr 0.000099	time 1.5491 (1.5491)	loss 5.2039 (5.2039)	miou 0.1722	grad_norm 18.1756 (18.1756)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:03:31 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [59/200][10/54]	eta 0:00:47 lr 0.000100	time 0.9837 (1.0813)	loss 7.9749 (8.1721)	miou 0.2003	grad_norm 13.7547 (19.3732)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:03:42 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [59/200][20/54]	eta 0:00:37 lr 0.000100	time 1.0865 (1.1042)	loss 8.1501 (7.8689)	miou 0.2173	grad_norm 13.0864 (18.1345)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:03:53 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [59/200][30/54]	eta 0:00:26 lr 0.000098	time 1.1403 (1.0861)	loss 10.6752 (8.0070)	miou 0.2138	grad_norm 26.1209 (18.8052)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:04:03 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [59/200][40/54]	eta 0:00:14 lr 0.000096	time 1.4077 (1.0679)	loss 6.4815 (7.8775)	miou 0.2262	grad_norm 20.7578 (18.6921)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:04:13 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [59/200][50/54]	eta 0:00:04 lr 0.000092	time 1.2437 (1.0626)	loss 8.0618 (7.9283)	miou 0.2282	grad_norm 16.2515 (19.5126)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:04:16 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 59 training takes 0:00:57
[32m[2025-07-12 14:04:16 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 14:04:26 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1809%
[32m[2025-07-12 14:04:26 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 14:04:28 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [60/200][0/54]	eta 0:01:41 lr 0.000090	time 1.8841 (1.8841)	loss 5.6133 (5.6133)	miou 0.1976	grad_norm 48.6339 (48.6339)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:04:38 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [60/200][10/54]	eta 0:00:49 lr 0.000085	time 1.3303 (1.1229)	loss 11.5222 (7.7778)	miou 0.2143	grad_norm 16.6097 (19.6161)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:04:48 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [60/200][20/54]	eta 0:00:35 lr 0.000079	time 1.0594 (1.0404)	loss 9.3353 (7.4890)	miou 0.2261	grad_norm 31.6470 (19.5701)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:04:59 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [60/200][30/54]	eta 0:00:25 lr 0.000072	time 0.9556 (1.0610)	loss 9.0338 (7.7476)	miou 0.2286	grad_norm 13.9594 (18.5741)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:05:10 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [60/200][40/54]	eta 0:00:14 lr 0.000065	time 0.9045 (1.0588)	loss 10.6071 (7.6954)	miou 0.2331	grad_norm 23.0824 (19.4178)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:05:19 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [60/200][50/54]	eta 0:00:04 lr 0.000057	time 1.4560 (1.0456)	loss 8.7008 (7.7540)	miou 0.2409	grad_norm 10.2024 (18.8684)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:05:23 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 60 training takes 0:00:56
[32m[2025-07-12 14:05:23 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 14:05:32 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1719%
[32m[2025-07-12 14:05:32 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 14:05:34 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [61/200][0/54]	eta 0:01:10 lr 0.000054	time 1.3063 (1.3063)	loss 7.0767 (7.0767)	miou 0.1728	grad_norm 21.2652 (21.2652)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:05:43 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [61/200][10/54]	eta 0:00:44 lr 0.000046	time 1.1165 (1.0053)	loss 9.4627 (8.0189)	miou 0.2099	grad_norm 19.4138 (19.7035)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:05:54 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [61/200][20/54]	eta 0:00:35 lr 0.000038	time 1.0593 (1.0366)	loss 9.7945 (7.6293)	miou 0.2258	grad_norm 34.5811 (21.6208)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:06:06 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [61/200][30/54]	eta 0:00:25 lr 0.000031	time 1.1214 (1.0760)	loss 6.5345 (7.4962)	miou 0.2263	grad_norm 11.1811 (21.4199)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:06:16 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [61/200][40/54]	eta 0:00:14 lr 0.000024	time 1.2282 (1.0675)	loss 12.7795 (7.5716)	miou 0.2345	grad_norm 15.7980 (21.1972)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:06:27 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [61/200][50/54]	eta 0:00:04 lr 0.000018	time 1.1923 (1.0658)	loss 6.0429 (7.4847)	miou 0.2404	grad_norm 15.1429 (20.3575)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:06:30 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 61 training takes 0:00:57
[32m[2025-07-12 14:06:30 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 14:06:39 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2026%
[32m[2025-07-12 14:06:39 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 14:06:41 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [62/200][0/54]	eta 0:01:23 lr 0.000015	time 1.5439 (1.5439)	loss 7.8897 (7.8897)	miou 0.2036	grad_norm 28.4867 (28.4867)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:06:51 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [62/200][10/54]	eta 0:00:47 lr 0.000010	time 1.3768 (1.0759)	loss 7.9500 (7.9861)	miou 0.2419	grad_norm 15.9900 (17.3505)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:07:02 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [62/200][20/54]	eta 0:00:36 lr 0.000006	time 1.0260 (1.0804)	loss 5.3753 (7.5446)	miou 0.2530	grad_norm 30.4771 (20.1965)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:07:12 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [62/200][30/54]	eta 0:00:24 lr 0.000003	time 0.9103 (1.0384)	loss 8.6307 (7.5410)	miou 0.2493	grad_norm 17.9364 (20.3960)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:07:22 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [62/200][40/54]	eta 0:00:14 lr 0.000001	time 0.8992 (1.0342)	loss 7.2956 (7.5242)	miou 0.2596	grad_norm 17.0454 (19.3005)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:07:32 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [62/200][50/54]	eta 0:00:04 lr 0.000000	time 1.0805 (1.0352)	loss 8.4617 (7.4363)	miou 0.2494	grad_norm 21.2215 (18.6667)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:07:35 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 62 training takes 0:00:55
[32m[2025-07-12 14:07:35 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 14:07:45 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1967%
[32m[2025-07-12 14:07:45 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 14:07:47 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [63/200][0/54]	eta 0:01:37 lr 0.000000	time 1.7999 (1.7999)	loss 6.7275 (6.7275)	miou 0.1962	grad_norm 18.7127 (18.7127)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:07:57 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [63/200][10/54]	eta 0:00:48 lr 0.000001	time 1.3213 (1.1126)	loss 9.0540 (7.7920)	miou 0.2251	grad_norm 40.0852 (17.9718)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:08:08 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [63/200][20/54]	eta 0:00:36 lr 0.000003	time 0.9618 (1.0819)	loss 9.7973 (7.8435)	miou 0.2332	grad_norm 12.5452 (16.5713)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:08:18 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [63/200][30/54]	eta 0:00:25 lr 0.000007	time 1.2067 (1.0679)	loss 6.9946 (7.5635)	miou 0.2407	grad_norm 18.6163 (16.2975)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:08:28 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [63/200][40/54]	eta 0:00:14 lr 0.000011	time 1.1934 (1.0477)	loss 6.8809 (7.6692)	miou 0.2442	grad_norm 15.1303 (16.1574)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:08:39 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [63/200][50/54]	eta 0:00:04 lr 0.000016	time 1.4751 (1.0591)	loss 7.9069 (7.4973)	miou 0.2443	grad_norm 13.1259 (16.3543)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:08:42 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 63 training takes 0:00:57
[32m[2025-07-12 14:08:42 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 14:08:52 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1939%
[32m[2025-07-12 14:08:52 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 14:08:53 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [64/200][0/54]	eta 0:01:09 lr 0.000019	time 1.2916 (1.2916)	loss 5.8706 (5.8706)	miou 0.1962	grad_norm 15.3071 (15.3071)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:09:04 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [64/200][10/54]	eta 0:00:48 lr 0.000025	time 0.8633 (1.0930)	loss 9.1029 (8.3299)	miou 0.2134	grad_norm 16.9372 (21.0172)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:09:15 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [64/200][20/54]	eta 0:00:37 lr 0.000032	time 1.2974 (1.0973)	loss 6.9129 (7.6703)	miou 0.2287	grad_norm 15.8595 (19.5604)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:09:25 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [64/200][30/54]	eta 0:00:25 lr 0.000040	time 0.8279 (1.0716)	loss 13.5286 (7.9389)	miou 0.2288	grad_norm 17.9354 (19.1295)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:09:36 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [64/200][40/54]	eta 0:00:14 lr 0.000048	time 1.0447 (1.0675)	loss 13.1335 (7.9660)	miou 0.2502	grad_norm 23.0117 (18.3753)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:09:46 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [64/200][50/54]	eta 0:00:04 lr 0.000055	time 0.7163 (1.0542)	loss 5.3678 (7.7893)	miou 0.2491	grad_norm 11.6427 (17.9865)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:09:49 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 64 training takes 0:00:56
[32m[2025-07-12 14:09:49 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 14:09:59 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2167%
[32m[2025-07-12 14:09:59 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 14:10:00 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [65/200][0/54]	eta 0:01:16 lr 0.000059	time 1.4120 (1.4120)	loss 5.6587 (5.6587)	miou 0.2188	grad_norm 18.0652 (18.0652)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:10:11 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [65/200][10/54]	eta 0:00:49 lr 0.000066	time 1.0992 (1.1253)	loss 9.3296 (6.8399)	miou 0.2319	grad_norm 9.9980 (17.3837)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:10:21 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [65/200][20/54]	eta 0:00:35 lr 0.000073	time 0.9585 (1.0504)	loss 5.8747 (6.9821)	miou 0.2336	grad_norm 27.4813 (18.5515)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:10:31 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [65/200][30/54]	eta 0:00:25 lr 0.000080	time 0.9571 (1.0526)	loss 8.8733 (7.2890)	miou 0.2316	grad_norm 12.9371 (18.6470)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:10:42 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [65/200][40/54]	eta 0:00:14 lr 0.000086	time 0.5791 (1.0626)	loss 7.3107 (7.4964)	miou 0.2303	grad_norm 19.4975 (18.4877)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:10:53 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [65/200][50/54]	eta 0:00:04 lr 0.000091	time 1.2136 (1.0617)	loss 5.6388 (7.5306)	miou 0.2380	grad_norm 14.8435 (18.4624)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:10:56 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 65 training takes 0:00:57
[32m[2025-07-12 14:10:56 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 14:11:06 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1931%
[32m[2025-07-12 14:11:06 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 14:11:07 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [66/200][0/54]	eta 0:01:16 lr 0.000093	time 1.4091 (1.4091)	loss 5.5622 (5.5622)	miou 0.1935	grad_norm 13.8760 (13.8760)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:11:17 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [66/200][10/54]	eta 0:00:44 lr 0.000096	time 1.1419 (1.0145)	loss 9.7349 (7.9999)	miou 0.2151	grad_norm 12.7896 (17.8362)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:11:28 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [66/200][20/54]	eta 0:00:35 lr 0.000099	time 1.3832 (1.0563)	loss 9.9093 (7.3163)	miou 0.2290	grad_norm 25.3441 (19.9423)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:11:39 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [66/200][30/54]	eta 0:00:25 lr 0.000100	time 0.9039 (1.0769)	loss 10.7731 (7.4558)	miou 0.2316	grad_norm 15.4601 (19.1630)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:11:50 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [66/200][40/54]	eta 0:00:15 lr 0.000100	time 1.0149 (1.0854)	loss 9.4352 (7.6561)	miou 0.2428	grad_norm 18.3798 (19.4889)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:12:01 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [66/200][50/54]	eta 0:00:04 lr 0.000099	time 1.3542 (1.0769)	loss 10.4516 (7.6524)	miou 0.2509	grad_norm 52.2820 (20.3281)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:12:04 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 66 training takes 0:00:57
[32m[2025-07-12 14:12:04 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 14:12:13 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2032%
[32m[2025-07-12 14:12:13 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 14:12:15 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [67/200][0/54]	eta 0:01:16 lr 0.000098	time 1.4094 (1.4094)	loss 7.7814 (7.7814)	miou 0.2036	grad_norm 28.1858 (28.1858)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:12:24 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [67/200][10/54]	eta 0:00:43 lr 0.000095	time 1.2154 (0.9907)	loss 5.9292 (7.5845)	miou 0.2280	grad_norm 14.7710 (19.0259)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:12:35 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [67/200][20/54]	eta 0:00:35 lr 0.000091	time 1.2553 (1.0451)	loss 7.2722 (7.7602)	miou 0.2301	grad_norm 18.7225 (18.2574)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:12:46 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [67/200][30/54]	eta 0:00:25 lr 0.000086	time 1.0151 (1.0521)	loss 7.5155 (7.4617)	miou 0.2436	grad_norm 10.9384 (18.4359)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:12:57 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [67/200][40/54]	eta 0:00:14 lr 0.000080	time 0.9473 (1.0698)	loss 11.8296 (7.5559)	miou 0.2404	grad_norm 17.0228 (18.6552)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:13:08 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [67/200][50/54]	eta 0:00:04 lr 0.000073	time 1.3862 (1.0729)	loss 5.2517 (7.5549)	miou 0.2432	grad_norm 23.4155 (18.2829)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:13:11 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 67 training takes 0:00:57
[32m[2025-07-12 14:13:11 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 14:13:21 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2279%
[32m[2025-07-12 14:13:21 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 14:13:23 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [68/200][0/54]	eta 0:01:38 lr 0.000071	time 1.8189 (1.8189)	loss 6.0392 (6.0392)	miou 0.2234	grad_norm 12.1335 (12.1335)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:13:34 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [68/200][10/54]	eta 0:00:50 lr 0.000063	time 1.0772 (1.1468)	loss 6.8858 (7.2087)	miou 0.2400	grad_norm 16.4718 (17.8119)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:13:43 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [68/200][20/54]	eta 0:00:35 lr 0.000055	time 0.8258 (1.0403)	loss 8.9129 (7.6193)	miou 0.2519	grad_norm 11.2831 (17.5195)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:13:55 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [68/200][30/54]	eta 0:00:25 lr 0.000048	time 1.2363 (1.0772)	loss 7.7790 (7.5023)	miou 0.2606	grad_norm 13.8046 (18.7958)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:14:05 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [68/200][40/54]	eta 0:00:14 lr 0.000040	time 0.9477 (1.0601)	loss 5.4797 (7.4629)	miou 0.2616	grad_norm 15.6153 (18.8433)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:14:16 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [68/200][50/54]	eta 0:00:04 lr 0.000032	time 0.8688 (1.0616)	loss 9.0202 (7.5596)	miou 0.2583	grad_norm 17.2184 (17.8874)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:14:19 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 68 training takes 0:00:57
[32m[2025-07-12 14:14:19 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 14:14:29 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2099%
[32m[2025-07-12 14:14:29 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 14:14:30 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [69/200][0/54]	eta 0:01:21 lr 0.000029	time 1.5161 (1.5161)	loss 6.2151 (6.2151)	miou 0.2206	grad_norm 10.1667 (10.1667)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:14:41 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [69/200][10/54]	eta 0:00:48 lr 0.000023	time 0.9629 (1.1082)	loss 9.3631 (7.9493)	miou 0.2326	grad_norm 18.8865 (16.1948)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:14:51 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [69/200][20/54]	eta 0:00:35 lr 0.000016	time 1.1752 (1.0510)	loss 8.0813 (7.8775)	miou 0.2367	grad_norm 11.7007 (16.9404)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:15:02 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [69/200][30/54]	eta 0:00:25 lr 0.000011	time 1.0688 (1.0730)	loss 9.6777 (7.8505)	miou 0.2386	grad_norm 12.6571 (17.0707)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:15:13 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [69/200][40/54]	eta 0:00:14 lr 0.000007	time 1.2597 (1.0676)	loss 7.8315 (7.4678)	miou 0.2391	grad_norm 18.5492 (17.1782)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:15:22 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [69/200][50/54]	eta 0:00:04 lr 0.000003	time 1.0753 (1.0447)	loss 8.3633 (7.5136)	miou 0.2477	grad_norm 32.9718 (18.0548)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:15:25 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 69 training takes 0:00:56
[32m[2025-07-12 14:15:25 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 14:15:35 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2203%
[32m[2025-07-12 14:15:35 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 14:15:37 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [70/200][0/54]	eta 0:01:25 lr 0.000002	time 1.5807 (1.5807)	loss 4.5443 (4.5443)	miou 0.2202	grad_norm 27.1308 (27.1308)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:15:47 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [70/200][10/54]	eta 0:00:47 lr 0.000000	time 1.0556 (1.0795)	loss 10.3250 (7.6519)	miou 0.2145	grad_norm 19.3369 (17.2322)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:15:57 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [70/200][20/54]	eta 0:00:35 lr 0.000000	time 0.8882 (1.0325)	loss 5.3149 (7.9311)	miou 0.2375	grad_norm 15.4205 (17.2707)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:16:07 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [70/200][30/54]	eta 0:00:24 lr 0.000001	time 0.9248 (1.0347)	loss 9.5903 (7.5217)	miou 0.2465	grad_norm 12.8566 (16.7602)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:16:17 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [70/200][40/54]	eta 0:00:14 lr 0.000003	time 0.9526 (1.0192)	loss 9.9387 (7.6165)	miou 0.2484	grad_norm 15.5676 (17.5948)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:16:28 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [70/200][50/54]	eta 0:00:04 lr 0.000006	time 0.9945 (1.0416)	loss 5.5536 (7.4041)	miou 0.2503	grad_norm 15.7261 (17.2899)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:16:31 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 70 training takes 0:00:56
[32m[2025-07-12 14:16:32 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 14:16:41 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2440%
[32m[2025-07-12 14:16:41 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 14:16:43 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [71/200][0/54]	eta 0:01:14 lr 0.000007	time 1.3807 (1.3807)	loss 5.4646 (5.4646)	miou 0.2517	grad_norm 15.0934 (15.0934)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:16:54 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [71/200][10/54]	eta 0:00:48 lr 0.000012	time 1.1498 (1.1074)	loss 6.1770 (6.5354)	miou 0.2586	grad_norm 31.7834 (20.7741)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:17:05 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [71/200][20/54]	eta 0:00:37 lr 0.000018	time 1.0724 (1.1077)	loss 5.6959 (7.2175)	miou 0.2670	grad_norm 14.0545 (21.4687)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:17:15 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [71/200][30/54]	eta 0:00:26 lr 0.000024	time 0.9553 (1.0934)	loss 9.0563 (7.3833)	miou 0.2714	grad_norm 19.3887 (19.1960)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:17:26 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [71/200][40/54]	eta 0:00:15 lr 0.000031	time 1.2457 (1.0769)	loss 7.1367 (7.3361)	miou 0.2790	grad_norm 15.6610 (18.2012)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:17:37 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [71/200][50/54]	eta 0:00:04 lr 0.000038	time 1.1853 (1.0824)	loss 7.0385 (7.2913)	miou 0.2709	grad_norm 9.1065 (17.2560)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:17:40 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 71 training takes 0:00:58
[32m[2025-07-12 14:17:40 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 14:17:50 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2076%
[32m[2025-07-12 14:17:50 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 14:17:52 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [72/200][0/54]	eta 0:01:40 lr 0.000041	time 1.8703 (1.8703)	loss 7.3231 (7.3231)	miou 0.2101	grad_norm 9.8900 (9.8900)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:18:02 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [72/200][10/54]	eta 0:00:50 lr 0.000049	time 0.8036 (1.1425)	loss 5.9769 (6.7132)	miou 0.2504	grad_norm 18.2376 (20.1334)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:18:12 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [72/200][20/54]	eta 0:00:36 lr 0.000057	time 1.3451 (1.0827)	loss 7.5292 (7.0048)	miou 0.2438	grad_norm 50.2812 (26.3996)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:18:23 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [72/200][30/54]	eta 0:00:25 lr 0.000065	time 0.8151 (1.0716)	loss 9.6381 (7.1567)	miou 0.2431	grad_norm 15.8283 (23.2576)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:18:33 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [72/200][40/54]	eta 0:00:14 lr 0.000072	time 1.0680 (1.0638)	loss 6.6062 (7.0509)	miou 0.2453	grad_norm 15.6415 (21.7128)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:18:44 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [72/200][50/54]	eta 0:00:04 lr 0.000079	time 1.1082 (1.0651)	loss 5.9667 (6.9691)	miou 0.2436	grad_norm 13.3813 (20.7084)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:18:47 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 72 training takes 0:00:57
[32m[2025-07-12 14:18:47 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 14:18:57 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1929%
[32m[2025-07-12 14:18:57 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 14:18:58 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [73/200][0/54]	eta 0:01:03 lr 0.000081	time 1.1740 (1.1740)	loss 6.4195 (6.4195)	miou 0.1950	grad_norm 15.8585 (15.8585)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:19:08 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [73/200][10/54]	eta 0:00:44 lr 0.000087	time 0.9897 (1.0105)	loss 6.9789 (7.2353)	miou 0.2301	grad_norm 15.4016 (14.7996)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:19:17 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [73/200][20/54]	eta 0:00:33 lr 0.000092	time 1.0157 (0.9861)	loss 8.1868 (7.2548)	miou 0.2338	grad_norm 12.5445 (15.3449)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:19:28 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [73/200][30/54]	eta 0:00:24 lr 0.000096	time 0.7848 (1.0115)	loss 9.3815 (7.6840)	miou 0.2317	grad_norm 19.0962 (16.6353)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:19:40 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [73/200][40/54]	eta 0:00:14 lr 0.000098	time 1.4701 (1.0486)	loss 11.5262 (7.5163)	miou 0.2381	grad_norm 13.7518 (16.8575)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:19:51 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [73/200][50/54]	eta 0:00:04 lr 0.000100	time 0.9717 (1.0579)	loss 8.3200 (7.6155)	miou 0.2452	grad_norm 12.9173 (17.0836)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:19:54 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 73 training takes 0:00:57
[32m[2025-07-12 14:19:54 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 14:20:04 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1613%
[32m[2025-07-12 14:20:04 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 14:20:05 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [74/200][0/54]	eta 0:01:37 lr 0.000100	time 1.8104 (1.8104)	loss 5.6664 (5.6664)	miou 0.1584	grad_norm 24.2697 (24.2697)	loss_scale 131072.0000 (131072.0000)	mem 3265MB
[32m[2025-07-12 14:20:15 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [74/200][10/54]	eta 0:00:46 lr 0.000100	time 1.2170 (1.0547)	loss 7.6571 (7.5436)	miou 0.2045	grad_norm 16.5082 (17.8008)	loss_scale 262144.0000 (226397.0909)	mem 3265MB
[32m[2025-07-12 14:20:26 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [74/200][20/54]	eta 0:00:35 lr 0.000098	time 1.0914 (1.0526)	loss 7.6674 (7.5317)	miou 0.2239	grad_norm 9.3187 (16.7853)	loss_scale 262144.0000 (243419.4286)	mem 3265MB
[32m[2025-07-12 14:20:36 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [74/200][30/54]	eta 0:00:25 lr 0.000096	time 1.3487 (1.0560)	loss 8.9756 (7.3905)	miou 0.2249	grad_norm 43.5596 (17.1095)	loss_scale 262144.0000 (249459.6129)	mem 3265MB
[32m[2025-07-12 14:20:47 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [74/200][40/54]	eta 0:00:14 lr 0.000092	time 0.9609 (1.0636)	loss 6.8573 (7.2716)	miou 0.2345	grad_norm 17.0458 (20.1391)	loss_scale 262144.0000 (252553.3659)	mem 3265MB
[32m[2025-07-12 14:20:57 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [74/200][50/54]	eta 0:00:04 lr 0.000087	time 0.9086 (1.0485)	loss 8.0514 (7.2569)	miou 0.2365	grad_norm 16.5235 (20.1729)	loss_scale 262144.0000 (254433.8824)	mem 3265MB
[32m[2025-07-12 14:21:01 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 74 training takes 0:00:57
[32m[2025-07-12 14:21:01 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 14:21:11 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2101%
[32m[2025-07-12 14:21:11 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 14:21:12 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [75/200][0/54]	eta 0:01:23 lr 0.000085	time 1.5393 (1.5393)	loss 6.9807 (6.9807)	miou 0.2146	grad_norm 18.7427 (18.7427)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:21:21 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [75/200][10/54]	eta 0:00:43 lr 0.000079	time 0.8803 (0.9827)	loss 4.9079 (8.2670)	miou 0.2149	grad_norm 15.7350 (17.1443)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:21:32 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [75/200][20/54]	eta 0:00:34 lr 0.000072	time 0.9833 (1.0052)	loss 10.6114 (8.3533)	miou 0.2230	grad_norm 18.6505 (16.3917)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:21:42 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [75/200][30/54]	eta 0:00:24 lr 0.000065	time 0.7878 (1.0182)	loss 8.7293 (7.7261)	miou 0.2275	grad_norm 16.4602 (16.5423)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:21:52 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [75/200][40/54]	eta 0:00:14 lr 0.000057	time 1.1190 (1.0208)	loss 9.7869 (7.6193)	miou 0.2342	grad_norm 32.0224 (16.9591)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:22:03 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [75/200][50/54]	eta 0:00:04 lr 0.000049	time 1.3629 (1.0241)	loss 6.1155 (7.6038)	miou 0.2393	grad_norm 12.9670 (17.8054)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:22:07 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 75 training takes 0:00:56
[32m[2025-07-12 14:22:07 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 14:22:17 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2025%
[32m[2025-07-12 14:22:17 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 14:22:18 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [76/200][0/54]	eta 0:01:25 lr 0.000046	time 1.5800 (1.5800)	loss 5.4163 (5.4163)	miou 0.2012	grad_norm 71.1914 (71.1914)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:22:29 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [76/200][10/54]	eta 0:00:49 lr 0.000038	time 0.9981 (1.1226)	loss 6.3717 (6.8560)	miou 0.2408	grad_norm 12.3005 (19.8638)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:22:39 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [76/200][20/54]	eta 0:00:36 lr 0.000031	time 1.0295 (1.0814)	loss 10.3155 (7.3138)	miou 0.2436	grad_norm 11.5811 (18.1475)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:22:49 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [76/200][30/54]	eta 0:00:25 lr 0.000024	time 1.1674 (1.0456)	loss 9.5051 (7.3763)	miou 0.2539	grad_norm 37.6621 (18.0114)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:23:00 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [76/200][40/54]	eta 0:00:14 lr 0.000018	time 1.2204 (1.0672)	loss 8.8549 (7.3076)	miou 0.2633	grad_norm 13.6691 (17.9512)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:23:11 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [76/200][50/54]	eta 0:00:04 lr 0.000012	time 1.0882 (1.0623)	loss 7.7561 (7.4077)	miou 0.2714	grad_norm 19.1780 (17.3316)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:23:14 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 76 training takes 0:00:57
[32m[2025-07-12 14:23:14 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 14:23:24 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2137%
[32m[2025-07-12 14:23:24 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 14:23:25 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [77/200][0/54]	eta 0:01:10 lr 0.000010	time 1.2978 (1.2978)	loss 5.4046 (5.4046)	miou 0.2321	grad_norm 16.6045 (16.6045)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:23:36 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [77/200][10/54]	eta 0:00:49 lr 0.000006	time 0.8389 (1.1146)	loss 6.3134 (7.3140)	miou 0.2414	grad_norm 14.4563 (17.6418)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:23:47 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [77/200][20/54]	eta 0:00:37 lr 0.000003	time 0.8711 (1.0902)	loss 5.2265 (6.8778)	miou 0.2583	grad_norm 19.7234 (15.8342)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:23:56 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [77/200][30/54]	eta 0:00:24 lr 0.000001	time 0.7054 (1.0371)	loss 8.1630 (6.9867)	miou 0.2630	grad_norm 9.3417 (15.2392)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:24:07 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [77/200][40/54]	eta 0:00:14 lr 0.000000	time 1.0276 (1.0531)	loss 9.7577 (6.8945)	miou 0.2641	grad_norm 18.5019 (16.2283)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:24:17 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [77/200][50/54]	eta 0:00:04 lr 0.000000	time 0.9299 (1.0526)	loss 7.9489 (7.0682)	miou 0.2614	grad_norm 13.7438 (15.9752)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:24:20 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 77 training takes 0:00:56
[32m[2025-07-12 14:24:20 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 14:24:30 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1991%
[32m[2025-07-12 14:24:30 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 14:24:32 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [78/200][0/54]	eta 0:01:36 lr 0.000001	time 1.7807 (1.7807)	loss 5.4147 (5.4147)	miou 0.2079	grad_norm 8.1811 (8.1811)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:24:41 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [78/200][10/54]	eta 0:00:45 lr 0.000003	time 0.9012 (1.0338)	loss 8.0686 (7.5576)	miou 0.2319	grad_norm 31.7291 (17.9337)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:24:52 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [78/200][20/54]	eta 0:00:35 lr 0.000007	time 1.0530 (1.0455)	loss 8.0419 (7.7461)	miou 0.2389	grad_norm 12.4359 (15.9298)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:25:03 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [78/200][30/54]	eta 0:00:25 lr 0.000011	time 0.9925 (1.0454)	loss 8.2160 (7.6208)	miou 0.2448	grad_norm 8.0382 (15.8397)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:25:14 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [78/200][40/54]	eta 0:00:14 lr 0.000016	time 0.9446 (1.0584)	loss 6.1487 (7.5828)	miou 0.2520	grad_norm 12.8464 (16.2605)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:25:23 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [78/200][50/54]	eta 0:00:04 lr 0.000023	time 0.7968 (1.0392)	loss 7.9426 (7.4975)	miou 0.2549	grad_norm 8.5750 (16.2845)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:25:26 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 78 training takes 0:00:56
[32m[2025-07-12 14:25:26 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 14:25:36 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2116%
[32m[2025-07-12 14:25:36 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 14:25:38 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [79/200][0/54]	eta 0:01:14 lr 0.000025	time 1.3801 (1.3801)	loss 6.5397 (6.5397)	miou 0.2156	grad_norm 14.2642 (14.2642)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:25:48 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [79/200][10/54]	eta 0:00:48 lr 0.000032	time 1.2334 (1.1008)	loss 8.6606 (8.3174)	miou 0.2362	grad_norm 12.0735 (14.5206)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:25:59 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [79/200][20/54]	eta 0:00:36 lr 0.000040	time 1.4762 (1.0707)	loss 5.7110 (7.9434)	miou 0.2524	grad_norm 11.8812 (15.9134)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:26:09 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [79/200][30/54]	eta 0:00:25 lr 0.000048	time 1.0238 (1.0597)	loss 5.9050 (7.7207)	miou 0.2636	grad_norm 12.3793 (16.0057)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:26:21 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [79/200][40/54]	eta 0:00:15 lr 0.000055	time 1.2846 (1.0790)	loss 7.4400 (7.6654)	miou 0.2631	grad_norm 19.5621 (16.3221)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:26:31 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [79/200][50/54]	eta 0:00:04 lr 0.000063	time 1.2628 (1.0682)	loss 5.2979 (7.6357)	miou 0.2619	grad_norm 10.0387 (16.3222)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:26:34 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 79 training takes 0:00:57
[32m[2025-07-12 14:26:34 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 14:26:44 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2271%
[32m[2025-07-12 14:26:44 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 14:26:45 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [80/200][0/54]	eta 0:01:02 lr 0.000066	time 1.1535 (1.1535)	loss 5.4570 (5.4570)	miou 0.2436	grad_norm 12.6402 (12.6402)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:26:54 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [80/200][10/54]	eta 0:00:42 lr 0.000073	time 1.0778 (0.9769)	loss 8.9991 (7.5646)	miou 0.2589	grad_norm 9.8656 (20.5563)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:27:06 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [80/200][20/54]	eta 0:00:35 lr 0.000080	time 0.9980 (1.0537)	loss 7.3971 (7.0486)	miou 0.2708	grad_norm 10.3346 (19.3220)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:27:15 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [80/200][30/54]	eta 0:00:24 lr 0.000086	time 0.9513 (1.0204)	loss 7.5874 (7.0683)	miou 0.2687	grad_norm 12.2457 (18.8481)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:27:26 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [80/200][40/54]	eta 0:00:14 lr 0.000091	time 1.2745 (1.0357)	loss 10.8435 (7.1443)	miou 0.2747	grad_norm 15.5618 (17.9383)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:27:37 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [80/200][50/54]	eta 0:00:04 lr 0.000095	time 1.2148 (1.0503)	loss 8.1051 (7.2010)	miou 0.2711	grad_norm 16.6388 (17.5706)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:27:40 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 80 training takes 0:00:56
[32m[2025-07-12 14:27:40 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 14:27:50 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1688%
[32m[2025-07-12 14:27:50 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 14:27:51 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [81/200][0/54]	eta 0:01:10 lr 0.000096	time 1.3141 (1.3141)	loss 7.0341 (7.0341)	miou 0.1814	grad_norm 16.2636 (16.2636)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:28:01 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [81/200][10/54]	eta 0:00:45 lr 0.000099	time 1.2735 (1.0284)	loss 6.0575 (7.4877)	miou 0.2197	grad_norm 14.6434 (16.7148)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:28:12 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [81/200][20/54]	eta 0:00:35 lr 0.000100	time 1.0448 (1.0410)	loss 9.9547 (7.3310)	miou 0.2345	grad_norm 17.5720 (16.9196)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:28:23 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [81/200][30/54]	eta 0:00:25 lr 0.000100	time 0.9275 (1.0612)	loss 8.1809 (7.4756)	miou 0.2453	grad_norm 14.0971 (17.1334)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:28:33 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [81/200][40/54]	eta 0:00:14 lr 0.000099	time 1.2272 (1.0562)	loss 8.7673 (7.2139)	miou 0.2547	grad_norm 10.5186 (17.5044)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:28:44 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [81/200][50/54]	eta 0:00:04 lr 0.000096	time 1.1158 (1.0500)	loss 7.8233 (7.2340)	miou 0.2576	grad_norm 9.9341 (17.8398)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:28:47 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 81 training takes 0:00:56
[32m[2025-07-12 14:28:47 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 14:28:56 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2087%
[32m[2025-07-12 14:28:56 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 14:28:58 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [82/200][0/54]	eta 0:01:14 lr 0.000095	time 1.3809 (1.3809)	loss 6.4159 (6.4159)	miou 0.2109	grad_norm 17.7954 (17.7954)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:29:09 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [82/200][10/54]	eta 0:00:48 lr 0.000091	time 1.3878 (1.0994)	loss 7.2585 (7.6401)	miou 0.2393	grad_norm 24.2046 (17.9137)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:29:20 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [82/200][20/54]	eta 0:00:38 lr 0.000086	time 0.9272 (1.1362)	loss 9.7402 (7.1361)	miou 0.2426	grad_norm 19.8813 (20.1014)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:29:31 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [82/200][30/54]	eta 0:00:26 lr 0.000080	time 1.0572 (1.0982)	loss 9.0018 (7.1344)	miou 0.2462	grad_norm 13.8461 (19.0183)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:29:41 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [82/200][40/54]	eta 0:00:15 lr 0.000073	time 1.1628 (1.0859)	loss 11.8835 (7.3630)	miou 0.2487	grad_norm 20.4660 (18.5064)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:29:52 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [82/200][50/54]	eta 0:00:04 lr 0.000066	time 1.0819 (1.0860)	loss 8.9388 (7.3855)	miou 0.2526	grad_norm 12.8390 (18.2997)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:29:55 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 82 training takes 0:00:58
[32m[2025-07-12 14:29:55 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 14:30:05 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1835%
[32m[2025-07-12 14:30:05 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 14:30:06 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [83/200][0/54]	eta 0:00:59 lr 0.000063	time 1.1071 (1.1071)	loss 8.0490 (8.0490)	miou 0.1885	grad_norm 21.9592 (21.9592)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:30:16 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [83/200][10/54]	eta 0:00:43 lr 0.000055	time 0.9865 (0.9841)	loss 8.1377 (7.5713)	miou 0.2143	grad_norm 11.3079 (14.5504)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:30:26 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [83/200][20/54]	eta 0:00:34 lr 0.000048	time 1.3315 (1.0109)	loss 6.3660 (7.5770)	miou 0.2313	grad_norm 44.2129 (17.8533)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:30:38 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [83/200][30/54]	eta 0:00:25 lr 0.000040	time 1.1917 (1.0696)	loss 9.0975 (7.4866)	miou 0.2318	grad_norm 13.0937 (16.6531)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:30:49 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [83/200][40/54]	eta 0:00:15 lr 0.000032	time 0.9912 (1.0827)	loss 8.2609 (7.3631)	miou 0.2346	grad_norm 12.9298 (17.3344)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:30:59 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [83/200][50/54]	eta 0:00:04 lr 0.000025	time 1.1114 (1.0694)	loss 6.5978 (7.2421)	miou 0.2342	grad_norm 9.1331 (17.6862)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:31:02 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 83 training takes 0:00:57
[32m[2025-07-12 14:31:03 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 14:31:12 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1694%
[32m[2025-07-12 14:31:12 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 14:31:14 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [84/200][0/54]	eta 0:01:27 lr 0.000023	time 1.6296 (1.6296)	loss 8.4066 (8.4066)	miou 0.1691	grad_norm 26.0738 (26.0738)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:31:24 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [84/200][10/54]	eta 0:00:48 lr 0.000016	time 1.0501 (1.1039)	loss 5.5424 (7.4562)	miou 0.2042	grad_norm 19.8875 (18.7274)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:31:35 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [84/200][20/54]	eta 0:00:36 lr 0.000011	time 1.1810 (1.0849)	loss 10.3500 (7.5750)	miou 0.2352	grad_norm 7.7225 (19.2503)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:31:46 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [84/200][30/54]	eta 0:00:25 lr 0.000007	time 0.9674 (1.0777)	loss 8.0824 (7.6499)	miou 0.2397	grad_norm 9.5848 (16.9622)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:31:56 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [84/200][40/54]	eta 0:00:15 lr 0.000003	time 1.1606 (1.0741)	loss 9.4873 (7.5425)	miou 0.2413	grad_norm 19.2847 (16.9548)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:32:08 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [84/200][50/54]	eta 0:00:04 lr 0.000001	time 1.3865 (1.0825)	loss 4.6741 (7.4521)	miou 0.2449	grad_norm 21.7251 (16.9366)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:32:11 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 84 training takes 0:00:58
[32m[2025-07-12 14:32:11 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 14:32:21 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1873%
[32m[2025-07-12 14:32:21 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 14:32:23 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [85/200][0/54]	eta 0:01:53 lr 0.000000	time 2.1061 (2.1061)	loss 5.2190 (5.2190)	miou 0.1892	grad_norm 49.4120 (49.4120)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:32:33 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [85/200][10/54]	eta 0:00:49 lr 0.000000	time 1.1098 (1.1348)	loss 6.0554 (7.2308)	miou 0.2178	grad_norm 13.7871 (18.4578)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:32:43 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [85/200][20/54]	eta 0:00:35 lr 0.000001	time 1.2534 (1.0473)	loss 9.8358 (7.7239)	miou 0.2282	grad_norm 12.1211 (16.7022)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:32:53 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [85/200][30/54]	eta 0:00:25 lr 0.000003	time 1.1888 (1.0529)	loss 7.0101 (7.5137)	miou 0.2277	grad_norm 15.6205 (16.5054)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:33:05 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [85/200][40/54]	eta 0:00:15 lr 0.000006	time 1.1640 (1.0833)	loss 5.6805 (7.4711)	miou 0.2340	grad_norm 16.8334 (16.1596)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:33:17 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [85/200][50/54]	eta 0:00:04 lr 0.000010	time 0.9618 (1.0946)	loss 6.8847 (7.5318)	miou 0.2380	grad_norm 12.0663 (15.5294)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:33:20 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 85 training takes 0:00:59
[32m[2025-07-12 14:33:20 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 14:33:30 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1857%
[32m[2025-07-12 14:33:30 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 14:33:31 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [86/200][0/54]	eta 0:01:21 lr 0.000012	time 1.5074 (1.5074)	loss 5.9659 (5.9659)	miou 0.1855	grad_norm 19.8611 (19.8611)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:33:42 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [86/200][10/54]	eta 0:00:50 lr 0.000018	time 0.9140 (1.1545)	loss 9.4342 (7.4453)	miou 0.2281	grad_norm 9.1711 (14.3142)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:33:52 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [86/200][20/54]	eta 0:00:36 lr 0.000024	time 1.4042 (1.0784)	loss 7.4285 (7.0946)	miou 0.2429	grad_norm 25.4636 (16.5240)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:34:03 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [86/200][30/54]	eta 0:00:25 lr 0.000031	time 0.9952 (1.0632)	loss 9.7490 (7.2310)	miou 0.2541	grad_norm 11.2972 (15.0092)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:34:14 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [86/200][40/54]	eta 0:00:14 lr 0.000038	time 1.0919 (1.0670)	loss 5.7894 (7.5552)	miou 0.2583	grad_norm 21.4227 (16.2510)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:34:24 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [86/200][50/54]	eta 0:00:04 lr 0.000046	time 0.8240 (1.0624)	loss 5.5517 (7.3855)	miou 0.2643	grad_norm 13.4957 (16.3757)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:34:27 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 86 training takes 0:00:57
[32m[2025-07-12 14:34:27 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 14:34:37 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2072%
[32m[2025-07-12 14:34:37 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 14:34:38 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [87/200][0/54]	eta 0:01:05 lr 0.000049	time 1.2211 (1.2211)	loss 7.0803 (7.0803)	miou 0.2090	grad_norm 17.4669 (17.4669)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:34:49 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [87/200][10/54]	eta 0:00:47 lr 0.000057	time 1.1640 (1.0852)	loss 13.3391 (7.6814)	miou 0.2411	grad_norm 32.3610 (17.3163)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:35:00 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [87/200][20/54]	eta 0:00:37 lr 0.000065	time 1.1698 (1.1016)	loss 8.4860 (7.6685)	miou 0.2482	grad_norm 8.8546 (16.0285)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:35:11 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [87/200][30/54]	eta 0:00:26 lr 0.000072	time 0.9535 (1.1066)	loss 12.2517 (7.7433)	miou 0.2522	grad_norm 19.3773 (16.1419)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:35:22 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [87/200][40/54]	eta 0:00:15 lr 0.000079	time 1.3815 (1.1112)	loss 6.5420 (7.5255)	miou 0.2597	grad_norm 13.1829 (16.2475)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:35:33 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [87/200][50/54]	eta 0:00:04 lr 0.000085	time 1.0059 (1.1015)	loss 6.5865 (7.3374)	miou 0.2679	grad_norm 9.9332 (15.5478)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:35:36 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 87 training takes 0:00:59
[32m[2025-07-12 14:35:36 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 14:35:46 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1513%
[32m[2025-07-12 14:35:46 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 14:35:47 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [88/200][0/54]	eta 0:01:03 lr 0.000087	time 1.1768 (1.1768)	loss 5.1632 (5.1632)	miou 0.1613	grad_norm 14.0705 (14.0705)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:35:58 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [88/200][10/54]	eta 0:00:44 lr 0.000092	time 1.4014 (1.0224)	loss 7.8039 (7.4340)	miou 0.1986	grad_norm 9.8651 (15.1636)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:36:08 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [88/200][20/54]	eta 0:00:34 lr 0.000096	time 0.9963 (1.0262)	loss 6.4099 (7.5439)	miou 0.2095	grad_norm 11.0452 (15.2354)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:36:18 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [88/200][30/54]	eta 0:00:24 lr 0.000098	time 1.2299 (1.0333)	loss 6.1180 (7.4951)	miou 0.2136	grad_norm 29.9049 (16.0807)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:36:28 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [88/200][40/54]	eta 0:00:14 lr 0.000100	time 0.9845 (1.0121)	loss 7.7567 (7.4356)	miou 0.2196	grad_norm 19.8027 (16.0296)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:36:38 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [88/200][50/54]	eta 0:00:04 lr 0.000100	time 1.1063 (1.0208)	loss 5.6116 (7.3966)	miou 0.2355	grad_norm 9.1860 (17.1736)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:36:41 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 88 training takes 0:00:55
[32m[2025-07-12 14:36:41 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 14:36:51 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1936%
[32m[2025-07-12 14:36:51 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 14:36:53 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [89/200][0/54]	eta 0:01:15 lr 0.000100	time 1.4032 (1.4032)	loss 7.3367 (7.3367)	miou 0.2084	grad_norm 11.1135 (11.1135)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:37:03 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [89/200][10/54]	eta 0:00:48 lr 0.000098	time 1.0647 (1.0923)	loss 7.3068 (7.9292)	miou 0.2120	grad_norm 13.6958 (18.3047)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:37:13 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [89/200][20/54]	eta 0:00:35 lr 0.000096	time 1.1861 (1.0380)	loss 4.8670 (7.6411)	miou 0.2346	grad_norm 23.2860 (22.0994)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:37:23 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [89/200][30/54]	eta 0:00:24 lr 0.000092	time 1.1082 (1.0224)	loss 6.9276 (7.7385)	miou 0.2396	grad_norm 16.8067 (20.2575)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:37:35 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [89/200][40/54]	eta 0:00:14 lr 0.000087	time 1.0275 (1.0587)	loss 6.7694 (7.7502)	miou 0.2344	grad_norm 12.9295 (20.0844)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:37:45 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [89/200][50/54]	eta 0:00:04 lr 0.000081	time 1.1104 (1.0542)	loss 5.2822 (7.8791)	miou 0.2464	grad_norm 16.4375 (19.1840)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:37:48 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 89 training takes 0:00:56
[32m[2025-07-12 14:37:48 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 14:37:58 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1497%
[32m[2025-07-12 14:37:58 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 14:37:59 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [90/200][0/54]	eta 0:01:17 lr 0.000079	time 1.4334 (1.4334)	loss 7.7777 (7.7777)	miou 0.1529	grad_norm 23.6192 (23.6192)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:38:09 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [90/200][10/54]	eta 0:00:45 lr 0.000072	time 0.9391 (1.0358)	loss 8.0770 (7.6037)	miou 0.2029	grad_norm 11.6070 (14.8695)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:38:19 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [90/200][20/54]	eta 0:00:34 lr 0.000065	time 1.1343 (1.0177)	loss 7.6445 (7.9268)	miou 0.2110	grad_norm 37.8054 (16.1758)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:38:30 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [90/200][30/54]	eta 0:00:25 lr 0.000057	time 0.7906 (1.0440)	loss 7.9554 (7.5377)	miou 0.2202	grad_norm 19.4190 (16.5813)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:38:41 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [90/200][40/54]	eta 0:00:14 lr 0.000049	time 1.0240 (1.0544)	loss 6.4685 (7.4178)	miou 0.2250	grad_norm 12.5722 (16.7258)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:38:53 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [90/200][50/54]	eta 0:00:04 lr 0.000041	time 1.4547 (1.0739)	loss 6.4554 (7.5065)	miou 0.2274	grad_norm 15.9613 (16.5037)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:38:56 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 90 training takes 0:00:57
[32m[2025-07-12 14:38:56 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 14:39:06 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1681%
[32m[2025-07-12 14:39:06 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 14:39:07 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [91/200][0/54]	eta 0:01:26 lr 0.000038	time 1.6044 (1.6044)	loss 7.5204 (7.5204)	miou 0.1790	grad_norm 25.5394 (25.5394)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:39:17 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [91/200][10/54]	eta 0:00:46 lr 0.000031	time 0.9331 (1.0517)	loss 10.8004 (8.7824)	miou 0.2179	grad_norm 8.9572 (14.3857)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:39:28 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [91/200][20/54]	eta 0:00:36 lr 0.000024	time 1.2574 (1.0596)	loss 6.7300 (8.0767)	miou 0.2296	grad_norm 24.4223 (15.2246)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:39:38 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [91/200][30/54]	eta 0:00:25 lr 0.000018	time 0.8926 (1.0448)	loss 7.8858 (7.8009)	miou 0.2365	grad_norm 9.7371 (16.1914)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:39:48 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [91/200][40/54]	eta 0:00:14 lr 0.000012	time 0.7945 (1.0376)	loss 10.4590 (7.6669)	miou 0.2462	grad_norm 10.5201 (16.5056)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:40:00 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [91/200][50/54]	eta 0:00:04 lr 0.000007	time 1.1501 (1.0643)	loss 6.2302 (7.6211)	miou 0.2540	grad_norm 39.5625 (16.8868)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:40:03 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 91 training takes 0:00:57
[32m[2025-07-12 14:40:03 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 14:40:13 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1839%
[32m[2025-07-12 14:40:13 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 14:40:14 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [92/200][0/54]	eta 0:01:15 lr 0.000006	time 1.4008 (1.4008)	loss 8.7320 (8.7320)	miou 0.1866	grad_norm 12.7511 (12.7511)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:40:24 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [92/200][10/54]	eta 0:00:45 lr 0.000003	time 0.8184 (1.0263)	loss 7.3735 (7.6280)	miou 0.2281	grad_norm 10.0880 (12.0919)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:40:35 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [92/200][20/54]	eta 0:00:35 lr 0.000001	time 1.0323 (1.0517)	loss 6.9260 (7.2861)	miou 0.2572	grad_norm 11.6608 (14.4827)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:40:46 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [92/200][30/54]	eta 0:00:25 lr 0.000000	time 0.7163 (1.0513)	loss 8.1785 (7.1401)	miou 0.2618	grad_norm 9.9624 (14.2998)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:40:56 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [92/200][40/54]	eta 0:00:14 lr 0.000000	time 1.4731 (1.0422)	loss 5.8795 (7.0799)	miou 0.2680	grad_norm 14.3059 (15.7976)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:41:07 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [92/200][50/54]	eta 0:00:04 lr 0.000002	time 0.8653 (1.0502)	loss 5.7131 (7.1247)	miou 0.2707	grad_norm 20.0154 (16.3359)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:41:10 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 92 training takes 0:00:57
[32m[2025-07-12 14:41:10 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 14:41:20 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1798%
[32m[2025-07-12 14:41:20 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 14:41:22 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [93/200][0/54]	eta 0:01:23 lr 0.000003	time 1.5375 (1.5375)	loss 9.9074 (9.9074)	miou 0.1808	grad_norm 10.3874 (10.3874)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:41:31 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [93/200][10/54]	eta 0:00:45 lr 0.000007	time 0.8406 (1.0265)	loss 7.7994 (8.4612)	miou 0.2078	grad_norm 15.5028 (14.1279)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:41:41 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [93/200][20/54]	eta 0:00:33 lr 0.000011	time 1.0494 (0.9821)	loss 7.9467 (8.2489)	miou 0.2191	grad_norm 14.5418 (15.5284)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:41:51 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [93/200][30/54]	eta 0:00:24 lr 0.000016	time 1.4571 (1.0083)	loss 7.8291 (8.0463)	miou 0.2267	grad_norm 9.2388 (14.5741)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:42:02 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [93/200][40/54]	eta 0:00:14 lr 0.000023	time 1.2644 (1.0282)	loss 8.2573 (7.8882)	miou 0.2355	grad_norm 20.5437 (16.2296)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:42:13 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [93/200][50/54]	eta 0:00:04 lr 0.000029	time 1.2233 (1.0490)	loss 5.1304 (7.5816)	miou 0.2385	grad_norm 15.0807 (16.6084)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:42:16 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 93 training takes 0:00:56
[32m[2025-07-12 14:42:16 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 14:42:26 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1903%
[32m[2025-07-12 14:42:26 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 14:42:27 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [94/200][0/54]	eta 0:01:19 lr 0.000032	time 1.4751 (1.4751)	loss 8.0063 (8.0063)	miou 0.1902	grad_norm 11.5130 (11.5130)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:42:37 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [94/200][10/54]	eta 0:00:44 lr 0.000040	time 1.0073 (1.0137)	loss 7.2643 (7.9413)	miou 0.2248	grad_norm 13.2288 (18.7083)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:42:48 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [94/200][20/54]	eta 0:00:35 lr 0.000048	time 1.1428 (1.0427)	loss 6.5797 (7.3247)	miou 0.2463	grad_norm 11.7154 (15.9913)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:42:59 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [94/200][30/54]	eta 0:00:25 lr 0.000055	time 1.2050 (1.0673)	loss 9.0736 (7.3588)	miou 0.2498	grad_norm 9.5568 (15.7920)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:43:10 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [94/200][40/54]	eta 0:00:15 lr 0.000063	time 0.9436 (1.0773)	loss 6.0536 (7.2956)	miou 0.2549	grad_norm 14.3964 (15.0140)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:43:21 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [94/200][50/54]	eta 0:00:04 lr 0.000071	time 1.2878 (1.0778)	loss 7.9548 (7.2437)	miou 0.2582	grad_norm 57.9060 (15.8041)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:43:24 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 94 training takes 0:00:58
[32m[2025-07-12 14:43:24 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 14:43:34 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.2091%
[32m[2025-07-12 14:43:34 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 14:43:35 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [95/200][0/54]	eta 0:01:05 lr 0.000073	time 1.2169 (1.2169)	loss 5.8104 (5.8104)	miou 0.2309	grad_norm 10.3256 (10.3256)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:43:46 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [95/200][10/54]	eta 0:00:45 lr 0.000080	time 0.8970 (1.0417)	loss 9.7949 (8.2291)	miou 0.2543	grad_norm 12.9587 (18.6754)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:43:56 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [95/200][20/54]	eta 0:00:35 lr 0.000086	time 1.1016 (1.0322)	loss 9.1002 (7.6585)	miou 0.2538	grad_norm 16.7688 (17.5193)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:44:06 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [95/200][30/54]	eta 0:00:24 lr 0.000091	time 1.0588 (1.0300)	loss 8.1075 (7.3162)	miou 0.2591	grad_norm 9.3992 (16.5648)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:44:16 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [95/200][40/54]	eta 0:00:14 lr 0.000095	time 0.9172 (1.0309)	loss 8.1207 (7.5425)	miou 0.2517	grad_norm 14.1528 (17.0141)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:44:27 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [95/200][50/54]	eta 0:00:04 lr 0.000098	time 1.0891 (1.0451)	loss 6.0861 (7.5403)	miou 0.2565	grad_norm 11.8310 (16.4302)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:44:30 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 95 training takes 0:00:56
[32m[2025-07-12 14:44:30 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 14:44:40 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1358%
[32m[2025-07-12 14:44:40 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 14:44:42 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [96/200][0/54]	eta 0:01:33 lr 0.000099	time 1.7267 (1.7267)	loss 6.5453 (6.5453)	miou 0.1366	grad_norm 16.2696 (16.2696)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:44:52 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [96/200][10/54]	eta 0:00:47 lr 0.000100	time 1.0110 (1.0705)	loss 6.4282 (7.5052)	miou 0.2073	grad_norm 13.3067 (16.0503)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:45:03 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [96/200][20/54]	eta 0:00:36 lr 0.000100	time 1.0454 (1.0628)	loss 6.3484 (7.5322)	miou 0.2273	grad_norm 15.6255 (15.2475)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:45:13 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [96/200][30/54]	eta 0:00:25 lr 0.000099	time 1.0408 (1.0544)	loss 7.4808 (7.2244)	miou 0.2381	grad_norm 11.3763 (14.6262)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:45:23 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [96/200][40/54]	eta 0:00:14 lr 0.000096	time 0.9410 (1.0379)	loss 7.7524 (7.1918)	miou 0.2457	grad_norm 14.0601 (15.0649)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:45:35 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [96/200][50/54]	eta 0:00:04 lr 0.000093	time 0.9750 (1.0708)	loss 9.8434 (7.1278)	miou 0.2432	grad_norm 13.3986 (15.9289)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:45:38 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 96 training takes 0:00:57
[32m[2025-07-12 14:45:38 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 14:45:48 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1569%
[32m[2025-07-12 14:45:48 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 14:45:50 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [97/200][0/54]	eta 0:01:44 lr 0.000091	time 1.9323 (1.9323)	loss 5.6477 (5.6477)	miou 0.1599	grad_norm 14.2068 (14.2068)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:46:00 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [97/200][10/54]	eta 0:00:47 lr 0.000086	time 1.1940 (1.0756)	loss 8.2640 (7.5394)	miou 0.1918	grad_norm 11.5050 (23.0652)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:46:11 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [97/200][20/54]	eta 0:00:36 lr 0.000080	time 1.1063 (1.0845)	loss 6.3626 (7.3033)	miou 0.2234	grad_norm 8.8000 (18.0603)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:46:22 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [97/200][30/54]	eta 0:00:26 lr 0.000073	time 0.9383 (1.1049)	loss 5.0119 (7.2688)	miou 0.2420	grad_norm 19.1894 (17.2088)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:46:33 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [97/200][40/54]	eta 0:00:15 lr 0.000066	time 1.4633 (1.0895)	loss 8.2415 (7.1872)	miou 0.2540	grad_norm 16.1781 (16.7708)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:46:43 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [97/200][50/54]	eta 0:00:04 lr 0.000059	time 0.8383 (1.0762)	loss 5.0601 (7.2706)	miou 0.2619	grad_norm 11.7511 (16.3579)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:46:47 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 97 training takes 0:00:58
[32m[2025-07-12 14:46:47 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 14:46:56 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1672%
[32m[2025-07-12 14:46:56 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 14:46:58 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [98/200][0/54]	eta 0:01:13 lr 0.000055	time 1.3684 (1.3684)	loss 5.5202 (5.5202)	miou 0.1789	grad_norm 10.1296 (10.1296)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:47:09 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [98/200][10/54]	eta 0:00:50 lr 0.000048	time 1.3400 (1.1453)	loss 9.9355 (7.3857)	miou 0.2104	grad_norm 18.8067 (15.4959)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:47:20 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [98/200][20/54]	eta 0:00:38 lr 0.000040	time 1.0908 (1.1326)	loss 6.2715 (7.6910)	miou 0.2405	grad_norm 21.8573 (15.1621)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:47:30 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [98/200][30/54]	eta 0:00:25 lr 0.000032	time 0.8878 (1.0747)	loss 7.4330 (7.7648)	miou 0.2563	grad_norm 19.3854 (16.3285)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:47:40 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [98/200][40/54]	eta 0:00:14 lr 0.000025	time 1.0088 (1.0584)	loss 8.4693 (7.8119)	miou 0.2529	grad_norm 20.1910 (16.3779)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:47:51 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [98/200][50/54]	eta 0:00:04 lr 0.000019	time 1.0043 (1.0658)	loss 7.0599 (7.7068)	miou 0.2554	grad_norm 12.5067 (15.7782)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:47:54 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 98 training takes 0:00:57
[32m[2025-07-12 14:47:54 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 14:48:04 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1723%
[32m[2025-07-12 14:48:04 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 14:48:05 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [99/200][0/54]	eta 0:01:16 lr 0.000016	time 1.4096 (1.4096)	loss 5.8522 (5.8522)	miou 0.1689	grad_norm 12.4061 (12.4061)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:48:15 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [99/200][10/54]	eta 0:00:46 lr 0.000011	time 0.9659 (1.0508)	loss 7.2564 (6.8915)	miou 0.2095	grad_norm 17.4952 (14.9727)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:48:25 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [99/200][20/54]	eta 0:00:35 lr 0.000007	time 0.8766 (1.0318)	loss 4.8915 (6.8154)	miou 0.2386	grad_norm 14.9257 (16.2714)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:48:36 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [99/200][30/54]	eta 0:00:25 lr 0.000003	time 1.3181 (1.0510)	loss 8.3408 (6.9412)	miou 0.2485	grad_norm 32.6418 (16.1917)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:48:47 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [99/200][40/54]	eta 0:00:14 lr 0.000001	time 0.9453 (1.0477)	loss 9.1907 (7.2239)	miou 0.2537	grad_norm 12.8630 (16.2453)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:48:58 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [99/200][50/54]	eta 0:00:04 lr 0.000000	time 1.4244 (1.0687)	loss 7.2701 (7.1752)	miou 0.2574	grad_norm 12.6680 (15.6903)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:49:01 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 99 training takes 0:00:57
[32m[2025-07-12 14:49:01 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 14:49:11 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1611%
[32m[2025-07-12 14:49:11 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 14:49:13 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [100/200][0/54]	eta 0:01:27 lr 0.000000	time 1.6173 (1.6173)	loss 8.0666 (8.0666)	miou 0.1642	grad_norm 7.9850 (7.9850)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:49:23 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [100/200][10/54]	eta 0:00:47 lr 0.000001	time 1.4383 (1.0874)	loss 11.1479 (7.6143)	miou 0.2170	grad_norm 12.4746 (20.6276)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:49:34 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [100/200][20/54]	eta 0:00:37 lr 0.000003	time 0.8994 (1.1023)	loss 6.6105 (8.0508)	miou 0.2192	grad_norm 13.3611 (20.6419)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:49:45 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [100/200][30/54]	eta 0:00:26 lr 0.000006	time 0.8820 (1.0845)	loss 7.7969 (7.9582)	miou 0.2385	grad_norm 12.0041 (18.3902)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:49:56 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [100/200][40/54]	eta 0:00:15 lr 0.000010	time 1.4428 (1.1039)	loss 9.1559 (7.8111)	miou 0.2501	grad_norm 9.5712 (17.6175)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:50:06 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [100/200][50/54]	eta 0:00:04 lr 0.000015	time 0.8636 (1.0795)	loss 7.5570 (7.6569)	miou 0.2575	grad_norm 17.6136 (16.9174)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:50:09 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 100 training takes 0:00:57
[32m[2025-07-12 14:50:09 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 14:50:19 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1628%
[32m[2025-07-12 14:50:19 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 14:50:20 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [101/200][0/54]	eta 0:01:15 lr 0.000018	time 1.4022 (1.4022)	loss 6.9342 (6.9342)	miou 0.1646	grad_norm 9.3512 (9.3512)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:50:31 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [101/200][10/54]	eta 0:00:48 lr 0.000024	time 1.0587 (1.0959)	loss 5.9438 (7.8033)	miou 0.2155	grad_norm 6.2221 (14.4081)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:50:41 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [101/200][20/54]	eta 0:00:36 lr 0.000031	time 0.8907 (1.0781)	loss 5.6061 (7.1594)	miou 0.2308	grad_norm 34.4743 (16.4785)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:50:51 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [101/200][30/54]	eta 0:00:25 lr 0.000038	time 0.9008 (1.0594)	loss 8.5737 (6.9830)	miou 0.2423	grad_norm 25.6253 (16.4027)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:51:02 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [101/200][40/54]	eta 0:00:14 lr 0.000046	time 1.2356 (1.0596)	loss 8.2723 (7.2461)	miou 0.2478	grad_norm 12.4723 (15.6768)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:51:12 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [101/200][50/54]	eta 0:00:04 lr 0.000054	time 0.9464 (1.0492)	loss 6.8682 (7.2458)	miou 0.2542	grad_norm 9.5579 (15.4458)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:51:15 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 101 training takes 0:00:56
[32m[2025-07-12 14:51:15 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 14:51:25 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1591%
[32m[2025-07-12 14:51:25 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 14:51:26 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [102/200][0/54]	eta 0:00:58 lr 0.000057	time 1.0854 (1.0854)	loss 7.9377 (7.9377)	miou 0.1595	grad_norm 17.5113 (17.5113)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:51:36 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [102/200][10/54]	eta 0:00:43 lr 0.000065	time 0.7367 (0.9954)	loss 6.6591 (6.6302)	miou 0.2196	grad_norm 16.3010 (16.6388)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:51:47 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [102/200][20/54]	eta 0:00:34 lr 0.000072	time 1.0564 (1.0196)	loss 5.9195 (6.9024)	miou 0.2244	grad_norm 12.3251 (17.2784)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:51:56 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [102/200][30/54]	eta 0:00:23 lr 0.000079	time 1.2106 (0.9984)	loss 8.8828 (7.0905)	miou 0.2420	grad_norm 18.7717 (16.9276)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:52:08 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [102/200][40/54]	eta 0:00:14 lr 0.000085	time 1.2174 (1.0373)	loss 7.3468 (6.9358)	miou 0.2535	grad_norm 11.3531 (18.3511)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:52:19 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [102/200][50/54]	eta 0:00:04 lr 0.000090	time 1.0095 (1.0545)	loss 5.0576 (6.8581)	miou 0.2600	grad_norm 13.5758 (17.3507)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:52:22 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 102 training takes 0:00:56
[32m[2025-07-12 14:52:22 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 14:52:32 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1445%
[32m[2025-07-12 14:52:32 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 14:52:33 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [103/200][0/54]	eta 0:01:24 lr 0.000092	time 1.5604 (1.5604)	loss 7.7678 (7.7678)	miou 0.1525	grad_norm 12.5547 (12.5547)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:52:45 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [103/200][10/54]	eta 0:00:51 lr 0.000096	time 0.9722 (1.1642)	loss 6.5654 (7.9244)	miou 0.1808	grad_norm 16.5600 (17.9628)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:52:54 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [103/200][20/54]	eta 0:00:36 lr 0.000098	time 0.9087 (1.0681)	loss 9.5727 (7.8214)	miou 0.2117	grad_norm 28.6357 (17.1057)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:53:05 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [103/200][30/54]	eta 0:00:25 lr 0.000100	time 1.1633 (1.0692)	loss 6.9405 (7.6104)	miou 0.2362	grad_norm 13.5592 (16.1995)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:53:16 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [103/200][40/54]	eta 0:00:15 lr 0.000100	time 1.4192 (1.0815)	loss 6.2059 (7.4062)	miou 0.2453	grad_norm 9.9234 (15.9611)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:53:27 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [103/200][50/54]	eta 0:00:04 lr 0.000099	time 0.9403 (1.0851)	loss 4.8798 (7.4314)	miou 0.2511	grad_norm 13.1118 (15.9696)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:53:30 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 103 training takes 0:00:58
[32m[2025-07-12 14:53:30 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 14:53:40 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1620%
[32m[2025-07-12 14:53:40 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 14:53:42 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [104/200][0/54]	eta 0:01:21 lr 0.000098	time 1.5043 (1.5043)	loss 10.8077 (10.8077)	miou 0.1647	grad_norm 18.5693 (18.5693)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:53:53 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [104/200][10/54]	eta 0:00:50 lr 0.000096	time 1.0960 (1.1568)	loss 7.0668 (6.9056)	miou 0.2174	grad_norm 13.9870 (15.1956)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:54:04 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [104/200][20/54]	eta 0:00:38 lr 0.000092	time 1.3490 (1.1323)	loss 7.4458 (7.4482)	miou 0.2377	grad_norm 33.1154 (16.6261)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:54:14 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [104/200][30/54]	eta 0:00:26 lr 0.000087	time 0.9097 (1.1039)	loss 6.5686 (7.5571)	miou 0.2459	grad_norm 15.9676 (16.6263)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:54:25 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [104/200][40/54]	eta 0:00:15 lr 0.000081	time 0.8321 (1.0829)	loss 6.6982 (7.3653)	miou 0.2505	grad_norm 11.3998 (15.0772)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:54:35 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [104/200][50/54]	eta 0:00:04 lr 0.000075	time 0.9713 (1.0776)	loss 5.2208 (7.2401)	miou 0.2584	grad_norm 16.6373 (15.8598)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:54:38 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 104 training takes 0:00:57
[32m[2025-07-12 14:54:38 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-12 14:54:48 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1426%
[32m[2025-07-12 14:54:48 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.2484%
[32m[2025-07-12 14:54:49 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [105/200][0/54]	eta 0:01:04 lr 0.000072	time 1.1971 (1.1971)	loss 5.5862 (5.5862)	miou 0.1417	grad_norm 31.2140 (31.2140)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:54:59 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [105/200][10/54]	eta 0:00:44 lr 0.000065	time 1.1750 (1.0088)	loss 6.7201 (7.3475)	miou 0.1785	grad_norm 12.8277 (16.3596)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
[32m[2025-07-12 14:55:09 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [105/200][20/54]	eta 0:00:33 lr 0.000057	time 0.8716 (0.9836)	loss 5.3769 (7.3623)	miou 0.2066	grad_norm 19.5614 (15.3972)	loss_scale 262144.0000 (262144.0000)	mem 3265MB
