{"os": "Linux-5.4.0-77-generic-x86_64-with-debian-bullseye-sid", "python": "3.7.16", "heartbeatAt": "2025-07-13T22:13:27.139615", "startedAt": "2025-07-13T22:13:26.716949", "docker": null, "cuda": null, "args": ["--local_rank=2", "--cfg", "config/base_train.yaml", "--output", "/home-local2/akath.extra.nobkp/sereact", "--data-path", "/home-local2/akath.extra.nobkp/dl_challenge", "--batch-size", "2"], "state": "running", "program": "main.py", "codePath": "main.py", "git": {"remote": "https://github.com/Shrinidhibhat87/codingchallenge_sereact.git", "commit": "667b7ded061f079ea281aa2193d47b61df08fec6"}, "email": "<EMAIL>", "root": "/home-local/akath.nobkp/sereact", "host": "lv3-32190", "username": "akath", "executable": "/gel/usr/akath/.conda/envs/swin/bin/python", "cpu_count": 6, "cpu_count_logical": 12, "cpu_freq": {"current": 1.81175, "min": 1200.0, "max": 4000.0}, "cpu_freq_per_core": [{"current": 1.435, "min": 1200.0, "max": 4000.0}, {"current": 1.511, "min": 1200.0, "max": 4000.0}, {"current": 1.687, "min": 1200.0, "max": 4000.0}, {"current": 1.399, "min": 1200.0, "max": 4000.0}, {"current": 1.199, "min": 1200.0, "max": 4000.0}, {"current": 3.696, "min": 1200.0, "max": 4000.0}, {"current": 1.348, "min": 1200.0, "max": 4000.0}, {"current": 1.522, "min": 1200.0, "max": 4000.0}, {"current": 1.674, "min": 1200.0, "max": 4000.0}, {"current": 1.433, "min": 1200.0, "max": 4000.0}, {"current": 1.199, "min": 1200.0, "max": 4000.0}, {"current": 3.638, "min": 1200.0, "max": 4000.0}], "disk": {"total": 111.2200813293457, "used": 53.13279342651367}, "gpu": "TITAN Xp", "gpu_count": 3, "gpu_devices": [{"name": "TITAN Xp", "memory_total": 12787122176}, {"name": "TITAN Xp", "memory_total": 12788498432}, {"name": "TITAN Xp", "memory_total": 12788498432}], "memory": {"total": 62.72977066040039}}