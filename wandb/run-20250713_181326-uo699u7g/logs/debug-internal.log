2025-07-13 18:13:26,733 INFO    StreamThr :2940712 [internal.py:wandb_internal():89] W&B internal server running at pid: 2940712, started at: 2025-07-13 18:13:26.732600
2025-07-13 18:13:26,736 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: status
2025-07-13 18:13:26,738 INFO    WriterThread:2940712 [datastore.py:open_for_write():85] open: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-uo699u7g/run-uo699u7g.wandb
2025-07-13 18:13:26,739 DEBUG   SenderThread:2940712 [sender.py:send():369] send: header
2025-07-13 18:13:26,756 DEBUG   SenderThread:2940712 [sender.py:send():369] send: run
2025-07-13 18:13:26,959 INFO    SenderThread:2940712 [dir_watcher.py:__init__():211] watching files in: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-uo699u7g/files
2025-07-13 18:13:26,960 INFO    SenderThread:2940712 [sender.py:_start_run_threads():1103] run started: uo699u7g with start time 1752444806.731293
2025-07-13 18:13:26,961 DEBUG   SenderThread:2940712 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:13:26,961 INFO    SenderThread:2940712 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:13:26,971 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: check_version
2025-07-13 18:13:26,971 DEBUG   SenderThread:2940712 [sender.py:send_request():396] send_request: check_version
2025-07-13 18:13:27,098 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: run_start
2025-07-13 18:13:27,103 DEBUG   HandlerThread:2940712 [system_info.py:__init__():31] System info init
2025-07-13 18:13:27,103 DEBUG   HandlerThread:2940712 [system_info.py:__init__():46] System info init done
2025-07-13 18:13:27,103 INFO    HandlerThread:2940712 [system_monitor.py:start():181] Starting system monitor
2025-07-13 18:13:27,103 INFO    SystemMonitor:2940712 [system_monitor.py:_start():145] Starting system asset monitoring threads
2025-07-13 18:13:27,104 INFO    HandlerThread:2940712 [system_monitor.py:probe():201] Collecting system info
2025-07-13 18:13:27,105 INFO    SystemMonitor:2940712 [interfaces.py:start():190] Started cpu monitoring
2025-07-13 18:13:27,105 INFO    SystemMonitor:2940712 [interfaces.py:start():190] Started disk monitoring
2025-07-13 18:13:27,106 INFO    SystemMonitor:2940712 [interfaces.py:start():190] Started gpu monitoring
2025-07-13 18:13:27,108 INFO    SystemMonitor:2940712 [interfaces.py:start():190] Started memory monitoring
2025-07-13 18:13:27,109 INFO    SystemMonitor:2940712 [interfaces.py:start():190] Started network monitoring
2025-07-13 18:13:27,139 DEBUG   HandlerThread:2940712 [system_info.py:probe():195] Probing system
2025-07-13 18:13:27,148 DEBUG   HandlerThread:2940712 [system_info.py:_probe_git():180] Probing git
2025-07-13 18:13:27,163 DEBUG   HandlerThread:2940712 [system_info.py:_probe_git():188] Probing git done
2025-07-13 18:13:27,164 DEBUG   HandlerThread:2940712 [system_info.py:probe():240] Probing system done
2025-07-13 18:13:27,164 DEBUG   HandlerThread:2940712 [system_monitor.py:probe():210] {'os': 'Linux-5.4.0-77-generic-x86_64-with-debian-bullseye-sid', 'python': '3.7.16', 'heartbeatAt': '2025-07-13T22:13:27.139615', 'startedAt': '2025-07-13T22:13:26.716949', 'docker': None, 'cuda': None, 'args': ('--local_rank=2', '--cfg', 'config/base_train.yaml', '--output', '/home-local2/akath.extra.nobkp/sereact', '--data-path', '/home-local2/akath.extra.nobkp/dl_challenge', '--batch-size', '2'), 'state': 'running', 'program': 'main.py', 'codePath': 'main.py', 'git': {'remote': 'https://github.com/Shrinidhibhat87/codingchallenge_sereact.git', 'commit': '667b7ded061f079ea281aa2193d47b61df08fec6'}, 'email': '<EMAIL>', 'root': '/home-local/akath.nobkp/sereact', 'host': 'lv3-32190', 'username': 'akath', 'executable': '/gel/usr/akath/.conda/envs/swin/bin/python', 'cpu_count': 6, 'cpu_count_logical': 12, 'cpu_freq': {'current': 1.81175, 'min': 1200.0, 'max': 4000.0}, 'cpu_freq_per_core': [{'current': 1.435, 'min': 1200.0, 'max': 4000.0}, {'current': 1.511, 'min': 1200.0, 'max': 4000.0}, {'current': 1.687, 'min': 1200.0, 'max': 4000.0}, {'current': 1.399, 'min': 1200.0, 'max': 4000.0}, {'current': 1.199, 'min': 1200.0, 'max': 4000.0}, {'current': 3.696, 'min': 1200.0, 'max': 4000.0}, {'current': 1.348, 'min': 1200.0, 'max': 4000.0}, {'current': 1.522, 'min': 1200.0, 'max': 4000.0}, {'current': 1.674, 'min': 1200.0, 'max': 4000.0}, {'current': 1.433, 'min': 1200.0, 'max': 4000.0}, {'current': 1.199, 'min': 1200.0, 'max': 4000.0}, {'current': 3.638, 'min': 1200.0, 'max': 4000.0}], 'disk': {'total': 111.2200813293457, 'used': 53.13279342651367}, 'gpu': 'TITAN Xp', 'gpu_count': 3, 'gpu_devices': [{'name': 'TITAN Xp', 'memory_total': 12787122176}, {'name': 'TITAN Xp', 'memory_total': 12788498432}, {'name': 'TITAN Xp', 'memory_total': 12788498432}], 'memory': {'total': 62.72977066040039}}
2025-07-13 18:13:27,164 INFO    HandlerThread:2940712 [system_monitor.py:probe():211] Finished collecting system info
2025-07-13 18:13:27,164 INFO    HandlerThread:2940712 [system_monitor.py:probe():214] Publishing system info
2025-07-13 18:13:27,164 DEBUG   HandlerThread:2940712 [system_info.py:_save_pip():52] Saving list of pip packages installed into the current environment
2025-07-13 18:13:27,165 DEBUG   HandlerThread:2940712 [system_info.py:_save_pip():67] Saving pip packages done
2025-07-13 18:13:27,165 DEBUG   HandlerThread:2940712 [system_info.py:_save_conda():75] Saving list of conda packages installed into the current environment
2025-07-13 18:13:27,962 INFO    Thread-12 :2940712 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-uo699u7g/files/requirements.txt
2025-07-13 18:13:27,963 INFO    Thread-12 :2940712 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-uo699u7g/files/wandb-summary.json
2025-07-13 18:13:27,963 INFO    Thread-12 :2940712 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-uo699u7g/files/conda-environment.yaml
2025-07-13 18:13:33,469 DEBUG   HandlerThread:2940712 [system_info.py:_save_conda():86] Saving conda packages done
2025-07-13 18:13:33,470 INFO    HandlerThread:2940712 [system_monitor.py:probe():216] Finished publishing system info
2025-07-13 18:13:33,479 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:13:33,479 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: keepalive
2025-07-13 18:13:33,480 DEBUG   SenderThread:2940712 [sender.py:send():369] send: files
2025-07-13 18:13:33,480 INFO    SenderThread:2940712 [sender.py:_save_file():1354] saving file wandb-metadata.json with policy now
2025-07-13 18:13:33,486 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: stop_status
2025-07-13 18:13:33,487 DEBUG   SenderThread:2940712 [sender.py:send_request():396] send_request: stop_status
2025-07-13 18:13:33,652 DEBUG   SenderThread:2940712 [sender.py:send():369] send: telemetry
2025-07-13 18:13:33,872 INFO    wandb-upload_0:2940712 [upload_job.py:push():133] Uploaded file /tmp/tmp1xrey1trwandb/vec4ojzc-wandb-metadata.json
2025-07-13 18:13:33,962 INFO    Thread-12 :2940712 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-uo699u7g/files/conda-environment.yaml
2025-07-13 18:13:33,963 INFO    Thread-12 :2940712 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-uo699u7g/files/wandb-metadata.json
2025-07-13 18:13:33,963 INFO    Thread-12 :2940712 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-uo699u7g/files/output.log
2025-07-13 18:13:35,963 INFO    Thread-12 :2940712 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-uo699u7g/files/output.log
2025-07-13 18:13:37,612 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:13:37,964 INFO    Thread-12 :2940712 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-uo699u7g/files/output.log
2025-07-13 18:13:42,569 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:13:42,570 DEBUG   SenderThread:2940712 [sender.py:send():369] send: history
2025-07-13 18:13:42,571 DEBUG   SenderThread:2940712 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:13:42,576 INFO    SenderThread:2940712 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:13:42,965 INFO    Thread-12 :2940712 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-uo699u7g/files/wandb-summary.json
2025-07-13 18:13:43,577 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:13:46,025 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:13:46,026 DEBUG   SenderThread:2940712 [sender.py:send():369] send: history
2025-07-13 18:13:46,026 DEBUG   SenderThread:2940712 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:13:46,028 INFO    SenderThread:2940712 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:13:46,967 INFO    Thread-12 :2940712 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-uo699u7g/files/wandb-summary.json
2025-07-13 18:13:48,486 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: stop_status
2025-07-13 18:13:48,487 DEBUG   SenderThread:2940712 [sender.py:send_request():396] send_request: stop_status
2025-07-13 18:13:48,615 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:13:49,447 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:13:49,448 DEBUG   SenderThread:2940712 [sender.py:send():369] send: history
2025-07-13 18:13:49,448 DEBUG   SenderThread:2940712 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:13:49,449 INFO    SenderThread:2940712 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:13:49,968 INFO    Thread-12 :2940712 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-uo699u7g/files/wandb-summary.json
2025-07-13 18:13:52,641 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:13:52,642 DEBUG   SenderThread:2940712 [sender.py:send():369] send: history
2025-07-13 18:13:52,643 DEBUG   SenderThread:2940712 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:13:52,644 INFO    SenderThread:2940712 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:13:52,969 INFO    Thread-12 :2940712 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-uo699u7g/files/wandb-summary.json
2025-07-13 18:13:53,644 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:13:55,617 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:13:55,618 DEBUG   SenderThread:2940712 [sender.py:send():369] send: history
2025-07-13 18:13:55,618 DEBUG   SenderThread:2940712 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:13:55,619 INFO    SenderThread:2940712 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:13:55,970 INFO    Thread-12 :2940712 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-uo699u7g/files/wandb-summary.json
2025-07-13 18:13:59,625 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:13:59,633 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:13:59,692 DEBUG   SenderThread:2940712 [sender.py:send():369] send: history
2025-07-13 18:13:59,692 DEBUG   SenderThread:2940712 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:13:59,694 INFO    SenderThread:2940712 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:13:59,972 INFO    Thread-12 :2940712 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-uo699u7g/files/config.yaml
2025-07-13 18:13:59,972 INFO    Thread-12 :2940712 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-uo699u7g/files/wandb-summary.json
2025-07-13 18:14:02,905 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:14:02,907 DEBUG   SenderThread:2940712 [sender.py:send():369] send: history
2025-07-13 18:14:02,907 DEBUG   SenderThread:2940712 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:14:02,907 INFO    SenderThread:2940712 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:14:02,973 INFO    Thread-12 :2940712 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-uo699u7g/files/wandb-summary.json
2025-07-13 18:14:03,486 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: stop_status
2025-07-13 18:14:03,487 DEBUG   SenderThread:2940712 [sender.py:send_request():396] send_request: stop_status
2025-07-13 18:14:05,583 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:14:06,413 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:14:06,414 DEBUG   SenderThread:2940712 [sender.py:send():369] send: history
2025-07-13 18:14:06,415 DEBUG   SenderThread:2940712 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:14:06,415 INFO    SenderThread:2940712 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:14:06,974 INFO    Thread-12 :2940712 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-uo699u7g/files/wandb-summary.json
2025-07-13 18:14:10,063 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:14:10,064 DEBUG   SenderThread:2940712 [sender.py:send():369] send: history
2025-07-13 18:14:10,065 DEBUG   SenderThread:2940712 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:14:10,065 INFO    SenderThread:2940712 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:14:10,976 INFO    Thread-12 :2940712 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-uo699u7g/files/wandb-summary.json
2025-07-13 18:14:11,065 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:14:13,648 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:14:13,648 DEBUG   SenderThread:2940712 [sender.py:send():369] send: history
2025-07-13 18:14:13,649 DEBUG   SenderThread:2940712 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:14:13,649 INFO    SenderThread:2940712 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:14:13,977 INFO    Thread-12 :2940712 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-uo699u7g/files/wandb-summary.json
2025-07-13 18:14:16,651 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:14:17,020 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:14:17,020 DEBUG   SenderThread:2940712 [sender.py:send():369] send: history
2025-07-13 18:14:17,021 DEBUG   SenderThread:2940712 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:14:17,021 INFO    SenderThread:2940712 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:14:17,978 INFO    Thread-12 :2940712 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-uo699u7g/files/wandb-summary.json
2025-07-13 18:14:18,487 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: stop_status
2025-07-13 18:14:18,488 DEBUG   SenderThread:2940712 [sender.py:send_request():396] send_request: stop_status
2025-07-13 18:14:20,085 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:14:20,086 DEBUG   SenderThread:2940712 [sender.py:send():369] send: history
2025-07-13 18:14:20,087 DEBUG   SenderThread:2940712 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:14:20,087 INFO    SenderThread:2940712 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:14:20,979 INFO    Thread-12 :2940712 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-uo699u7g/files/wandb-summary.json
2025-07-13 18:14:22,089 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:14:22,991 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:14:22,992 DEBUG   SenderThread:2940712 [sender.py:send():369] send: history
2025-07-13 18:14:22,992 DEBUG   SenderThread:2940712 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:14:22,992 INFO    SenderThread:2940712 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:14:23,980 INFO    Thread-12 :2940712 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-uo699u7g/files/wandb-summary.json
2025-07-13 18:14:26,445 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:14:26,446 DEBUG   SenderThread:2940712 [sender.py:send():369] send: history
2025-07-13 18:14:26,446 DEBUG   SenderThread:2940712 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:14:26,447 INFO    SenderThread:2940712 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:14:26,981 INFO    Thread-12 :2940712 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-uo699u7g/files/wandb-summary.json
2025-07-13 18:14:27,110 DEBUG   SystemMonitor:2940712 [system_monitor.py:_start():159] Starting system metrics aggregation loop
2025-07-13 18:14:27,112 DEBUG   SenderThread:2940712 [sender.py:send():369] send: stats
2025-07-13 18:14:27,113 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:14:30,271 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:14:30,273 DEBUG   SenderThread:2940712 [sender.py:send():369] send: history
2025-07-13 18:14:30,273 DEBUG   SenderThread:2940712 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:14:30,273 INFO    SenderThread:2940712 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:14:30,983 INFO    Thread-12 :2940712 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-uo699u7g/files/wandb-summary.json
2025-07-13 18:14:32,274 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:14:33,486 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: stop_status
2025-07-13 18:14:33,486 DEBUG   SenderThread:2940712 [sender.py:send_request():396] send_request: stop_status
2025-07-13 18:14:33,660 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:14:33,661 DEBUG   SenderThread:2940712 [sender.py:send():369] send: history
2025-07-13 18:14:33,661 DEBUG   SenderThread:2940712 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:14:33,661 INFO    SenderThread:2940712 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:14:33,984 INFO    Thread-12 :2940712 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-uo699u7g/files/wandb-summary.json
2025-07-13 18:14:36,626 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:14:36,628 DEBUG   SenderThread:2940712 [sender.py:send():369] send: history
2025-07-13 18:14:36,628 DEBUG   SenderThread:2940712 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:14:36,633 INFO    SenderThread:2940712 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:14:36,985 INFO    Thread-12 :2940712 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-uo699u7g/files/wandb-summary.json
2025-07-13 18:14:37,633 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:14:38,782 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:14:38,783 DEBUG   SenderThread:2940712 [sender.py:send():369] send: history
2025-07-13 18:14:38,783 DEBUG   SenderThread:2940712 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:14:38,784 INFO    SenderThread:2940712 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:14:38,986 INFO    Thread-12 :2940712 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-uo699u7g/files/wandb-summary.json
2025-07-13 18:14:41,703 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:14:41,703 DEBUG   SenderThread:2940712 [sender.py:send():369] send: history
2025-07-13 18:14:41,703 DEBUG   SenderThread:2940712 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:14:41,704 INFO    SenderThread:2940712 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:14:41,987 INFO    Thread-12 :2940712 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-uo699u7g/files/wandb-summary.json
2025-07-13 18:14:42,704 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:14:44,241 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:14:44,242 DEBUG   SenderThread:2940712 [sender.py:send():369] send: history
2025-07-13 18:14:44,242 DEBUG   SenderThread:2940712 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:14:44,243 INFO    SenderThread:2940712 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:14:44,988 INFO    Thread-12 :2940712 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-uo699u7g/files/wandb-summary.json
2025-07-13 18:14:47,504 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:14:47,505 DEBUG   SenderThread:2940712 [sender.py:send():369] send: history
2025-07-13 18:14:47,505 DEBUG   SenderThread:2940712 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:14:47,507 INFO    SenderThread:2940712 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:14:47,989 INFO    Thread-12 :2940712 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-uo699u7g/files/wandb-summary.json
2025-07-13 18:14:48,486 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: stop_status
2025-07-13 18:14:48,487 DEBUG   SenderThread:2940712 [sender.py:send_request():396] send_request: stop_status
2025-07-13 18:14:48,557 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:14:51,231 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:14:51,232 DEBUG   SenderThread:2940712 [sender.py:send():369] send: history
2025-07-13 18:14:51,232 DEBUG   SenderThread:2940712 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:14:51,233 INFO    SenderThread:2940712 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:14:51,990 INFO    Thread-12 :2940712 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-uo699u7g/files/wandb-summary.json
2025-07-13 18:14:53,358 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:14:53,359 DEBUG   SenderThread:2940712 [sender.py:send():369] send: history
2025-07-13 18:14:53,359 DEBUG   SenderThread:2940712 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:14:53,360 INFO    SenderThread:2940712 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:14:53,991 INFO    Thread-12 :2940712 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-uo699u7g/files/wandb-summary.json
2025-07-13 18:14:54,361 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:14:56,134 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:14:56,135 DEBUG   SenderThread:2940712 [sender.py:send():369] send: history
2025-07-13 18:14:56,135 DEBUG   SenderThread:2940712 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:14:56,136 INFO    SenderThread:2940712 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:14:56,992 INFO    Thread-12 :2940712 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-uo699u7g/files/wandb-summary.json
2025-07-13 18:14:57,113 DEBUG   SenderThread:2940712 [sender.py:send():369] send: stats
2025-07-13 18:14:59,063 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:14:59,064 DEBUG   SenderThread:2940712 [sender.py:send():369] send: history
2025-07-13 18:14:59,065 DEBUG   SenderThread:2940712 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:14:59,066 INFO    SenderThread:2940712 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:14:59,993 INFO    Thread-12 :2940712 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-uo699u7g/files/wandb-summary.json
2025-07-13 18:15:00,067 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:15:02,544 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:15:02,545 DEBUG   SenderThread:2940712 [sender.py:send():369] send: history
2025-07-13 18:15:02,545 DEBUG   SenderThread:2940712 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:15:02,546 INFO    SenderThread:2940712 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:15:02,994 INFO    Thread-12 :2940712 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-uo699u7g/files/wandb-summary.json
2025-07-13 18:15:03,486 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: stop_status
2025-07-13 18:15:03,487 DEBUG   SenderThread:2940712 [sender.py:send_request():396] send_request: stop_status
2025-07-13 18:15:05,553 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:15:06,255 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:15:06,256 DEBUG   SenderThread:2940712 [sender.py:send():369] send: history
2025-07-13 18:15:06,257 DEBUG   SenderThread:2940712 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:15:06,258 INFO    SenderThread:2940712 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:15:06,995 INFO    Thread-12 :2940712 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-uo699u7g/files/wandb-summary.json
2025-07-13 18:15:07,193 DEBUG   SenderThread:2940712 [sender.py:send():369] send: exit
2025-07-13 18:15:07,193 INFO    SenderThread:2940712 [sender.py:send_exit():574] handling exit code: 1
2025-07-13 18:15:07,194 INFO    SenderThread:2940712 [sender.py:send_exit():576] handling runtime: 100
2025-07-13 18:15:07,194 INFO    SenderThread:2940712 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:15:07,194 INFO    SenderThread:2940712 [sender.py:send_exit():582] send defer
2025-07-13 18:15:07,194 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:15:07,195 INFO    HandlerThread:2940712 [handler.py:handle_request_defer():170] handle defer: 0
2025-07-13 18:15:07,195 DEBUG   SenderThread:2940712 [sender.py:send_request():396] send_request: defer
2025-07-13 18:15:07,195 INFO    SenderThread:2940712 [sender.py:send_request_defer():598] handle sender defer: 0
2025-07-13 18:15:07,195 INFO    SenderThread:2940712 [sender.py:transition_state():602] send defer: 1
2025-07-13 18:15:07,195 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:15:07,195 INFO    HandlerThread:2940712 [handler.py:handle_request_defer():170] handle defer: 1
2025-07-13 18:15:07,195 DEBUG   SenderThread:2940712 [sender.py:send_request():396] send_request: defer
2025-07-13 18:15:07,195 INFO    SenderThread:2940712 [sender.py:send_request_defer():598] handle sender defer: 1
2025-07-13 18:15:07,195 INFO    SenderThread:2940712 [sender.py:transition_state():602] send defer: 2
2025-07-13 18:15:07,195 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:15:07,195 INFO    HandlerThread:2940712 [handler.py:handle_request_defer():170] handle defer: 2
2025-07-13 18:15:07,195 INFO    HandlerThread:2940712 [system_monitor.py:finish():190] Stopping system monitor
2025-07-13 18:15:07,196 DEBUG   SystemMonitor:2940712 [system_monitor.py:_start():166] Finished system metrics aggregation loop
2025-07-13 18:15:07,196 INFO    HandlerThread:2940712 [interfaces.py:finish():202] Joined cpu monitor
2025-07-13 18:15:07,196 DEBUG   SystemMonitor:2940712 [system_monitor.py:_start():170] Publishing last batch of metrics
2025-07-13 18:15:07,196 INFO    HandlerThread:2940712 [interfaces.py:finish():202] Joined disk monitor
2025-07-13 18:15:07,325 INFO    HandlerThread:2940712 [interfaces.py:finish():202] Joined gpu monitor
2025-07-13 18:15:07,325 INFO    HandlerThread:2940712 [interfaces.py:finish():202] Joined memory monitor
2025-07-13 18:15:07,325 INFO    HandlerThread:2940712 [interfaces.py:finish():202] Joined network monitor
2025-07-13 18:15:07,326 DEBUG   SenderThread:2940712 [sender.py:send_request():396] send_request: defer
2025-07-13 18:15:07,326 INFO    SenderThread:2940712 [sender.py:send_request_defer():598] handle sender defer: 2
2025-07-13 18:15:07,326 INFO    SenderThread:2940712 [sender.py:transition_state():602] send defer: 3
2025-07-13 18:15:07,327 DEBUG   SenderThread:2940712 [sender.py:send():369] send: stats
2025-07-13 18:15:07,327 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:15:07,327 INFO    HandlerThread:2940712 [handler.py:handle_request_defer():170] handle defer: 3
2025-07-13 18:15:07,327 DEBUG   SenderThread:2940712 [sender.py:send_request():396] send_request: defer
2025-07-13 18:15:07,327 INFO    SenderThread:2940712 [sender.py:send_request_defer():598] handle sender defer: 3
2025-07-13 18:15:07,327 INFO    SenderThread:2940712 [sender.py:transition_state():602] send defer: 4
2025-07-13 18:15:07,327 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:15:07,327 INFO    HandlerThread:2940712 [handler.py:handle_request_defer():170] handle defer: 4
2025-07-13 18:15:07,328 DEBUG   SenderThread:2940712 [sender.py:send_request():396] send_request: defer
2025-07-13 18:15:07,328 INFO    SenderThread:2940712 [sender.py:send_request_defer():598] handle sender defer: 4
2025-07-13 18:15:07,328 INFO    SenderThread:2940712 [sender.py:transition_state():602] send defer: 5
2025-07-13 18:15:07,328 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:15:07,328 INFO    HandlerThread:2940712 [handler.py:handle_request_defer():170] handle defer: 5
2025-07-13 18:15:07,328 DEBUG   SenderThread:2940712 [sender.py:send():369] send: summary
2025-07-13 18:15:07,328 INFO    SenderThread:2940712 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:15:07,329 DEBUG   SenderThread:2940712 [sender.py:send_request():396] send_request: defer
2025-07-13 18:15:07,329 INFO    SenderThread:2940712 [sender.py:send_request_defer():598] handle sender defer: 5
2025-07-13 18:15:07,329 INFO    SenderThread:2940712 [sender.py:transition_state():602] send defer: 6
2025-07-13 18:15:07,329 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:15:07,329 INFO    HandlerThread:2940712 [handler.py:handle_request_defer():170] handle defer: 6
2025-07-13 18:15:07,329 DEBUG   SenderThread:2940712 [sender.py:send_request():396] send_request: defer
2025-07-13 18:15:07,329 INFO    SenderThread:2940712 [sender.py:send_request_defer():598] handle sender defer: 6
2025-07-13 18:15:07,329 INFO    SenderThread:2940712 [sender.py:transition_state():602] send defer: 7
2025-07-13 18:15:07,329 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:15:07,329 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:15:07,329 INFO    HandlerThread:2940712 [handler.py:handle_request_defer():170] handle defer: 7
2025-07-13 18:15:07,330 DEBUG   SenderThread:2940712 [sender.py:send_request():396] send_request: defer
2025-07-13 18:15:07,330 INFO    SenderThread:2940712 [sender.py:send_request_defer():598] handle sender defer: 7
2025-07-13 18:15:07,996 INFO    Thread-12 :2940712 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-uo699u7g/files/wandb-summary.json
2025-07-13 18:15:08,194 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: poll_exit
2025-07-13 18:15:09,198 INFO    SenderThread:2940712 [sender.py:transition_state():602] send defer: 8
2025-07-13 18:15:09,198 DEBUG   SenderThread:2940712 [sender.py:send_request():396] send_request: poll_exit
2025-07-13 18:15:09,199 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:15:09,200 INFO    HandlerThread:2940712 [handler.py:handle_request_defer():170] handle defer: 8
2025-07-13 18:15:09,200 DEBUG   SenderThread:2940712 [sender.py:send_request():396] send_request: defer
2025-07-13 18:15:09,201 INFO    SenderThread:2940712 [sender.py:send_request_defer():598] handle sender defer: 8
2025-07-13 18:15:09,201 INFO    SenderThread:2940712 [job_builder.py:build():232] Attempting to build job artifact
2025-07-13 18:15:09,201 INFO    SenderThread:2940712 [job_builder.py:build():256] is repo sourced job
2025-07-13 18:15:09,204 INFO    SenderThread:2940712 [job_builder.py:build():297] adding wandb-job metadata file
2025-07-13 18:15:09,210 INFO    SenderThread:2940712 [sender.py:transition_state():602] send defer: 9
2025-07-13 18:15:09,210 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:15:09,211 DEBUG   SenderThread:2940712 [sender.py:send():369] send: artifact
2025-07-13 18:15:09,211 INFO    HandlerThread:2940712 [handler.py:handle_request_defer():170] handle defer: 9
2025-07-13 18:15:09,996 INFO    wandb-upload_0:2940712 [upload_job.py:push():91] Uploaded file /gel/usr/akath/.local/share/wandb/artifacts/staging/tmpg4wht2qu
2025-07-13 18:15:09,997 INFO    Thread-12 :2940712 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-uo699u7g/files/output.log
2025-07-13 18:15:10,018 INFO    wandb-upload_1:2940712 [upload_job.py:push():91] Uploaded file /gel/usr/akath/.local/share/wandb/artifacts/staging/tmplovxhlpb
2025-07-13 18:15:11,037 INFO    SenderThread:2940712 [sender.py:send_artifact():1450] sent artifact job-https___github.com_Shrinidhibhat87_codingchallenge_sereact.git_main.py - {'id': 'QXJ0aWZhY3Q6MTg3NzkyMzY4NQ==', 'digest': '01f3dd739b2c8dbc3409e0ca40376bfa', 'state': 'PENDING', 'aliases': [], 'artifactSequence': {'id': 'QXJ0aWZhY3RDb2xsZWN0aW9uOjY4NDgwMzQ0Ng==', 'latestArtifact': {'id': 'QXJ0aWZhY3Q6MTg3NTAxNDMwNQ==', 'versionIndex': 5}}, 'version': 'latest'}
2025-07-13 18:15:11,038 DEBUG   SenderThread:2940712 [sender.py:send_request():396] send_request: defer
2025-07-13 18:15:11,038 INFO    SenderThread:2940712 [sender.py:send_request_defer():598] handle sender defer: 9
2025-07-13 18:15:11,038 INFO    SenderThread:2940712 [dir_watcher.py:finish():359] shutting down directory watcher
2025-07-13 18:15:11,998 INFO    SenderThread:2940712 [dir_watcher.py:finish():389] scan: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-uo699u7g/files
2025-07-13 18:15:11,999 INFO    SenderThread:2940712 [dir_watcher.py:finish():403] scan save: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-uo699u7g/files/config.yaml config.yaml
2025-07-13 18:15:11,999 INFO    SenderThread:2940712 [dir_watcher.py:finish():403] scan save: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-uo699u7g/files/wandb-summary.json wandb-summary.json
2025-07-13 18:15:12,000 INFO    SenderThread:2940712 [dir_watcher.py:finish():403] scan save: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-uo699u7g/files/requirements.txt requirements.txt
2025-07-13 18:15:12,006 INFO    SenderThread:2940712 [dir_watcher.py:finish():403] scan save: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-uo699u7g/files/conda-environment.yaml conda-environment.yaml
2025-07-13 18:15:12,012 INFO    SenderThread:2940712 [dir_watcher.py:finish():403] scan save: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-uo699u7g/files/wandb-metadata.json wandb-metadata.json
2025-07-13 18:15:12,013 INFO    SenderThread:2940712 [dir_watcher.py:finish():403] scan save: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-uo699u7g/files/output.log output.log
2025-07-13 18:15:12,026 INFO    SenderThread:2940712 [sender.py:transition_state():602] send defer: 10
2025-07-13 18:15:12,027 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:15:12,027 INFO    HandlerThread:2940712 [handler.py:handle_request_defer():170] handle defer: 10
2025-07-13 18:15:12,032 DEBUG   SenderThread:2940712 [sender.py:send_request():396] send_request: defer
2025-07-13 18:15:12,033 INFO    SenderThread:2940712 [sender.py:send_request_defer():598] handle sender defer: 10
2025-07-13 18:15:12,036 INFO    SenderThread:2940712 [file_pusher.py:finish():159] shutting down file pusher
2025-07-13 18:15:12,346 INFO    wandb-upload_3:2940712 [upload_job.py:push():133] Uploaded file /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-uo699u7g/files/config.yaml
2025-07-13 18:15:12,366 INFO    wandb-upload_5:2940712 [upload_job.py:push():133] Uploaded file /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-uo699u7g/files/requirements.txt
2025-07-13 18:15:12,372 INFO    wandb-upload_4:2940712 [upload_job.py:push():133] Uploaded file /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-uo699u7g/files/wandb-summary.json
2025-07-13 18:15:12,375 INFO    wandb-upload_0:2940712 [upload_job.py:push():133] Uploaded file /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-uo699u7g/files/output.log
2025-07-13 18:15:12,460 INFO    wandb-upload_6:2940712 [upload_job.py:push():133] Uploaded file /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-uo699u7g/files/conda-environment.yaml
2025-07-13 18:15:12,661 INFO    Thread-11 :2940712 [sender.py:transition_state():602] send defer: 11
2025-07-13 18:15:12,661 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:15:12,662 INFO    HandlerThread:2940712 [handler.py:handle_request_defer():170] handle defer: 11
2025-07-13 18:15:12,662 DEBUG   SenderThread:2940712 [sender.py:send_request():396] send_request: defer
2025-07-13 18:15:12,663 INFO    SenderThread:2940712 [sender.py:send_request_defer():598] handle sender defer: 11
2025-07-13 18:15:12,663 INFO    SenderThread:2940712 [file_pusher.py:join():164] waiting for file pusher
2025-07-13 18:15:12,663 INFO    SenderThread:2940712 [sender.py:transition_state():602] send defer: 12
2025-07-13 18:15:12,663 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:15:12,663 INFO    HandlerThread:2940712 [handler.py:handle_request_defer():170] handle defer: 12
2025-07-13 18:15:12,664 DEBUG   SenderThread:2940712 [sender.py:send_request():396] send_request: defer
2025-07-13 18:15:12,664 INFO    SenderThread:2940712 [sender.py:send_request_defer():598] handle sender defer: 12
2025-07-13 18:15:12,726 INFO    SenderThread:2940712 [sender.py:transition_state():602] send defer: 13
2025-07-13 18:15:12,726 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:15:12,727 INFO    HandlerThread:2940712 [handler.py:handle_request_defer():170] handle defer: 13
2025-07-13 18:15:12,727 DEBUG   SenderThread:2940712 [sender.py:send_request():396] send_request: defer
2025-07-13 18:15:12,727 INFO    SenderThread:2940712 [sender.py:send_request_defer():598] handle sender defer: 13
2025-07-13 18:15:12,727 INFO    SenderThread:2940712 [sender.py:transition_state():602] send defer: 14
2025-07-13 18:15:12,728 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:15:12,728 DEBUG   SenderThread:2940712 [sender.py:send():369] send: final
2025-07-13 18:15:12,728 INFO    HandlerThread:2940712 [handler.py:handle_request_defer():170] handle defer: 14
2025-07-13 18:15:12,728 DEBUG   SenderThread:2940712 [sender.py:send():369] send: footer
2025-07-13 18:15:12,729 DEBUG   SenderThread:2940712 [sender.py:send_request():396] send_request: defer
2025-07-13 18:15:12,729 INFO    SenderThread:2940712 [sender.py:send_request_defer():598] handle sender defer: 14
2025-07-13 18:15:12,730 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: poll_exit
2025-07-13 18:15:12,730 DEBUG   SenderThread:2940712 [sender.py:send_request():396] send_request: poll_exit
2025-07-13 18:15:12,731 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: server_info
2025-07-13 18:15:12,732 DEBUG   SenderThread:2940712 [sender.py:send_request():396] send_request: server_info
2025-07-13 18:15:12,737 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: get_summary
2025-07-13 18:15:12,738 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: sampled_history
2025-07-13 18:15:12,783 INFO    MainThread:2940712 [wandb_run.py:_footer_history_summary_info():3467] rendering history
2025-07-13 18:15:12,784 INFO    MainThread:2940712 [wandb_run.py:_footer_history_summary_info():3499] rendering summary
2025-07-13 18:15:12,785 INFO    MainThread:2940712 [wandb_run.py:_footer_sync_info():3426] logging synced files
2025-07-13 18:15:12,785 DEBUG   HandlerThread:2940712 [handler.py:handle_request():144] handle_request: shutdown
2025-07-13 18:15:12,786 INFO    HandlerThread:2940712 [handler.py:finish():854] shutting down handler
2025-07-13 18:15:13,732 INFO    WriterThread:2940712 [datastore.py:close():298] close: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-uo699u7g/run-uo699u7g.wandb
2025-07-13 18:15:13,783 INFO    SenderThread:2940712 [sender.py:finish():1526] shutting down sender
2025-07-13 18:15:13,784 INFO    SenderThread:2940712 [file_pusher.py:finish():159] shutting down file pusher
2025-07-13 18:15:13,784 INFO    SenderThread:2940712 [file_pusher.py:join():164] waiting for file pusher
