2025-07-12 00:49:22,467 INFO    StreamThr :2859588 [internal.py:wandb_internal():89] W&B internal server running at pid: 2859588, started at: 2025-07-12 00:49:22.466468
2025-07-12 00:49:22,469 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: status
2025-07-12 00:49:22,471 INFO    WriterThread:2859588 [datastore.py:open_for_write():85] open: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-36tgsa8a/run-36tgsa8a.wandb
2025-07-12 00:49:22,473 DEBUG   SenderThread:2859588 [sender.py:send():369] send: header
2025-07-12 00:49:22,488 DEBUG   SenderThread:2859588 [sender.py:send():369] send: run
2025-07-12 00:49:22,797 INFO    SenderThread:2859588 [dir_watcher.py:__init__():211] watching files in: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-36tgsa8a/files
2025-07-12 00:49:22,797 INFO    SenderThread:2859588 [sender.py:_start_run_threads():1103] run started: 36tgsa8a with start time 1752295762.465052
2025-07-12 00:49:22,798 DEBUG   SenderThread:2859588 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:49:22,799 INFO    SenderThread:2859588 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:49:22,810 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: check_version
2025-07-12 00:49:22,811 DEBUG   SenderThread:2859588 [sender.py:send_request():396] send_request: check_version
2025-07-12 00:49:22,886 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: run_start
2025-07-12 00:49:22,891 DEBUG   HandlerThread:2859588 [system_info.py:__init__():31] System info init
2025-07-12 00:49:22,891 DEBUG   HandlerThread:2859588 [system_info.py:__init__():46] System info init done
2025-07-12 00:49:22,891 INFO    HandlerThread:2859588 [system_monitor.py:start():181] Starting system monitor
2025-07-12 00:49:22,891 INFO    SystemMonitor:2859588 [system_monitor.py:_start():145] Starting system asset monitoring threads
2025-07-12 00:49:22,891 INFO    HandlerThread:2859588 [system_monitor.py:probe():201] Collecting system info
2025-07-12 00:49:22,892 INFO    SystemMonitor:2859588 [interfaces.py:start():190] Started cpu monitoring
2025-07-12 00:49:22,892 INFO    SystemMonitor:2859588 [interfaces.py:start():190] Started disk monitoring
2025-07-12 00:49:22,893 INFO    SystemMonitor:2859588 [interfaces.py:start():190] Started gpu monitoring
2025-07-12 00:49:22,893 INFO    SystemMonitor:2859588 [interfaces.py:start():190] Started memory monitoring
2025-07-12 00:49:22,894 INFO    SystemMonitor:2859588 [interfaces.py:start():190] Started network monitoring
2025-07-12 00:49:22,911 DEBUG   HandlerThread:2859588 [system_info.py:probe():195] Probing system
2025-07-12 00:49:22,919 DEBUG   HandlerThread:2859588 [system_info.py:_probe_git():180] Probing git
2025-07-12 00:49:22,937 DEBUG   HandlerThread:2859588 [system_info.py:_probe_git():188] Probing git done
2025-07-12 00:49:22,937 DEBUG   HandlerThread:2859588 [system_info.py:probe():240] Probing system done
2025-07-12 00:49:22,937 DEBUG   HandlerThread:2859588 [system_monitor.py:probe():210] {'os': 'Linux-5.4.0-77-generic-x86_64-with-debian-bullseye-sid', 'python': '3.7.16', 'heartbeatAt': '2025-07-12T04:49:22.912078', 'startedAt': '2025-07-12T04:49:22.451543', 'docker': None, 'cuda': None, 'args': ('--local_rank=1', '--cfg', 'config/base_train.yaml', '--output', '/home-local2/akath.extra.nobkp/sereact', '--data-path', '/home-local2/akath.extra.nobkp/dl_challenge', '--batch-size', '1'), 'state': 'running', 'program': 'main.py', 'codePath': 'main.py', 'git': {'remote': 'https://github.com/Shrinidhibhat87/codingchallenge_sereact.git', 'commit': 'a3ff60e13dfced588b500c8a1de00f36fdf22d49'}, 'email': '<EMAIL>', 'root': '/home-local/akath.nobkp/codingchallenge_sereact', 'host': 'lv3-32190', 'username': 'akath', 'executable': '/gel/usr/akath/.conda/envs/swin/bin/python', 'cpu_count': 6, 'cpu_count_logical': 12, 'cpu_freq': {'current': 2.1245833333333333, 'min': 1200.0, 'max': 4000.0}, 'cpu_freq_per_core': [{'current': 1.99, 'min': 1200.0, 'max': 4000.0}, {'current': 1.771, 'min': 1200.0, 'max': 4000.0}, {'current': 1.77, 'min': 1200.0, 'max': 4000.0}, {'current': 2.281, 'min': 1200.0, 'max': 4000.0}, {'current': 1.605, 'min': 1200.0, 'max': 4000.0}, {'current': 2.622, 'min': 1200.0, 'max': 4000.0}, {'current': 1.574, 'min': 1200.0, 'max': 4000.0}, {'current': 2.251, 'min': 1200.0, 'max': 4000.0}, {'current': 2.461, 'min': 1200.0, 'max': 4000.0}, {'current': 2.562, 'min': 1200.0, 'max': 4000.0}, {'current': 1.935, 'min': 1200.0, 'max': 4000.0}, {'current': 2.673, 'min': 1200.0, 'max': 4000.0}], 'disk': {'total': 111.2200813293457, 'used': 53.24767303466797}, 'gpu': 'TITAN Xp', 'gpu_count': 3, 'gpu_devices': [{'name': 'TITAN Xp', 'memory_total': 12787122176}, {'name': 'TITAN Xp', 'memory_total': 12788498432}, {'name': 'TITAN Xp', 'memory_total': 12788498432}], 'memory': {'total': 62.72977066040039}}
2025-07-12 00:49:22,937 INFO    HandlerThread:2859588 [system_monitor.py:probe():211] Finished collecting system info
2025-07-12 00:49:22,937 INFO    HandlerThread:2859588 [system_monitor.py:probe():214] Publishing system info
2025-07-12 00:49:22,937 DEBUG   HandlerThread:2859588 [system_info.py:_save_pip():52] Saving list of pip packages installed into the current environment
2025-07-12 00:49:22,938 DEBUG   HandlerThread:2859588 [system_info.py:_save_pip():67] Saving pip packages done
2025-07-12 00:49:22,938 DEBUG   HandlerThread:2859588 [system_info.py:_save_conda():75] Saving list of conda packages installed into the current environment
2025-07-12 00:49:23,801 INFO    Thread-12 :2859588 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-36tgsa8a/files/requirements.txt
2025-07-12 00:49:23,802 INFO    Thread-12 :2859588 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-36tgsa8a/files/wandb-summary.json
2025-07-12 00:49:23,802 INFO    Thread-12 :2859588 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-36tgsa8a/files/conda-environment.yaml
2025-07-12 00:49:25,388 DEBUG   HandlerThread:2859588 [system_info.py:_save_conda():86] Saving conda packages done
2025-07-12 00:49:25,390 INFO    HandlerThread:2859588 [system_monitor.py:probe():216] Finished publishing system info
2025-07-12 00:49:25,402 DEBUG   SenderThread:2859588 [sender.py:send():369] send: files
2025-07-12 00:49:25,402 INFO    SenderThread:2859588 [sender.py:_save_file():1354] saving file wandb-metadata.json with policy now
2025-07-12 00:49:25,412 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: stop_status
2025-07-12 00:49:25,412 DEBUG   SenderThread:2859588 [sender.py:send_request():396] send_request: stop_status
2025-07-12 00:49:25,528 DEBUG   SenderThread:2859588 [sender.py:send():369] send: telemetry
2025-07-12 00:49:25,799 INFO    Thread-12 :2859588 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-36tgsa8a/files/conda-environment.yaml
2025-07-12 00:49:25,799 INFO    Thread-12 :2859588 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-36tgsa8a/files/wandb-metadata.json
2025-07-12 00:49:25,799 INFO    Thread-12 :2859588 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-36tgsa8a/files/output.log
2025-07-12 00:49:25,804 INFO    wandb-upload_0:2859588 [upload_job.py:push():133] Uploaded file /tmp/tmp4o9xwguhwandb/1tn171pt-wandb-metadata.json
2025-07-12 00:49:27,529 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: status_report
2025-07-12 00:49:27,800 INFO    Thread-12 :2859588 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-36tgsa8a/files/output.log
2025-07-12 00:49:29,800 INFO    Thread-12 :2859588 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-36tgsa8a/files/output.log
2025-07-12 00:49:31,590 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:49:31,592 DEBUG   SenderThread:2859588 [sender.py:send():369] send: history
2025-07-12 00:49:31,592 DEBUG   SenderThread:2859588 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:49:31,601 INFO    SenderThread:2859588 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:49:31,801 INFO    Thread-12 :2859588 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-36tgsa8a/files/wandb-summary.json
2025-07-12 00:49:32,601 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: status_report
2025-07-12 00:49:34,206 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:49:34,207 DEBUG   SenderThread:2859588 [sender.py:send():369] send: history
2025-07-12 00:49:34,207 DEBUG   SenderThread:2859588 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:49:34,208 INFO    SenderThread:2859588 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:49:34,802 INFO    Thread-12 :2859588 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-36tgsa8a/files/wandb-summary.json
2025-07-12 00:49:36,015 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:49:36,016 DEBUG   SenderThread:2859588 [sender.py:send():369] send: history
2025-07-12 00:49:36,017 DEBUG   SenderThread:2859588 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:49:36,021 INFO    SenderThread:2859588 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:49:36,803 INFO    Thread-12 :2859588 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-36tgsa8a/files/wandb-summary.json
2025-07-12 00:49:38,023 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: status_report
2025-07-12 00:49:38,151 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:49:38,152 DEBUG   SenderThread:2859588 [sender.py:send():369] send: history
2025-07-12 00:49:38,152 DEBUG   SenderThread:2859588 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:49:38,155 INFO    SenderThread:2859588 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:49:38,804 INFO    Thread-12 :2859588 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-36tgsa8a/files/wandb-summary.json
2025-07-12 00:49:40,253 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:49:40,254 DEBUG   SenderThread:2859588 [sender.py:send():369] send: history
2025-07-12 00:49:40,255 DEBUG   SenderThread:2859588 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:49:40,257 INFO    SenderThread:2859588 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:49:40,412 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: stop_status
2025-07-12 00:49:40,412 DEBUG   SenderThread:2859588 [sender.py:send_request():396] send_request: stop_status
2025-07-12 00:49:40,804 INFO    Thread-12 :2859588 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-36tgsa8a/files/wandb-summary.json
2025-07-12 00:49:42,396 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:49:42,397 DEBUG   SenderThread:2859588 [sender.py:send():369] send: history
2025-07-12 00:49:42,397 DEBUG   SenderThread:2859588 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:49:42,398 INFO    SenderThread:2859588 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:49:42,805 INFO    Thread-12 :2859588 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-36tgsa8a/files/wandb-summary.json
2025-07-12 00:49:43,399 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: status_report
2025-07-12 00:49:44,110 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:49:44,111 DEBUG   SenderThread:2859588 [sender.py:send():369] send: history
2025-07-12 00:49:44,111 DEBUG   SenderThread:2859588 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:49:44,112 INFO    SenderThread:2859588 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:49:44,806 INFO    Thread-12 :2859588 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-36tgsa8a/files/wandb-summary.json
2025-07-12 00:49:46,089 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:49:46,090 DEBUG   SenderThread:2859588 [sender.py:send():369] send: history
2025-07-12 00:49:46,090 DEBUG   SenderThread:2859588 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:49:46,092 INFO    SenderThread:2859588 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:49:46,807 INFO    Thread-12 :2859588 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-36tgsa8a/files/wandb-summary.json
2025-07-12 00:49:48,166 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:49:48,167 DEBUG   SenderThread:2859588 [sender.py:send():369] send: history
2025-07-12 00:49:48,167 DEBUG   SenderThread:2859588 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:49:48,170 INFO    SenderThread:2859588 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:49:48,807 INFO    Thread-12 :2859588 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-36tgsa8a/files/wandb-summary.json
2025-07-12 00:49:49,171 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: status_report
2025-07-12 00:49:50,144 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:49:50,145 DEBUG   SenderThread:2859588 [sender.py:send():369] send: history
2025-07-12 00:49:50,145 DEBUG   SenderThread:2859588 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:49:50,146 INFO    SenderThread:2859588 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:49:50,808 INFO    Thread-12 :2859588 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-36tgsa8a/files/wandb-summary.json
2025-07-12 00:49:52,613 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:49:52,614 DEBUG   SenderThread:2859588 [sender.py:send():369] send: history
2025-07-12 00:49:52,614 DEBUG   SenderThread:2859588 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:49:52,615 INFO    SenderThread:2859588 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:49:52,809 INFO    Thread-12 :2859588 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-36tgsa8a/files/wandb-summary.json
2025-07-12 00:49:54,482 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:49:54,484 DEBUG   SenderThread:2859588 [sender.py:send():369] send: history
2025-07-12 00:49:54,493 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: status_report
2025-07-12 00:49:54,585 DEBUG   SenderThread:2859588 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:49:54,585 INFO    SenderThread:2859588 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:49:54,809 INFO    Thread-12 :2859588 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-36tgsa8a/files/wandb-summary.json
2025-07-12 00:49:54,810 INFO    Thread-12 :2859588 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-36tgsa8a/files/config.yaml
2025-07-12 00:49:55,412 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: stop_status
2025-07-12 00:49:55,412 DEBUG   SenderThread:2859588 [sender.py:send_request():396] send_request: stop_status
2025-07-12 00:49:56,646 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:49:56,647 DEBUG   SenderThread:2859588 [sender.py:send():369] send: history
2025-07-12 00:49:56,648 DEBUG   SenderThread:2859588 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:49:56,648 INFO    SenderThread:2859588 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:49:56,810 INFO    Thread-12 :2859588 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-36tgsa8a/files/wandb-summary.json
2025-07-12 00:49:58,275 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:49:58,276 DEBUG   SenderThread:2859588 [sender.py:send():369] send: history
2025-07-12 00:49:58,276 DEBUG   SenderThread:2859588 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:49:58,276 INFO    SenderThread:2859588 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:49:58,811 INFO    Thread-12 :2859588 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-36tgsa8a/files/wandb-summary.json
2025-07-12 00:49:59,918 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:49:59,920 DEBUG   SenderThread:2859588 [sender.py:send():369] send: history
2025-07-12 00:49:59,920 DEBUG   SenderThread:2859588 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:49:59,922 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: status_report
2025-07-12 00:49:59,923 INFO    SenderThread:2859588 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:00,669 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:00,670 DEBUG   SenderThread:2859588 [sender.py:send():369] send: history
2025-07-12 00:50:00,671 DEBUG   SenderThread:2859588 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:00,672 INFO    SenderThread:2859588 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:00,812 INFO    Thread-12 :2859588 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-36tgsa8a/files/wandb-summary.json
2025-07-12 00:50:02,761 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:02,762 DEBUG   SenderThread:2859588 [sender.py:send():369] send: history
2025-07-12 00:50:02,763 DEBUG   SenderThread:2859588 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:02,763 INFO    SenderThread:2859588 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:02,812 INFO    Thread-12 :2859588 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-36tgsa8a/files/wandb-summary.json
2025-07-12 00:50:04,397 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:04,398 DEBUG   SenderThread:2859588 [sender.py:send():369] send: history
2025-07-12 00:50:04,398 DEBUG   SenderThread:2859588 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:04,399 INFO    SenderThread:2859588 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:04,813 INFO    Thread-12 :2859588 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-36tgsa8a/files/wandb-summary.json
2025-07-12 00:50:05,399 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: status_report
2025-07-12 00:50:06,740 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:06,742 DEBUG   SenderThread:2859588 [sender.py:send():369] send: history
2025-07-12 00:50:06,742 DEBUG   SenderThread:2859588 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:06,742 INFO    SenderThread:2859588 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:06,814 INFO    Thread-12 :2859588 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-36tgsa8a/files/wandb-summary.json
2025-07-12 00:50:08,698 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:08,699 DEBUG   SenderThread:2859588 [sender.py:send():369] send: history
2025-07-12 00:50:08,699 DEBUG   SenderThread:2859588 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:08,702 INFO    SenderThread:2859588 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:08,814 INFO    Thread-12 :2859588 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-36tgsa8a/files/wandb-summary.json
2025-07-12 00:50:10,411 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: stop_status
2025-07-12 00:50:10,412 DEBUG   SenderThread:2859588 [sender.py:send_request():396] send_request: stop_status
2025-07-12 00:50:10,532 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: status_report
2025-07-12 00:50:10,918 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:10,919 DEBUG   SenderThread:2859588 [sender.py:send():369] send: history
2025-07-12 00:50:10,920 DEBUG   SenderThread:2859588 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:10,923 INFO    SenderThread:2859588 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:11,816 INFO    Thread-12 :2859588 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-36tgsa8a/files/wandb-summary.json
2025-07-12 00:50:12,843 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:12,844 DEBUG   SenderThread:2859588 [sender.py:send():369] send: history
2025-07-12 00:50:12,844 DEBUG   SenderThread:2859588 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:12,845 INFO    SenderThread:2859588 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:13,816 INFO    Thread-12 :2859588 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-36tgsa8a/files/wandb-summary.json
2025-07-12 00:50:13,956 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:13,956 DEBUG   SenderThread:2859588 [sender.py:send():369] send: history
2025-07-12 00:50:13,957 DEBUG   SenderThread:2859588 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:13,959 INFO    SenderThread:2859588 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:14,817 INFO    Thread-12 :2859588 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-36tgsa8a/files/wandb-summary.json
2025-07-12 00:50:15,629 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:15,630 DEBUG   SenderThread:2859588 [sender.py:send():369] send: history
2025-07-12 00:50:15,630 DEBUG   SenderThread:2859588 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:15,630 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: status_report
2025-07-12 00:50:15,631 INFO    SenderThread:2859588 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:15,817 INFO    Thread-12 :2859588 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-36tgsa8a/files/wandb-summary.json
2025-07-12 00:50:17,556 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:17,557 DEBUG   SenderThread:2859588 [sender.py:send():369] send: history
2025-07-12 00:50:17,557 DEBUG   SenderThread:2859588 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:17,560 INFO    SenderThread:2859588 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:17,818 INFO    Thread-12 :2859588 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-36tgsa8a/files/wandb-summary.json
2025-07-12 00:50:19,543 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:19,545 DEBUG   SenderThread:2859588 [sender.py:send():369] send: history
2025-07-12 00:50:19,545 DEBUG   SenderThread:2859588 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:19,546 INFO    SenderThread:2859588 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:19,818 INFO    Thread-12 :2859588 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-36tgsa8a/files/wandb-summary.json
2025-07-12 00:50:20,640 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:20,641 DEBUG   SenderThread:2859588 [sender.py:send():369] send: history
2025-07-12 00:50:20,641 DEBUG   SenderThread:2859588 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:20,641 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: status_report
2025-07-12 00:50:20,644 INFO    SenderThread:2859588 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:20,819 INFO    Thread-12 :2859588 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-36tgsa8a/files/wandb-summary.json
2025-07-12 00:50:22,760 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:22,761 DEBUG   SenderThread:2859588 [sender.py:send():369] send: history
2025-07-12 00:50:22,762 DEBUG   SenderThread:2859588 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:22,762 INFO    SenderThread:2859588 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:22,819 INFO    Thread-12 :2859588 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-36tgsa8a/files/wandb-summary.json
2025-07-12 00:50:22,894 DEBUG   SystemMonitor:2859588 [system_monitor.py:_start():159] Starting system metrics aggregation loop
2025-07-12 00:50:22,896 DEBUG   SenderThread:2859588 [sender.py:send():369] send: stats
2025-07-12 00:50:25,055 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:25,056 DEBUG   SenderThread:2859588 [sender.py:send():369] send: history
2025-07-12 00:50:25,056 DEBUG   SenderThread:2859588 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:25,057 INFO    SenderThread:2859588 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:25,412 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: stop_status
2025-07-12 00:50:25,412 DEBUG   SenderThread:2859588 [sender.py:send_request():396] send_request: stop_status
2025-07-12 00:50:25,820 INFO    Thread-12 :2859588 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-36tgsa8a/files/wandb-summary.json
2025-07-12 00:50:26,192 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:26,193 DEBUG   SenderThread:2859588 [sender.py:send():369] send: history
2025-07-12 00:50:26,193 DEBUG   SenderThread:2859588 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:26,194 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: status_report
2025-07-12 00:50:26,194 INFO    SenderThread:2859588 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:26,821 INFO    Thread-12 :2859588 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-36tgsa8a/files/wandb-summary.json
2025-07-12 00:50:27,232 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:27,234 DEBUG   SenderThread:2859588 [sender.py:send():369] send: history
2025-07-12 00:50:27,234 DEBUG   SenderThread:2859588 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:27,235 INFO    SenderThread:2859588 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:27,821 INFO    Thread-12 :2859588 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-36tgsa8a/files/wandb-summary.json
2025-07-12 00:50:29,373 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:29,374 DEBUG   SenderThread:2859588 [sender.py:send():369] send: history
2025-07-12 00:50:29,375 DEBUG   SenderThread:2859588 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:29,375 INFO    SenderThread:2859588 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:29,822 INFO    Thread-12 :2859588 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-36tgsa8a/files/wandb-summary.json
2025-07-12 00:50:31,008 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:31,009 DEBUG   SenderThread:2859588 [sender.py:send():369] send: history
2025-07-12 00:50:31,009 DEBUG   SenderThread:2859588 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:31,011 INFO    SenderThread:2859588 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:31,823 INFO    Thread-12 :2859588 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-36tgsa8a/files/wandb-summary.json
2025-07-12 00:50:32,012 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: status_report
2025-07-12 00:50:33,094 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:33,095 DEBUG   SenderThread:2859588 [sender.py:send():369] send: history
2025-07-12 00:50:33,095 DEBUG   SenderThread:2859588 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:33,095 INFO    SenderThread:2859588 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:33,824 INFO    Thread-12 :2859588 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-36tgsa8a/files/wandb-summary.json
2025-07-12 00:50:35,056 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:35,057 DEBUG   SenderThread:2859588 [sender.py:send():369] send: history
2025-07-12 00:50:35,057 DEBUG   SenderThread:2859588 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:35,057 INFO    SenderThread:2859588 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:35,824 INFO    Thread-12 :2859588 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-36tgsa8a/files/wandb-summary.json
2025-07-12 00:50:36,804 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:36,805 DEBUG   SenderThread:2859588 [sender.py:send():369] send: history
2025-07-12 00:50:36,805 DEBUG   SenderThread:2859588 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:36,805 INFO    SenderThread:2859588 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:36,825 INFO    Thread-12 :2859588 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-36tgsa8a/files/wandb-summary.json
2025-07-12 00:50:37,806 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: status_report
2025-07-12 00:50:38,730 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:38,731 DEBUG   SenderThread:2859588 [sender.py:send():369] send: history
2025-07-12 00:50:38,732 DEBUG   SenderThread:2859588 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:38,733 INFO    SenderThread:2859588 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:38,825 INFO    Thread-12 :2859588 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-36tgsa8a/files/wandb-summary.json
2025-07-12 00:50:40,412 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: stop_status
2025-07-12 00:50:40,413 DEBUG   SenderThread:2859588 [sender.py:send_request():396] send_request: stop_status
2025-07-12 00:50:40,651 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:40,652 DEBUG   SenderThread:2859588 [sender.py:send():369] send: history
2025-07-12 00:50:40,653 DEBUG   SenderThread:2859588 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:40,653 INFO    SenderThread:2859588 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:40,826 INFO    Thread-12 :2859588 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-36tgsa8a/files/wandb-summary.json
2025-07-12 00:50:41,758 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:41,759 DEBUG   SenderThread:2859588 [sender.py:send():369] send: history
2025-07-12 00:50:41,759 DEBUG   SenderThread:2859588 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:41,762 INFO    SenderThread:2859588 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:41,827 INFO    Thread-12 :2859588 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-36tgsa8a/files/wandb-summary.json
2025-07-12 00:50:42,848 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:42,849 DEBUG   SenderThread:2859588 [sender.py:send():369] send: history
2025-07-12 00:50:42,849 DEBUG   SenderThread:2859588 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:42,849 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: status_report
2025-07-12 00:50:42,850 INFO    SenderThread:2859588 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:43,708 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:43,709 DEBUG   SenderThread:2859588 [sender.py:send():369] send: history
2025-07-12 00:50:43,709 DEBUG   SenderThread:2859588 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:43,709 INFO    SenderThread:2859588 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:43,827 INFO    Thread-12 :2859588 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-36tgsa8a/files/wandb-summary.json
2025-07-12 00:50:45,658 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:45,659 DEBUG   SenderThread:2859588 [sender.py:send():369] send: history
2025-07-12 00:50:45,659 DEBUG   SenderThread:2859588 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:45,659 INFO    SenderThread:2859588 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:45,828 INFO    Thread-12 :2859588 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-36tgsa8a/files/wandb-summary.json
2025-07-12 00:50:46,876 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:46,877 DEBUG   SenderThread:2859588 [sender.py:send():369] send: history
2025-07-12 00:50:46,877 DEBUG   SenderThread:2859588 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:46,877 INFO    SenderThread:2859588 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:47,829 INFO    Thread-12 :2859588 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-36tgsa8a/files/wandb-summary.json
2025-07-12 00:50:47,878 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: status_report
2025-07-12 00:50:48,960 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:48,962 DEBUG   SenderThread:2859588 [sender.py:send():369] send: history
2025-07-12 00:50:48,962 DEBUG   SenderThread:2859588 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:48,962 INFO    SenderThread:2859588 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:49,830 INFO    Thread-12 :2859588 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-36tgsa8a/files/wandb-summary.json
2025-07-12 00:50:49,831 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:49,832 DEBUG   SenderThread:2859588 [sender.py:send():369] send: history
2025-07-12 00:50:49,832 DEBUG   SenderThread:2859588 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:49,832 INFO    SenderThread:2859588 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:50,801 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:50,801 DEBUG   SenderThread:2859588 [sender.py:send():369] send: history
2025-07-12 00:50:50,802 DEBUG   SenderThread:2859588 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:50,802 INFO    SenderThread:2859588 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:50,830 INFO    Thread-12 :2859588 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-36tgsa8a/files/wandb-summary.json
2025-07-12 00:50:52,666 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:52,666 DEBUG   SenderThread:2859588 [sender.py:send():369] send: history
2025-07-12 00:50:52,667 DEBUG   SenderThread:2859588 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:52,669 INFO    SenderThread:2859588 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:52,831 INFO    Thread-12 :2859588 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-36tgsa8a/files/wandb-summary.json
2025-07-12 00:50:52,897 DEBUG   SenderThread:2859588 [sender.py:send():369] send: stats
2025-07-12 00:50:52,898 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: status_report
2025-07-12 00:50:53,578 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:53,579 DEBUG   SenderThread:2859588 [sender.py:send():369] send: history
2025-07-12 00:50:53,579 DEBUG   SenderThread:2859588 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:53,580 INFO    SenderThread:2859588 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:53,831 INFO    Thread-12 :2859588 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-36tgsa8a/files/wandb-summary.json
2025-07-12 00:50:54,648 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:54,649 DEBUG   SenderThread:2859588 [sender.py:send():369] send: history
2025-07-12 00:50:54,649 DEBUG   SenderThread:2859588 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:54,650 INFO    SenderThread:2859588 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:54,832 INFO    Thread-12 :2859588 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-36tgsa8a/files/wandb-summary.json
2025-07-12 00:50:55,412 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: stop_status
2025-07-12 00:50:55,413 DEBUG   SenderThread:2859588 [sender.py:send_request():396] send_request: stop_status
2025-07-12 00:50:56,012 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:56,012 DEBUG   SenderThread:2859588 [sender.py:send():369] send: history
2025-07-12 00:50:56,013 DEBUG   SenderThread:2859588 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:56,013 INFO    SenderThread:2859588 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:56,718 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:56,719 DEBUG   SenderThread:2859588 [sender.py:send():369] send: history
2025-07-12 00:50:56,719 DEBUG   SenderThread:2859588 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:56,720 INFO    SenderThread:2859588 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:56,833 INFO    Thread-12 :2859588 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-36tgsa8a/files/wandb-summary.json
2025-07-12 00:50:57,989 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:57,989 DEBUG   SenderThread:2859588 [sender.py:send():369] send: history
2025-07-12 00:50:57,990 DEBUG   SenderThread:2859588 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:57,990 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: status_report
2025-07-12 00:50:57,991 INFO    SenderThread:2859588 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:58,788 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:50:58,788 DEBUG   SenderThread:2859588 [sender.py:send():369] send: history
2025-07-12 00:50:58,789 DEBUG   SenderThread:2859588 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:50:58,789 INFO    SenderThread:2859588 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:50:58,833 INFO    Thread-12 :2859588 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-36tgsa8a/files/wandb-summary.json
2025-07-12 00:51:00,495 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: partial_history
2025-07-12 00:51:00,496 DEBUG   SenderThread:2859588 [sender.py:send():369] send: history
2025-07-12 00:51:00,496 DEBUG   SenderThread:2859588 [sender.py:send_request():396] send_request: summary_record
2025-07-12 00:51:00,496 INFO    SenderThread:2859588 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-12 00:51:00,834 INFO    Thread-12 :2859588 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250712_004922-36tgsa8a/files/wandb-summary.json
2025-07-12 00:51:03,497 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: status_report
2025-07-12 00:51:08,498 DEBUG   HandlerThread:2859588 [handler.py:handle_request():144] handle_request: status_report
2025-07-12 00:51:10,933 INFO    memory    :2859588 [interfaces.py:monitor():140] Process proc.memory.rssMB has exited.
2025-07-12 00:51:10,933 DEBUG   SystemMonitor:2859588 [system_monitor.py:_start():166] Finished system metrics aggregation loop
2025-07-12 00:51:10,934 DEBUG   SystemMonitor:2859588 [system_monitor.py:_start():170] Publishing last batch of metrics
2025-07-12 00:51:10,937 DEBUG   SenderThread:2859588 [sender.py:send():369] send: stats
2025-07-12 00:51:11,908 INFO    MainThread:2859588 [internal.py:handle_exit():76] Internal process exited
