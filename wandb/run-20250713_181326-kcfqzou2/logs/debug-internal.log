2025-07-13 18:13:26,742 INFO    StreamThr :2940713 [internal.py:wandb_internal():89] W&B internal server running at pid: 2940713, started at: 2025-07-13 18:13:26.741816
2025-07-13 18:13:26,745 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: status
2025-07-13 18:13:26,751 INFO    WriterThread:2940713 [datastore.py:open_for_write():85] open: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-kcfqzou2/run-kcfqzou2.wandb
2025-07-13 18:13:26,752 DEBUG   SenderThread:2940713 [sender.py:send():369] send: header
2025-07-13 18:13:26,770 DEBUG   SenderThread:2940713 [sender.py:send():369] send: run
2025-07-13 18:13:27,144 INFO    SenderThread:2940713 [dir_watcher.py:__init__():211] watching files in: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-kcfqzou2/files
2025-07-13 18:13:27,144 INFO    SenderThread:2940713 [sender.py:_start_run_threads():1103] run started: kcfqzou2 with start time 1752444806.742851
2025-07-13 18:13:27,147 DEBUG   SenderThread:2940713 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:13:27,147 INFO    SenderThread:2940713 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:13:27,155 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: check_version
2025-07-13 18:13:27,156 DEBUG   SenderThread:2940713 [sender.py:send_request():396] send_request: check_version
2025-07-13 18:13:27,219 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: run_start
2025-07-13 18:13:27,225 DEBUG   HandlerThread:2940713 [system_info.py:__init__():31] System info init
2025-07-13 18:13:27,226 DEBUG   HandlerThread:2940713 [system_info.py:__init__():46] System info init done
2025-07-13 18:13:27,226 INFO    HandlerThread:2940713 [system_monitor.py:start():181] Starting system monitor
2025-07-13 18:13:27,226 INFO    SystemMonitor:2940713 [system_monitor.py:_start():145] Starting system asset monitoring threads
2025-07-13 18:13:27,227 INFO    HandlerThread:2940713 [system_monitor.py:probe():201] Collecting system info
2025-07-13 18:13:27,227 INFO    SystemMonitor:2940713 [interfaces.py:start():190] Started cpu monitoring
2025-07-13 18:13:27,228 INFO    SystemMonitor:2940713 [interfaces.py:start():190] Started disk monitoring
2025-07-13 18:13:27,229 INFO    SystemMonitor:2940713 [interfaces.py:start():190] Started gpu monitoring
2025-07-13 18:13:27,230 INFO    SystemMonitor:2940713 [interfaces.py:start():190] Started memory monitoring
2025-07-13 18:13:27,231 INFO    SystemMonitor:2940713 [interfaces.py:start():190] Started network monitoring
2025-07-13 18:13:27,249 DEBUG   HandlerThread:2940713 [system_info.py:probe():195] Probing system
2025-07-13 18:13:27,257 DEBUG   HandlerThread:2940713 [system_info.py:_probe_git():180] Probing git
2025-07-13 18:13:27,274 DEBUG   HandlerThread:2940713 [system_info.py:_probe_git():188] Probing git done
2025-07-13 18:13:27,274 DEBUG   HandlerThread:2940713 [system_info.py:probe():240] Probing system done
2025-07-13 18:13:27,274 DEBUG   HandlerThread:2940713 [system_monitor.py:probe():210] {'os': 'Linux-5.4.0-77-generic-x86_64-with-debian-bullseye-sid', 'python': '3.7.16', 'heartbeatAt': '2025-07-13T22:13:27.249952', 'startedAt': '2025-07-13T22:13:26.722337', 'docker': None, 'cuda': None, 'args': ('--local_rank=0', '--cfg', 'config/base_train.yaml', '--output', '/home-local2/akath.extra.nobkp/sereact', '--data-path', '/home-local2/akath.extra.nobkp/dl_challenge', '--batch-size', '2'), 'state': 'running', 'program': 'main.py', 'codePath': 'main.py', 'git': {'remote': 'https://github.com/Shrinidhibhat87/codingchallenge_sereact.git', 'commit': '667b7ded061f079ea281aa2193d47b61df08fec6'}, 'email': '<EMAIL>', 'root': '/home-local/akath.nobkp/sereact', 'host': 'lv3-32190', 'username': 'akath', 'executable': '/gel/usr/akath/.conda/envs/swin/bin/python', 'cpu_count': 6, 'cpu_count_logical': 12, 'cpu_freq': {'current': 2.416916666666666, 'min': 1200.0, 'max': 4000.0}, 'cpu_freq_per_core': [{'current': 3.096, 'min': 1200.0, 'max': 4000.0}, {'current': 2.372, 'min': 1200.0, 'max': 4000.0}, {'current': 1.862, 'min': 1200.0, 'max': 4000.0}, {'current': 2.424, 'min': 1200.0, 'max': 4000.0}, {'current': 2.207, 'min': 1200.0, 'max': 4000.0}, {'current': 3.459, 'min': 1200.0, 'max': 4000.0}, {'current': 2.476, 'min': 1200.0, 'max': 4000.0}, {'current': 2.544, 'min': 1200.0, 'max': 4000.0}, {'current': 2.191, 'min': 1200.0, 'max': 4000.0}, {'current': 2.054, 'min': 1200.0, 'max': 4000.0}, {'current': 1.52, 'min': 1200.0, 'max': 4000.0}, {'current': 2.798, 'min': 1200.0, 'max': 4000.0}], 'disk': {'total': 111.2200813293457, 'used': 53.13279342651367}, 'gpu': 'TITAN Xp', 'gpu_count': 3, 'gpu_devices': [{'name': 'TITAN Xp', 'memory_total': 12787122176}, {'name': 'TITAN Xp', 'memory_total': 12788498432}, {'name': 'TITAN Xp', 'memory_total': 12788498432}], 'memory': {'total': 62.72977066040039}}
2025-07-13 18:13:27,274 INFO    HandlerThread:2940713 [system_monitor.py:probe():211] Finished collecting system info
2025-07-13 18:13:27,274 INFO    HandlerThread:2940713 [system_monitor.py:probe():214] Publishing system info
2025-07-13 18:13:27,274 DEBUG   HandlerThread:2940713 [system_info.py:_save_pip():52] Saving list of pip packages installed into the current environment
2025-07-13 18:13:27,275 DEBUG   HandlerThread:2940713 [system_info.py:_save_pip():67] Saving pip packages done
2025-07-13 18:13:27,275 DEBUG   HandlerThread:2940713 [system_info.py:_save_conda():75] Saving list of conda packages installed into the current environment
2025-07-13 18:13:28,146 INFO    Thread-12 :2940713 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-kcfqzou2/files/requirements.txt
2025-07-13 18:13:28,147 INFO    Thread-12 :2940713 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-kcfqzou2/files/conda-environment.yaml
2025-07-13 18:13:28,147 INFO    Thread-12 :2940713 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-kcfqzou2/files/wandb-summary.json
2025-07-13 18:13:33,722 DEBUG   HandlerThread:2940713 [system_info.py:_save_conda():86] Saving conda packages done
2025-07-13 18:13:33,723 INFO    HandlerThread:2940713 [system_monitor.py:probe():216] Finished publishing system info
2025-07-13 18:13:33,732 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:13:33,732 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: keepalive
2025-07-13 18:13:33,733 DEBUG   SenderThread:2940713 [sender.py:send():369] send: files
2025-07-13 18:13:33,733 INFO    SenderThread:2940713 [sender.py:_save_file():1354] saving file wandb-metadata.json with policy now
2025-07-13 18:13:33,745 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: stop_status
2025-07-13 18:13:33,746 DEBUG   SenderThread:2940713 [sender.py:send_request():396] send_request: stop_status
2025-07-13 18:13:33,925 DEBUG   SenderThread:2940713 [sender.py:send():369] send: telemetry
2025-07-13 18:13:34,000 INFO    wandb-upload_0:2940713 [upload_job.py:push():133] Uploaded file /tmp/tmpbmwpxrurwandb/8sib20i1-wandb-metadata.json
2025-07-13 18:13:34,146 INFO    Thread-12 :2940713 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-kcfqzou2/files/conda-environment.yaml
2025-07-13 18:13:34,147 INFO    Thread-12 :2940713 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-kcfqzou2/files/output.log
2025-07-13 18:13:34,147 INFO    Thread-12 :2940713 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-kcfqzou2/files/wandb-metadata.json
2025-07-13 18:13:36,147 INFO    Thread-12 :2940713 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-kcfqzou2/files/output.log
2025-07-13 18:13:37,912 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:13:38,148 INFO    Thread-12 :2940713 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-kcfqzou2/files/output.log
2025-07-13 18:13:42,601 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:13:42,602 DEBUG   SenderThread:2940713 [sender.py:send():369] send: history
2025-07-13 18:13:42,603 DEBUG   SenderThread:2940713 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:13:42,605 INFO    SenderThread:2940713 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:13:43,150 INFO    Thread-12 :2940713 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-kcfqzou2/files/wandb-summary.json
2025-07-13 18:13:43,605 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:13:44,150 INFO    Thread-12 :2940713 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-kcfqzou2/files/output.log
2025-07-13 18:13:46,027 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:13:46,028 DEBUG   SenderThread:2940713 [sender.py:send():369] send: history
2025-07-13 18:13:46,028 DEBUG   SenderThread:2940713 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:13:46,029 INFO    SenderThread:2940713 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:13:46,151 INFO    Thread-12 :2940713 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-kcfqzou2/files/wandb-summary.json
2025-07-13 18:13:48,743 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: stop_status
2025-07-13 18:13:48,743 DEBUG   SenderThread:2940713 [sender.py:send_request():396] send_request: stop_status
2025-07-13 18:13:48,914 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:13:49,447 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:13:49,448 DEBUG   SenderThread:2940713 [sender.py:send():369] send: history
2025-07-13 18:13:49,448 DEBUG   SenderThread:2940713 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:13:49,449 INFO    SenderThread:2940713 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:13:50,152 INFO    Thread-12 :2940713 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-kcfqzou2/files/wandb-summary.json
2025-07-13 18:13:52,620 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:13:52,621 DEBUG   SenderThread:2940713 [sender.py:send():369] send: history
2025-07-13 18:13:52,622 DEBUG   SenderThread:2940713 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:13:52,622 INFO    SenderThread:2940713 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:13:53,153 INFO    Thread-12 :2940713 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-kcfqzou2/files/wandb-summary.json
2025-07-13 18:13:54,624 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:13:55,622 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:13:55,624 DEBUG   SenderThread:2940713 [sender.py:send():369] send: history
2025-07-13 18:13:55,624 DEBUG   SenderThread:2940713 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:13:55,624 INFO    SenderThread:2940713 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:13:56,155 INFO    Thread-12 :2940713 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-kcfqzou2/files/wandb-summary.json
2025-07-13 18:13:59,620 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:13:59,621 DEBUG   SenderThread:2940713 [sender.py:send():369] send: history
2025-07-13 18:13:59,621 DEBUG   SenderThread:2940713 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:13:59,622 INFO    SenderThread:2940713 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:14:00,156 INFO    Thread-12 :2940713 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-kcfqzou2/files/wandb-summary.json
2025-07-13 18:14:00,627 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:14:01,156 INFO    Thread-12 :2940713 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-kcfqzou2/files/config.yaml
2025-07-13 18:14:02,911 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:14:02,912 DEBUG   SenderThread:2940713 [sender.py:send():369] send: history
2025-07-13 18:14:02,912 DEBUG   SenderThread:2940713 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:14:02,913 INFO    SenderThread:2940713 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:14:03,157 INFO    Thread-12 :2940713 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-kcfqzou2/files/wandb-summary.json
2025-07-13 18:14:03,743 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: stop_status
2025-07-13 18:14:03,743 DEBUG   SenderThread:2940713 [sender.py:send_request():396] send_request: stop_status
2025-07-13 18:14:05,832 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:14:06,409 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:14:06,410 DEBUG   SenderThread:2940713 [sender.py:send():369] send: history
2025-07-13 18:14:06,411 DEBUG   SenderThread:2940713 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:14:06,411 INFO    SenderThread:2940713 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:14:07,159 INFO    Thread-12 :2940713 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-kcfqzou2/files/wandb-summary.json
2025-07-13 18:14:10,081 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:14:10,082 DEBUG   SenderThread:2940713 [sender.py:send():369] send: history
2025-07-13 18:14:10,082 DEBUG   SenderThread:2940713 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:14:10,083 INFO    SenderThread:2940713 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:14:10,160 INFO    Thread-12 :2940713 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-kcfqzou2/files/wandb-summary.json
2025-07-13 18:14:11,084 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:14:13,653 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:14:13,654 DEBUG   SenderThread:2940713 [sender.py:send():369] send: history
2025-07-13 18:14:13,654 DEBUG   SenderThread:2940713 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:14:13,655 INFO    SenderThread:2940713 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:14:14,161 INFO    Thread-12 :2940713 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-kcfqzou2/files/wandb-summary.json
2025-07-13 18:14:16,657 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:14:17,035 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:14:17,037 DEBUG   SenderThread:2940713 [sender.py:send():369] send: history
2025-07-13 18:14:17,037 DEBUG   SenderThread:2940713 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:14:17,038 INFO    SenderThread:2940713 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:14:17,162 INFO    Thread-12 :2940713 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-kcfqzou2/files/wandb-summary.json
2025-07-13 18:14:18,162 INFO    Thread-12 :2940713 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-kcfqzou2/files/output.log
2025-07-13 18:14:18,743 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: stop_status
2025-07-13 18:14:18,744 DEBUG   SenderThread:2940713 [sender.py:send_request():396] send_request: stop_status
2025-07-13 18:14:20,097 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:14:20,097 DEBUG   SenderThread:2940713 [sender.py:send():369] send: history
2025-07-13 18:14:20,098 DEBUG   SenderThread:2940713 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:14:20,100 INFO    SenderThread:2940713 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:14:20,163 INFO    Thread-12 :2940713 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-kcfqzou2/files/wandb-summary.json
2025-07-13 18:14:22,100 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:14:22,989 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:14:22,989 DEBUG   SenderThread:2940713 [sender.py:send():369] send: history
2025-07-13 18:14:22,989 DEBUG   SenderThread:2940713 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:14:22,990 INFO    SenderThread:2940713 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:14:23,164 INFO    Thread-12 :2940713 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-kcfqzou2/files/wandb-summary.json
2025-07-13 18:14:26,428 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:14:26,429 DEBUG   SenderThread:2940713 [sender.py:send():369] send: history
2025-07-13 18:14:26,429 DEBUG   SenderThread:2940713 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:14:26,429 INFO    SenderThread:2940713 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:14:27,165 INFO    Thread-12 :2940713 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-kcfqzou2/files/wandb-summary.json
2025-07-13 18:14:27,231 DEBUG   SystemMonitor:2940713 [system_monitor.py:_start():159] Starting system metrics aggregation loop
2025-07-13 18:14:27,233 DEBUG   SenderThread:2940713 [sender.py:send():369] send: stats
2025-07-13 18:14:27,234 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:14:30,266 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:14:30,267 DEBUG   SenderThread:2940713 [sender.py:send():369] send: history
2025-07-13 18:14:30,267 DEBUG   SenderThread:2940713 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:14:30,268 INFO    SenderThread:2940713 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:14:31,167 INFO    Thread-12 :2940713 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-kcfqzou2/files/wandb-summary.json
2025-07-13 18:14:32,268 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:14:33,656 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:14:33,657 DEBUG   SenderThread:2940713 [sender.py:send():369] send: history
2025-07-13 18:14:33,658 DEBUG   SenderThread:2940713 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:14:33,658 INFO    SenderThread:2940713 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:14:33,743 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: stop_status
2025-07-13 18:14:33,743 DEBUG   SenderThread:2940713 [sender.py:send_request():396] send_request: stop_status
2025-07-13 18:14:34,168 INFO    Thread-12 :2940713 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-kcfqzou2/files/wandb-summary.json
2025-07-13 18:14:36,619 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:14:36,620 DEBUG   SenderThread:2940713 [sender.py:send():369] send: history
2025-07-13 18:14:36,620 DEBUG   SenderThread:2940713 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:14:36,624 INFO    SenderThread:2940713 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:14:37,169 INFO    Thread-12 :2940713 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-kcfqzou2/files/wandb-summary.json
2025-07-13 18:14:37,625 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:14:38,782 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:14:38,784 DEBUG   SenderThread:2940713 [sender.py:send():369] send: history
2025-07-13 18:14:38,785 DEBUG   SenderThread:2940713 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:14:38,785 INFO    SenderThread:2940713 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:14:39,170 INFO    Thread-12 :2940713 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-kcfqzou2/files/wandb-summary.json
2025-07-13 18:14:41,707 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:14:41,708 DEBUG   SenderThread:2940713 [sender.py:send():369] send: history
2025-07-13 18:14:41,708 DEBUG   SenderThread:2940713 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:14:41,708 INFO    SenderThread:2940713 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:14:42,171 INFO    Thread-12 :2940713 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-kcfqzou2/files/wandb-summary.json
2025-07-13 18:14:42,709 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:14:44,235 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:14:44,236 DEBUG   SenderThread:2940713 [sender.py:send():369] send: history
2025-07-13 18:14:44,237 DEBUG   SenderThread:2940713 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:14:44,237 INFO    SenderThread:2940713 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:14:45,172 INFO    Thread-12 :2940713 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-kcfqzou2/files/wandb-summary.json
2025-07-13 18:14:47,490 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:14:47,492 DEBUG   SenderThread:2940713 [sender.py:send():369] send: history
2025-07-13 18:14:47,492 DEBUG   SenderThread:2940713 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:14:47,494 INFO    SenderThread:2940713 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:14:48,173 INFO    Thread-12 :2940713 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-kcfqzou2/files/output.log
2025-07-13 18:14:48,173 INFO    Thread-12 :2940713 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-kcfqzou2/files/wandb-summary.json
2025-07-13 18:14:48,495 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:14:48,743 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: stop_status
2025-07-13 18:14:48,744 DEBUG   SenderThread:2940713 [sender.py:send_request():396] send_request: stop_status
2025-07-13 18:14:51,237 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:14:51,238 DEBUG   SenderThread:2940713 [sender.py:send():369] send: history
2025-07-13 18:14:51,238 DEBUG   SenderThread:2940713 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:14:51,238 INFO    SenderThread:2940713 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:14:52,174 INFO    Thread-12 :2940713 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-kcfqzou2/files/wandb-summary.json
2025-07-13 18:14:53,366 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:14:53,367 DEBUG   SenderThread:2940713 [sender.py:send():369] send: history
2025-07-13 18:14:53,367 DEBUG   SenderThread:2940713 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:14:53,367 INFO    SenderThread:2940713 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:14:54,175 INFO    Thread-12 :2940713 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-kcfqzou2/files/wandb-summary.json
2025-07-13 18:14:54,368 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:14:56,142 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:14:56,143 DEBUG   SenderThread:2940713 [sender.py:send():369] send: history
2025-07-13 18:14:56,143 DEBUG   SenderThread:2940713 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:14:56,143 INFO    SenderThread:2940713 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:14:56,175 INFO    Thread-12 :2940713 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-kcfqzou2/files/wandb-summary.json
2025-07-13 18:14:57,235 DEBUG   SenderThread:2940713 [sender.py:send():369] send: stats
2025-07-13 18:14:59,073 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:14:59,074 DEBUG   SenderThread:2940713 [sender.py:send():369] send: history
2025-07-13 18:14:59,075 DEBUG   SenderThread:2940713 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:14:59,077 INFO    SenderThread:2940713 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:14:59,176 INFO    Thread-12 :2940713 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-kcfqzou2/files/wandb-summary.json
2025-07-13 18:15:00,078 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:15:02,528 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:15:02,529 DEBUG   SenderThread:2940713 [sender.py:send():369] send: history
2025-07-13 18:15:02,529 DEBUG   SenderThread:2940713 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:15:02,529 INFO    SenderThread:2940713 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:15:03,178 INFO    Thread-12 :2940713 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-kcfqzou2/files/wandb-summary.json
2025-07-13 18:15:03,743 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: stop_status
2025-07-13 18:15:03,744 DEBUG   SenderThread:2940713 [sender.py:send_request():396] send_request: stop_status
2025-07-13 18:15:05,872 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:15:06,239 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: partial_history
2025-07-13 18:15:06,239 DEBUG   SenderThread:2940713 [sender.py:send():369] send: history
2025-07-13 18:15:06,240 DEBUG   SenderThread:2940713 [sender.py:send_request():396] send_request: summary_record
2025-07-13 18:15:06,240 INFO    SenderThread:2940713 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:15:07,179 INFO    Thread-12 :2940713 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-kcfqzou2/files/wandb-summary.json
2025-07-13 18:15:07,184 DEBUG   SenderThread:2940713 [sender.py:send():369] send: exit
2025-07-13 18:15:07,184 INFO    SenderThread:2940713 [sender.py:send_exit():574] handling exit code: 1
2025-07-13 18:15:07,184 INFO    SenderThread:2940713 [sender.py:send_exit():576] handling runtime: 99
2025-07-13 18:15:07,185 INFO    SenderThread:2940713 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:15:07,185 INFO    SenderThread:2940713 [sender.py:send_exit():582] send defer
2025-07-13 18:15:07,185 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:15:07,185 INFO    HandlerThread:2940713 [handler.py:handle_request_defer():170] handle defer: 0
2025-07-13 18:15:07,185 DEBUG   SenderThread:2940713 [sender.py:send_request():396] send_request: defer
2025-07-13 18:15:07,185 INFO    SenderThread:2940713 [sender.py:send_request_defer():598] handle sender defer: 0
2025-07-13 18:15:07,185 INFO    SenderThread:2940713 [sender.py:transition_state():602] send defer: 1
2025-07-13 18:15:07,186 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:15:07,186 INFO    HandlerThread:2940713 [handler.py:handle_request_defer():170] handle defer: 1
2025-07-13 18:15:07,186 DEBUG   SenderThread:2940713 [sender.py:send_request():396] send_request: defer
2025-07-13 18:15:07,186 INFO    SenderThread:2940713 [sender.py:send_request_defer():598] handle sender defer: 1
2025-07-13 18:15:07,186 INFO    SenderThread:2940713 [sender.py:transition_state():602] send defer: 2
2025-07-13 18:15:07,186 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:15:07,186 INFO    HandlerThread:2940713 [handler.py:handle_request_defer():170] handle defer: 2
2025-07-13 18:15:07,186 INFO    HandlerThread:2940713 [system_monitor.py:finish():190] Stopping system monitor
2025-07-13 18:15:07,186 DEBUG   SystemMonitor:2940713 [system_monitor.py:_start():166] Finished system metrics aggregation loop
2025-07-13 18:15:07,187 INFO    HandlerThread:2940713 [interfaces.py:finish():202] Joined cpu monitor
2025-07-13 18:15:07,187 DEBUG   SystemMonitor:2940713 [system_monitor.py:_start():170] Publishing last batch of metrics
2025-07-13 18:15:07,187 INFO    HandlerThread:2940713 [interfaces.py:finish():202] Joined disk monitor
2025-07-13 18:15:07,313 INFO    HandlerThread:2940713 [interfaces.py:finish():202] Joined gpu monitor
2025-07-13 18:15:07,313 INFO    HandlerThread:2940713 [interfaces.py:finish():202] Joined memory monitor
2025-07-13 18:15:07,313 INFO    HandlerThread:2940713 [interfaces.py:finish():202] Joined network monitor
2025-07-13 18:15:07,314 DEBUG   SenderThread:2940713 [sender.py:send_request():396] send_request: defer
2025-07-13 18:15:07,314 INFO    SenderThread:2940713 [sender.py:send_request_defer():598] handle sender defer: 2
2025-07-13 18:15:07,314 INFO    SenderThread:2940713 [sender.py:transition_state():602] send defer: 3
2025-07-13 18:15:07,314 DEBUG   SenderThread:2940713 [sender.py:send():369] send: stats
2025-07-13 18:15:07,314 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:15:07,314 INFO    HandlerThread:2940713 [handler.py:handle_request_defer():170] handle defer: 3
2025-07-13 18:15:07,315 DEBUG   SenderThread:2940713 [sender.py:send_request():396] send_request: defer
2025-07-13 18:15:07,315 INFO    SenderThread:2940713 [sender.py:send_request_defer():598] handle sender defer: 3
2025-07-13 18:15:07,315 INFO    SenderThread:2940713 [sender.py:transition_state():602] send defer: 4
2025-07-13 18:15:07,315 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:15:07,315 INFO    HandlerThread:2940713 [handler.py:handle_request_defer():170] handle defer: 4
2025-07-13 18:15:07,315 DEBUG   SenderThread:2940713 [sender.py:send_request():396] send_request: defer
2025-07-13 18:15:07,315 INFO    SenderThread:2940713 [sender.py:send_request_defer():598] handle sender defer: 4
2025-07-13 18:15:07,315 INFO    SenderThread:2940713 [sender.py:transition_state():602] send defer: 5
2025-07-13 18:15:07,315 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:15:07,315 INFO    HandlerThread:2940713 [handler.py:handle_request_defer():170] handle defer: 5
2025-07-13 18:15:07,316 DEBUG   SenderThread:2940713 [sender.py:send():369] send: summary
2025-07-13 18:15:07,316 INFO    SenderThread:2940713 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-13 18:15:07,316 DEBUG   SenderThread:2940713 [sender.py:send_request():396] send_request: defer
2025-07-13 18:15:07,316 INFO    SenderThread:2940713 [sender.py:send_request_defer():598] handle sender defer: 5
2025-07-13 18:15:07,316 INFO    SenderThread:2940713 [sender.py:transition_state():602] send defer: 6
2025-07-13 18:15:07,316 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:15:07,316 INFO    HandlerThread:2940713 [handler.py:handle_request_defer():170] handle defer: 6
2025-07-13 18:15:07,316 DEBUG   SenderThread:2940713 [sender.py:send_request():396] send_request: defer
2025-07-13 18:15:07,316 INFO    SenderThread:2940713 [sender.py:send_request_defer():598] handle sender defer: 6
2025-07-13 18:15:07,317 INFO    SenderThread:2940713 [sender.py:transition_state():602] send defer: 7
2025-07-13 18:15:07,317 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: status_report
2025-07-13 18:15:07,317 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:15:07,317 INFO    HandlerThread:2940713 [handler.py:handle_request_defer():170] handle defer: 7
2025-07-13 18:15:07,317 DEBUG   SenderThread:2940713 [sender.py:send_request():396] send_request: defer
2025-07-13 18:15:07,317 INFO    SenderThread:2940713 [sender.py:send_request_defer():598] handle sender defer: 7
2025-07-13 18:15:08,179 INFO    Thread-12 :2940713 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-kcfqzou2/files/output.log
2025-07-13 18:15:08,180 INFO    Thread-12 :2940713 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-kcfqzou2/files/wandb-summary.json
2025-07-13 18:15:08,185 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: poll_exit
2025-07-13 18:15:09,190 INFO    SenderThread:2940713 [sender.py:transition_state():602] send defer: 8
2025-07-13 18:15:09,191 DEBUG   SenderThread:2940713 [sender.py:send_request():396] send_request: poll_exit
2025-07-13 18:15:09,191 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:15:09,191 INFO    HandlerThread:2940713 [handler.py:handle_request_defer():170] handle defer: 8
2025-07-13 18:15:09,192 DEBUG   SenderThread:2940713 [sender.py:send_request():396] send_request: defer
2025-07-13 18:15:09,192 INFO    SenderThread:2940713 [sender.py:send_request_defer():598] handle sender defer: 8
2025-07-13 18:15:09,192 INFO    SenderThread:2940713 [job_builder.py:build():232] Attempting to build job artifact
2025-07-13 18:15:09,192 INFO    SenderThread:2940713 [job_builder.py:build():256] is repo sourced job
2025-07-13 18:15:09,194 INFO    SenderThread:2940713 [job_builder.py:build():297] adding wandb-job metadata file
2025-07-13 18:15:09,208 INFO    SenderThread:2940713 [sender.py:transition_state():602] send defer: 9
2025-07-13 18:15:09,209 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:15:09,209 DEBUG   SenderThread:2940713 [sender.py:send():369] send: artifact
2025-07-13 18:15:09,209 INFO    HandlerThread:2940713 [handler.py:handle_request_defer():170] handle defer: 9
2025-07-13 18:15:09,972 INFO    wandb-upload_0:2940713 [upload_job.py:push():91] Uploaded file /gel/usr/akath/.local/share/wandb/artifacts/staging/tmpx0bcc6tn
2025-07-13 18:15:09,975 INFO    wandb-upload_1:2940713 [upload_job.py:push():91] Uploaded file /gel/usr/akath/.local/share/wandb/artifacts/staging/tmpm6qr_7ct
2025-07-13 18:15:10,180 INFO    Thread-12 :2940713 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-kcfqzou2/files/output.log
2025-07-13 18:15:10,819 INFO    SenderThread:2940713 [sender.py:send_artifact():1450] sent artifact job-https___github.com_Shrinidhibhat87_codingchallenge_sereact.git_main.py - {'id': 'QXJ0aWZhY3Q6MTg3NzkyMzY4NA==', 'digest': '01f3dd739b2c8dbc3409e0ca40376bfa', 'state': 'PENDING', 'aliases': [], 'artifactSequence': {'id': 'QXJ0aWZhY3RDb2xsZWN0aW9uOjY4NDgwMzQ0Ng==', 'latestArtifact': {'id': 'QXJ0aWZhY3Q6MTg3NTAxNDMwNQ==', 'versionIndex': 5}}, 'version': 'latest'}
2025-07-13 18:15:10,819 DEBUG   SenderThread:2940713 [sender.py:send_request():396] send_request: defer
2025-07-13 18:15:10,819 INFO    SenderThread:2940713 [sender.py:send_request_defer():598] handle sender defer: 9
2025-07-13 18:15:10,819 INFO    SenderThread:2940713 [dir_watcher.py:finish():359] shutting down directory watcher
2025-07-13 18:15:11,181 INFO    SenderThread:2940713 [dir_watcher.py:finish():389] scan: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-kcfqzou2/files
2025-07-13 18:15:11,181 INFO    SenderThread:2940713 [dir_watcher.py:finish():403] scan save: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-kcfqzou2/files/config.yaml config.yaml
2025-07-13 18:15:11,181 INFO    SenderThread:2940713 [dir_watcher.py:finish():403] scan save: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-kcfqzou2/files/wandb-summary.json wandb-summary.json
2025-07-13 18:15:11,182 INFO    SenderThread:2940713 [dir_watcher.py:finish():403] scan save: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-kcfqzou2/files/requirements.txt requirements.txt
2025-07-13 18:15:11,187 INFO    SenderThread:2940713 [dir_watcher.py:finish():403] scan save: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-kcfqzou2/files/conda-environment.yaml conda-environment.yaml
2025-07-13 18:15:11,197 INFO    SenderThread:2940713 [dir_watcher.py:finish():403] scan save: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-kcfqzou2/files/wandb-metadata.json wandb-metadata.json
2025-07-13 18:15:11,197 INFO    SenderThread:2940713 [dir_watcher.py:finish():403] scan save: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-kcfqzou2/files/output.log output.log
2025-07-13 18:15:11,203 INFO    SenderThread:2940713 [sender.py:transition_state():602] send defer: 10
2025-07-13 18:15:11,203 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:15:11,209 INFO    HandlerThread:2940713 [handler.py:handle_request_defer():170] handle defer: 10
2025-07-13 18:15:11,210 DEBUG   SenderThread:2940713 [sender.py:send_request():396] send_request: defer
2025-07-13 18:15:11,211 INFO    SenderThread:2940713 [sender.py:send_request_defer():598] handle sender defer: 10
2025-07-13 18:15:11,211 INFO    SenderThread:2940713 [file_pusher.py:finish():159] shutting down file pusher
2025-07-13 18:15:11,470 INFO    wandb-upload_0:2940713 [upload_job.py:push():133] Uploaded file /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-kcfqzou2/files/wandb-summary.json
2025-07-13 18:15:11,531 INFO    wandb-upload_6:2940713 [upload_job.py:push():133] Uploaded file /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-kcfqzou2/files/conda-environment.yaml
2025-07-13 18:15:11,545 INFO    wandb-upload_7:2940713 [upload_job.py:push():133] Uploaded file /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-kcfqzou2/files/output.log
2025-07-13 18:15:11,576 INFO    wandb-upload_3:2940713 [upload_job.py:push():133] Uploaded file /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-kcfqzou2/files/config.yaml
2025-07-13 18:15:11,600 INFO    wandb-upload_5:2940713 [upload_job.py:push():133] Uploaded file /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-kcfqzou2/files/requirements.txt
2025-07-13 18:15:11,800 INFO    Thread-11 :2940713 [sender.py:transition_state():602] send defer: 11
2025-07-13 18:15:11,801 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:15:11,801 INFO    HandlerThread:2940713 [handler.py:handle_request_defer():170] handle defer: 11
2025-07-13 18:15:11,802 DEBUG   SenderThread:2940713 [sender.py:send_request():396] send_request: defer
2025-07-13 18:15:11,802 INFO    SenderThread:2940713 [sender.py:send_request_defer():598] handle sender defer: 11
2025-07-13 18:15:11,802 INFO    SenderThread:2940713 [file_pusher.py:join():164] waiting for file pusher
2025-07-13 18:15:11,802 INFO    SenderThread:2940713 [sender.py:transition_state():602] send defer: 12
2025-07-13 18:15:11,802 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:15:11,802 INFO    HandlerThread:2940713 [handler.py:handle_request_defer():170] handle defer: 12
2025-07-13 18:15:11,803 DEBUG   SenderThread:2940713 [sender.py:send_request():396] send_request: defer
2025-07-13 18:15:11,803 INFO    SenderThread:2940713 [sender.py:send_request_defer():598] handle sender defer: 12
2025-07-13 18:15:11,883 INFO    SenderThread:2940713 [sender.py:transition_state():602] send defer: 13
2025-07-13 18:15:11,884 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:15:11,884 INFO    HandlerThread:2940713 [handler.py:handle_request_defer():170] handle defer: 13
2025-07-13 18:15:11,884 DEBUG   SenderThread:2940713 [sender.py:send_request():396] send_request: defer
2025-07-13 18:15:11,884 INFO    SenderThread:2940713 [sender.py:send_request_defer():598] handle sender defer: 13
2025-07-13 18:15:11,884 INFO    SenderThread:2940713 [sender.py:transition_state():602] send defer: 14
2025-07-13 18:15:11,885 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: defer
2025-07-13 18:15:11,885 DEBUG   SenderThread:2940713 [sender.py:send():369] send: final
2025-07-13 18:15:11,885 INFO    HandlerThread:2940713 [handler.py:handle_request_defer():170] handle defer: 14
2025-07-13 18:15:11,886 DEBUG   SenderThread:2940713 [sender.py:send():369] send: footer
2025-07-13 18:15:11,886 DEBUG   SenderThread:2940713 [sender.py:send_request():396] send_request: defer
2025-07-13 18:15:11,886 INFO    SenderThread:2940713 [sender.py:send_request_defer():598] handle sender defer: 14
2025-07-13 18:15:11,888 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: poll_exit
2025-07-13 18:15:11,888 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: server_info
2025-07-13 18:15:11,888 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: get_summary
2025-07-13 18:15:11,889 DEBUG   SenderThread:2940713 [sender.py:send_request():396] send_request: poll_exit
2025-07-13 18:15:11,889 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: sampled_history
2025-07-13 18:15:11,889 DEBUG   SenderThread:2940713 [sender.py:send_request():396] send_request: server_info
2025-07-13 18:15:11,951 INFO    MainThread:2940713 [wandb_run.py:_footer_history_summary_info():3467] rendering history
2025-07-13 18:15:11,951 INFO    MainThread:2940713 [wandb_run.py:_footer_history_summary_info():3499] rendering summary
2025-07-13 18:15:11,952 INFO    MainThread:2940713 [wandb_run.py:_footer_sync_info():3426] logging synced files
2025-07-13 18:15:11,952 DEBUG   HandlerThread:2940713 [handler.py:handle_request():144] handle_request: shutdown
2025-07-13 18:15:11,953 INFO    HandlerThread:2940713 [handler.py:finish():854] shutting down handler
2025-07-13 18:15:12,889 INFO    WriterThread:2940713 [datastore.py:close():298] close: /home-local/akath.nobkp/sereact/wandb/run-20250713_181326-kcfqzou2/run-kcfqzou2.wandb
2025-07-13 18:15:12,951 INFO    SenderThread:2940713 [sender.py:finish():1526] shutting down sender
2025-07-13 18:15:12,951 INFO    SenderThread:2940713 [file_pusher.py:finish():159] shutting down file pusher
2025-07-13 18:15:12,951 INFO    SenderThread:2940713 [file_pusher.py:join():164] waiting for file pusher
