2025-07-11 22:38:57,604 INFO    StreamThr :2842103 [internal.py:wandb_internal():89] W&B internal server running at pid: 2842103, started at: 2025-07-11 22:38:57.603415
2025-07-11 22:38:57,609 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status
2025-07-11 22:38:57,613 INFO    WriterThread:2842103 [datastore.py:open_for_write():85] open: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/run-lso6324k.wandb
2025-07-11 22:38:57,614 DEBUG   SenderThread:2842103 [sender.py:send():369] send: header
2025-07-11 22:38:57,629 DEBUG   SenderThread:2842103 [sender.py:send():369] send: run
2025-07-11 22:38:57,846 INFO    SenderThread:2842103 [dir_watcher.py:__init__():211] watching files in: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files
2025-07-11 22:38:57,846 INFO    SenderThread:2842103 [sender.py:_start_run_threads():1103] run started: lso6324k with start time 1752287937.60462
2025-07-11 22:38:57,847 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:38:57,847 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:38:57,855 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: check_version
2025-07-11 22:38:57,855 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: check_version
2025-07-11 22:38:57,918 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: run_start
2025-07-11 22:38:57,922 DEBUG   HandlerThread:2842103 [system_info.py:__init__():31] System info init
2025-07-11 22:38:57,922 DEBUG   HandlerThread:2842103 [system_info.py:__init__():46] System info init done
2025-07-11 22:38:57,922 INFO    HandlerThread:2842103 [system_monitor.py:start():181] Starting system monitor
2025-07-11 22:38:57,922 INFO    SystemMonitor:2842103 [system_monitor.py:_start():145] Starting system asset monitoring threads
2025-07-11 22:38:57,923 INFO    HandlerThread:2842103 [system_monitor.py:probe():201] Collecting system info
2025-07-11 22:38:57,923 INFO    SystemMonitor:2842103 [interfaces.py:start():190] Started cpu monitoring
2025-07-11 22:38:57,924 INFO    SystemMonitor:2842103 [interfaces.py:start():190] Started disk monitoring
2025-07-11 22:38:57,925 INFO    SystemMonitor:2842103 [interfaces.py:start():190] Started gpu monitoring
2025-07-11 22:38:57,927 INFO    SystemMonitor:2842103 [interfaces.py:start():190] Started memory monitoring
2025-07-11 22:38:57,928 INFO    SystemMonitor:2842103 [interfaces.py:start():190] Started network monitoring
2025-07-11 22:38:57,962 DEBUG   HandlerThread:2842103 [system_info.py:probe():195] Probing system
2025-07-11 22:38:57,970 DEBUG   HandlerThread:2842103 [system_info.py:_probe_git():180] Probing git
2025-07-11 22:38:57,984 DEBUG   HandlerThread:2842103 [system_info.py:_probe_git():188] Probing git done
2025-07-11 22:38:57,984 DEBUG   HandlerThread:2842103 [system_info.py:probe():240] Probing system done
2025-07-11 22:38:57,984 DEBUG   HandlerThread:2842103 [system_monitor.py:probe():210] {'os': 'Linux-5.4.0-77-generic-x86_64-with-debian-bullseye-sid', 'python': '3.7.16', 'heartbeatAt': '2025-07-12T02:38:57.962409', 'startedAt': '2025-07-12T02:38:57.588257', 'docker': None, 'cuda': None, 'args': ('--local_rank=0', '--cfg', 'config/base_train.yaml', '--output', '/home-local2/akath.extra.nobkp/sereact', '--data-path', '/home-local2/akath.extra.nobkp/dl_challenge', '--batch-size', '2'), 'state': 'running', 'program': 'main.py', 'codePath': 'main.py', 'git': {'remote': 'https://github.com/Shrinidhibhat87/codingchallenge_sereact.git', 'commit': 'a3ff60e13dfced588b500c8a1de00f36fdf22d49'}, 'email': '<EMAIL>', 'root': '/home-local/akath.nobkp/codingchallenge_sereact', 'host': 'lv3-32190', 'username': 'akath', 'executable': '/gel/usr/akath/.conda/envs/swin/bin/python', 'cpu_count': 6, 'cpu_count_logical': 12, 'cpu_freq': {'current': 1.5655000000000001, 'min': 1200.0, 'max': 4000.0}, 'cpu_freq_per_core': [{'current': 1.768, 'min': 1200.0, 'max': 4000.0}, {'current': 1.199, 'min': 1200.0, 'max': 4000.0}, {'current': 1.201, 'min': 1200.0, 'max': 4000.0}, {'current': 1.6, 'min': 1200.0, 'max': 4000.0}, {'current': 1.551, 'min': 1200.0, 'max': 4000.0}, {'current': 2.099, 'min': 1200.0, 'max': 4000.0}, {'current': 1.524, 'min': 1200.0, 'max': 4000.0}, {'current': 1.199, 'min': 1200.0, 'max': 4000.0}, {'current': 1.202, 'min': 1200.0, 'max': 4000.0}, {'current': 1.584, 'min': 1200.0, 'max': 4000.0}, {'current': 1.754, 'min': 1200.0, 'max': 4000.0}, {'current': 2.105, 'min': 1200.0, 'max': 4000.0}], 'disk': {'total': 111.2200813293457, 'used': 53.244991302490234}, 'gpu': 'TITAN Xp', 'gpu_count': 3, 'gpu_devices': [{'name': 'TITAN Xp', 'memory_total': 12787122176}, {'name': 'TITAN Xp', 'memory_total': 12788498432}, {'name': 'TITAN Xp', 'memory_total': 12788498432}], 'memory': {'total': 62.72977066040039}}
2025-07-11 22:38:57,984 INFO    HandlerThread:2842103 [system_monitor.py:probe():211] Finished collecting system info
2025-07-11 22:38:57,984 INFO    HandlerThread:2842103 [system_monitor.py:probe():214] Publishing system info
2025-07-11 22:38:57,985 DEBUG   HandlerThread:2842103 [system_info.py:_save_pip():52] Saving list of pip packages installed into the current environment
2025-07-11 22:38:57,985 DEBUG   HandlerThread:2842103 [system_info.py:_save_pip():67] Saving pip packages done
2025-07-11 22:38:57,985 DEBUG   HandlerThread:2842103 [system_info.py:_save_conda():75] Saving list of conda packages installed into the current environment
2025-07-11 22:38:58,849 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/conda-environment.yaml
2025-07-11 22:38:58,850 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:38:58,850 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/requirements.txt
2025-07-11 22:39:00,529 DEBUG   HandlerThread:2842103 [system_info.py:_save_conda():86] Saving conda packages done
2025-07-11 22:39:00,531 INFO    HandlerThread:2842103 [system_monitor.py:probe():216] Finished publishing system info
2025-07-11 22:39:00,540 DEBUG   SenderThread:2842103 [sender.py:send():369] send: files
2025-07-11 22:39:00,540 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-metadata.json with policy now
2025-07-11 22:39:00,545 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:39:00,546 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:39:00,668 DEBUG   SenderThread:2842103 [sender.py:send():369] send: telemetry
2025-07-11 22:39:00,847 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/conda-environment.yaml
2025-07-11 22:39:00,848 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-metadata.json
2025-07-11 22:39:00,848 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/output.log
2025-07-11 22:39:00,880 INFO    wandb-upload_0:2842103 [upload_job.py:push():133] Uploaded file /tmp/tmp7oyp1cdcwandb/nkr6tflw-wandb-metadata.json
2025-07-11 22:39:02,670 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:39:02,849 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/output.log
2025-07-11 22:39:04,850 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/output.log
2025-07-11 22:39:07,779 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:39:08,825 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:39:08,827 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:39:08,827 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:39:08,831 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:39:08,851 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:39:10,852 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/output.log
2025-07-11 22:39:11,612 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:39:11,614 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:39:11,614 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:39:11,617 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:39:11,852 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:39:13,618 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:39:14,462 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:39:14,464 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:39:14,464 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:39:14,465 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:39:14,853 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:39:15,545 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:39:15,546 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:39:17,116 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:39:17,117 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:39:17,118 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:39:17,118 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:39:17,854 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:39:19,120 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:39:19,580 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:39:19,581 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:39:19,582 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:39:19,582 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:39:19,855 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:39:22,571 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:39:22,572 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:39:22,572 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:39:22,573 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:39:22,856 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:39:24,574 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:39:25,000 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:39:25,002 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:39:25,002 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:39:25,002 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:39:25,857 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:39:27,606 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:39:27,607 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:39:27,607 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:39:27,608 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:39:27,858 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:39:29,614 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:39:29,859 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/config.yaml
2025-07-11 22:39:30,254 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:39:30,255 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:39:30,255 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:39:30,256 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:39:30,545 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:39:30,545 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:39:30,859 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:39:33,200 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:39:33,201 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:39:33,201 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:39:33,202 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:39:33,860 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:39:35,204 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:39:36,100 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:39:36,101 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:39:36,101 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:39:36,102 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:39:36,861 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/output.log
2025-07-11 22:39:36,862 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:39:38,811 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:39:38,812 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:39:38,812 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:39:38,812 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:39:38,862 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:39:40,419 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:39:40,419 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:39:40,419 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:39:40,420 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:39:40,420 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:39:40,863 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:39:43,012 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:39:43,013 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:39:43,014 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:39:43,015 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:39:43,864 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:39:45,545 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:39:45,546 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:39:45,623 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:39:45,908 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:39:45,909 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:39:45,909 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:39:45,910 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:39:46,865 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:39:48,303 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:39:48,304 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:39:48,304 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:39:48,304 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:39:48,866 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:39:50,860 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:39:50,861 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:39:50,861 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:39:50,861 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:39:50,862 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:39:50,866 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:39:53,229 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:39:53,230 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:39:53,230 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:39:53,231 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:39:53,867 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:39:55,836 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:39:55,837 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:39:55,837 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:39:55,838 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:39:55,868 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:39:56,838 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:39:57,846 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:39:57,847 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:39:57,847 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:39:57,847 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:39:57,868 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:39:57,928 DEBUG   SystemMonitor:2842103 [system_monitor.py:_start():159] Starting system metrics aggregation loop
2025-07-11 22:39:57,930 DEBUG   SenderThread:2842103 [sender.py:send():369] send: stats
2025-07-11 22:40:00,321 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:00,322 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:40:00,322 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:00,322 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:00,545 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:40:00,545 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:40:00,869 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:40:02,702 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:40:02,870 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/output.log
2025-07-11 22:40:03,093 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:03,094 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:40:03,094 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:03,095 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:03,870 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:40:05,656 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:05,656 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:40:05,657 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:05,659 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:05,871 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:40:07,447 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:07,448 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:40:07,448 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:07,449 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:07,871 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:40:08,449 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:40:09,795 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:09,796 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:40:09,796 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:09,797 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:09,872 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:40:11,190 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:11,191 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:40:11,191 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:11,192 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:11,872 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:40:12,605 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:12,606 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:40:12,606 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:12,607 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:12,873 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:40:13,664 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:40:14,873 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/output.log
2025-07-11 22:40:15,546 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:40:15,547 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:40:19,647 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:40:24,445 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:24,446 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:40:24,446 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:24,449 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:24,842 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:40:24,878 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:40:26,878 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/output.log
2025-07-11 22:40:27,932 DEBUG   SenderThread:2842103 [sender.py:send():369] send: stats
2025-07-11 22:40:28,700 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:28,701 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:40:28,702 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:28,703 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:28,879 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:40:30,480 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:30,481 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:40:30,482 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:30,482 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:40:30,483 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:30,546 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:40:30,546 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:40:30,880 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/output.log
2025-07-11 22:40:30,880 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:40:32,807 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:32,808 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:40:32,808 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:32,809 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:32,880 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:40:35,634 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:35,635 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:40:35,636 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:35,637 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:40:35,638 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:35,881 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:40:37,135 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:37,136 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:40:37,137 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:37,137 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:37,882 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:40:39,739 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:39,741 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:40:39,741 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:39,742 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:39,882 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:40:40,743 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:40:41,484 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:41,485 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:40:41,485 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:41,486 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:41,883 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:40:43,152 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:43,153 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:40:43,153 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:43,153 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:43,884 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:40:44,724 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:44,725 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:40:44,725 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:44,725 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:44,884 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:40:45,545 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:40:45,546 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:40:46,709 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:40:47,810 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:47,811 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:40:47,811 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:47,813 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:47,885 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:40:49,876 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:49,877 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:40:49,877 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:49,877 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:49,886 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:40:50,886 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/output.log
2025-07-11 22:40:51,878 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:40:52,494 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:52,496 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:40:52,496 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:52,496 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:52,887 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:40:55,095 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:55,097 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:40:55,097 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:55,097 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:55,888 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:40:56,922 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:56,923 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:40:56,923 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:56,924 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:40:56,924 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:57,889 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:40:57,934 DEBUG   SenderThread:2842103 [sender.py:send():369] send: stats
2025-07-11 22:40:58,256 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:40:58,257 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:40:58,257 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:40:58,258 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:40:58,889 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:41:00,045 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:00,046 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:41:00,046 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:00,046 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:00,545 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:41:00,546 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:41:00,890 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:41:01,141 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:01,142 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:41:01,142 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:01,143 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:01,891 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:41:02,143 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:41:02,319 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:02,320 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:41:02,320 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:02,321 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:02,891 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:41:04,203 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:04,204 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:41:04,205 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:04,205 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:04,892 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:41:05,486 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:05,487 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:41:05,487 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:05,488 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:05,892 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:41:07,488 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:41:07,498 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:07,499 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:41:07,499 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:07,500 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:07,893 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:41:09,744 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:09,745 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:41:09,745 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:09,745 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:09,893 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/output.log
2025-07-11 22:41:09,894 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:41:11,666 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:11,666 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:41:11,667 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:11,667 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:11,894 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:41:12,668 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:41:13,465 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:13,466 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:41:13,466 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:13,466 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:13,895 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:41:15,546 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:41:15,546 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:41:15,597 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:15,696 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:41:15,696 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:15,697 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:15,895 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:41:17,050 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:17,051 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:41:17,051 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:17,052 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:17,896 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:41:18,052 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:41:19,661 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:19,663 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:41:19,663 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:19,667 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:19,897 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:41:21,898 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/output.log
2025-07-11 22:41:23,729 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:41:27,948 DEBUG   SenderThread:2842103 [sender.py:send():369] send: stats
2025-07-11 22:41:28,949 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:41:30,089 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:30,090 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:41:30,090 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:30,092 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:30,546 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:41:30,547 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:41:30,900 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:41:31,901 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/output.log
2025-07-11 22:41:33,470 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:33,470 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:41:33,470 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:33,471 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:33,901 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:41:34,471 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:41:34,676 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:34,677 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:41:34,677 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:34,677 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:34,902 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:41:35,902 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/output.log
2025-07-11 22:41:36,244 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:36,246 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:41:36,246 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:36,246 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:36,902 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:41:38,254 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:38,255 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:41:38,255 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:38,256 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:38,903 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:41:40,256 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:41:41,058 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:41,060 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:41:41,060 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:41,062 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:41,904 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:41:42,627 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:42,628 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:41:42,629 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:42,629 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:42,904 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:41:45,093 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:45,094 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:41:45,094 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:45,095 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:45,546 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:41:45,546 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:41:45,617 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:41:45,905 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:41:46,668 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:46,669 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:41:46,670 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:46,670 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:46,906 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:41:48,098 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:48,098 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:41:48,099 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:48,100 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:48,906 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:41:49,564 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:49,565 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:41:49,566 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:49,566 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:49,907 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:41:50,999 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:51,000 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:41:51,000 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:41:51,001 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:51,001 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:51,907 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:41:52,799 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:52,800 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:41:52,800 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:52,802 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:52,908 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:41:53,908 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/output.log
2025-07-11 22:41:54,038 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:54,039 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:41:54,039 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:54,039 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:54,908 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:41:55,839 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:55,840 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:41:55,841 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:55,841 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:55,909 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:41:56,842 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:41:57,711 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:57,713 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:41:57,713 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:57,713 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:57,909 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:41:57,936 DEBUG   SenderThread:2842103 [sender.py:send():369] send: stats
2025-07-11 22:41:59,716 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:41:59,716 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:41:59,717 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:41:59,717 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:41:59,910 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:42:00,546 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:42:00,546 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:42:00,895 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:00,896 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:42:00,896 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:00,897 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:00,911 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:42:01,897 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:42:02,099 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:02,100 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:42:02,101 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:02,101 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:02,911 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:42:03,338 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:03,339 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:42:03,339 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:03,342 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:03,911 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:42:05,810 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:05,811 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:42:05,811 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:05,811 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:05,912 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:42:07,812 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:42:07,944 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:07,945 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:42:07,945 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:07,946 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:08,913 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:42:09,563 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:09,564 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:42:09,564 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:09,564 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:09,913 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/output.log
2025-07-11 22:42:09,914 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:42:11,572 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:11,573 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:42:11,573 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:11,574 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:11,914 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:42:13,574 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:42:13,672 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:13,673 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:42:13,674 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:13,676 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:13,915 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:42:15,236 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:15,237 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:42:15,237 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:15,238 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:15,546 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:42:15,547 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:42:15,915 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:42:16,705 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:16,706 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:42:16,706 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:16,707 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:16,915 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:42:18,553 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:18,554 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:42:18,554 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:18,555 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:18,614 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:42:18,916 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:42:21,917 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/output.log
2025-07-11 22:42:23,620 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:42:27,938 DEBUG   SenderThread:2842103 [sender.py:send():369] send: stats
2025-07-11 22:42:28,606 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:28,607 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:42:28,607 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:28,608 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:28,920 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:42:28,929 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:42:30,546 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:42:30,547 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:42:31,749 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:31,749 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:42:31,750 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:31,750 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:31,921 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/output.log
2025-07-11 22:42:31,921 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:42:33,122 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:33,123 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:42:33,123 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:33,124 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:33,922 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/output.log
2025-07-11 22:42:33,922 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:42:34,125 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:42:34,817 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:34,818 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:42:34,819 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:34,819 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:34,922 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:42:36,733 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:36,734 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:42:36,735 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:36,735 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:36,923 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:42:38,336 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:38,337 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:42:38,338 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:38,340 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:38,923 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:42:39,340 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:42:39,692 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:39,693 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:42:39,693 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:39,694 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:39,924 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:42:41,636 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:41,637 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:42:41,637 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:41,637 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:41,924 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:42:43,469 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:43,470 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:42:43,471 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:43,471 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:43,925 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:42:44,471 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:42:45,067 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:45,068 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:42:45,068 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:45,068 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:45,546 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:42:45,546 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:42:45,926 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:42:47,923 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:47,924 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:42:47,924 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:47,924 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:47,926 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:42:49,333 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:49,334 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:42:49,334 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:49,337 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:49,927 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:42:50,338 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:42:51,173 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:51,174 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:42:51,174 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:51,174 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:51,928 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/output.log
2025-07-11 22:42:51,928 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:42:52,739 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:52,740 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:42:52,740 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:52,741 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:52,928 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:42:54,255 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:54,256 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:42:54,256 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:54,256 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:54,929 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:42:55,690 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:55,691 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:42:55,692 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:55,692 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:42:55,693 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:55,929 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:42:56,920 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:56,921 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:42:56,922 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:56,923 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:56,930 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:42:57,939 DEBUG   SenderThread:2842103 [sender.py:send():369] send: stats
2025-07-11 22:42:58,893 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:42:58,894 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:42:58,894 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:42:58,895 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:42:58,930 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:43:00,547 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:43:00,547 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:43:01,378 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:01,378 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:43:01,379 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:01,380 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:43:01,381 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:01,931 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:43:02,850 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:02,850 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:43:02,851 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:02,851 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:02,932 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:43:04,789 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:04,790 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:43:04,790 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:04,791 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:04,932 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:43:06,319 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:06,320 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:43:06,320 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:06,321 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:06,933 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:43:07,322 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:43:07,930 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:07,931 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:43:07,931 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:07,932 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:07,933 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/output.log
2025-07-11 22:43:07,933 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:43:09,329 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:09,330 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:43:09,330 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:09,330 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:09,934 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:43:10,844 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:10,845 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:43:10,845 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:10,846 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:10,934 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:43:12,138 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:12,139 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:43:12,139 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:12,142 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:12,935 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:43:13,143 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:43:13,593 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:13,594 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:43:13,594 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:13,595 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:13,935 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:43:15,546 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:43:15,547 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:43:15,671 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:15,672 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:43:15,673 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:15,674 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:15,936 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:43:17,937 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/output.log
2025-07-11 22:43:18,746 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:43:23,746 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:43:25,763 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:25,764 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:43:25,764 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:25,766 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:25,939 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:43:27,941 DEBUG   SenderThread:2842103 [sender.py:send():369] send: stats
2025-07-11 22:43:27,942 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/output.log
2025-07-11 22:43:28,412 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:28,414 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:43:28,414 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:28,415 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:28,943 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:43:29,415 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:43:29,943 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/output.log
2025-07-11 22:43:30,209 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:30,209 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:43:30,210 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:30,210 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:30,547 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:43:30,547 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:43:30,943 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:43:31,679 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:31,680 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:43:31,681 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:31,681 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:31,943 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:43:33,284 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:33,285 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:43:33,285 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:33,286 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:33,944 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:43:35,286 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:43:35,420 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:35,421 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:43:35,421 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:35,421 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:35,945 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:43:37,327 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:37,328 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:43:37,328 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:37,330 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:37,945 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:43:39,036 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:39,036 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:43:39,037 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:39,037 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:39,946 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:43:40,671 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:40,672 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:43:40,672 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:40,673 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:43:40,673 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:40,946 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:43:42,225 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:42,227 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:43:42,227 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:42,228 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:42,947 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:43:43,817 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:43,819 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:43:43,819 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:43,820 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:43,947 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:43:45,547 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:43:45,547 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:43:45,589 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:45,667 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:43:45,668 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:45,668 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:45,948 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:43:46,669 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:43:47,241 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:47,242 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:43:47,243 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:47,243 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:47,948 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/output.log
2025-07-11 22:43:47,949 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:43:48,723 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:48,724 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:43:48,725 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:48,727 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:48,949 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:43:50,391 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:50,392 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:43:50,392 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:50,393 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:50,949 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:43:52,333 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:52,334 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:43:52,334 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:52,335 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:43:52,335 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:52,950 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:43:54,216 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:54,217 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:43:54,218 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:54,219 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:54,951 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:43:55,712 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:55,714 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:43:55,714 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:55,715 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:55,951 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:43:57,292 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:57,293 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:43:57,294 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:57,294 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:57,943 DEBUG   SenderThread:2842103 [sender.py:send():369] send: stats
2025-07-11 22:43:57,944 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:43:57,952 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:43:58,974 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:43:58,975 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:43:58,976 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:43:58,976 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:43:59,952 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:44:00,547 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:44:00,547 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:44:00,828 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:00,829 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:44:00,829 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:00,830 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:00,953 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:44:01,974 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:01,975 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:44:01,975 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:01,975 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:02,953 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:44:02,976 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:44:03,665 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:03,666 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:44:03,666 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:03,667 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:03,954 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/output.log
2025-07-11 22:44:03,954 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:44:05,410 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:05,411 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:44:05,412 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:05,412 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:05,954 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:44:06,942 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:06,943 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:44:06,943 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:06,944 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:06,954 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:44:08,944 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:44:09,043 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:09,044 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:44:09,044 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:09,044 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:09,955 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:44:10,640 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:10,641 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:44:10,641 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:10,642 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:10,956 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:44:13,194 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:13,195 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:44:13,196 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:13,196 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:13,957 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:44:14,266 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:44:15,547 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:44:15,547 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:44:15,957 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/output.log
2025-07-11 22:44:19,611 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:44:23,249 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:23,250 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:44:23,250 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:23,252 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:23,960 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:44:25,601 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:44:25,764 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:25,764 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:44:25,764 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:25,765 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:25,961 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/output.log
2025-07-11 22:44:25,961 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:44:27,333 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:27,347 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:44:27,348 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:27,348 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:27,944 DEBUG   SenderThread:2842103 [sender.py:send():369] send: stats
2025-07-11 22:44:27,961 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/output.log
2025-07-11 22:44:27,962 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:44:29,206 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:29,207 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:44:29,207 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:29,207 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:29,962 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:44:30,547 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:44:30,548 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:44:30,705 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:44:30,939 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:30,940 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:44:30,940 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:30,940 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:30,962 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:44:32,342 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:32,343 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:44:32,343 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:32,343 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:32,963 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:44:33,879 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:33,880 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:44:33,881 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:33,881 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:33,963 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:44:35,872 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:35,873 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:44:35,873 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:35,873 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:44:35,874 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:35,963 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:44:37,121 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:37,123 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:44:37,123 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:37,124 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:37,964 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:44:38,715 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:38,716 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:44:38,717 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:38,717 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:38,964 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:44:40,233 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:40,234 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:44:40,234 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:40,235 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:40,965 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:44:41,235 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:44:41,632 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:41,633 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:44:41,633 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:41,634 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:41,965 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:44:43,393 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:43,394 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:44:43,395 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:43,395 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:43,966 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/output.log
2025-07-11 22:44:43,967 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:44:44,860 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:44,861 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:44:44,861 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:44,862 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:44,966 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:44:45,547 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:44:45,548 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:44:46,656 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:44:46,716 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:46,717 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:44:46,718 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:46,718 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:46,967 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:44:47,899 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:47,900 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:44:47,900 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:47,900 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:47,968 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:44:49,496 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:49,498 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:44:49,498 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:49,498 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:49,968 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:44:50,989 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:50,990 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:44:50,991 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:50,991 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:51,969 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:44:51,991 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:44:52,560 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:52,561 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:44:52,562 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:52,562 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:52,969 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:44:54,230 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:54,231 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:44:54,231 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:54,233 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:54,970 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:44:56,243 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:56,244 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:44:56,244 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:56,245 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:56,970 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:44:57,246 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:44:57,794 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:57,795 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:44:57,795 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:57,795 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:57,946 DEBUG   SenderThread:2842103 [sender.py:send():369] send: stats
2025-07-11 22:44:57,970 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:44:59,245 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:44:59,246 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:44:59,247 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:44:59,247 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:44:59,971 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/output.log
2025-07-11 22:44:59,971 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:45:00,547 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:45:00,548 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:45:00,908 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:00,908 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:45:00,909 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:00,909 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:00,972 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:45:02,300 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:02,301 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:45:02,301 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:02,301 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:45:02,302 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:02,972 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:45:03,618 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:03,618 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:45:03,619 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:03,619 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:03,973 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:45:05,762 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:05,763 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:45:05,763 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:05,763 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:05,973 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:45:07,218 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:07,218 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:45:07,219 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:07,219 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:07,974 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:45:08,286 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:45:09,975 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/output.log
2025-07-11 22:45:13,286 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:45:15,548 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:45:15,549 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:45:17,283 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:17,284 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:45:17,284 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:17,284 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:17,978 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:45:18,688 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:45:19,709 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:19,711 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:45:19,711 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:19,712 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:19,978 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/output.log
2025-07-11 22:45:19,979 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:45:21,252 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:21,253 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:45:21,254 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:21,254 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:21,979 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/output.log
2025-07-11 22:45:21,979 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:45:23,196 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:23,198 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:45:23,198 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:23,199 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:23,980 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:45:24,199 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:45:24,813 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:24,813 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:45:24,814 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:24,815 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:24,980 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:45:26,454 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:26,456 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:45:26,456 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:26,456 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:26,981 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:45:27,632 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:27,634 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:45:27,634 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:27,634 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:27,947 DEBUG   SenderThread:2842103 [sender.py:send():369] send: stats
2025-07-11 22:45:27,981 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:45:29,466 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:29,467 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:45:29,467 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:29,467 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:45:29,468 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:29,982 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:45:30,548 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:45:30,548 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:45:30,935 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:30,936 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:45:30,936 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:30,937 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:30,982 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:45:32,216 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:32,217 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:45:32,217 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:32,217 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:32,983 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:45:33,886 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:33,887 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:45:33,887 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:33,888 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:33,984 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:45:34,888 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:45:35,558 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:35,559 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:45:35,559 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:35,560 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:35,984 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:45:37,202 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:37,204 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:45:37,204 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:37,205 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:37,985 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/output.log
2025-07-11 22:45:37,985 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:45:38,805 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:38,806 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:45:38,807 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:38,807 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:38,985 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:45:40,791 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:40,792 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:45:40,792 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:40,792 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:45:40,793 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:40,986 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:45:42,021 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:42,022 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:45:42,022 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:42,023 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:42,986 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:45:43,368 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:43,369 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:45:43,370 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:43,370 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:43,987 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:45:45,085 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:45,086 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:45:45,086 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:45,087 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:45,548 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:45:45,548 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:45:45,987 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:45:46,618 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:46,620 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:45:46,620 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:46,620 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:45:46,621 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:46,988 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:45:48,176 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:48,177 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:45:48,177 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:48,178 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:48,988 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:45:49,715 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:49,716 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:45:49,717 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:49,717 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:49,988 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:45:51,717 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:45:51,782 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:51,783 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:45:51,783 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:51,784 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:51,989 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:45:53,262 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:53,263 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:45:53,263 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:53,264 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:53,990 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/output.log
2025-07-11 22:45:53,990 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:45:54,706 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:54,707 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:45:54,708 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:54,708 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:54,990 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:45:55,886 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:55,887 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:45:55,888 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:55,890 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:55,990 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:45:56,890 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:45:57,650 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:57,651 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:45:57,651 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:57,652 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:57,949 DEBUG   SenderThread:2842103 [sender.py:send():369] send: stats
2025-07-11 22:45:57,991 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:45:59,141 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:45:59,142 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:45:59,142 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:45:59,142 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:45:59,992 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:46:00,548 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:46:00,548 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:46:00,788 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:00,798 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:46:00,798 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:00,798 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:00,992 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:46:01,993 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/output.log
2025-07-11 22:46:02,861 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:46:07,861 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:46:10,899 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:10,900 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:46:10,900 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:10,901 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:10,996 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:46:11,996 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/output.log
2025-07-11 22:46:12,733 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:12,734 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:46:12,734 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:12,735 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:12,996 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:46:13,735 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:46:13,997 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/output.log
2025-07-11 22:46:14,558 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:14,559 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:46:14,559 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:14,559 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:14,997 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:46:15,548 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:46:15,549 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:46:16,604 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:16,605 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:46:16,606 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:16,606 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:16,998 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:46:18,231 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:18,232 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:46:18,233 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:18,233 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:18,998 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:46:19,234 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:46:19,554 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:19,555 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:46:19,555 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:19,556 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:19,999 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:46:21,002 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:21,003 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:46:21,003 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:21,003 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:21,999 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:46:22,817 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:22,818 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:46:22,819 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:22,819 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:23,000 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:46:24,183 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:24,183 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:46:24,184 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:24,184 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:25,001 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:46:25,184 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:46:26,138 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:26,139 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:46:26,140 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:26,142 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:27,001 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:46:27,465 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:27,466 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:46:27,466 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:27,467 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:27,951 DEBUG   SenderThread:2842103 [sender.py:send():369] send: stats
2025-07-11 22:46:28,002 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:46:28,759 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:28,760 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:46:28,761 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:28,761 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:29,002 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:46:30,002 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/output.log
2025-07-11 22:46:30,357 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:30,358 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:46:30,358 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:30,358 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:46:30,358 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:30,548 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:46:30,548 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:46:31,003 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:46:31,930 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:31,931 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:46:31,931 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:31,931 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:32,003 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:46:33,471 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:33,472 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:46:33,472 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:33,473 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:34,004 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:46:35,323 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:35,324 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:46:35,325 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:35,325 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:36,004 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:46:36,326 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:46:36,892 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:36,893 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:46:36,893 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:36,894 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:37,005 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:46:39,013 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:39,014 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:46:39,014 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:39,015 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:40,006 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:46:40,329 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:40,329 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:46:40,330 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:40,330 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:41,006 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:46:41,330 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:46:42,292 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:42,293 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:46:42,293 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:42,294 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:43,007 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:46:44,283 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:44,284 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:46:44,284 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:44,285 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:45,007 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:46:45,548 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:46:45,548 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:46:45,968 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:45,969 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:46:45,970 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:45,970 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:46,008 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:46:46,971 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:46:48,008 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/output.log
2025-07-11 22:46:48,123 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:48,124 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:46:48,124 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:48,124 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:49,009 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:46:49,410 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:49,412 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:46:49,412 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:49,413 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:50,009 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:46:50,975 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:50,977 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:46:50,977 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:50,977 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:51,009 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:46:51,978 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:46:52,832 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:52,833 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:46:52,833 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:52,834 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:53,010 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:46:54,195 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:54,197 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:46:54,197 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:54,197 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:55,010 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:46:55,515 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:46:55,516 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:46:55,516 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:46:55,516 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:46:56,010 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:46:57,584 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:46:57,952 DEBUG   SenderThread:2842103 [sender.py:send():369] send: stats
2025-07-11 22:46:58,011 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/output.log
2025-07-11 22:47:00,548 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:47:00,549 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:47:02,621 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:47:05,583 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:47:05,584 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:47:05,585 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:47:05,586 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:47:06,014 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:47:07,653 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:47:07,653 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:47:07,654 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:47:07,655 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:47:07,655 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:47:08,015 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/output.log
2025-07-11 22:47:08,015 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:47:09,508 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:47:09,509 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:47:09,509 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:47:09,510 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:47:10,015 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/output.log
2025-07-11 22:47:10,015 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:47:11,077 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:47:11,078 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:47:11,079 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:47:11,079 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:47:12,016 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:47:12,912 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:47:12,913 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:47:12,913 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:47:12,913 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:47:12,914 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:47:13,016 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:47:14,969 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:47:14,970 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:47:14,970 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:47:14,971 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:47:15,017 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:47:15,548 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:47:15,549 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:47:17,145 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:47:17,146 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:47:17,146 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:47:17,146 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:47:18,018 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:47:18,147 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:47:18,592 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:47:18,593 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:47:18,594 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:47:18,594 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:47:19,018 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:47:20,167 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:47:20,168 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:47:20,168 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:47:20,169 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:47:21,019 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:47:21,737 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:47:21,738 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:47:21,739 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:47:21,739 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:47:22,019 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:47:23,293 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:47:23,294 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:47:23,295 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:47:23,295 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:47:23,295 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:47:24,019 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:47:24,995 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:47:25,007 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:47:25,007 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:47:25,008 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:47:25,020 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:47:26,020 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/output.log
2025-07-11 22:47:26,490 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:47:26,491 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:47:26,492 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:47:26,492 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:47:27,020 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:47:27,866 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:47:27,868 DEBUG   SenderThread:2842103 [sender.py:send():369] send: history
2025-07-11 22:47:27,868 DEBUG   SenderThread:2842103 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:47:27,871 INFO    SenderThread:2842103 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:47:27,953 DEBUG   SenderThread:2842103 [sender.py:send():369] send: stats
2025-07-11 22:47:28,021 INFO    Thread-12 :2842103 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_223857-lso6324k/files/wandb-summary.json
2025-07-11 22:47:28,954 DEBUG   HandlerThread:2842103 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:47:32,089 INFO    memory    :2842103 [interfaces.py:monitor():140] Process proc.memory.rssMB has exited.
2025-07-11 22:47:32,089 DEBUG   SystemMonitor:2842103 [system_monitor.py:_start():166] Finished system metrics aggregation loop
2025-07-11 22:47:32,090 DEBUG   SystemMonitor:2842103 [system_monitor.py:_start():170] Publishing last batch of metrics
2025-07-11 22:47:32,092 DEBUG   SenderThread:2842103 [sender.py:send():369] send: stats
2025-07-11 22:47:33,008 INFO    MainThread:2842103 [internal.py:handle_exit():76] Internal process exited
