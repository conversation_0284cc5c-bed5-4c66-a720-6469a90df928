=> merge config from config/base_train.yaml
RANK and WORLD_SIZE in environ: 0/3
[32m[2025-07-11 22:39:03 3DDETR.yaml][33m(main.py 549)[39m: INFO Full config saved to /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/config.json
[32m[2025-07-11 22:39:03 3DDETR.yaml][33m(main.py 552)[39m: INFO AMP_ENABLE: true
TAG: default
amp_opt_level: ''
base:
- ''
data:
  augment: false
  batch_size: 2
  cache_mode: part
  data_path: /home-local2/akath.extra.nobkp/dl_challenge
  dataset: Sereact_dataset
  debug: false
  num_workers: 4
  pin_memory: true
  transform: null
  zip_mode: false
eval_mode: false
local_rank: 0
loss:
  matcher_costs:
    cost_box_corners: 1.0
    giou: 5.0
    l1: 2.0
  weights:
    box_corners: 1.0
    giou: 1.0
    size: 1.0
    size_reg: 1.0
model:
  decoder:
    dim: 256
    dropout: 0.1
    ffn_dim: 256
    nhead: 4
    num_layers: 3
  encoder:
    activation: relu
    dim: 256
    dropout: 0.1
    ffn_dim: 128
    nheads: 4
    num_layers: 3
    preencoder_npoints: 2048
    type: vanilla
    use_color: false
  export_model: false
  mlp_dropout: 0.3
  name: 3DDETR.yaml
  num_angular_bins: 12
  num_queries: 256
  position_embedding: fourier
  pretrained: null
  pretrained_weights_path: /home/<USER>/Coding/Pre_trained_Weights/3detr/scannet_ep1080.pth
  resume: ''
  training: true
  unit_test: false
output: /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123
print_freq: 10
save_freq: 1
seed: 40
tag: '123'
train:
  accumulation_steps: 1
  auto_resume: false
  base_lr: 0.0001
  clip_grad: 0.1
  filter_biases_wd: true
  final_lr: 1.0e-06
  lr_scheduler: cosine
  max_epoch: 200
  start_epoch: 0
  unit_test_epoch: 100
  use_checkpoint: false
  warm_lr: 5.0e-06
  warm_lr_epochs: 9
  weight_decay: 0.01
unit_test: false
[32m[2025-07-11 22:39:03 3DDETR.yaml][33m(main.py 553)[39m: INFO {"cfg": "config/base_train.yaml", "opts": null, "batch_size": 2, "data_path": "/home-local2/akath.extra.nobkp/dl_challenge", "pretrained": null, "resume": null, "accumulation_steps": null, "use_checkpoint": false, "disable_amp": false, "amp_opt_level": null, "output": "/home-local2/akath.extra.nobkp/sereact", "tag": null, "eval": false, "unit_test": false, "base_lr": null, "local_rank": 0}
local rank 0 / global rank 0 successfully build train dataset
local rank 0 / global rank 0 successfully build val dataset
[32m[2025-07-11 22:39:03 3DDETR.yaml][33m(main.py 102)[39m: INFO Model3DDETR(
  (pre_encoder): PointnetSAModuleVotes(
    (grouper): QueryAndGroup()
    (mlp_module): SharedMLP(
      (layer0): Conv2d(
        (conv): Conv2d(3, 64, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn): BatchNorm2d(
          (bn): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        )
        (activation): ReLU(inplace=True)
      )
      (layer1): Conv2d(
        (conv): Conv2d(64, 128, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn): BatchNorm2d(
          (bn): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        )
        (activation): ReLU(inplace=True)
      )
      (layer2): Conv2d(
        (conv): Conv2d(128, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn): BatchNorm2d(
          (bn): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        )
        (activation): ReLU(inplace=True)
      )
    )
  )
  (encoder): TransformerEncoder(
    (layers): ModuleList(
      (0): TransformerEncoderLayer(
        attn_dr=0.1
        (self_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (linear1): Linear(in_features=256, out_features=128, bias=True)
        (dropout): Dropout(p=0.1, inplace=False)
        (linear2): Linear(in_features=128, out_features=256, bias=True)
        (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout2): Dropout(p=0.1, inplace=False)
        (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout1): Dropout(p=0.1, inplace=False)
        (activation): ReLU()
      )
      (1): TransformerEncoderLayer(
        attn_dr=0.1
        (self_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (linear1): Linear(in_features=256, out_features=128, bias=True)
        (dropout): Dropout(p=0.1, inplace=False)
        (linear2): Linear(in_features=128, out_features=256, bias=True)
        (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout2): Dropout(p=0.1, inplace=False)
        (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout1): Dropout(p=0.1, inplace=False)
        (activation): ReLU()
      )
      (2): TransformerEncoderLayer(
        attn_dr=0.1
        (self_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (linear1): Linear(in_features=256, out_features=128, bias=True)
        (dropout): Dropout(p=0.1, inplace=False)
        (linear2): Linear(in_features=128, out_features=256, bias=True)
        (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout2): Dropout(p=0.1, inplace=False)
        (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout1): Dropout(p=0.1, inplace=False)
        (activation): ReLU()
      )
    )
  )
  (encoder_decoder_projection): GenericMLP(
    (layers): Sequential(
      (0): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
      (1): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (2): ReLU()
      (3): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
      (4): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (5): ReLU()
    )
  )
  (positional_embedding): PositionEmbeddingCoordsSine(type=fourier, scale=6.283185307179586, normalize=True, gaussB_shape=torch.Size([3, 128]), gaussB_sum=-17.944507598876953)
  (query_projection): GenericMLP(
    (layers): Sequential(
      (0): Conv1d(256, 256, kernel_size=(1,), stride=(1,))
      (1): ReLU()
      (2): Conv1d(256, 256, kernel_size=(1,), stride=(1,))
      (3): ReLU()
    )
  )
  (decoder): TransformerDecoder(
    (layers): ModuleList(
      (0): TransformerDecoderLayer(
        (self_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (multihead_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm3): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout1): Dropout(p=0.1, inplace=False)
        (dropout2): Dropout(p=0.1, inplace=False)
        (dropout3): Dropout(p=0.1, inplace=False)
        (linear1): Linear(in_features=256, out_features=256, bias=True)
        (dropout): Dropout(p=0.1, inplace=False)
        (linear2): Linear(in_features=256, out_features=256, bias=True)
        (activation): ReLU()
      )
      (1): TransformerDecoderLayer(
        (self_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (multihead_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm3): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout1): Dropout(p=0.1, inplace=False)
        (dropout2): Dropout(p=0.1, inplace=False)
        (dropout3): Dropout(p=0.1, inplace=False)
        (linear1): Linear(in_features=256, out_features=256, bias=True)
        (dropout): Dropout(p=0.1, inplace=False)
        (linear2): Linear(in_features=256, out_features=256, bias=True)
        (activation): ReLU()
      )
      (2): TransformerDecoderLayer(
        (self_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (multihead_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm3): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout1): Dropout(p=0.1, inplace=False)
        (dropout2): Dropout(p=0.1, inplace=False)
        (dropout3): Dropout(p=0.1, inplace=False)
        (linear1): Linear(in_features=256, out_features=256, bias=True)
        (dropout): Dropout(p=0.1, inplace=False)
        (linear2): Linear(in_features=256, out_features=256, bias=True)
        (activation): ReLU()
      )
    )
    (norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (mlp_heads): ModuleDict(
    (center_head): GenericMLP(
      (layers): Sequential(
        (0): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
        (1): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU()
        (3): Dropout(p=0.3, inplace=False)
        (4): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
        (5): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (6): ReLU()
        (7): Dropout(p=0.3, inplace=False)
        (8): Conv1d(256, 3, kernel_size=(1,), stride=(1,))
      )
    )
    (size_head): GenericMLP(
      (layers): Sequential(
        (0): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
        (1): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU()
        (3): Dropout(p=0.3, inplace=False)
        (4): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
        (5): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (6): ReLU()
        (7): Dropout(p=0.3, inplace=False)
        (8): Conv1d(256, 3, kernel_size=(1,), stride=(1,))
      )
    )
    (angle_cls_head): GenericMLP(
      (layers): Sequential(
        (0): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
        (1): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU()
        (3): Dropout(p=0.3, inplace=False)
        (4): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
        (5): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (6): ReLU()
        (7): Dropout(p=0.3, inplace=False)
        (8): Conv1d(256, 12, kernel_size=(1,), stride=(1,))
      )
    )
    (angle_residual_head): GenericMLP(
      (layers): Sequential(
        (0): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
        (1): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU()
        (3): Dropout(p=0.3, inplace=False)
        (4): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
        (5): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (6): ReLU()
        (7): Dropout(p=0.3, inplace=False)
        (8): Conv1d(256, 12, kernel_size=(1,), stride=(1,))
      )
    )
  )
)
[32m[2025-07-11 22:39:03 3DDETR.yaml][33m(main.py 104)[39m: INFO number of params: 3811038
[32m[2025-07-11 22:39:03 3DDETR.yaml][33m(main.py 153)[39m: INFO Start training
[32m[2025-07-11 22:39:08 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [0/200][0/27]	eta 0:02:16 lr 0.000100	time 5.0698 (5.0698)	loss 19.7473 (19.7473)	miou 0.1350	grad_norm 142.4439 (142.4439)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 22:39:36 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [0/200][10/27]	eta 0:00:49 lr 0.000099	time 2.8995 (2.9405)	loss 13.9518 (17.9870)	miou 0.1909	grad_norm 57.7850 (108.1239)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 22:40:00 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [0/200][20/27]	eta 0:00:18 lr 0.000097	time 2.4745 (2.6936)	loss 9.4613 (16.4137)	miou 0.1868	grad_norm 37.4838 (80.9266)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 22:40:12 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 0 training takes 0:01:08
[32m[2025-07-11 22:40:12 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
0
[32m[2025-07-11 22:40:24 3DDETR.yaml][33m(utils_help.py 202)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saving......
[32m[2025-07-11 22:40:24 3DDETR.yaml][33m(utils_help.py 204)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saved !!!
[32m[2025-07-11 22:40:24 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.0887%
[32m[2025-07-11 22:40:24 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.0887%
[32m[2025-07-11 22:40:28 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [1/200][0/27]	eta 0:01:44 lr 0.000095	time 3.8542 (3.8542)	loss 14.0423 (14.0423)	miou 0.0887	grad_norm 119.3601 (119.3601)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 22:40:49 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [1/200][10/27]	eta 0:00:38 lr 0.000091	time 2.0654 (2.2756)	loss 10.3168 (12.7411)	miou 0.1272	grad_norm 53.7233 (54.7363)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 22:41:07 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [1/200][20/27]	eta 0:00:14 lr 0.000086	time 2.0120 (2.0311)	loss 14.6192 (12.6867)	miou 0.1432	grad_norm 56.7440 (47.6981)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 22:41:19 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 1 training takes 0:00:54
[32m[2025-07-11 22:41:19 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
1
[32m[2025-07-11 22:41:30 3DDETR.yaml][33m(utils_help.py 202)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saving......
[32m[2025-07-11 22:41:30 3DDETR.yaml][33m(utils_help.py 204)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saved !!!
[32m[2025-07-11 22:41:30 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.0987%
[32m[2025-07-11 22:41:30 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.0987%
[32m[2025-07-11 22:41:33 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [2/200][0/27]	eta 0:01:22 lr 0.000082	time 3.0606 (3.0606)	loss 12.1001 (12.1001)	miou 0.0961	grad_norm 23.6865 (23.6865)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 22:41:50 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [2/200][10/27]	eta 0:00:31 lr 0.000076	time 1.4345 (1.8719)	loss 10.0254 (9.9917)	miou 0.1483	grad_norm 30.0939 (30.8217)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 22:42:07 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [2/200][20/27]	eta 0:00:12 lr 0.000069	time 2.1338 (1.7874)	loss 17.5772 (11.5909)	miou 0.1606	grad_norm 43.6638 (37.6691)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 22:42:18 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 2 training takes 0:00:48
[32m[2025-07-11 22:42:18 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
2
[32m[2025-07-11 22:42:28 3DDETR.yaml][33m(utils_help.py 202)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saving......
[32m[2025-07-11 22:42:28 3DDETR.yaml][33m(utils_help.py 204)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saved !!!
[32m[2025-07-11 22:42:28 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1203%
[32m[2025-07-11 22:42:28 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.1203%
[32m[2025-07-11 22:42:31 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [3/200][0/27]	eta 0:01:16 lr 0.000064	time 2.8175 (2.8175)	loss 7.8050 (7.8050)	miou 0.1266	grad_norm 34.9827 (34.9827)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 22:42:49 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [3/200][10/27]	eta 0:00:31 lr 0.000056	time 1.4096 (1.8547)	loss 7.9784 (11.0728)	miou 0.1429	grad_norm 28.3707 (31.5872)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 22:43:06 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [3/200][20/27]	eta 0:00:12 lr 0.000048	time 1.5292 (1.7804)	loss 14.5455 (11.0611)	miou 0.1579	grad_norm 48.5620 (32.2488)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 22:43:15 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 3 training takes 0:00:46
[32m[2025-07-11 22:43:15 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 22:43:25 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.0817%
[32m[2025-07-11 22:43:25 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.1203%
[32m[2025-07-11 22:43:28 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [4/200][0/27]	eta 0:01:11 lr 0.000043	time 2.6424 (2.6424)	loss 12.4800 (12.4800)	miou 0.0829	grad_norm 30.8575 (30.8575)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 22:43:45 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [4/200][10/27]	eta 0:00:30 lr 0.000035	time 1.7709 (1.8018)	loss 10.4514 (9.9513)	miou 0.1575	grad_norm 53.5535 (34.0708)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 22:44:01 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [4/200][20/27]	eta 0:00:12 lr 0.000028	time 1.1455 (1.7241)	loss 11.5833 (10.5375)	miou 0.1721	grad_norm 25.4118 (33.9960)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 22:44:13 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 4 training takes 0:00:47
[32m[2025-07-11 22:44:13 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
4
[32m[2025-07-11 22:44:23 3DDETR.yaml][33m(utils_help.py 202)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saving......
[32m[2025-07-11 22:44:23 3DDETR.yaml][33m(utils_help.py 204)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saved !!!
[32m[2025-07-11 22:44:23 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1560%
[32m[2025-07-11 22:44:23 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.1560%
[32m[2025-07-11 22:44:25 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [5/200][0/27]	eta 0:00:58 lr 0.000023	time 2.1593 (2.1593)	loss 8.3726 (8.3726)	miou 0.1551	grad_norm 87.2108 (87.2108)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 22:44:41 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [5/200][10/27]	eta 0:00:27 lr 0.000017	time 1.3981 (1.6389)	loss 9.7682 (10.7083)	miou 0.1814	grad_norm 29.9511 (40.8162)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 22:44:57 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [5/200][20/27]	eta 0:00:11 lr 0.000011	time 1.5497 (1.6281)	loss 13.7322 (10.5040)	miou 0.1804	grad_norm 26.2886 (39.7278)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 22:45:07 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 5 training takes 0:00:43
[32m[2025-07-11 22:45:07 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
5
[32m[2025-07-11 22:45:17 3DDETR.yaml][33m(utils_help.py 202)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saving......
[32m[2025-07-11 22:45:17 3DDETR.yaml][33m(utils_help.py 204)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saved !!!
[32m[2025-07-11 22:45:17 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1799%
[32m[2025-07-11 22:45:17 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.1799%
[32m[2025-07-11 22:45:19 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [6/200][0/27]	eta 0:00:54 lr 0.000008	time 2.0186 (2.0186)	loss 11.1829 (11.1829)	miou 0.1811	grad_norm 34.2269 (34.2269)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 22:45:35 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [6/200][10/27]	eta 0:00:27 lr 0.000004	time 1.6711 (1.6243)	loss 11.5724 (10.7413)	miou 0.1918	grad_norm 18.2111 (35.0031)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 22:45:51 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [6/200][20/27]	eta 0:00:11 lr 0.000002	time 2.0658 (1.6234)	loss 8.2115 (10.3838)	miou 0.1979	grad_norm 28.5618 (37.1245)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 22:46:00 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 6 training takes 0:00:43
[32m[2025-07-11 22:46:00 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 22:46:10 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1695%
[32m[2025-07-11 22:46:10 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.1799%
[32m[2025-07-11 22:46:12 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [7/200][0/27]	eta 0:00:49 lr 0.000001	time 1.8269 (1.8269)	loss 10.9778 (10.9778)	miou 0.1654	grad_norm 21.0911 (21.0911)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 22:46:28 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [7/200][10/27]	eta 0:00:27 lr 0.000000	time 1.2937 (1.6231)	loss 10.0810 (10.5066)	miou 0.1690	grad_norm 24.5533 (27.8720)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 22:46:45 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [7/200][20/27]	eta 0:00:11 lr 0.000001	time 1.6845 (1.6697)	loss 9.9946 (10.3744)	miou 0.1818	grad_norm 24.1886 (30.2174)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 22:46:55 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 7 training takes 0:00:44
[32m[2025-07-11 22:46:55 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
[32m[2025-07-11 22:47:05 3DDETR.yaml][33m(main.py 167)[39m: INFO Mean IOU of the network on the 40 test images: 0.1672%
[32m[2025-07-11 22:47:05 3DDETR.yaml][33m(main.py 169)[39m: INFO Max miou: 0.1799%
[32m[2025-07-11 22:47:07 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [8/200][0/27]	eta 0:00:55 lr 0.000002	time 2.0649 (2.0649)	loss 7.2640 (7.2640)	miou 0.1718	grad_norm 38.0564 (38.0564)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
