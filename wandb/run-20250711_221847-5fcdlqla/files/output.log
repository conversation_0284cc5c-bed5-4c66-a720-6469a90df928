=> merge config from config/base_train.yaml
RANK and WORLD_SIZE in environ: 0/3
[32m[2025-07-11 22:18:54 3DDETR.yaml][33m(main.py 549)[39m: INFO Full config saved to /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/config.json
[32m[2025-07-11 22:18:54 3DDETR.yaml][33m(main.py 552)[39m: INFO AMP_ENABLE: true
TAG: default
amp_opt_level: ''
base:
- ''
data:
  augment: false
  batch_size: 2
  cache_mode: part
  data_path: /home-local2/akath.extra.nobkp/dl_challenge
  dataset: Sereact_dataset
  debug: false
  num_workers: 4
  pin_memory: true
  transform: null
  zip_mode: false
eval_mode: false
local_rank: 0
loss:
  matcher_costs:
    cost_box_corners: 1.0
    giou: 5.0
    l1: 2.0
  weights:
    box_corners: 1.0
    giou: 1.0
    size: 1.0
    size_reg: 1.0
model:
  decoder:
    dim: 256
    dropout: 0.1
    ffn_dim: 256
    nhead: 4
    num_layers: 3
  encoder:
    activation: relu
    dim: 256
    dropout: 0.1
    ffn_dim: 128
    nheads: 4
    num_layers: 3
    preencoder_npoints: 2048
    type: vanilla
    use_color: false
  export_model: false
  mlp_dropout: 0.3
  name: 3DDETR.yaml
  num_angular_bins: 12
  num_queries: 256
  position_embedding: fourier
  pretrained: null
  pretrained_weights_path: /home/<USER>/Coding/Pre_trained_Weights/3detr/scannet_ep1080.pth
  resume: ''
  training: true
  unit_test: false
output: /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123
print_freq: 10
save_freq: 1
seed: 40
tag: '123'
train:
  accumulation_steps: 1
  auto_resume: false
  base_lr: 0.0001
  clip_grad: 0.1
  filter_biases_wd: true
  final_lr: 1.0e-06
  lr_scheduler: cosine
  max_epoch: 200
  start_epoch: 0
  unit_test_epoch: 100
  use_checkpoint: false
  warm_lr: 5.0e-06
  warm_lr_epochs: 9
  weight_decay: 0.01
unit_test: false
[32m[2025-07-11 22:18:54 3DDETR.yaml][33m(main.py 553)[39m: INFO {"cfg": "config/base_train.yaml", "opts": null, "batch_size": 2, "data_path": "/home-local2/akath.extra.nobkp/dl_challenge", "pretrained": null, "resume": null, "accumulation_steps": null, "use_checkpoint": false, "disable_amp": false, "amp_opt_level": null, "output": "/home-local2/akath.extra.nobkp/sereact", "tag": null, "eval": false, "unit_test": false, "base_lr": null, "local_rank": 0}
local rank 0 / global rank 0 successfully build train dataset
local rank 0 / global rank 0 successfully build val dataset
[32m[2025-07-11 22:18:54 3DDETR.yaml][33m(main.py 102)[39m: INFO Model3DDETR(
  (pre_encoder): PointnetSAModuleVotes(
    (grouper): QueryAndGroup()
    (mlp_module): SharedMLP(
      (layer0): Conv2d(
        (conv): Conv2d(3, 64, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn): BatchNorm2d(
          (bn): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        )
        (activation): ReLU(inplace=True)
      )
      (layer1): Conv2d(
        (conv): Conv2d(64, 128, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn): BatchNorm2d(
          (bn): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        )
        (activation): ReLU(inplace=True)
      )
      (layer2): Conv2d(
        (conv): Conv2d(128, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn): BatchNorm2d(
          (bn): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        )
        (activation): ReLU(inplace=True)
      )
    )
  )
  (encoder): TransformerEncoder(
    (layers): ModuleList(
      (0): TransformerEncoderLayer(
        attn_dr=0.1
        (self_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (linear1): Linear(in_features=256, out_features=128, bias=True)
        (dropout): Dropout(p=0.1, inplace=False)
        (linear2): Linear(in_features=128, out_features=256, bias=True)
        (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout2): Dropout(p=0.1, inplace=False)
        (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout1): Dropout(p=0.1, inplace=False)
        (activation): ReLU()
      )
      (1): TransformerEncoderLayer(
        attn_dr=0.1
        (self_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (linear1): Linear(in_features=256, out_features=128, bias=True)
        (dropout): Dropout(p=0.1, inplace=False)
        (linear2): Linear(in_features=128, out_features=256, bias=True)
        (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout2): Dropout(p=0.1, inplace=False)
        (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout1): Dropout(p=0.1, inplace=False)
        (activation): ReLU()
      )
      (2): TransformerEncoderLayer(
        attn_dr=0.1
        (self_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (linear1): Linear(in_features=256, out_features=128, bias=True)
        (dropout): Dropout(p=0.1, inplace=False)
        (linear2): Linear(in_features=128, out_features=256, bias=True)
        (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout2): Dropout(p=0.1, inplace=False)
        (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout1): Dropout(p=0.1, inplace=False)
        (activation): ReLU()
      )
    )
  )
  (encoder_decoder_projection): GenericMLP(
    (layers): Sequential(
      (0): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
      (1): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (2): ReLU()
      (3): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
      (4): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (5): ReLU()
    )
  )
  (positional_embedding): PositionEmbeddingCoordsSine(type=fourier, scale=6.283185307179586, normalize=True, gaussB_shape=torch.Size([3, 128]), gaussB_sum=-17.944507598876953)
  (query_projection): GenericMLP(
    (layers): Sequential(
      (0): Conv1d(256, 256, kernel_size=(1,), stride=(1,))
      (1): ReLU()
      (2): Conv1d(256, 256, kernel_size=(1,), stride=(1,))
      (3): ReLU()
    )
  )
  (decoder): TransformerDecoder(
    (layers): ModuleList(
      (0): TransformerDecoderLayer(
        (self_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (multihead_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm3): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout1): Dropout(p=0.1, inplace=False)
        (dropout2): Dropout(p=0.1, inplace=False)
        (dropout3): Dropout(p=0.1, inplace=False)
        (linear1): Linear(in_features=256, out_features=256, bias=True)
        (dropout): Dropout(p=0.1, inplace=False)
        (linear2): Linear(in_features=256, out_features=256, bias=True)
        (activation): ReLU()
      )
      (1): TransformerDecoderLayer(
        (self_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (multihead_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm3): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout1): Dropout(p=0.1, inplace=False)
        (dropout2): Dropout(p=0.1, inplace=False)
        (dropout3): Dropout(p=0.1, inplace=False)
        (linear1): Linear(in_features=256, out_features=256, bias=True)
        (dropout): Dropout(p=0.1, inplace=False)
        (linear2): Linear(in_features=256, out_features=256, bias=True)
        (activation): ReLU()
      )
      (2): TransformerDecoderLayer(
        (self_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (multihead_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm3): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout1): Dropout(p=0.1, inplace=False)
        (dropout2): Dropout(p=0.1, inplace=False)
        (dropout3): Dropout(p=0.1, inplace=False)
        (linear1): Linear(in_features=256, out_features=256, bias=True)
        (dropout): Dropout(p=0.1, inplace=False)
        (linear2): Linear(in_features=256, out_features=256, bias=True)
        (activation): ReLU()
      )
    )
    (norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (mlp_heads): ModuleDict(
    (center_head): GenericMLP(
      (layers): Sequential(
        (0): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
        (1): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU()
        (3): Dropout(p=0.3, inplace=False)
        (4): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
        (5): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (6): ReLU()
        (7): Dropout(p=0.3, inplace=False)
        (8): Conv1d(256, 3, kernel_size=(1,), stride=(1,))
      )
    )
    (size_head): GenericMLP(
      (layers): Sequential(
        (0): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
        (1): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU()
        (3): Dropout(p=0.3, inplace=False)
        (4): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
        (5): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (6): ReLU()
        (7): Dropout(p=0.3, inplace=False)
        (8): Conv1d(256, 3, kernel_size=(1,), stride=(1,))
      )
    )
    (angle_cls_head): GenericMLP(
      (layers): Sequential(
        (0): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
        (1): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU()
        (3): Dropout(p=0.3, inplace=False)
        (4): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
        (5): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (6): ReLU()
        (7): Dropout(p=0.3, inplace=False)
        (8): Conv1d(256, 12, kernel_size=(1,), stride=(1,))
      )
    )
    (angle_residual_head): GenericMLP(
      (layers): Sequential(
        (0): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
        (1): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU()
        (3): Dropout(p=0.3, inplace=False)
        (4): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
        (5): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (6): ReLU()
        (7): Dropout(p=0.3, inplace=False)
        (8): Conv1d(256, 12, kernel_size=(1,), stride=(1,))
      )
    )
  )
)
[32m[2025-07-11 22:18:54 3DDETR.yaml][33m(main.py 104)[39m: INFO number of params: 3811038
[32m[2025-07-11 22:18:54 3DDETR.yaml][33m(main.py 153)[39m: INFO Start training
[32m[2025-07-11 22:18:59 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [0/200][0/27]	eta 0:02:14 lr 0.000100	time 4.9894 (4.9894)	loss 38.3961 (38.3961)	miou 0.0295	grad_norm 184.7570 (184.7570)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 22:19:28 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [0/200][10/27]	eta 0:00:53 lr 0.000099	time 3.0850 (3.1282)	loss 14.2551 (21.7018)	miou 0.1707	grad_norm 55.3244 (118.7287)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 22:19:55 3DDETR.yaml][33m(main.py 279)[39m: INFO Train: [0/200][20/27]	eta 0:00:20 lr 0.000097	time 2.6262 (2.9211)	loss 12.2538 (20.2328)	miou 0.1596	grad_norm 49.5610 (93.5207)	loss_scale 65536.0000 (65536.0000)	mem 4188MB
[32m[2025-07-11 22:20:10 3DDETR.yaml][33m(main.py 299)[39m: INFO EPOCH 0 training takes 0:01:16
[32m[2025-07-11 22:20:10 3DDETR.yaml][33m(main.py 320)[39m: INFO Starting validation...
Traceback (most recent call last):
  File "main.py", line 555, in <module>
    main(config)
  File "main.py", line 161, in main
    miou, loss = validate(config, loss_module, epoch, iou_evaluator, data_loader_val, model)
  File "/gel/usr/akath/.conda/envs/swin/lib/python3.7/site-packages/torch/autograd/grad_mode.py", line 27, in decorate_context
    return func(*args, **kwargs)
  File "main.py", line 321, in validate
    for _, batch_data in enumerate(data_loader):
  File "/gel/usr/akath/.conda/envs/swin/lib/python3.7/site-packages/torch/utils/data/dataloader.py", line 517, in __next__
    data = self._next_data()
  File "/gel/usr/akath/.conda/envs/swin/lib/python3.7/site-packages/torch/utils/data/dataloader.py", line 1199, in _next_data
    return self._process_data(data)
  File "/gel/usr/akath/.conda/envs/swin/lib/python3.7/site-packages/torch/utils/data/dataloader.py", line 1225, in _process_data
    data.reraise()
  File "/gel/usr/akath/.conda/envs/swin/lib/python3.7/site-packages/torch/_utils.py", line 429, in reraise
    raise self.exc_type(msg)
AssertionError: Caught AssertionError in DataLoader worker process 0.
Original Traceback (most recent call last):
  File "/gel/usr/akath/.conda/envs/swin/lib/python3.7/site-packages/torch/utils/data/_utils/worker.py", line 202, in _worker_loop
    data = fetcher.fetch(index)
  File "/gel/usr/akath/.conda/envs/swin/lib/python3.7/site-packages/torch/utils/data/_utils/fetch.py", line 44, in fetch
    data = [self.dataset[idx] for idx in possibly_batched_index]
  File "/gel/usr/akath/.conda/envs/swin/lib/python3.7/site-packages/torch/utils/data/_utils/fetch.py", line 44, in <listcomp>
    data = [self.dataset[idx] for idx in possibly_batched_index]
  File "/home-local/akath.nobkp/codingchallenge_sereact/dataloader/sereact_data_loader.py", line 119, in __getitem__
    pcd_normalized, centroid, max_dist = self.normalize_pointcloud(pcd) #(368836, 3)
  File "/home-local/akath.nobkp/codingchallenge_sereact/dataloader/sereact_data_loader.py", line 58, in normalize_pointcloud
    assert points.ndim == 2 and points.shape[1] == 3, f"Expected shape (N, 3), got {points.shape}"
AssertionError: Expected shape (N, 3), got (3, 483, 651)