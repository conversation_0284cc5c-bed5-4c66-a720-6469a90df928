2025-07-11 22:18:47,822 INFO    StreamThr :2831848 [internal.py:wandb_internal():89] W&B internal server running at pid: 2831848, started at: 2025-07-11 22:18:47.821807
2025-07-11 22:18:47,824 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: status
2025-07-11 22:18:47,835 INFO    WriterThread:2831848 [datastore.py:open_for_write():85] open: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-5fcdlqla/run-5fcdlqla.wandb
2025-07-11 22:18:47,835 DEBUG   SenderThread:2831848 [sender.py:send():369] send: header
2025-07-11 22:18:47,852 DEBUG   SenderThread:2831848 [sender.py:send():369] send: run
2025-07-11 22:18:48,040 ERROR   SenderThread:2831848 [internal_api.py:execute():323] 500 response executing GraphQL.
2025-07-11 22:18:48,040 ERROR   SenderThread:2831848 [internal_api.py:execute():324] {"errors":[{"message":"An internal error occurred. Please contact support.","path":["upsertBucket"]}],"data":{"upsertBucket":null}}
2025-07-11 22:18:49,288 INFO    SenderThread:2831848 [dir_watcher.py:__init__():211] watching files in: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-5fcdlqla/files
2025-07-11 22:18:49,288 INFO    SenderThread:2831848 [sender.py:_start_run_threads():1103] run started: 5fcdlqla with start time 1752286727.822319
2025-07-11 22:18:49,289 DEBUG   SenderThread:2831848 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:18:49,290 INFO    SenderThread:2831848 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:18:49,298 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: check_version
2025-07-11 22:18:49,298 DEBUG   SenderThread:2831848 [sender.py:send_request():396] send_request: check_version
2025-07-11 22:18:49,371 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: run_start
2025-07-11 22:18:49,376 DEBUG   HandlerThread:2831848 [system_info.py:__init__():31] System info init
2025-07-11 22:18:49,376 DEBUG   HandlerThread:2831848 [system_info.py:__init__():46] System info init done
2025-07-11 22:18:49,376 INFO    HandlerThread:2831848 [system_monitor.py:start():181] Starting system monitor
2025-07-11 22:18:49,376 INFO    SystemMonitor:2831848 [system_monitor.py:_start():145] Starting system asset monitoring threads
2025-07-11 22:18:49,376 INFO    HandlerThread:2831848 [system_monitor.py:probe():201] Collecting system info
2025-07-11 22:18:49,377 INFO    SystemMonitor:2831848 [interfaces.py:start():190] Started cpu monitoring
2025-07-11 22:18:49,378 INFO    SystemMonitor:2831848 [interfaces.py:start():190] Started disk monitoring
2025-07-11 22:18:49,379 INFO    SystemMonitor:2831848 [interfaces.py:start():190] Started gpu monitoring
2025-07-11 22:18:49,379 INFO    SystemMonitor:2831848 [interfaces.py:start():190] Started memory monitoring
2025-07-11 22:18:49,380 INFO    SystemMonitor:2831848 [interfaces.py:start():190] Started network monitoring
2025-07-11 22:18:49,397 DEBUG   HandlerThread:2831848 [system_info.py:probe():195] Probing system
2025-07-11 22:18:49,404 DEBUG   HandlerThread:2831848 [system_info.py:_probe_git():180] Probing git
2025-07-11 22:18:49,420 DEBUG   HandlerThread:2831848 [system_info.py:_probe_git():188] Probing git done
2025-07-11 22:18:49,420 DEBUG   HandlerThread:2831848 [system_info.py:probe():240] Probing system done
2025-07-11 22:18:49,420 DEBUG   HandlerThread:2831848 [system_monitor.py:probe():210] {'os': 'Linux-5.4.0-77-generic-x86_64-with-debian-bullseye-sid', 'python': '3.7.16', 'heartbeatAt': '2025-07-12T02:18:49.397419', 'startedAt': '2025-07-12T02:18:47.806239', 'docker': None, 'cuda': None, 'args': ('--local_rank=0', '--cfg', 'config/base_train.yaml', '--output', '/home-local2/akath.extra.nobkp/sereact', '--data-path', '/home-local2/akath.extra.nobkp/dl_challenge', '--batch-size', '2'), 'state': 'running', 'program': 'main.py', 'codePath': 'main.py', 'git': {'remote': 'https://github.com/Shrinidhibhat87/codingchallenge_sereact.git', 'commit': 'a3ff60e13dfced588b500c8a1de00f36fdf22d49'}, 'email': '<EMAIL>', 'root': '/home-local/akath.nobkp/codingchallenge_sereact', 'host': 'lv3-32190', 'username': 'akath', 'executable': '/gel/usr/akath/.conda/envs/swin/bin/python', 'cpu_count': 6, 'cpu_count_logical': 12, 'cpu_freq': {'current': 1.9152500000000003, 'min': 1200.0, 'max': 4000.0}, 'cpu_freq_per_core': [{'current': 1.466, 'min': 1200.0, 'max': 4000.0}, {'current': 1.286, 'min': 1200.0, 'max': 4000.0}, {'current': 1.851, 'min': 1200.0, 'max': 4000.0}, {'current': 1.512, 'min': 1200.0, 'max': 4000.0}, {'current': 2.242, 'min': 1200.0, 'max': 4000.0}, {'current': 3.531, 'min': 1200.0, 'max': 4000.0}, {'current': 1.822, 'min': 1200.0, 'max': 4000.0}, {'current': 1.291, 'min': 1200.0, 'max': 4000.0}, {'current': 1.317, 'min': 1200.0, 'max': 4000.0}, {'current': 1.289, 'min': 1200.0, 'max': 4000.0}, {'current': 1.946, 'min': 1200.0, 'max': 4000.0}, {'current': 3.43, 'min': 1200.0, 'max': 4000.0}], 'disk': {'total': 111.2200813293457, 'used': 53.229248046875}, 'gpu': 'TITAN Xp', 'gpu_count': 3, 'gpu_devices': [{'name': 'TITAN Xp', 'memory_total': 12787122176}, {'name': 'TITAN Xp', 'memory_total': 12788498432}, {'name': 'TITAN Xp', 'memory_total': 12788498432}], 'memory': {'total': 62.72977066040039}}
2025-07-11 22:18:49,420 INFO    HandlerThread:2831848 [system_monitor.py:probe():211] Finished collecting system info
2025-07-11 22:18:49,420 INFO    HandlerThread:2831848 [system_monitor.py:probe():214] Publishing system info
2025-07-11 22:18:49,420 DEBUG   HandlerThread:2831848 [system_info.py:_save_pip():52] Saving list of pip packages installed into the current environment
2025-07-11 22:18:49,421 DEBUG   HandlerThread:2831848 [system_info.py:_save_pip():67] Saving pip packages done
2025-07-11 22:18:49,421 DEBUG   HandlerThread:2831848 [system_info.py:_save_conda():75] Saving list of conda packages installed into the current environment
2025-07-11 22:18:50,291 INFO    Thread-12 :2831848 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-5fcdlqla/files/conda-environment.yaml
2025-07-11 22:18:50,292 INFO    Thread-12 :2831848 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-5fcdlqla/files/requirements.txt
2025-07-11 22:18:50,292 INFO    Thread-12 :2831848 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-5fcdlqla/files/wandb-summary.json
2025-07-11 22:18:51,877 DEBUG   HandlerThread:2831848 [system_info.py:_save_conda():86] Saving conda packages done
2025-07-11 22:18:51,878 INFO    HandlerThread:2831848 [system_monitor.py:probe():216] Finished publishing system info
2025-07-11 22:18:51,890 DEBUG   SenderThread:2831848 [sender.py:send():369] send: files
2025-07-11 22:18:51,890 INFO    SenderThread:2831848 [sender.py:_save_file():1354] saving file wandb-metadata.json with policy now
2025-07-11 22:18:51,900 DEBUG   SenderThread:2831848 [sender.py:send():369] send: telemetry
2025-07-11 22:18:51,900 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:18:51,901 DEBUG   SenderThread:2831848 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:18:52,261 INFO    wandb-upload_0:2831848 [upload_job.py:push():133] Uploaded file /tmp/tmplxpkzl7owandb/xsrnnl1y-wandb-metadata.json
2025-07-11 22:18:52,290 INFO    Thread-12 :2831848 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-5fcdlqla/files/conda-environment.yaml
2025-07-11 22:18:52,291 INFO    Thread-12 :2831848 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-5fcdlqla/files/wandb-metadata.json
2025-07-11 22:18:52,291 INFO    Thread-12 :2831848 [dir_watcher.py:_on_file_created():272] file/dir created: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-5fcdlqla/files/output.log
2025-07-11 22:18:53,039 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:18:54,291 INFO    Thread-12 :2831848 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-5fcdlqla/files/output.log
2025-07-11 22:18:56,292 INFO    Thread-12 :2831848 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-5fcdlqla/files/output.log
2025-07-11 22:18:58,299 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:18:59,264 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:18:59,266 DEBUG   SenderThread:2831848 [sender.py:send():369] send: history
2025-07-11 22:18:59,267 DEBUG   SenderThread:2831848 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:18:59,269 INFO    SenderThread:2831848 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:18:59,293 INFO    Thread-12 :2831848 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-5fcdlqla/files/wandb-summary.json
2025-07-11 22:19:00,293 INFO    Thread-12 :2831848 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-5fcdlqla/files/output.log
2025-07-11 22:19:02,337 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:19:02,338 DEBUG   SenderThread:2831848 [sender.py:send():369] send: history
2025-07-11 22:19:02,338 DEBUG   SenderThread:2831848 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:19:02,338 INFO    SenderThread:2831848 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:19:03,294 INFO    Thread-12 :2831848 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-5fcdlqla/files/wandb-summary.json
2025-07-11 22:19:03,339 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:19:05,276 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:19:05,278 DEBUG   SenderThread:2831848 [sender.py:send():369] send: history
2025-07-11 22:19:05,278 DEBUG   SenderThread:2831848 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:19:05,278 INFO    SenderThread:2831848 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:19:05,295 INFO    Thread-12 :2831848 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-5fcdlqla/files/wandb-summary.json
2025-07-11 22:19:06,899 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:19:06,900 DEBUG   SenderThread:2831848 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:19:08,029 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:19:08,030 DEBUG   SenderThread:2831848 [sender.py:send():369] send: history
2025-07-11 22:19:08,031 DEBUG   SenderThread:2831848 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:19:08,031 INFO    SenderThread:2831848 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:19:08,295 INFO    Thread-12 :2831848 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-5fcdlqla/files/wandb-summary.json
2025-07-11 22:19:09,032 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:19:10,697 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:19:10,698 DEBUG   SenderThread:2831848 [sender.py:send():369] send: history
2025-07-11 22:19:10,698 DEBUG   SenderThread:2831848 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:19:10,699 INFO    SenderThread:2831848 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:19:11,297 INFO    Thread-12 :2831848 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-5fcdlqla/files/wandb-summary.json
2025-07-11 22:19:14,070 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:19:14,070 DEBUG   SenderThread:2831848 [sender.py:send():369] send: history
2025-07-11 22:19:14,071 DEBUG   SenderThread:2831848 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:19:14,071 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:19:14,072 INFO    SenderThread:2831848 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:19:14,297 INFO    Thread-12 :2831848 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-5fcdlqla/files/wandb-summary.json
2025-07-11 22:19:16,614 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:19:16,615 DEBUG   SenderThread:2831848 [sender.py:send():369] send: history
2025-07-11 22:19:16,615 DEBUG   SenderThread:2831848 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:19:16,615 INFO    SenderThread:2831848 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:19:17,299 INFO    Thread-12 :2831848 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-5fcdlqla/files/wandb-summary.json
2025-07-11 22:19:19,273 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:19:19,274 DEBUG   SenderThread:2831848 [sender.py:send():369] send: history
2025-07-11 22:19:19,281 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:19:19,365 DEBUG   SenderThread:2831848 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:19:19,365 INFO    SenderThread:2831848 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:19:20,300 INFO    Thread-12 :2831848 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-5fcdlqla/files/config.yaml
2025-07-11 22:19:20,300 INFO    Thread-12 :2831848 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-5fcdlqla/files/wandb-summary.json
2025-07-11 22:19:21,900 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:19:21,900 DEBUG   SenderThread:2831848 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:19:22,626 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:19:22,627 DEBUG   SenderThread:2831848 [sender.py:send():369] send: history
2025-07-11 22:19:22,627 DEBUG   SenderThread:2831848 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:19:22,627 INFO    SenderThread:2831848 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:19:23,301 INFO    Thread-12 :2831848 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-5fcdlqla/files/wandb-summary.json
2025-07-11 22:19:24,630 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:19:25,599 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:19:25,600 DEBUG   SenderThread:2831848 [sender.py:send():369] send: history
2025-07-11 22:19:25,600 DEBUG   SenderThread:2831848 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:19:25,601 INFO    SenderThread:2831848 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:19:26,302 INFO    Thread-12 :2831848 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-5fcdlqla/files/wandb-summary.json
2025-07-11 22:19:28,684 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:19:28,685 DEBUG   SenderThread:2831848 [sender.py:send():369] send: history
2025-07-11 22:19:28,686 DEBUG   SenderThread:2831848 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:19:28,686 INFO    SenderThread:2831848 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:19:29,303 INFO    Thread-12 :2831848 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-5fcdlqla/files/wandb-summary.json
2025-07-11 22:19:29,687 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:19:30,303 INFO    Thread-12 :2831848 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-5fcdlqla/files/output.log
2025-07-11 22:19:31,068 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:19:31,069 DEBUG   SenderThread:2831848 [sender.py:send():369] send: history
2025-07-11 22:19:31,070 DEBUG   SenderThread:2831848 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:19:31,070 INFO    SenderThread:2831848 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:19:31,304 INFO    Thread-12 :2831848 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-5fcdlqla/files/wandb-summary.json
2025-07-11 22:19:33,350 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:19:33,351 DEBUG   SenderThread:2831848 [sender.py:send():369] send: history
2025-07-11 22:19:33,351 DEBUG   SenderThread:2831848 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:19:33,352 INFO    SenderThread:2831848 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:19:34,305 INFO    Thread-12 :2831848 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-5fcdlqla/files/wandb-summary.json
2025-07-11 22:19:35,353 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:19:36,280 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:19:36,280 DEBUG   SenderThread:2831848 [sender.py:send():369] send: history
2025-07-11 22:19:36,281 DEBUG   SenderThread:2831848 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:19:36,281 INFO    SenderThread:2831848 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:19:36,306 INFO    Thread-12 :2831848 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-5fcdlqla/files/wandb-summary.json
2025-07-11 22:19:36,900 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:19:36,901 DEBUG   SenderThread:2831848 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:19:39,809 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:19:39,810 DEBUG   SenderThread:2831848 [sender.py:send():369] send: history
2025-07-11 22:19:39,810 DEBUG   SenderThread:2831848 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:19:39,811 INFO    SenderThread:2831848 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:19:40,307 INFO    Thread-12 :2831848 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-5fcdlqla/files/wandb-summary.json
2025-07-11 22:19:40,811 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:19:42,592 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:19:42,593 DEBUG   SenderThread:2831848 [sender.py:send():369] send: history
2025-07-11 22:19:42,593 DEBUG   SenderThread:2831848 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:19:42,594 INFO    SenderThread:2831848 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:19:43,308 INFO    Thread-12 :2831848 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-5fcdlqla/files/wandb-summary.json
2025-07-11 22:19:45,171 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:19:45,173 DEBUG   SenderThread:2831848 [sender.py:send():369] send: history
2025-07-11 22:19:45,173 DEBUG   SenderThread:2831848 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:19:45,174 INFO    SenderThread:2831848 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:19:45,309 INFO    Thread-12 :2831848 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-5fcdlqla/files/wandb-summary.json
2025-07-11 22:19:46,174 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:19:47,841 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:19:47,843 DEBUG   SenderThread:2831848 [sender.py:send():369] send: history
2025-07-11 22:19:47,843 DEBUG   SenderThread:2831848 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:19:47,844 INFO    SenderThread:2831848 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:19:48,310 INFO    Thread-12 :2831848 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-5fcdlqla/files/wandb-summary.json
2025-07-11 22:19:49,381 DEBUG   SystemMonitor:2831848 [system_monitor.py:_start():159] Starting system metrics aggregation loop
2025-07-11 22:19:49,383 DEBUG   SenderThread:2831848 [sender.py:send():369] send: stats
2025-07-11 22:19:50,233 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:19:50,234 DEBUG   SenderThread:2831848 [sender.py:send():369] send: history
2025-07-11 22:19:50,234 DEBUG   SenderThread:2831848 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:19:50,234 INFO    SenderThread:2831848 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:19:50,310 INFO    Thread-12 :2831848 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-5fcdlqla/files/wandb-summary.json
2025-07-11 22:19:51,235 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:19:51,900 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:19:51,900 DEBUG   SenderThread:2831848 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:19:52,991 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:19:52,992 DEBUG   SenderThread:2831848 [sender.py:send():369] send: history
2025-07-11 22:19:52,992 DEBUG   SenderThread:2831848 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:19:52,993 INFO    SenderThread:2831848 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:19:53,311 INFO    Thread-12 :2831848 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-5fcdlqla/files/wandb-summary.json
2025-07-11 22:19:55,618 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:19:55,619 DEBUG   SenderThread:2831848 [sender.py:send():369] send: history
2025-07-11 22:19:55,620 DEBUG   SenderThread:2831848 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:19:55,620 INFO    SenderThread:2831848 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:19:56,312 INFO    Thread-12 :2831848 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-5fcdlqla/files/wandb-summary.json
2025-07-11 22:19:56,313 INFO    Thread-12 :2831848 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-5fcdlqla/files/output.log
2025-07-11 22:19:56,621 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:19:58,773 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:19:58,774 DEBUG   SenderThread:2831848 [sender.py:send():369] send: history
2025-07-11 22:19:58,774 DEBUG   SenderThread:2831848 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:19:58,776 INFO    SenderThread:2831848 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:19:59,314 INFO    Thread-12 :2831848 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-5fcdlqla/files/wandb-summary.json
2025-07-11 22:20:00,488 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:20:00,489 DEBUG   SenderThread:2831848 [sender.py:send():369] send: history
2025-07-11 22:20:00,490 DEBUG   SenderThread:2831848 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:20:00,490 INFO    SenderThread:2831848 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:20:01,314 INFO    Thread-12 :2831848 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-5fcdlqla/files/wandb-summary.json
2025-07-11 22:20:02,283 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:20:02,283 DEBUG   SenderThread:2831848 [sender.py:send():369] send: history
2025-07-11 22:20:02,284 DEBUG   SenderThread:2831848 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:20:02,284 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:20:02,284 INFO    SenderThread:2831848 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:20:02,315 INFO    Thread-12 :2831848 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-5fcdlqla/files/wandb-summary.json
2025-07-11 22:20:04,578 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:20:04,578 DEBUG   SenderThread:2831848 [sender.py:send():369] send: history
2025-07-11 22:20:04,579 DEBUG   SenderThread:2831848 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:20:04,579 INFO    SenderThread:2831848 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:20:05,316 INFO    Thread-12 :2831848 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-5fcdlqla/files/wandb-summary.json
2025-07-11 22:20:06,900 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: stop_status
2025-07-11 22:20:06,901 DEBUG   SenderThread:2831848 [sender.py:send_request():396] send_request: stop_status
2025-07-11 22:20:07,476 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:20:07,477 DEBUG   SenderThread:2831848 [sender.py:send():369] send: history
2025-07-11 22:20:07,478 DEBUG   SenderThread:2831848 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:20:07,478 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:20:07,479 INFO    SenderThread:2831848 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:20:08,317 INFO    Thread-12 :2831848 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-5fcdlqla/files/wandb-summary.json
2025-07-11 22:20:10,693 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: partial_history
2025-07-11 22:20:10,694 DEBUG   SenderThread:2831848 [sender.py:send():369] send: history
2025-07-11 22:20:10,695 DEBUG   SenderThread:2831848 [sender.py:send_request():396] send_request: summary_record
2025-07-11 22:20:10,695 INFO    SenderThread:2831848 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:20:10,916 DEBUG   SenderThread:2831848 [sender.py:send():369] send: exit
2025-07-11 22:20:10,916 INFO    SenderThread:2831848 [sender.py:send_exit():574] handling exit code: 1
2025-07-11 22:20:10,916 INFO    SenderThread:2831848 [sender.py:send_exit():576] handling runtime: 81
2025-07-11 22:20:10,917 INFO    SenderThread:2831848 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:20:10,917 INFO    SenderThread:2831848 [sender.py:send_exit():582] send defer
2025-07-11 22:20:10,918 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: defer
2025-07-11 22:20:10,918 INFO    HandlerThread:2831848 [handler.py:handle_request_defer():170] handle defer: 0
2025-07-11 22:20:10,918 DEBUG   SenderThread:2831848 [sender.py:send_request():396] send_request: defer
2025-07-11 22:20:10,918 INFO    SenderThread:2831848 [sender.py:send_request_defer():598] handle sender defer: 0
2025-07-11 22:20:10,919 INFO    SenderThread:2831848 [sender.py:transition_state():602] send defer: 1
2025-07-11 22:20:10,919 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: defer
2025-07-11 22:20:10,919 INFO    HandlerThread:2831848 [handler.py:handle_request_defer():170] handle defer: 1
2025-07-11 22:20:10,919 DEBUG   SenderThread:2831848 [sender.py:send_request():396] send_request: defer
2025-07-11 22:20:10,919 INFO    SenderThread:2831848 [sender.py:send_request_defer():598] handle sender defer: 1
2025-07-11 22:20:10,919 INFO    SenderThread:2831848 [sender.py:transition_state():602] send defer: 2
2025-07-11 22:20:10,919 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: defer
2025-07-11 22:20:10,919 INFO    HandlerThread:2831848 [handler.py:handle_request_defer():170] handle defer: 2
2025-07-11 22:20:10,919 INFO    HandlerThread:2831848 [system_monitor.py:finish():190] Stopping system monitor
2025-07-11 22:20:10,919 DEBUG   SystemMonitor:2831848 [system_monitor.py:_start():166] Finished system metrics aggregation loop
2025-07-11 22:20:10,920 DEBUG   SystemMonitor:2831848 [system_monitor.py:_start():170] Publishing last batch of metrics
2025-07-11 22:20:10,920 INFO    HandlerThread:2831848 [interfaces.py:finish():202] Joined cpu monitor
2025-07-11 22:20:10,921 INFO    HandlerThread:2831848 [interfaces.py:finish():202] Joined disk monitor
2025-07-11 22:20:11,057 INFO    HandlerThread:2831848 [interfaces.py:finish():202] Joined gpu monitor
2025-07-11 22:20:11,058 INFO    HandlerThread:2831848 [interfaces.py:finish():202] Joined memory monitor
2025-07-11 22:20:11,058 INFO    HandlerThread:2831848 [interfaces.py:finish():202] Joined network monitor
2025-07-11 22:20:11,059 DEBUG   SenderThread:2831848 [sender.py:send_request():396] send_request: defer
2025-07-11 22:20:11,059 INFO    SenderThread:2831848 [sender.py:send_request_defer():598] handle sender defer: 2
2025-07-11 22:20:11,059 INFO    SenderThread:2831848 [sender.py:transition_state():602] send defer: 3
2025-07-11 22:20:11,059 DEBUG   SenderThread:2831848 [sender.py:send():369] send: stats
2025-07-11 22:20:11,060 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: defer
2025-07-11 22:20:11,060 INFO    HandlerThread:2831848 [handler.py:handle_request_defer():170] handle defer: 3
2025-07-11 22:20:11,061 DEBUG   SenderThread:2831848 [sender.py:send_request():396] send_request: defer
2025-07-11 22:20:11,061 INFO    SenderThread:2831848 [sender.py:send_request_defer():598] handle sender defer: 3
2025-07-11 22:20:11,061 INFO    SenderThread:2831848 [sender.py:transition_state():602] send defer: 4
2025-07-11 22:20:11,061 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: defer
2025-07-11 22:20:11,061 INFO    HandlerThread:2831848 [handler.py:handle_request_defer():170] handle defer: 4
2025-07-11 22:20:11,062 DEBUG   SenderThread:2831848 [sender.py:send_request():396] send_request: defer
2025-07-11 22:20:11,062 INFO    SenderThread:2831848 [sender.py:send_request_defer():598] handle sender defer: 4
2025-07-11 22:20:11,062 INFO    SenderThread:2831848 [sender.py:transition_state():602] send defer: 5
2025-07-11 22:20:11,062 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: defer
2025-07-11 22:20:11,062 INFO    HandlerThread:2831848 [handler.py:handle_request_defer():170] handle defer: 5
2025-07-11 22:20:11,063 DEBUG   SenderThread:2831848 [sender.py:send():369] send: summary
2025-07-11 22:20:11,063 INFO    SenderThread:2831848 [sender.py:_save_file():1354] saving file wandb-summary.json with policy end
2025-07-11 22:20:11,064 DEBUG   SenderThread:2831848 [sender.py:send_request():396] send_request: defer
2025-07-11 22:20:11,064 INFO    SenderThread:2831848 [sender.py:send_request_defer():598] handle sender defer: 5
2025-07-11 22:20:11,064 INFO    SenderThread:2831848 [sender.py:transition_state():602] send defer: 6
2025-07-11 22:20:11,064 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: defer
2025-07-11 22:20:11,064 INFO    HandlerThread:2831848 [handler.py:handle_request_defer():170] handle defer: 6
2025-07-11 22:20:11,065 DEBUG   SenderThread:2831848 [sender.py:send_request():396] send_request: defer
2025-07-11 22:20:11,065 INFO    SenderThread:2831848 [sender.py:send_request_defer():598] handle sender defer: 6
2025-07-11 22:20:11,065 INFO    SenderThread:2831848 [sender.py:transition_state():602] send defer: 7
2025-07-11 22:20:11,065 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: status_report
2025-07-11 22:20:11,065 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: defer
2025-07-11 22:20:11,065 INFO    HandlerThread:2831848 [handler.py:handle_request_defer():170] handle defer: 7
2025-07-11 22:20:11,066 DEBUG   SenderThread:2831848 [sender.py:send_request():396] send_request: defer
2025-07-11 22:20:11,066 INFO    SenderThread:2831848 [sender.py:send_request_defer():598] handle sender defer: 7
2025-07-11 22:20:11,318 INFO    Thread-12 :2831848 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-5fcdlqla/files/wandb-summary.json
2025-07-11 22:20:11,917 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: poll_exit
2025-07-11 22:20:12,318 INFO    Thread-12 :2831848 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-5fcdlqla/files/output.log
2025-07-11 22:20:12,924 INFO    SenderThread:2831848 [sender.py:transition_state():602] send defer: 8
2025-07-11 22:20:12,924 DEBUG   SenderThread:2831848 [sender.py:send_request():396] send_request: poll_exit
2025-07-11 22:20:12,924 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: defer
2025-07-11 22:20:12,925 INFO    HandlerThread:2831848 [handler.py:handle_request_defer():170] handle defer: 8
2025-07-11 22:20:12,925 DEBUG   SenderThread:2831848 [sender.py:send_request():396] send_request: defer
2025-07-11 22:20:12,926 INFO    SenderThread:2831848 [sender.py:send_request_defer():598] handle sender defer: 8
2025-07-11 22:20:12,926 INFO    SenderThread:2831848 [job_builder.py:build():232] Attempting to build job artifact
2025-07-11 22:20:12,926 INFO    SenderThread:2831848 [job_builder.py:build():256] is repo sourced job
2025-07-11 22:20:12,928 INFO    SenderThread:2831848 [job_builder.py:build():297] adding wandb-job metadata file
2025-07-11 22:20:12,932 INFO    SenderThread:2831848 [sender.py:transition_state():602] send defer: 9
2025-07-11 22:20:12,932 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: defer
2025-07-11 22:20:12,932 DEBUG   SenderThread:2831848 [sender.py:send():369] send: artifact
2025-07-11 22:20:12,932 INFO    HandlerThread:2831848 [handler.py:handle_request_defer():170] handle defer: 9
2025-07-11 22:20:13,319 INFO    Thread-12 :2831848 [dir_watcher.py:_on_file_modified():289] file/dir modified: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-5fcdlqla/files/output.log
2025-07-11 22:20:13,578 INFO    wandb-upload_0:2831848 [upload_job.py:push():91] Uploaded file /gel/usr/akath/.local/share/wandb/artifacts/staging/tmpaoh3av33
2025-07-11 22:20:13,736 INFO    wandb-upload_1:2831848 [upload_job.py:push():91] Uploaded file /gel/usr/akath/.local/share/wandb/artifacts/staging/tmp98qr0z8a
2025-07-11 22:20:14,497 INFO    SenderThread:2831848 [sender.py:send_artifact():1450] sent artifact job-https___github.com_Shrinidhibhat87_codingchallenge_sereact.git_main.py - {'id': 'QXJ0aWZhY3Q6MTg3NDAzMjEwMg==', 'digest': '4f4ba9767a27b00621d65a0eba015245', 'state': 'PENDING', 'aliases': [], 'artifactSequence': {'id': 'QXJ0aWZhY3RDb2xsZWN0aW9uOjY4NDgwMzQ0Ng==', 'latestArtifact': None}, 'version': 'latest'}
2025-07-11 22:20:14,497 DEBUG   SenderThread:2831848 [sender.py:send_request():396] send_request: defer
2025-07-11 22:20:14,497 INFO    SenderThread:2831848 [sender.py:send_request_defer():598] handle sender defer: 9
2025-07-11 22:20:14,497 INFO    SenderThread:2831848 [dir_watcher.py:finish():359] shutting down directory watcher
2025-07-11 22:20:15,320 INFO    SenderThread:2831848 [dir_watcher.py:finish():389] scan: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-5fcdlqla/files
2025-07-11 22:20:15,320 INFO    SenderThread:2831848 [dir_watcher.py:finish():403] scan save: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-5fcdlqla/files/config.yaml config.yaml
2025-07-11 22:20:15,320 INFO    SenderThread:2831848 [dir_watcher.py:finish():403] scan save: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-5fcdlqla/files/wandb-summary.json wandb-summary.json
2025-07-11 22:20:15,321 INFO    SenderThread:2831848 [dir_watcher.py:finish():403] scan save: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-5fcdlqla/files/requirements.txt requirements.txt
2025-07-11 22:20:15,325 INFO    SenderThread:2831848 [dir_watcher.py:finish():403] scan save: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-5fcdlqla/files/conda-environment.yaml conda-environment.yaml
2025-07-11 22:20:15,326 INFO    SenderThread:2831848 [dir_watcher.py:finish():403] scan save: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-5fcdlqla/files/wandb-metadata.json wandb-metadata.json
2025-07-11 22:20:15,327 INFO    SenderThread:2831848 [dir_watcher.py:finish():403] scan save: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-5fcdlqla/files/output.log output.log
2025-07-11 22:20:15,329 INFO    SenderThread:2831848 [sender.py:transition_state():602] send defer: 10
2025-07-11 22:20:15,332 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: defer
2025-07-11 22:20:15,332 INFO    HandlerThread:2831848 [handler.py:handle_request_defer():170] handle defer: 10
2025-07-11 22:20:15,342 DEBUG   SenderThread:2831848 [sender.py:send_request():396] send_request: defer
2025-07-11 22:20:15,342 INFO    SenderThread:2831848 [sender.py:send_request_defer():598] handle sender defer: 10
2025-07-11 22:20:15,342 INFO    SenderThread:2831848 [file_pusher.py:finish():159] shutting down file pusher
2025-07-11 22:20:15,568 INFO    wandb-upload_3:2831848 [upload_job.py:push():133] Uploaded file /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-5fcdlqla/files/config.yaml
2025-07-11 22:20:15,638 INFO    wandb-upload_2:2831848 [upload_job.py:push():133] Uploaded file /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-5fcdlqla/files/requirements.txt
2025-07-11 22:20:15,672 INFO    wandb-upload_4:2831848 [upload_job.py:push():133] Uploaded file /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-5fcdlqla/files/output.log
2025-07-11 22:20:15,707 INFO    wandb-upload_0:2831848 [upload_job.py:push():133] Uploaded file /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-5fcdlqla/files/wandb-summary.json
2025-07-11 22:20:15,715 INFO    wandb-upload_1:2831848 [upload_job.py:push():133] Uploaded file /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-5fcdlqla/files/conda-environment.yaml
2025-07-11 22:20:15,915 INFO    Thread-11 :2831848 [sender.py:transition_state():602] send defer: 11
2025-07-11 22:20:15,916 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: defer
2025-07-11 22:20:15,916 INFO    HandlerThread:2831848 [handler.py:handle_request_defer():170] handle defer: 11
2025-07-11 22:20:15,917 DEBUG   SenderThread:2831848 [sender.py:send_request():396] send_request: defer
2025-07-11 22:20:15,917 INFO    SenderThread:2831848 [sender.py:send_request_defer():598] handle sender defer: 11
2025-07-11 22:20:15,917 INFO    SenderThread:2831848 [file_pusher.py:join():164] waiting for file pusher
2025-07-11 22:20:15,917 INFO    SenderThread:2831848 [sender.py:transition_state():602] send defer: 12
2025-07-11 22:20:15,917 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: defer
2025-07-11 22:20:15,918 INFO    HandlerThread:2831848 [handler.py:handle_request_defer():170] handle defer: 12
2025-07-11 22:20:15,918 DEBUG   SenderThread:2831848 [sender.py:send_request():396] send_request: defer
2025-07-11 22:20:15,918 INFO    SenderThread:2831848 [sender.py:send_request_defer():598] handle sender defer: 12
2025-07-11 22:20:16,212 INFO    SenderThread:2831848 [sender.py:transition_state():602] send defer: 13
2025-07-11 22:20:16,213 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: defer
2025-07-11 22:20:16,213 INFO    HandlerThread:2831848 [handler.py:handle_request_defer():170] handle defer: 13
2025-07-11 22:20:16,214 DEBUG   SenderThread:2831848 [sender.py:send_request():396] send_request: defer
2025-07-11 22:20:16,214 INFO    SenderThread:2831848 [sender.py:send_request_defer():598] handle sender defer: 13
2025-07-11 22:20:16,214 INFO    SenderThread:2831848 [sender.py:transition_state():602] send defer: 14
2025-07-11 22:20:16,215 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: defer
2025-07-11 22:20:16,215 DEBUG   SenderThread:2831848 [sender.py:send():369] send: final
2025-07-11 22:20:16,215 INFO    HandlerThread:2831848 [handler.py:handle_request_defer():170] handle defer: 14
2025-07-11 22:20:16,215 DEBUG   SenderThread:2831848 [sender.py:send():369] send: footer
2025-07-11 22:20:16,216 DEBUG   SenderThread:2831848 [sender.py:send_request():396] send_request: defer
2025-07-11 22:20:16,216 INFO    SenderThread:2831848 [sender.py:send_request_defer():598] handle sender defer: 14
2025-07-11 22:20:16,217 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: poll_exit
2025-07-11 22:20:16,217 DEBUG   SenderThread:2831848 [sender.py:send_request():396] send_request: poll_exit
2025-07-11 22:20:16,218 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: server_info
2025-07-11 22:20:16,218 DEBUG   SenderThread:2831848 [sender.py:send_request():396] send_request: server_info
2025-07-11 22:20:16,223 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: get_summary
2025-07-11 22:20:16,224 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: sampled_history
2025-07-11 22:20:16,272 INFO    MainThread:2831848 [wandb_run.py:_footer_history_summary_info():3467] rendering history
2025-07-11 22:20:16,273 INFO    MainThread:2831848 [wandb_run.py:_footer_history_summary_info():3499] rendering summary
2025-07-11 22:20:16,273 INFO    MainThread:2831848 [wandb_run.py:_footer_sync_info():3426] logging synced files
2025-07-11 22:20:16,274 DEBUG   HandlerThread:2831848 [handler.py:handle_request():144] handle_request: shutdown
2025-07-11 22:20:16,274 INFO    HandlerThread:2831848 [handler.py:finish():854] shutting down handler
2025-07-11 22:20:16,913 WARNING StreamThr :2831848 [internal.py:is_dead():415] Internal process exiting, parent pid 2831798 disappeared
2025-07-11 22:20:16,914 ERROR   StreamThr :2831848 [internal.py:wandb_internal():152] Internal process shutdown.
2025-07-11 22:20:17,219 INFO    WriterThread:2831848 [datastore.py:close():298] close: /home-local/akath.nobkp/codingchallenge_sereact/wandb/run-20250711_221847-5fcdlqla/run-5fcdlqla.wandb
2025-07-11 22:20:17,272 INFO    SenderThread:2831848 [sender.py:finish():1526] shutting down sender
2025-07-11 22:20:17,272 INFO    SenderThread:2831848 [file_pusher.py:finish():159] shutting down file pusher
2025-07-11 22:20:17,272 INFO    SenderThread:2831848 [file_pusher.py:join():164] waiting for file pusher
