=> merge config from config/base_train.yaml
RANK and WORLD_SIZE in environ: 0/3
[32m[2025-07-13 19:26:57 3DDETR.yaml][33m(main.py 552)[39m: INFO Full config saved to /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/config.json
[32m[2025-07-13 19:26:57 3DDETR.yaml][33m(main.py 555)[39m: INFO AMP_ENABLE: true
TAG: default
amp_opt_level: ''
base:
- ''
data:
  augment: false
  batch_size: 2
  cache_mode: part
  data_path: /home-local2/akath.extra.nobkp/dl_challenge
  dataset: Sereact_dataset
  debug: false
  num_workers: 4
  pin_memory: true
  transform: null
  zip_mode: false
eval_mode: false
local_rank: 0
loss:
  matcher_costs:
    cost_box_corners: 1.0
    giou: 5.0
    l1: 2.0
  weights:
    box_corners: 1.0
    giou: 1.0
    size: 1.0
    size_reg: 1.0
model:
  decoder:
    dim: 256
    dropout: 0.1
    ffn_dim: 256
    nhead: 4
    num_layers: 3
  encoder:
    activation: relu
    dim: 256
    dropout: 0.1
    ffn_dim: 128
    nheads: 4
    num_layers: 3
    preencoder_npoints: 2048
    type: vanilla
    use_color: false
  export_model: false
  mlp_dropout: 0.3
  name: 3DDETR.yaml
  num_angular_bins: 12
  num_queries: 256
  position_embedding: fourier
  pretrained: null
  pretrained_weights_path: /home/<USER>/Coding/Pre_trained_Weights/3detr/scannet_ep1080.pth
  resume: ''
  training: true
  unit_test: false
output: /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123
print_freq: 10
save_freq: 1
seed: 40
tag: '123'
train:
  accumulation_steps: 1
  auto_resume: false
  base_lr: 0.0001
  clip_grad: 0.1
  filter_biases_wd: true
  final_lr: 1.0e-06
  lr_scheduler: cosine
  max_epoch: 200
  start_epoch: 0
  unit_test_epoch: 100
  use_checkpoint: false
  warm_lr: 5.0e-06
  warm_lr_epochs: 9
  weight_decay: 0.01
unit_test: false
[32m[2025-07-13 19:26:57 3DDETR.yaml][33m(main.py 556)[39m: INFO {"cfg": "config/base_train.yaml", "opts": null, "batch_size": 2, "data_path": "/home-local2/akath.extra.nobkp/dl_challenge", "pretrained": null, "resume": null, "accumulation_steps": null, "use_checkpoint": false, "disable_amp": false, "amp_opt_level": null, "output": "/home-local2/akath.extra.nobkp/sereact", "tag": null, "eval": false, "unit_test": false, "base_lr": null, "local_rank": 0}
local rank 0 / global rank 0 successfully build train dataset
local rank 0 / global rank 0 successfully build val dataset
[32m[2025-07-13 19:26:58 3DDETR.yaml][33m(main.py 101)[39m: INFO Model3DDETR(
  (pre_encoder): PointnetSAModuleVotes(
    (grouper): QueryAndGroup()
    (mlp_module): SharedMLP(
      (layer0): Conv2d(
        (conv): Conv2d(3, 64, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn): BatchNorm2d(
          (bn): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        )
        (activation): ReLU(inplace=True)
      )
      (layer1): Conv2d(
        (conv): Conv2d(64, 128, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn): BatchNorm2d(
          (bn): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        )
        (activation): ReLU(inplace=True)
      )
      (layer2): Conv2d(
        (conv): Conv2d(128, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn): BatchNorm2d(
          (bn): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        )
        (activation): ReLU(inplace=True)
      )
    )
  )
  (encoder): TransformerEncoder(
    (layers): ModuleList(
      (0): TransformerEncoderLayer(
        attn_dr=0.1
        (self_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (linear1): Linear(in_features=256, out_features=128, bias=True)
        (dropout): Dropout(p=0.1, inplace=False)
        (linear2): Linear(in_features=128, out_features=256, bias=True)
        (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout2): Dropout(p=0.1, inplace=False)
        (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout1): Dropout(p=0.1, inplace=False)
        (activation): ReLU()
      )
      (1): TransformerEncoderLayer(
        attn_dr=0.1
        (self_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (linear1): Linear(in_features=256, out_features=128, bias=True)
        (dropout): Dropout(p=0.1, inplace=False)
        (linear2): Linear(in_features=128, out_features=256, bias=True)
        (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout2): Dropout(p=0.1, inplace=False)
        (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout1): Dropout(p=0.1, inplace=False)
        (activation): ReLU()
      )
      (2): TransformerEncoderLayer(
        attn_dr=0.1
        (self_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (linear1): Linear(in_features=256, out_features=128, bias=True)
        (dropout): Dropout(p=0.1, inplace=False)
        (linear2): Linear(in_features=128, out_features=256, bias=True)
        (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout2): Dropout(p=0.1, inplace=False)
        (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout1): Dropout(p=0.1, inplace=False)
        (activation): ReLU()
      )
    )
  )
  (encoder_decoder_projection): GenericMLP(
    (layers): Sequential(
      (0): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
      (1): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (2): ReLU()
      (3): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
      (4): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (5): ReLU()
    )
  )
  (positional_embedding): PositionEmbeddingCoordsSine(type=fourier, scale=6.283185307179586, normalize=True, gaussB_shape=torch.Size([3, 128]), gaussB_sum=-17.944507598876953)
  (query_projection): GenericMLP(
    (layers): Sequential(
      (0): Conv1d(256, 256, kernel_size=(1,), stride=(1,))
      (1): ReLU()
      (2): Conv1d(256, 256, kernel_size=(1,), stride=(1,))
      (3): ReLU()
    )
  )
  (rgb_backbone): Sequential(
    (0): Conv2d(3, 64, kernel_size=(7, 7), stride=(2, 2), padding=(3, 3), bias=False)
    (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
    (2): ReLU(inplace=True)
    (3): MaxPool2d(kernel_size=3, stride=2, padding=1, dilation=1, ceil_mode=False)
    (4): Sequential(
      (0): BasicBlock(
        (conv1): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
        (conv2): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      )
      (1): BasicBlock(
        (conv1): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
        (conv2): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      )
    )
    (5): Sequential(
      (0): BasicBlock(
        (conv1): Conv2d(64, 128, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1), bias=False)
        (bn1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
        (conv2): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (downsample): Sequential(
          (0): Conv2d(64, 128, kernel_size=(1, 1), stride=(2, 2), bias=False)
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        )
      )
      (1): BasicBlock(
        (conv1): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
        (conv2): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      )
    )
  )
  (rgb_proj): Conv1d(128, 3, kernel_size=(1,), stride=(1,))
  (decoder): TransformerDecoder(
    (layers): ModuleList(
      (0): TransformerDecoderLayer(
        (self_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (multihead_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm3): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout1): Dropout(p=0.1, inplace=False)
        (dropout2): Dropout(p=0.1, inplace=False)
        (dropout3): Dropout(p=0.1, inplace=False)
        (linear1): Linear(in_features=256, out_features=256, bias=True)
        (dropout): Dropout(p=0.1, inplace=False)
        (linear2): Linear(in_features=256, out_features=256, bias=True)
        (activation): ReLU()
      )
      (1): TransformerDecoderLayer(
        (self_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (multihead_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm3): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout1): Dropout(p=0.1, inplace=False)
        (dropout2): Dropout(p=0.1, inplace=False)
        (dropout3): Dropout(p=0.1, inplace=False)
        (linear1): Linear(in_features=256, out_features=256, bias=True)
        (dropout): Dropout(p=0.1, inplace=False)
        (linear2): Linear(in_features=256, out_features=256, bias=True)
        (activation): ReLU()
      )
      (2): TransformerDecoderLayer(
        (self_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (multihead_attn): MultiheadAttention(
          (out_proj): _LinearWithBias(in_features=256, out_features=256, bias=True)
        )
        (norm1): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm2): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (norm3): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
        (dropout1): Dropout(p=0.1, inplace=False)
        (dropout2): Dropout(p=0.1, inplace=False)
        (dropout3): Dropout(p=0.1, inplace=False)
        (linear1): Linear(in_features=256, out_features=256, bias=True)
        (dropout): Dropout(p=0.1, inplace=False)
        (linear2): Linear(in_features=256, out_features=256, bias=True)
        (activation): ReLU()
      )
    )
    (norm): LayerNorm((256,), eps=1e-05, elementwise_affine=True)
  )
  (mlp_heads): ModuleDict(
    (center_head): GenericMLP(
      (layers): Sequential(
        (0): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
        (1): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU()
        (3): Dropout(p=0.3, inplace=False)
        (4): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
        (5): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (6): ReLU()
        (7): Dropout(p=0.3, inplace=False)
        (8): Conv1d(256, 3, kernel_size=(1,), stride=(1,))
      )
    )
    (size_head): GenericMLP(
      (layers): Sequential(
        (0): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
        (1): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU()
        (3): Dropout(p=0.3, inplace=False)
        (4): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
        (5): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (6): ReLU()
        (7): Dropout(p=0.3, inplace=False)
        (8): Conv1d(256, 3, kernel_size=(1,), stride=(1,))
      )
    )
    (angle_cls_head): GenericMLP(
      (layers): Sequential(
        (0): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
        (1): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU()
        (3): Dropout(p=0.3, inplace=False)
        (4): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
        (5): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (6): ReLU()
        (7): Dropout(p=0.3, inplace=False)
        (8): Conv1d(256, 12, kernel_size=(1,), stride=(1,))
      )
    )
    (angle_residual_head): GenericMLP(
      (layers): Sequential(
        (0): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
        (1): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU()
        (3): Dropout(p=0.3, inplace=False)
        (4): Conv1d(256, 256, kernel_size=(1,), stride=(1,), bias=False)
        (5): BatchNorm1d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (6): ReLU()
        (7): Dropout(p=0.3, inplace=False)
        (8): Conv1d(256, 12, kernel_size=(1,), stride=(1,))
      )
    )
  )
)
[32m[2025-07-13 19:26:58 3DDETR.yaml][33m(main.py 103)[39m: INFO number of params: 4494497
[32m[2025-07-13 19:26:58 3DDETR.yaml][33m(main.py 151)[39m: INFO Start training
[32m[2025-07-13 19:27:03 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [0/200][0/27]	eta 0:02:32 lr 0.000100	time 5.6342 (5.6342)	loss 43.0113 (43.0113)	miou 0.0194	grad_norm 445.2315 (445.2315)	loss_scale 65536.0000 (65536.0000)	mem 4934MB
[32m[2025-07-13 19:27:39 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [0/200][10/27]	eta 0:01:03 lr 0.000099	time 3.7667 (3.7484)	loss 36.0397 (27.1976)	miou 0.1028	grad_norm 617.9929 (1552.3750)	loss_scale 65536.0000 (65536.0000)	mem 5748MB
[32m[2025-07-13 19:28:13 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [0/200][20/27]	eta 0:00:25 lr 0.000097	time 3.2751 (3.5836)	loss 16.7835 (25.0025)	miou 0.1148	grad_norm 340.5855 (978.5594)	loss_scale 65536.0000 (65536.0000)	mem 5748MB
[32m[2025-07-13 19:28:32 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 0 training takes 0:01:34
[32m[2025-07-13 19:28:32 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
0
[32m[2025-07-13 19:28:45 3DDETR.yaml][33m(utils_help.py 85)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saving......
[32m[2025-07-13 19:28:46 3DDETR.yaml][33m(utils_help.py 87)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saved !!!
[32m[2025-07-13 19:28:46 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1068%
[32m[2025-07-13 19:28:46 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.1068%
[32m[2025-07-13 19:28:50 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [1/200][0/27]	eta 0:01:41 lr 0.000095	time 3.7496 (3.7496)	loss 20.6171 (20.6171)	miou 0.1062	grad_norm 310.9843 (310.9843)	loss_scale 65536.0000 (65536.0000)	mem 5748MB
[32m[2025-07-13 19:29:18 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [1/200][10/27]	eta 0:00:50 lr 0.000091	time 3.9151 (2.9507)	loss 15.6486 (19.2678)	miou 0.1115	grad_norm 10589.6768 (1615.9069)	loss_scale 65536.0000 (65536.0000)	mem 5748MB
[32m[2025-07-13 19:29:43 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [1/200][20/27]	eta 0:00:18 lr 0.000086	time 2.7088 (2.7106)	loss 25.9692 (17.5029)	miou 0.1361	grad_norm 738.2509 (955.8503)	loss_scale 65536.0000 (65536.0000)	mem 5748MB
[32m[2025-07-13 19:29:59 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 1 training takes 0:01:12
[32m[2025-07-13 19:29:59 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
1
[32m[2025-07-13 19:30:09 3DDETR.yaml][33m(utils_help.py 85)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saving......
[32m[2025-07-13 19:30:10 3DDETR.yaml][33m(utils_help.py 87)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saved !!!
[32m[2025-07-13 19:30:10 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.2365%
[32m[2025-07-13 19:30:10 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2365%
[32m[2025-07-13 19:30:13 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [2/200][0/27]	eta 0:01:34 lr 0.000082	time 3.5105 (3.5105)	loss 15.1565 (15.1565)	miou 0.2249	grad_norm 539.1919 (539.1919)	loss_scale 65536.0000 (65536.0000)	mem 5748MB
[32m[2025-07-13 19:30:38 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [2/200][10/27]	eta 0:00:43 lr 0.000076	time 2.6386 (2.5568)	loss 10.5715 (12.9327)	miou 0.1940	grad_norm 36.9385 (441.3306)	loss_scale 65536.0000 (65536.0000)	mem 5748MB
[32m[2025-07-13 19:31:01 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [2/200][20/27]	eta 0:00:17 lr 0.000069	time 2.4692 (2.4519)	loss 26.3388 (15.8757)	miou 0.1746	grad_norm 692.8367 (567.5120)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:31:15 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 2 training takes 0:01:05
[32m[2025-07-13 19:31:15 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 19:31:25 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.0069%
[32m[2025-07-13 19:31:25 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2365%
[32m[2025-07-13 19:31:28 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [3/200][0/27]	eta 0:01:16 lr 0.000064	time 2.8159 (2.8159)	loss 15.5616 (15.5616)	miou 0.0111	grad_norm 259.3672 (259.3672)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:31:51 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [3/200][10/27]	eta 0:00:38 lr 0.000056	time 2.3127 (2.2924)	loss 9.6890 (18.5331)	miou 0.0582	grad_norm 185.9895 (2304.0935)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:32:13 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [3/200][20/27]	eta 0:00:16 lr 0.000048	time 2.6007 (2.2881)	loss 13.7865 (16.1366)	miou 0.0818	grad_norm 125.2084 (1577.0253)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:32:27 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 3 training takes 0:01:02
[32m[2025-07-13 19:32:27 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 19:32:38 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.2341%
[32m[2025-07-13 19:32:38 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2365%
[32m[2025-07-13 19:32:40 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [4/200][0/27]	eta 0:01:09 lr 0.000043	time 2.5878 (2.5878)	loss 12.5228 (12.5228)	miou 0.2255	grad_norm 345.2545 (345.2545)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:33:01 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [4/200][10/27]	eta 0:00:35 lr 0.000035	time 1.9422 (2.0988)	loss 11.9206 (12.9897)	miou 0.1897	grad_norm 206.6075 (367.0274)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:33:21 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [4/200][20/27]	eta 0:00:14 lr 0.000028	time 1.9214 (2.0655)	loss 13.3372 (13.7768)	miou 0.1699	grad_norm 85.0848 (433.4334)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:33:36 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 4 training takes 0:00:57
[32m[2025-07-13 19:33:36 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 19:33:46 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1916%
[32m[2025-07-13 19:33:46 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2365%
[32m[2025-07-13 19:33:49 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [5/200][0/27]	eta 0:01:17 lr 0.000023	time 2.8555 (2.8555)	loss 8.1618 (8.1618)	miou 0.1882	grad_norm 317.2101 (317.2101)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:34:09 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [5/200][10/27]	eta 0:00:35 lr 0.000017	time 1.8932 (2.0965)	loss 9.8083 (12.8024)	miou 0.1811	grad_norm 113.0969 (210.4944)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:34:31 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [5/200][20/27]	eta 0:00:14 lr 0.000011	time 1.9950 (2.1263)	loss 13.1018 (12.1188)	miou 0.1661	grad_norm 99.9429 (212.7224)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:34:43 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 5 training takes 0:00:57
[32m[2025-07-13 19:34:43 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
5
[32m[2025-07-13 19:34:53 3DDETR.yaml][33m(utils_help.py 85)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saving......
[32m[2025-07-13 19:34:54 3DDETR.yaml][33m(utils_help.py 87)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saved !!!
[32m[2025-07-13 19:34:54 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.2404%
[32m[2025-07-13 19:34:54 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2404%
[32m[2025-07-13 19:34:56 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [6/200][0/27]	eta 0:01:10 lr 0.000008	time 2.6043 (2.6043)	loss 10.0059 (10.0059)	miou 0.2546	grad_norm 121.2078 (121.2078)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:35:16 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [6/200][10/27]	eta 0:00:34 lr 0.000004	time 1.9889 (2.0484)	loss 12.4325 (13.3481)	miou 0.2089	grad_norm 65.2154 (291.7382)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:35:37 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [6/200][20/27]	eta 0:00:14 lr 0.000002	time 2.4370 (2.0498)	loss 10.7729 (12.6592)	miou 0.1919	grad_norm 1511.6588 (522.3708)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:35:48 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 6 training takes 0:00:54
[32m[2025-07-13 19:35:48 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 19:35:59 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1980%
[32m[2025-07-13 19:35:59 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2404%
[32m[2025-07-13 19:36:01 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [7/200][0/27]	eta 0:01:09 lr 0.000001	time 2.5841 (2.5841)	loss 10.3650 (10.3650)	miou 0.1885	grad_norm 85.7738 (85.7738)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:36:22 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [7/200][10/27]	eta 0:00:35 lr 0.000000	time 1.7344 (2.0825)	loss 8.1883 (11.3811)	miou 0.1688	grad_norm 79.1565 (718.0146)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:36:41 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [7/200][20/27]	eta 0:00:14 lr 0.000001	time 2.0866 (2.0313)	loss 13.6471 (11.6677)	miou 0.1513	grad_norm 87.5999 (606.8836)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:36:53 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 7 training takes 0:00:54
[32m[2025-07-13 19:36:53 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 19:37:03 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.2153%
[32m[2025-07-13 19:37:03 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2404%
[32m[2025-07-13 19:37:06 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [8/200][0/27]	eta 0:01:08 lr 0.000002	time 2.5269 (2.5269)	loss 9.4604 (9.4604)	miou 0.2128	grad_norm 143.0959 (143.0959)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:37:27 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [8/200][10/27]	eta 0:00:35 lr 0.000004	time 2.0444 (2.1123)	loss 10.5373 (11.0697)	miou 0.1677	grad_norm 526.7374 (356.2909)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:37:47 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [8/200][20/27]	eta 0:00:14 lr 0.000008	time 2.0625 (2.0755)	loss 14.8548 (12.6408)	miou 0.1580	grad_norm 302.7663 (316.0722)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:37:59 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 8 training takes 0:00:55
[32m[2025-07-13 19:37:59 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 19:38:09 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.2028%
[32m[2025-07-13 19:38:09 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2404%
[32m[2025-07-13 19:38:11 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [9/200][0/27]	eta 0:00:56 lr 0.000011	time 2.1064 (2.1064)	loss 12.8350 (12.8350)	miou 0.1993	grad_norm 114.9637 (114.9637)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:38:31 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [9/200][10/27]	eta 0:00:34 lr 0.000017	time 1.9727 (2.0276)	loss 8.5148 (11.8263)	miou 0.1769	grad_norm 88.0821 (346.7624)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:38:52 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [9/200][20/27]	eta 0:00:14 lr 0.000023	time 2.5085 (2.0436)	loss 12.1492 (11.5055)	miou 0.1559	grad_norm 287.5724 (310.3932)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:39:03 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 9 training takes 0:00:54
[32m[2025-07-13 19:39:04 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
9
[32m[2025-07-13 19:39:14 3DDETR.yaml][33m(utils_help.py 85)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saving......
[32m[2025-07-13 19:39:14 3DDETR.yaml][33m(utils_help.py 87)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saved !!!
[32m[2025-07-13 19:39:14 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.2509%
[32m[2025-07-13 19:39:14 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2509%
[32m[2025-07-13 19:39:17 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [10/200][0/27]	eta 0:01:06 lr 0.000028	time 2.4633 (2.4633)	loss 9.8635 (9.8635)	miou 0.2431	grad_norm 505.4349 (505.4349)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:39:36 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [10/200][10/27]	eta 0:00:33 lr 0.000035	time 1.5685 (1.9676)	loss 7.1223 (11.3174)	miou 0.2065	grad_norm 199.7158 (172.8302)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:39:56 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [10/200][20/27]	eta 0:00:13 lr 0.000043	time 2.3049 (1.9908)	loss 12.8305 (11.0115)	miou 0.1872	grad_norm 1481.3738 (500.4151)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:40:09 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 10 training takes 0:00:54
[32m[2025-07-13 19:40:09 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 19:40:19 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.2487%
[32m[2025-07-13 19:40:19 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2509%
[32m[2025-07-13 19:40:22 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [11/200][0/27]	eta 0:01:12 lr 0.000048	time 2.6704 (2.6704)	loss 13.5928 (13.5928)	miou 0.2340	grad_norm 391.5174 (391.5174)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:40:42 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [11/200][10/27]	eta 0:00:35 lr 0.000056	time 2.4542 (2.0983)	loss 8.4777 (10.9940)	miou 0.1992	grad_norm 2197.2344 (450.4443)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:41:02 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [11/200][20/27]	eta 0:00:14 lr 0.000064	time 1.8498 (2.0428)	loss 8.4212 (10.5835)	miou 0.1800	grad_norm 114.2458 (462.1039)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:41:14 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 11 training takes 0:00:54
[32m[2025-07-13 19:41:14 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 19:41:24 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.2439%
[32m[2025-07-13 19:41:24 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2509%
[32m[2025-07-13 19:41:27 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [12/200][0/27]	eta 0:01:16 lr 0.000069	time 2.8470 (2.8470)	loss 10.2602 (10.2602)	miou 0.2228	grad_norm 500.3293 (500.3293)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:41:47 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [12/200][10/27]	eta 0:00:34 lr 0.000076	time 2.1067 (2.0506)	loss 8.1584 (9.6484)	miou 0.1960	grad_norm 185.9639 (346.7342)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:42:08 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [12/200][20/27]	eta 0:00:14 lr 0.000082	time 2.3717 (2.0594)	loss 8.1193 (9.9325)	miou 0.1834	grad_norm 154.3786 (366.2127)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:42:20 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 12 training takes 0:00:55
[32m[2025-07-13 19:42:20 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 19:42:30 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1770%
[32m[2025-07-13 19:42:30 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2509%
[32m[2025-07-13 19:42:33 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [13/200][0/27]	eta 0:01:13 lr 0.000086	time 2.7204 (2.7204)	loss 8.4914 (8.4914)	miou 0.1716	grad_norm 66.8784 (66.8784)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:42:53 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [13/200][10/27]	eta 0:00:35 lr 0.000091	time 1.9427 (2.0852)	loss 9.8791 (9.2073)	miou 0.1734	grad_norm 90.5075 (306.0905)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:43:13 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [13/200][20/27]	eta 0:00:14 lr 0.000095	time 1.9131 (2.0586)	loss 8.2388 (8.9969)	miou 0.1729	grad_norm 864.3086 (264.9050)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:43:26 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 13 training takes 0:00:55
[32m[2025-07-13 19:43:26 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
13
[32m[2025-07-13 19:43:36 3DDETR.yaml][33m(utils_help.py 85)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saving......
[32m[2025-07-13 19:43:36 3DDETR.yaml][33m(utils_help.py 87)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saved !!!
[32m[2025-07-13 19:43:36 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.2528%
[32m[2025-07-13 19:43:36 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2528%
[32m[2025-07-13 19:43:39 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [14/200][0/27]	eta 0:01:01 lr 0.000097	time 2.2701 (2.2701)	loss 9.8805 (9.8805)	miou 0.2398	grad_norm 279.5233 (279.5233)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:44:00 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [14/200][10/27]	eta 0:00:36 lr 0.000099	time 2.4510 (2.1182)	loss 8.8385 (8.7436)	miou 0.2074	grad_norm 83.4407 (97.6945)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:44:19 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [14/200][20/27]	eta 0:00:14 lr 0.000100	time 2.0317 (2.0307)	loss 8.8317 (9.1700)	miou 0.1869	grad_norm 620.1725 (193.4754)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:44:31 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 14 training takes 0:00:54
[32m[2025-07-13 19:44:31 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 19:44:42 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.0272%
[32m[2025-07-13 19:44:42 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2528%
[32m[2025-07-13 19:44:44 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [15/200][0/27]	eta 0:01:05 lr 0.000100	time 2.4267 (2.4267)	loss 7.4585 (7.4585)	miou 0.0546	grad_norm 79.1426 (79.1426)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:45:03 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [15/200][10/27]	eta 0:00:33 lr 0.000098	time 1.5661 (1.9613)	loss 9.1718 (7.9364)	miou 0.1161	grad_norm 238.9078 (230.0721)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:45:25 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [15/200][20/27]	eta 0:00:14 lr 0.000096	time 2.3018 (2.0431)	loss 11.2801 (8.1890)	miou 0.1272	grad_norm 428.4289 (241.8332)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:45:36 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 15 training takes 0:00:54
[32m[2025-07-13 19:45:36 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 19:45:46 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.2385%
[32m[2025-07-13 19:45:46 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2528%
[32m[2025-07-13 19:45:49 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [16/200][0/27]	eta 0:01:09 lr 0.000093	time 2.5649 (2.5649)	loss 11.5327 (11.5327)	miou 0.2365	grad_norm 487.1728 (487.1728)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:46:09 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [16/200][10/27]	eta 0:00:35 lr 0.000089	time 1.9236 (2.0690)	loss 8.6168 (9.1648)	miou 0.2166	grad_norm 38.7627 (174.8666)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:46:29 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [16/200][20/27]	eta 0:00:14 lr 0.000084	time 2.2857 (2.0433)	loss 8.8196 (8.6690)	miou 0.1943	grad_norm 82.2333 (207.1565)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:46:41 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 16 training takes 0:00:54
[32m[2025-07-13 19:46:41 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 19:46:51 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.2074%
[32m[2025-07-13 19:46:51 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2528%
[32m[2025-07-13 19:46:53 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [17/200][0/27]	eta 0:01:00 lr 0.000079	time 2.2303 (2.2303)	loss 5.8687 (5.8687)	miou 0.2097	grad_norm 84.9548 (84.9548)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:47:13 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [17/200][10/27]	eta 0:00:33 lr 0.000073	time 2.0952 (1.9900)	loss 11.1118 (7.3740)	miou 0.2001	grad_norm 35.8508 (101.1834)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:47:33 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [17/200][20/27]	eta 0:00:13 lr 0.000065	time 2.2163 (1.9795)	loss 10.1690 (7.9986)	miou 0.1823	grad_norm 44.5235 (104.1706)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:47:45 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 17 training takes 0:00:53
[32m[2025-07-13 19:47:45 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 19:47:55 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1866%
[32m[2025-07-13 19:47:55 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2528%
[32m[2025-07-13 19:47:58 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [18/200][0/27]	eta 0:01:11 lr 0.000060	time 2.6377 (2.6377)	loss 7.1715 (7.1715)	miou 0.1854	grad_norm 67.2800 (67.2800)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:48:18 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [18/200][10/27]	eta 0:00:35 lr 0.000052	time 2.1337 (2.0672)	loss 9.9059 (7.4503)	miou 0.1797	grad_norm 38.9687 (77.6552)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:48:37 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [18/200][20/27]	eta 0:00:13 lr 0.000045	time 1.5927 (1.9969)	loss 6.2022 (7.6221)	miou 0.1869	grad_norm 144.6117 (89.7939)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:48:50 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 18 training takes 0:00:55
[32m[2025-07-13 19:48:50 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 19:49:01 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1133%
[32m[2025-07-13 19:49:01 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2528%
[32m[2025-07-13 19:49:03 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [19/200][0/27]	eta 0:00:57 lr 0.000039	time 2.1127 (2.1127)	loss 11.1270 (11.1270)	miou 0.1187	grad_norm 108.6732 (108.6732)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:49:23 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [19/200][10/27]	eta 0:00:34 lr 0.000032	time 2.4571 (2.0415)	loss 10.4989 (8.4783)	miou 0.1384	grad_norm 134.5245 (123.1178)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:49:43 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [19/200][20/27]	eta 0:00:14 lr 0.000025	time 1.9813 (2.0065)	loss 9.4318 (7.8929)	miou 0.1428	grad_norm 32.0445 (114.1224)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:49:55 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 19 training takes 0:00:54
[32m[2025-07-13 19:49:55 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 19:50:05 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1074%
[32m[2025-07-13 19:50:05 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2528%
[32m[2025-07-13 19:50:09 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [20/200][0/27]	eta 0:01:21 lr 0.000020	time 3.0243 (3.0243)	loss 7.1986 (7.1986)	miou 0.1116	grad_norm 28.0081 (28.0081)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:50:28 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [20/200][10/27]	eta 0:00:35 lr 0.000014	time 1.6822 (2.0606)	loss 9.9721 (8.2804)	miou 0.1465	grad_norm 27.9747 (63.6179)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:50:49 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [20/200][20/27]	eta 0:00:14 lr 0.000009	time 2.1200 (2.0625)	loss 6.0894 (8.0783)	miou 0.1519	grad_norm 291.1310 (93.0071)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:51:01 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 20 training takes 0:00:55
[32m[2025-07-13 19:51:01 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 19:51:11 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1308%
[32m[2025-07-13 19:51:11 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2528%
[32m[2025-07-13 19:51:13 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [21/200][0/27]	eta 0:01:00 lr 0.000006	time 2.2546 (2.2546)	loss 8.0072 (8.0072)	miou 0.1376	grad_norm 62.8051 (62.8051)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:51:34 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [21/200][10/27]	eta 0:00:35 lr 0.000003	time 2.1197 (2.0812)	loss 9.1232 (9.8360)	miou 0.1618	grad_norm 26.3431 (654.8496)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:51:54 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [21/200][20/27]	eta 0:00:14 lr 0.000001	time 2.1264 (2.0378)	loss 5.4642 (8.5697)	miou 0.1790	grad_norm 31.2961 (370.9032)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:52:05 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 21 training takes 0:00:54
[32m[2025-07-13 19:52:05 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 19:52:15 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1096%
[32m[2025-07-13 19:52:15 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2528%
[32m[2025-07-13 19:52:18 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [22/200][0/27]	eta 0:01:04 lr 0.000000	time 2.3705 (2.3705)	loss 8.0258 (8.0258)	miou 0.1183	grad_norm 96.9020 (96.9020)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:52:38 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [22/200][10/27]	eta 0:00:34 lr 0.000000	time 1.7467 (2.0159)	loss 7.2941 (7.7217)	miou 0.1624	grad_norm 51.5694 (70.5725)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:52:58 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [22/200][20/27]	eta 0:00:14 lr 0.000001	time 2.2965 (2.0110)	loss 6.2903 (7.8164)	miou 0.1745	grad_norm 26.4746 (68.1574)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:53:09 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 22 training takes 0:00:53
[32m[2025-07-13 19:53:09 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 19:53:19 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1326%
[32m[2025-07-13 19:53:19 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2528%
[32m[2025-07-13 19:53:22 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [23/200][0/27]	eta 0:01:19 lr 0.000003	time 2.9542 (2.9542)	loss 8.3448 (8.3448)	miou 0.1409	grad_norm 133.9143 (133.9143)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:53:42 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [23/200][10/27]	eta 0:00:35 lr 0.000006	time 2.1763 (2.0595)	loss 7.5337 (8.0169)	miou 0.1503	grad_norm 43.1469 (65.3176)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:54:02 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [23/200][20/27]	eta 0:00:14 lr 0.000010	time 2.1345 (2.0184)	loss 7.1209 (7.8437)	miou 0.1672	grad_norm 73.0009 (77.7851)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:54:14 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 23 training takes 0:00:54
[32m[2025-07-13 19:54:14 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 19:54:24 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1170%
[32m[2025-07-13 19:54:24 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2528%
[32m[2025-07-13 19:54:27 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [24/200][0/27]	eta 0:01:04 lr 0.000014	time 2.3947 (2.3947)	loss 7.5761 (7.5761)	miou 0.1444	grad_norm 105.0553 (105.0553)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:54:46 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [24/200][10/27]	eta 0:00:34 lr 0.000020	time 2.0440 (2.0277)	loss 9.2840 (7.9850)	miou 0.1727	grad_norm 169.8245 (67.8322)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:55:05 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [24/200][20/27]	eta 0:00:13 lr 0.000027	time 1.9934 (1.9494)	loss 8.0452 (7.9641)	miou 0.1735	grad_norm 35.3387 (83.5203)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:55:17 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 24 training takes 0:00:53
[32m[2025-07-13 19:55:17 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 19:55:28 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1454%
[32m[2025-07-13 19:55:28 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2528%
[32m[2025-07-13 19:55:31 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [25/200][0/27]	eta 0:01:23 lr 0.000032	time 3.0877 (3.0877)	loss 5.9168 (5.9168)	miou 0.1450	grad_norm 38.4248 (38.4248)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:55:50 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [25/200][10/27]	eta 0:00:34 lr 0.000039	time 1.8859 (2.0196)	loss 12.1275 (8.7574)	miou 0.1678	grad_norm 44.5817 (85.3769)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:56:10 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [25/200][20/27]	eta 0:00:14 lr 0.000047	time 1.8892 (2.0029)	loss 8.2000 (8.3509)	miou 0.1773	grad_norm 41.2641 (77.2886)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:56:23 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 25 training takes 0:00:54
[32m[2025-07-13 19:56:23 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 19:56:33 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1529%
[32m[2025-07-13 19:56:33 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2528%
[32m[2025-07-13 19:56:36 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [26/200][0/27]	eta 0:01:11 lr 0.000052	time 2.6337 (2.6337)	loss 6.3660 (6.3660)	miou 0.1663	grad_norm 26.9600 (26.9600)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:56:56 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [26/200][10/27]	eta 0:00:34 lr 0.000060	time 1.7715 (2.0489)	loss 6.0006 (8.2062)	miou 0.1798	grad_norm 329.3182 (104.6684)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:57:15 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [26/200][20/27]	eta 0:00:13 lr 0.000068	time 1.8814 (1.9955)	loss 8.8017 (8.5580)	miou 0.1704	grad_norm 319.6068 (297.0382)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:57:27 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 26 training takes 0:00:54
[32m[2025-07-13 19:57:27 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 19:57:38 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1584%
[32m[2025-07-13 19:57:38 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2528%
[32m[2025-07-13 19:57:41 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [27/200][0/27]	eta 0:01:18 lr 0.000073	time 2.9238 (2.9238)	loss 4.3595 (4.3595)	miou 0.1692	grad_norm 85.7238 (85.7238)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:58:01 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [27/200][10/27]	eta 0:00:35 lr 0.000079	time 2.5887 (2.1046)	loss 8.6467 (6.8381)	miou 0.1726	grad_norm 92.5118 (145.8465)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:58:21 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [27/200][20/27]	eta 0:00:14 lr 0.000085	time 2.1001 (2.0723)	loss 5.4734 (6.9287)	miou 0.1749	grad_norm 338.6701 (128.4217)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:58:32 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 27 training takes 0:00:54
[32m[2025-07-13 19:58:32 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 19:58:43 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1362%
[32m[2025-07-13 19:58:43 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2528%
[32m[2025-07-13 19:58:45 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [28/200][0/27]	eta 0:01:12 lr 0.000089	time 2.6767 (2.6767)	loss 7.2767 (7.2767)	miou 0.1347	grad_norm 66.2898 (66.2898)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:59:05 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [28/200][10/27]	eta 0:00:34 lr 0.000093	time 1.9550 (2.0577)	loss 6.1244 (7.9654)	miou 0.1694	grad_norm 36.2345 (103.8898)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:59:26 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [28/200][20/27]	eta 0:00:14 lr 0.000097	time 2.3241 (2.0371)	loss 8.5638 (7.6764)	miou 0.1912	grad_norm 89.2473 (126.8470)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 19:59:37 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 28 training takes 0:00:54
[32m[2025-07-13 19:59:37 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 19:59:48 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1710%
[32m[2025-07-13 19:59:48 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2528%
[32m[2025-07-13 19:59:50 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [29/200][0/27]	eta 0:01:05 lr 0.000098	time 2.4262 (2.4262)	loss 7.3425 (7.3425)	miou 0.1674	grad_norm 59.6996 (59.6996)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:00:09 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [29/200][10/27]	eta 0:00:33 lr 0.000100	time 1.9025 (1.9831)	loss 8.5588 (8.3015)	miou 0.1890	grad_norm 39.6425 (160.8500)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:00:30 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [29/200][20/27]	eta 0:00:13 lr 0.000100	time 1.8862 (1.9985)	loss 9.2152 (7.7779)	miou 0.1933	grad_norm 65.5919 (123.1540)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:00:42 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 29 training takes 0:00:54
[32m[2025-07-13 20:00:42 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 20:00:52 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1723%
[32m[2025-07-13 20:00:52 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2528%
[32m[2025-07-13 20:00:55 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [30/200][0/27]	eta 0:01:03 lr 0.000099	time 2.3618 (2.3618)	loss 12.5259 (12.5259)	miou 0.1682	grad_norm 157.3412 (157.3412)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:01:14 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [30/200][10/27]	eta 0:00:33 lr 0.000097	time 1.9312 (1.9895)	loss 5.5424 (7.8014)	miou 0.1812	grad_norm 88.4250 (110.0545)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:01:35 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [30/200][20/27]	eta 0:00:14 lr 0.000094	time 2.2802 (2.0332)	loss 9.3102 (7.9733)	miou 0.1765	grad_norm 68.6396 (155.7576)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:01:48 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 30 training takes 0:00:55
[32m[2025-07-13 20:01:48 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 20:01:58 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.2097%
[32m[2025-07-13 20:01:58 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2528%
[32m[2025-07-13 20:02:02 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [31/200][0/27]	eta 0:01:22 lr 0.000091	time 3.0735 (3.0735)	loss 6.7444 (6.7444)	miou 0.2254	grad_norm 531.4509 (531.4509)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:02:22 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [31/200][10/27]	eta 0:00:35 lr 0.000086	time 1.5196 (2.1015)	loss 6.5984 (7.3221)	miou 0.2131	grad_norm 46.4571 (364.7640)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:02:41 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [31/200][20/27]	eta 0:00:14 lr 0.000081	time 1.9417 (2.0237)	loss 5.9269 (7.3987)	miou 0.2172	grad_norm 73.2329 (386.5717)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:02:54 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 31 training takes 0:00:55
[32m[2025-07-13 20:02:54 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 20:03:04 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1562%
[32m[2025-07-13 20:03:04 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2528%
[32m[2025-07-13 20:03:07 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [32/200][0/27]	eta 0:01:11 lr 0.000076	time 2.6581 (2.6581)	loss 5.7758 (5.7758)	miou 0.1646	grad_norm 172.1671 (172.1671)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:03:28 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [32/200][10/27]	eta 0:00:36 lr 0.000069	time 2.2992 (2.1551)	loss 9.1699 (7.1136)	miou 0.1876	grad_norm 34.4661 (100.2449)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:03:46 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [32/200][20/27]	eta 0:00:14 lr 0.000062	time 2.3358 (2.0209)	loss 6.9797 (7.3277)	miou 0.1932	grad_norm 28.8340 (95.0185)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:03:58 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 32 training takes 0:00:54
[32m[2025-07-13 20:03:58 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 20:04:09 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1838%
[32m[2025-07-13 20:04:09 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2528%
[32m[2025-07-13 20:04:11 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [33/200][0/27]	eta 0:01:02 lr 0.000056	time 2.3149 (2.3149)	loss 6.2907 (6.2907)	miou 0.1898	grad_norm 56.8805 (56.8805)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:04:31 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [33/200][10/27]	eta 0:00:34 lr 0.000048	time 1.7806 (2.0392)	loss 8.8542 (7.5213)	miou 0.1949	grad_norm 78.6832 (61.3486)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:04:52 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [33/200][20/27]	eta 0:00:14 lr 0.000041	time 1.6921 (2.0655)	loss 7.7124 (7.3364)	miou 0.1944	grad_norm 144.5367 (405.4743)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:05:04 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 33 training takes 0:00:55
[32m[2025-07-13 20:05:04 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 20:05:14 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1606%
[32m[2025-07-13 20:05:14 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2528%
[32m[2025-07-13 20:05:17 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [34/200][0/27]	eta 0:01:04 lr 0.000035	time 2.4019 (2.4019)	loss 9.2635 (9.2635)	miou 0.1607	grad_norm 34.6771 (34.6771)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:05:36 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [34/200][10/27]	eta 0:00:34 lr 0.000028	time 1.8586 (2.0184)	loss 8.2224 (8.1796)	miou 0.1675	grad_norm 81.0375 (76.2238)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:05:56 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [34/200][20/27]	eta 0:00:13 lr 0.000021	time 2.0345 (1.9994)	loss 7.5278 (8.0291)	miou 0.1900	grad_norm 37.0043 (73.7940)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:06:08 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 34 training takes 0:00:54
[32m[2025-07-13 20:06:08 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 20:06:19 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1906%
[32m[2025-07-13 20:06:19 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2528%
[32m[2025-07-13 20:06:21 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [35/200][0/27]	eta 0:01:08 lr 0.000017	time 2.5277 (2.5277)	loss 7.6449 (7.6449)	miou 0.2148	grad_norm 54.7928 (54.7928)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:06:41 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [35/200][10/27]	eta 0:00:34 lr 0.000011	time 2.1049 (2.0297)	loss 5.8722 (6.3661)	miou 0.2206	grad_norm 28.2121 (67.0182)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:06:59 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [35/200][20/27]	eta 0:00:13 lr 0.000007	time 1.8895 (1.9391)	loss 6.2932 (7.4448)	miou 0.2227	grad_norm 27.7520 (74.6531)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:07:12 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 35 training takes 0:00:52
[32m[2025-07-13 20:07:12 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 20:07:22 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1703%
[32m[2025-07-13 20:07:22 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2528%
[32m[2025-07-13 20:07:25 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [36/200][0/27]	eta 0:01:07 lr 0.000004	time 2.5127 (2.5127)	loss 11.0360 (11.0360)	miou 0.1683	grad_norm 72.6065 (72.6065)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:07:43 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [36/200][10/27]	eta 0:00:33 lr 0.000002	time 1.8199 (1.9486)	loss 5.7417 (7.6494)	miou 0.1922	grad_norm 37.7227 (89.8441)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:08:05 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [36/200][20/27]	eta 0:00:14 lr 0.000000	time 2.2090 (2.0295)	loss 5.8305 (7.5677)	miou 0.1920	grad_norm 50.8689 (65.0592)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:08:16 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 36 training takes 0:00:54
[32m[2025-07-13 20:08:16 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 20:08:27 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1748%
[32m[2025-07-13 20:08:27 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2528%
[32m[2025-07-13 20:08:29 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [37/200][0/27]	eta 0:01:16 lr 0.000000	time 2.8163 (2.8163)	loss 6.1730 (6.1730)	miou 0.1703	grad_norm 42.2310 (42.2310)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:08:49 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [37/200][10/27]	eta 0:00:34 lr 0.000001	time 2.1744 (2.0460)	loss 11.3040 (7.0828)	miou 0.2003	grad_norm 79.0502 (71.8224)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:09:10 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [37/200][20/27]	eta 0:00:14 lr 0.000002	time 1.6063 (2.0486)	loss 5.9297 (7.0543)	miou 0.2100	grad_norm 51.6078 (135.4463)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:09:22 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 37 training takes 0:00:55
[32m[2025-07-13 20:09:22 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 20:09:33 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1704%
[32m[2025-07-13 20:09:33 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2528%
[32m[2025-07-13 20:09:36 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [38/200][0/27]	eta 0:01:26 lr 0.000004	time 3.2021 (3.2021)	loss 7.0967 (7.0967)	miou 0.1698	grad_norm 36.6629 (36.6629)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:09:56 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [38/200][10/27]	eta 0:00:35 lr 0.000008	time 2.1203 (2.1074)	loss 6.4119 (7.1111)	miou 0.1708	grad_norm 22.3401 (41.2861)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:10:16 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [38/200][20/27]	eta 0:00:14 lr 0.000013	time 2.0093 (2.0591)	loss 9.1362 (7.0152)	miou 0.1722	grad_norm 24.8734 (58.5849)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:10:28 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 38 training takes 0:00:55
[32m[2025-07-13 20:10:28 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 20:10:39 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1810%
[32m[2025-07-13 20:10:39 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2528%
[32m[2025-07-13 20:10:41 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [39/200][0/27]	eta 0:01:01 lr 0.000017	time 2.2760 (2.2760)	loss 6.7984 (6.7984)	miou 0.1860	grad_norm 75.4982 (75.4982)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:11:01 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [39/200][10/27]	eta 0:00:34 lr 0.000023	time 2.0174 (2.0487)	loss 6.4225 (7.5539)	miou 0.2032	grad_norm 44.7237 (64.6115)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:11:21 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [39/200][20/27]	eta 0:00:14 lr 0.000030	time 2.1709 (2.0305)	loss 9.0629 (7.3346)	miou 0.1979	grad_norm 34.1336 (61.5942)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:11:33 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 39 training takes 0:00:53
[32m[2025-07-13 20:11:33 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 20:11:43 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.2067%
[32m[2025-07-13 20:11:43 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2528%
[32m[2025-07-13 20:11:45 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [40/200][0/27]	eta 0:01:03 lr 0.000035	time 2.3531 (2.3531)	loss 8.5956 (8.5956)	miou 0.1985	grad_norm 21.6803 (21.6803)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:12:06 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [40/200][10/27]	eta 0:00:35 lr 0.000043	time 1.9453 (2.0701)	loss 7.8092 (7.2906)	miou 0.2022	grad_norm 41.5586 (45.5924)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:12:26 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [40/200][20/27]	eta 0:00:14 lr 0.000051	time 2.0173 (2.0236)	loss 8.6043 (7.1143)	miou 0.2127	grad_norm 46.5152 (52.2103)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:12:38 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 40 training takes 0:00:54
[32m[2025-07-13 20:12:38 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
40
[32m[2025-07-13 20:12:48 3DDETR.yaml][33m(utils_help.py 85)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saving......
[32m[2025-07-13 20:12:48 3DDETR.yaml][33m(utils_help.py 87)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saved !!!
[32m[2025-07-13 20:12:48 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.2707%
[32m[2025-07-13 20:12:48 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2707%
[32m[2025-07-13 20:12:50 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [41/200][0/27]	eta 0:00:45 lr 0.000056	time 1.6888 (1.6888)	loss 5.8775 (5.8775)	miou 0.2664	grad_norm 111.2558 (111.2558)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:13:10 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [41/200][10/27]	eta 0:00:33 lr 0.000064	time 2.1259 (1.9481)	loss 8.0020 (7.3442)	miou 0.2380	grad_norm 46.1824 (53.3896)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:13:29 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [41/200][20/27]	eta 0:00:13 lr 0.000071	time 2.1448 (1.9476)	loss 7.8310 (7.4910)	miou 0.2432	grad_norm 32.9315 (62.8137)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:13:42 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 41 training takes 0:00:53
[32m[2025-07-13 20:13:42 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 20:13:52 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.2103%
[32m[2025-07-13 20:13:52 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2707%
[32m[2025-07-13 20:13:54 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [42/200][0/27]	eta 0:01:03 lr 0.000076	time 2.3612 (2.3612)	loss 5.7513 (5.7513)	miou 0.2217	grad_norm 38.9454 (38.9454)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:14:13 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [42/200][10/27]	eta 0:00:33 lr 0.000082	time 2.0106 (1.9495)	loss 9.3686 (7.2415)	miou 0.2175	grad_norm 23.3371 (40.1059)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:14:34 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [42/200][20/27]	eta 0:00:13 lr 0.000088	time 1.8447 (1.9801)	loss 6.8986 (7.2671)	miou 0.2186	grad_norm 37.7419 (82.5778)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:14:45 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 42 training takes 0:00:53
[32m[2025-07-13 20:14:45 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 20:14:56 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.2625%
[32m[2025-07-13 20:14:56 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2707%
[32m[2025-07-13 20:14:59 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [43/200][0/27]	eta 0:01:21 lr 0.000091	time 3.0040 (3.0040)	loss 6.2516 (6.2516)	miou 0.2621	grad_norm 17.3686 (17.3686)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:15:20 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [43/200][10/27]	eta 0:00:36 lr 0.000095	time 2.0083 (2.1706)	loss 9.7002 (7.9585)	miou 0.2190	grad_norm 57.0889 (54.6165)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:15:39 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [43/200][20/27]	eta 0:00:14 lr 0.000098	time 1.9204 (2.0530)	loss 5.2966 (7.7023)	miou 0.2117	grad_norm 26.6669 (50.1233)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:15:50 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 43 training takes 0:00:54
[32m[2025-07-13 20:15:50 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
43
[32m[2025-07-13 20:16:01 3DDETR.yaml][33m(utils_help.py 85)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saving......
[32m[2025-07-13 20:16:01 3DDETR.yaml][33m(utils_help.py 87)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saved !!!
[32m[2025-07-13 20:16:01 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.2843%
[32m[2025-07-13 20:16:01 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2843%
[32m[2025-07-13 20:16:04 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [44/200][0/27]	eta 0:01:26 lr 0.000099	time 3.2036 (3.2036)	loss 8.2209 (8.2209)	miou 0.2779	grad_norm 40.0241 (40.0241)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:16:24 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [44/200][10/27]	eta 0:00:35 lr 0.000100	time 1.8959 (2.0930)	loss 8.7461 (7.5416)	miou 0.2276	grad_norm 50.0725 (106.3063)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:16:43 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [44/200][20/27]	eta 0:00:14 lr 0.000100	time 2.1876 (2.0135)	loss 7.5261 (7.7833)	miou 0.2073	grad_norm 69.9195 (89.5303)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:16:56 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 44 training takes 0:00:54
[32m[2025-07-13 20:16:56 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 20:17:06 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1926%
[32m[2025-07-13 20:17:06 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2843%
[32m[2025-07-13 20:17:08 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [45/200][0/27]	eta 0:01:01 lr 0.000098	time 2.2659 (2.2659)	loss 6.6812 (6.6812)	miou 0.2005	grad_norm 29.2009 (29.2009)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:17:29 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [45/200][10/27]	eta 0:00:36 lr 0.000096	time 2.2066 (2.1208)	loss 7.1210 (7.7048)	miou 0.1876	grad_norm 21.4682 (87.9964)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:17:48 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [45/200][20/27]	eta 0:00:14 lr 0.000092	time 2.2810 (2.0175)	loss 5.9684 (7.4190)	miou 0.1983	grad_norm 46.9997 (71.4160)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:18:00 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 45 training takes 0:00:54
[32m[2025-07-13 20:18:00 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 20:18:10 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.2467%
[32m[2025-07-13 20:18:10 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2843%
[32m[2025-07-13 20:18:13 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [46/200][0/27]	eta 0:01:15 lr 0.000089	time 2.7783 (2.7783)	loss 5.7063 (5.7063)	miou 0.2451	grad_norm 30.5169 (30.5169)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:18:33 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [46/200][10/27]	eta 0:00:34 lr 0.000084	time 1.8972 (2.0333)	loss 10.6622 (7.0715)	miou 0.2237	grad_norm 43.8185 (53.3612)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:18:52 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [46/200][20/27]	eta 0:00:13 lr 0.000077	time 1.7283 (1.9840)	loss 6.4936 (6.8836)	miou 0.2163	grad_norm 20.0418 (52.7100)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:19:04 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 46 training takes 0:00:53
[32m[2025-07-13 20:19:04 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 20:19:15 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1530%
[32m[2025-07-13 20:19:15 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2843%
[32m[2025-07-13 20:19:17 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [47/200][0/27]	eta 0:00:58 lr 0.000073	time 2.1771 (2.1771)	loss 9.3747 (9.3747)	miou 0.1506	grad_norm 12.9385 (12.9385)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:19:37 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [47/200][10/27]	eta 0:00:35 lr 0.000065	time 1.8164 (2.0633)	loss 9.3336 (7.8672)	miou 0.1727	grad_norm 18.9478 (39.6343)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:19:57 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [47/200][20/27]	eta 0:00:14 lr 0.000058	time 2.0155 (2.0156)	loss 6.1025 (7.8770)	miou 0.1943	grad_norm 387.7383 (60.0552)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:20:08 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 47 training takes 0:00:53
[32m[2025-07-13 20:20:08 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 20:20:19 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1682%
[32m[2025-07-13 20:20:19 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2843%
[32m[2025-07-13 20:20:21 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [48/200][0/27]	eta 0:00:58 lr 0.000052	time 2.1613 (2.1613)	loss 8.8113 (8.8113)	miou 0.1735	grad_norm 7829.9292 (7829.9292)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:20:41 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [48/200][10/27]	eta 0:00:34 lr 0.000045	time 2.0463 (2.0090)	loss 6.1632 (7.5575)	miou 0.1982	grad_norm 18.5014 (755.4642)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:21:01 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [48/200][20/27]	eta 0:00:13 lr 0.000037	time 1.7937 (1.9922)	loss 5.1473 (7.1582)	miou 0.2187	grad_norm 26.6972 (415.4054)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:21:13 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 48 training takes 0:00:54
[32m[2025-07-13 20:21:13 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 20:21:23 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1542%
[32m[2025-07-13 20:21:23 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2843%
[32m[2025-07-13 20:21:26 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [49/200][0/27]	eta 0:01:03 lr 0.000032	time 2.3698 (2.3698)	loss 6.9957 (6.9957)	miou 0.1641	grad_norm 41.0188 (41.0188)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:21:46 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [49/200][10/27]	eta 0:00:35 lr 0.000025	time 1.7478 (2.0722)	loss 5.9947 (6.9109)	miou 0.1994	grad_norm 40.2039 (37.7745)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:22:06 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [49/200][20/27]	eta 0:00:14 lr 0.000018	time 1.9763 (2.0216)	loss 5.3836 (7.0595)	miou 0.2153	grad_norm 131.6047 (48.4104)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:22:18 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 49 training takes 0:00:54
[32m[2025-07-13 20:22:18 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 20:22:29 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.2221%
[32m[2025-07-13 20:22:29 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2843%
[32m[2025-07-13 20:22:31 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [50/200][0/27]	eta 0:00:55 lr 0.000014	time 2.0665 (2.0665)	loss 5.9916 (5.9916)	miou 0.2207	grad_norm 62.1503 (62.1503)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:22:51 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [50/200][10/27]	eta 0:00:33 lr 0.000009	time 2.1195 (1.9891)	loss 7.5281 (6.5938)	miou 0.2146	grad_norm 19.9799 (39.0006)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:23:11 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [50/200][20/27]	eta 0:00:14 lr 0.000005	time 2.5330 (2.0236)	loss 6.8612 (6.7202)	miou 0.2324	grad_norm 22.4387 (35.7257)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:23:23 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 50 training takes 0:00:54
[32m[2025-07-13 20:23:23 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 20:23:34 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.2414%
[32m[2025-07-13 20:23:34 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2843%
[32m[2025-07-13 20:23:36 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [51/200][0/27]	eta 0:00:59 lr 0.000003	time 2.1948 (2.1948)	loss 10.3371 (10.3371)	miou 0.2346	grad_norm 52.7298 (52.7298)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:23:56 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [51/200][10/27]	eta 0:00:33 lr 0.000001	time 2.0210 (1.9666)	loss 8.3441 (8.5274)	miou 0.2038	grad_norm 23.0519 (38.3973)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:24:16 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [51/200][20/27]	eta 0:00:13 lr 0.000000	time 1.9298 (1.9954)	loss 7.6633 (7.9375)	miou 0.2072	grad_norm 17.1422 (44.9084)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:24:27 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 51 training takes 0:00:53
[32m[2025-07-13 20:24:27 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 20:24:38 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.2261%
[32m[2025-07-13 20:24:38 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2843%
[32m[2025-07-13 20:24:40 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [52/200][0/27]	eta 0:01:15 lr 0.000000	time 2.7921 (2.7921)	loss 6.4020 (6.4020)	miou 0.2155	grad_norm 34.1899 (34.1899)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:24:59 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [52/200][10/27]	eta 0:00:33 lr 0.000001	time 1.5708 (1.9746)	loss 5.7635 (6.6176)	miou 0.2316	grad_norm 19.4240 (46.5331)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:25:20 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [52/200][20/27]	eta 0:00:14 lr 0.000004	time 2.0210 (2.0308)	loss 6.3318 (6.9914)	miou 0.2378	grad_norm 18.5734 (42.1759)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:25:33 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 52 training takes 0:00:55
[32m[2025-07-13 20:25:33 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 20:25:43 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1839%
[32m[2025-07-13 20:25:43 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2843%
[32m[2025-07-13 20:25:46 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [53/200][0/27]	eta 0:01:15 lr 0.000006	time 2.8085 (2.8085)	loss 9.2328 (9.2328)	miou 0.1790	grad_norm 53.1478 (53.1478)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:26:06 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [53/200][10/27]	eta 0:00:36 lr 0.000010	time 2.0327 (2.1196)	loss 5.4756 (7.1167)	miou 0.2182	grad_norm 34.5689 (33.1861)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:26:26 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [53/200][20/27]	eta 0:00:14 lr 0.000016	time 2.5861 (2.0467)	loss 7.5003 (6.7771)	miou 0.2209	grad_norm 31.8755 (35.2770)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:26:37 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 53 training takes 0:00:54
[32m[2025-07-13 20:26:37 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 20:26:48 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1737%
[32m[2025-07-13 20:26:48 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2843%
[32m[2025-07-13 20:26:50 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [54/200][0/27]	eta 0:01:10 lr 0.000020	time 2.6139 (2.6139)	loss 6.9576 (6.9576)	miou 0.1697	grad_norm 37.5792 (37.5792)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:27:11 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [54/200][10/27]	eta 0:00:36 lr 0.000027	time 2.4679 (2.1194)	loss 9.1434 (7.4482)	miou 0.2033	grad_norm 18.1664 (38.7229)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:27:32 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [54/200][20/27]	eta 0:00:14 lr 0.000034	time 2.1372 (2.1059)	loss 5.2925 (7.1652)	miou 0.2152	grad_norm 22.5220 (73.4955)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:27:43 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 54 training takes 0:00:55
[32m[2025-07-13 20:27:43 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 20:27:54 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.2455%
[32m[2025-07-13 20:27:54 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2843%
[32m[2025-07-13 20:27:56 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [55/200][0/27]	eta 0:01:10 lr 0.000039	time 2.6221 (2.6221)	loss 7.7668 (7.7668)	miou 0.2556	grad_norm 28.8004 (28.8004)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:28:16 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [55/200][10/27]	eta 0:00:34 lr 0.000047	time 1.8434 (2.0068)	loss 9.4282 (7.7552)	miou 0.2521	grad_norm 35.4319 (41.7711)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:28:36 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [55/200][20/27]	eta 0:00:14 lr 0.000055	time 2.0771 (2.0058)	loss 5.5849 (7.4950)	miou 0.2256	grad_norm 30.3237 (38.0228)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:28:48 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 55 training takes 0:00:54
[32m[2025-07-13 20:28:48 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 20:28:59 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1934%
[32m[2025-07-13 20:28:59 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2843%
[32m[2025-07-13 20:29:02 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [56/200][0/27]	eta 0:01:17 lr 0.000060	time 2.8751 (2.8751)	loss 6.2633 (6.2633)	miou 0.1990	grad_norm 29.0527 (29.0527)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:29:21 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [56/200][10/27]	eta 0:00:34 lr 0.000068	time 1.7184 (2.0433)	loss 6.5442 (7.3966)	miou 0.1973	grad_norm 31.9908 (34.6670)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:29:41 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [56/200][20/27]	eta 0:00:14 lr 0.000075	time 2.2132 (2.0194)	loss 5.6292 (6.9539)	miou 0.2147	grad_norm 41.5539 (48.4969)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:29:53 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 56 training takes 0:00:53
[32m[2025-07-13 20:29:53 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 20:30:03 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.2188%
[32m[2025-07-13 20:30:03 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2843%
[32m[2025-07-13 20:30:06 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [57/200][0/27]	eta 0:01:12 lr 0.000079	time 2.6867 (2.6867)	loss 8.1646 (8.1646)	miou 0.2249	grad_norm 37.6186 (37.6186)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:30:25 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [57/200][10/27]	eta 0:00:34 lr 0.000085	time 1.7924 (2.0081)	loss 7.7461 (7.2760)	miou 0.2151	grad_norm 30.6837 (52.7051)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:30:45 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [57/200][20/27]	eta 0:00:14 lr 0.000090	time 1.6302 (2.0277)	loss 7.2882 (7.3617)	miou 0.2128	grad_norm 65.5448 (49.4947)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:30:57 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 57 training takes 0:00:54
[32m[2025-07-13 20:30:57 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 20:31:07 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.2201%
[32m[2025-07-13 20:31:07 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2843%
[32m[2025-07-13 20:31:10 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [58/200][0/27]	eta 0:01:10 lr 0.000093	time 2.6277 (2.6277)	loss 8.1590 (8.1590)	miou 0.2241	grad_norm 24.6373 (24.6373)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:31:30 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [58/200][10/27]	eta 0:00:35 lr 0.000097	time 2.2489 (2.0685)	loss 5.7085 (7.2167)	miou 0.2047	grad_norm 22.5111 (33.2423)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:31:51 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [58/200][20/27]	eta 0:00:14 lr 0.000099	time 2.0544 (2.0789)	loss 6.6513 (7.2789)	miou 0.2145	grad_norm 19.0869 (44.7978)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:32:03 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 58 training takes 0:00:55
[32m[2025-07-13 20:32:03 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 20:32:14 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.2009%
[32m[2025-07-13 20:32:14 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2843%
[32m[2025-07-13 20:32:16 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [59/200][0/27]	eta 0:00:57 lr 0.000100	time 2.1304 (2.1304)	loss 6.4726 (6.4726)	miou 0.2070	grad_norm 100.3953 (100.3953)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:32:37 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [59/200][10/27]	eta 0:00:35 lr 0.000100	time 2.5235 (2.1169)	loss 7.8598 (7.8494)	miou 0.1952	grad_norm 26.3606 (70.0195)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:32:56 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [59/200][20/27]	eta 0:00:14 lr 0.000099	time 1.9740 (2.0231)	loss 5.5946 (7.4160)	miou 0.2090	grad_norm 32.0040 (54.1455)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:33:08 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 59 training takes 0:00:54
[32m[2025-07-13 20:33:08 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 20:33:19 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.2489%
[32m[2025-07-13 20:33:19 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2843%
[32m[2025-07-13 20:33:22 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [60/200][0/27]	eta 0:01:27 lr 0.000097	time 3.2583 (3.2583)	loss 6.5207 (6.5207)	miou 0.2596	grad_norm 84.3477 (84.3477)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:33:42 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [60/200][10/27]	eta 0:00:35 lr 0.000094	time 2.1726 (2.1057)	loss 5.7241 (7.3369)	miou 0.2393	grad_norm 56.2435 (3629.1738)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:34:02 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [60/200][20/27]	eta 0:00:14 lr 0.000090	time 1.7759 (2.0744)	loss 7.3580 (7.1873)	miou 0.2236	grad_norm 48.6453 (1919.1185)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:34:14 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 60 training takes 0:00:55
[32m[2025-07-13 20:34:14 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 20:34:25 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.2577%
[32m[2025-07-13 20:34:25 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2843%
[32m[2025-07-13 20:34:27 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [61/200][0/27]	eta 0:01:07 lr 0.000086	time 2.5091 (2.5091)	loss 7.7931 (7.7931)	miou 0.2562	grad_norm 33.9315 (33.9315)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:34:47 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [61/200][10/27]	eta 0:00:34 lr 0.000081	time 2.1985 (2.0271)	loss 7.7670 (7.3666)	miou 0.2508	grad_norm 45.1389 (41.5423)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:35:07 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [61/200][20/27]	eta 0:00:14 lr 0.000074	time 1.7389 (2.0242)	loss 8.2824 (7.5459)	miou 0.2309	grad_norm 18.9885 (53.0567)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:35:19 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 61 training takes 0:00:54
[32m[2025-07-13 20:35:19 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 20:35:29 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.2630%
[32m[2025-07-13 20:35:29 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2843%
[32m[2025-07-13 20:35:32 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [62/200][0/27]	eta 0:01:05 lr 0.000069	time 2.4292 (2.4292)	loss 8.2602 (8.2602)	miou 0.2534	grad_norm 41.1719 (41.1719)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:35:51 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [62/200][10/27]	eta 0:00:34 lr 0.000062	time 2.0016 (2.0196)	loss 4.4478 (6.8979)	miou 0.2401	grad_norm 60.1673 (51.9172)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:36:11 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [62/200][20/27]	eta 0:00:13 lr 0.000054	time 2.0636 (1.9780)	loss 6.7678 (7.4801)	miou 0.2207	grad_norm 20.9177 (47.1193)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:36:23 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 62 training takes 0:00:53
[32m[2025-07-13 20:36:23 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 20:36:33 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.2554%
[32m[2025-07-13 20:36:33 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2843%
[32m[2025-07-13 20:36:36 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [63/200][0/27]	eta 0:01:09 lr 0.000048	time 2.5881 (2.5881)	loss 6.9960 (6.9960)	miou 0.2434	grad_norm 637.4335 (637.4335)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:36:55 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [63/200][10/27]	eta 0:00:34 lr 0.000041	time 1.9208 (2.0381)	loss 10.0299 (7.3319)	miou 0.2217	grad_norm 42.4749 (121.9596)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:37:14 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [63/200][20/27]	eta 0:00:13 lr 0.000033	time 2.0477 (1.9685)	loss 5.2609 (7.2626)	miou 0.2276	grad_norm 32.2834 (76.5733)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:37:27 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 63 training takes 0:00:53
[32m[2025-07-13 20:37:27 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 20:37:37 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.2576%
[32m[2025-07-13 20:37:37 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2843%
[32m[2025-07-13 20:37:39 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [64/200][0/27]	eta 0:00:57 lr 0.000028	time 2.1331 (2.1331)	loss 5.3495 (5.3495)	miou 0.2653	grad_norm 48.7413 (48.7413)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:38:00 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [64/200][10/27]	eta 0:00:35 lr 0.000021	time 2.2471 (2.0652)	loss 7.3413 (7.1747)	miou 0.2367	grad_norm 27.5834 (736.0867)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:38:20 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [64/200][20/27]	eta 0:00:14 lr 0.000015	time 1.8158 (2.0359)	loss 7.6907 (7.4550)	miou 0.2437	grad_norm 41.8664 (407.8054)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:38:31 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 64 training takes 0:00:54
[32m[2025-07-13 20:38:31 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 20:38:42 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.2389%
[32m[2025-07-13 20:38:42 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2843%
[32m[2025-07-13 20:38:44 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [65/200][0/27]	eta 0:01:19 lr 0.000011	time 2.9309 (2.9309)	loss 5.5685 (5.5685)	miou 0.2492	grad_norm 22.2534 (22.2534)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:39:05 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [65/200][10/27]	eta 0:00:36 lr 0.000007	time 2.1921 (2.1211)	loss 4.9239 (6.7255)	miou 0.2384	grad_norm 67.0807 (63.6417)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:39:24 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [65/200][20/27]	eta 0:00:14 lr 0.000004	time 1.7061 (2.0278)	loss 6.3819 (7.1540)	miou 0.2221	grad_norm 34.2413 (51.8272)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:39:37 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 65 training takes 0:00:55
[32m[2025-07-13 20:39:37 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 20:39:47 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.2579%
[32m[2025-07-13 20:39:47 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2843%
[32m[2025-07-13 20:39:49 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [66/200][0/27]	eta 0:00:52 lr 0.000002	time 1.9588 (1.9588)	loss 5.6355 (5.6355)	miou 0.2581	grad_norm 32.4430 (32.4430)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:40:10 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [66/200][10/27]	eta 0:00:34 lr 0.000000	time 2.4224 (2.0556)	loss 8.9796 (6.9985)	miou 0.2447	grad_norm 53.5815 (43.1534)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:40:30 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [66/200][20/27]	eta 0:00:14 lr 0.000000	time 2.0846 (2.0325)	loss 8.3962 (7.1128)	miou 0.2483	grad_norm 18.8598 (39.3129)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:40:41 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 66 training takes 0:00:53
[32m[2025-07-13 20:40:41 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 20:40:51 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.2448%
[32m[2025-07-13 20:40:51 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2843%
[32m[2025-07-13 20:40:54 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [67/200][0/27]	eta 0:01:03 lr 0.000001	time 2.3643 (2.3643)	loss 7.7004 (7.7004)	miou 0.2446	grad_norm 79.0971 (79.0971)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:41:14 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [67/200][10/27]	eta 0:00:35 lr 0.000002	time 2.1156 (2.0595)	loss 6.8931 (7.5724)	miou 0.2361	grad_norm 33.4814 (31.2098)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:41:34 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [67/200][20/27]	eta 0:00:14 lr 0.000005	time 1.7197 (2.0597)	loss 5.2440 (7.2210)	miou 0.2355	grad_norm 28.2168 (41.4121)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:41:46 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 67 training takes 0:00:54
[32m[2025-07-13 20:41:46 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 20:41:57 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.2586%
[32m[2025-07-13 20:41:57 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2843%
[32m[2025-07-13 20:41:59 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [68/200][0/27]	eta 0:01:17 lr 0.000008	time 2.8678 (2.8678)	loss 6.1688 (6.1688)	miou 0.2537	grad_norm 18.4287 (18.4287)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:42:18 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [68/200][10/27]	eta 0:00:33 lr 0.000013	time 1.9525 (1.9610)	loss 8.8024 (7.2310)	miou 0.2473	grad_norm 15.1715 (35.7021)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:42:38 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [68/200][20/27]	eta 0:00:13 lr 0.000019	time 1.7841 (1.9865)	loss 5.7290 (7.0037)	miou 0.2494	grad_norm 74.8241 (137.4365)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:42:51 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 68 training takes 0:00:54
[32m[2025-07-13 20:42:51 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 20:43:01 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.2417%
[32m[2025-07-13 20:43:01 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2843%
[32m[2025-07-13 20:43:04 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [69/200][0/27]	eta 0:01:15 lr 0.000023	time 2.8074 (2.8074)	loss 6.6045 (6.6045)	miou 0.2453	grad_norm 19.1723 (19.1723)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:43:24 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [69/200][10/27]	eta 0:00:35 lr 0.000030	time 2.0913 (2.1100)	loss 7.4009 (6.9251)	miou 0.2449	grad_norm 17.1435 (56.0046)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:43:45 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [69/200][20/27]	eta 0:00:14 lr 0.000038	time 2.0294 (2.0659)	loss 7.3526 (6.7180)	miou 0.2505	grad_norm 38.0997 (49.8855)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:43:56 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 69 training takes 0:00:54
[32m[2025-07-13 20:43:56 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 20:44:06 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.2560%
[32m[2025-07-13 20:44:06 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2843%
[32m[2025-07-13 20:44:09 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [70/200][0/27]	eta 0:01:02 lr 0.000043	time 2.2973 (2.2973)	loss 5.2204 (5.2204)	miou 0.2542	grad_norm 81.3247 (81.3247)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:44:29 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [70/200][10/27]	eta 0:00:34 lr 0.000051	time 1.6096 (2.0475)	loss 4.7223 (7.3501)	miou 0.2232	grad_norm 27.1944 (69.5594)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:44:48 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [70/200][20/27]	eta 0:00:13 lr 0.000059	time 1.9266 (1.9850)	loss 7.9837 (7.5114)	miou 0.2247	grad_norm 21.0894 (51.6330)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:45:00 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 70 training takes 0:00:53
[32m[2025-07-13 20:45:00 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 20:45:10 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.2793%
[32m[2025-07-13 20:45:10 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.2843%
[32m[2025-07-13 20:45:13 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [71/200][0/27]	eta 0:01:03 lr 0.000064	time 2.3406 (2.3406)	loss 6.1270 (6.1270)	miou 0.2776	grad_norm 92.5429 (92.5429)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:45:33 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [71/200][10/27]	eta 0:00:34 lr 0.000071	time 1.7998 (2.0556)	loss 5.5772 (6.4840)	miou 0.2656	grad_norm 22.1038 (57.1098)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:45:51 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [71/200][20/27]	eta 0:00:13 lr 0.000078	time 1.9125 (1.9485)	loss 6.9574 (6.5430)	miou 0.2702	grad_norm 20.4865 (41.8885)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:46:03 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 71 training takes 0:00:52
[32m[2025-07-13 20:46:03 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
71
[32m[2025-07-13 20:46:13 3DDETR.yaml][33m(utils_help.py 85)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saving......
[32m[2025-07-13 20:46:14 3DDETR.yaml][33m(utils_help.py 87)[39m: INFO /home-local2/akath.extra.nobkp/sereact/3DDETR.yaml/123/ckpt_epoch_best_new_resume.pth saved !!!
[32m[2025-07-13 20:46:14 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.3018%
[32m[2025-07-13 20:46:14 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.3018%
[32m[2025-07-13 20:46:16 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [72/200][0/27]	eta 0:01:06 lr 0.000082	time 2.4494 (2.4494)	loss 7.0880 (7.0880)	miou 0.2982	grad_norm 34.7673 (34.7673)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:46:37 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [72/200][10/27]	eta 0:00:35 lr 0.000088	time 2.0237 (2.0972)	loss 5.6540 (6.1815)	miou 0.2665	grad_norm 52.5434 (43.3413)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:46:57 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [72/200][20/27]	eta 0:00:14 lr 0.000093	time 1.9337 (2.0428)	loss 5.5595 (6.5370)	miou 0.2538	grad_norm 50.3147 (82.5646)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:47:09 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 72 training takes 0:00:54
[32m[2025-07-13 20:47:09 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 20:47:19 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.2141%
[32m[2025-07-13 20:47:19 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.3018%
[32m[2025-07-13 20:47:21 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [73/200][0/27]	eta 0:00:52 lr 0.000095	time 1.9574 (1.9574)	loss 6.2485 (6.2485)	miou 0.2128	grad_norm 78.4830 (78.4830)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:47:40 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [73/200][10/27]	eta 0:00:31 lr 0.000098	time 1.7673 (1.8677)	loss 8.2921 (7.0957)	miou 0.2159	grad_norm 34.6894 (29.8053)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:48:01 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [73/200][20/27]	eta 0:00:13 lr 0.000100	time 2.4773 (1.9932)	loss 8.1133 (7.4613)	miou 0.2115	grad_norm 27.8435 (53.5200)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:48:14 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 73 training takes 0:00:54
[32m[2025-07-13 20:48:14 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 20:48:24 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.2346%
[32m[2025-07-13 20:48:24 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.3018%
[32m[2025-07-13 20:48:27 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [74/200][0/27]	eta 0:01:17 lr 0.000100	time 2.8852 (2.8852)	loss 5.3653 (5.3653)	miou 0.2310	grad_norm 32.6705 (32.6705)	loss_scale 65536.0000 (65536.0000)	mem 5971MB
[32m[2025-07-13 20:48:47 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [74/200][10/27]	eta 0:00:35 lr 0.000100	time 1.8565 (2.0677)	loss 7.3770 (7.6901)	miou 0.2233	grad_norm 12.3645 (28.5574)	loss_scale 131072.0000 (125114.1818)	mem 5971MB
[32m[2025-07-13 20:49:06 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [74/200][20/27]	eta 0:00:14 lr 0.000098	time 2.1272 (2.0176)	loss 7.7838 (7.3781)	miou 0.2382	grad_norm 23.7359 (36.6374)	loss_scale 131072.0000 (127951.2381)	mem 5971MB
[32m[2025-07-13 20:49:18 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 74 training takes 0:00:54
[32m[2025-07-13 20:49:18 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 20:49:29 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.2454%
[32m[2025-07-13 20:49:29 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.3018%
[32m[2025-07-13 20:49:31 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [75/200][0/27]	eta 0:01:12 lr 0.000096	time 2.6774 (2.6774)	loss 7.4843 (7.4843)	miou 0.2396	grad_norm 22.8341 (22.8341)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 20:49:51 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [75/200][10/27]	eta 0:00:34 lr 0.000092	time 1.8484 (2.0557)	loss 8.3977 (7.4429)	miou 0.2232	grad_norm 24.7623 (38.1826)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 20:50:11 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [75/200][20/27]	eta 0:00:14 lr 0.000088	time 1.9207 (2.0267)	loss 8.1144 (6.8450)	miou 0.2358	grad_norm 32.6236 (47.8736)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 20:50:23 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 75 training takes 0:00:54
[32m[2025-07-13 20:50:23 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 20:50:34 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1748%
[32m[2025-07-13 20:50:34 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.3018%
[32m[2025-07-13 20:50:36 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [76/200][0/27]	eta 0:01:09 lr 0.000084	time 2.5900 (2.5900)	loss 5.3899 (5.3899)	miou 0.1749	grad_norm 53.7911 (53.7911)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 20:50:57 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [76/200][10/27]	eta 0:00:36 lr 0.000077	time 1.9491 (2.1483)	loss 6.8710 (6.8562)	miou 0.1874	grad_norm 31.0238 (42.6877)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 20:51:17 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [76/200][20/27]	eta 0:00:14 lr 0.000071	time 2.0501 (2.0573)	loss 6.2854 (7.1083)	miou 0.1982	grad_norm 20.7103 (44.5830)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 20:51:29 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 76 training takes 0:00:55
[32m[2025-07-13 20:51:29 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 20:51:40 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.2671%
[32m[2025-07-13 20:51:40 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.3018%
[32m[2025-07-13 20:51:43 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [77/200][0/27]	eta 0:01:16 lr 0.000065	time 2.8442 (2.8442)	loss 6.4807 (6.4807)	miou 0.2713	grad_norm 263.1127 (263.1127)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 20:52:03 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [77/200][10/27]	eta 0:00:35 lr 0.000058	time 1.7629 (2.0922)	loss 5.1158 (6.5981)	miou 0.2568	grad_norm 65.3061 (89.8349)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 20:52:22 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [77/200][20/27]	eta 0:00:14 lr 0.000050	time 1.9102 (2.0310)	loss 9.9915 (7.0698)	miou 0.2492	grad_norm 70.8953 (64.8069)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 20:52:35 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 77 training takes 0:00:54
[32m[2025-07-13 20:52:35 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 20:52:45 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1949%
[32m[2025-07-13 20:52:45 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.3018%
[32m[2025-07-13 20:52:48 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [78/200][0/27]	eta 0:01:06 lr 0.000045	time 2.4462 (2.4462)	loss 5.7648 (5.7648)	miou 0.2110	grad_norm 73.3389 (73.3389)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 20:53:06 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [78/200][10/27]	eta 0:00:32 lr 0.000037	time 1.7977 (1.9361)	loss 6.9205 (6.8290)	miou 0.2263	grad_norm 18.8428 (33.2419)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 20:53:27 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [78/200][20/27]	eta 0:00:14 lr 0.000029	time 1.9685 (2.0082)	loss 6.6986 (6.8809)	miou 0.2310	grad_norm 58.2759 (33.0618)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 20:53:39 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 78 training takes 0:00:54
[32m[2025-07-13 20:53:39 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 20:53:50 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.2266%
[32m[2025-07-13 20:53:50 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.3018%
[32m[2025-07-13 20:53:52 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [79/200][0/27]	eta 0:01:09 lr 0.000025	time 2.5716 (2.5716)	loss 6.7856 (6.7856)	miou 0.2423	grad_norm 24.1924 (24.1924)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 20:54:12 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [79/200][10/27]	eta 0:00:34 lr 0.000018	time 2.3862 (2.0534)	loss 5.5747 (6.7352)	miou 0.2548	grad_norm 15.5945 (25.0297)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 20:54:33 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [79/200][20/27]	eta 0:00:14 lr 0.000012	time 2.0555 (2.0359)	loss 5.2364 (6.8609)	miou 0.2617	grad_norm 36.5542 (29.9800)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 20:54:44 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 79 training takes 0:00:54
[32m[2025-07-13 20:54:44 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 20:54:55 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.2714%
[32m[2025-07-13 20:54:55 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.3018%
[32m[2025-07-13 20:54:57 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [80/200][0/27]	eta 0:01:02 lr 0.000009	time 2.3140 (2.3140)	loss 5.5626 (5.5626)	miou 0.2845	grad_norm 78.5664 (78.5664)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 20:55:18 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [80/200][10/27]	eta 0:00:35 lr 0.000005	time 2.1050 (2.1118)	loss 6.5849 (5.9972)	miou 0.2858	grad_norm 18.8472 (28.6652)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 20:55:38 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [80/200][20/27]	eta 0:00:14 lr 0.000002	time 2.0942 (2.0490)	loss 5.4690 (6.2755)	miou 0.2601	grad_norm 16.7124 (27.6170)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 20:55:50 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 80 training takes 0:00:55
[32m[2025-07-13 20:55:50 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 20:56:00 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.2081%
[32m[2025-07-13 20:56:00 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.3018%
[32m[2025-07-13 20:56:03 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [81/200][0/27]	eta 0:01:01 lr 0.000001	time 2.2676 (2.2676)	loss 7.1820 (7.1820)	miou 0.2062	grad_norm 20.1066 (20.1066)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 20:56:23 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [81/200][10/27]	eta 0:00:34 lr 0.000000	time 2.1883 (2.0114)	loss 9.4087 (6.7331)	miou 0.2100	grad_norm 15.9612 (36.8252)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 20:56:43 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [81/200][20/27]	eta 0:00:14 lr 0.000000	time 2.0953 (2.0345)	loss 8.0083 (6.8021)	miou 0.2191	grad_norm 16.9559 (38.5290)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 20:56:54 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 81 training takes 0:00:53
[32m[2025-07-13 20:56:54 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 20:57:05 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.2547%
[32m[2025-07-13 20:57:05 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.3018%
[32m[2025-07-13 20:57:07 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [82/200][0/27]	eta 0:00:57 lr 0.000001	time 2.1425 (2.1425)	loss 7.0731 (7.0731)	miou 0.2555	grad_norm 17.4036 (17.4036)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 20:57:27 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [82/200][10/27]	eta 0:00:34 lr 0.000004	time 1.8421 (2.0111)	loss 7.1833 (6.7690)	miou 0.2453	grad_norm 23.9900 (40.9566)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 20:57:46 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [82/200][20/27]	eta 0:00:13 lr 0.000007	time 1.8708 (1.9866)	loss 8.3795 (6.9961)	miou 0.2415	grad_norm 88.7972 (37.2672)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 20:57:59 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 82 training takes 0:00:53
[32m[2025-07-13 20:57:59 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 20:58:09 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.2634%
[32m[2025-07-13 20:58:09 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.3018%
[32m[2025-07-13 20:58:11 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [83/200][0/27]	eta 0:00:55 lr 0.000010	time 2.0478 (2.0478)	loss 8.6267 (8.6267)	miou 0.2593	grad_norm 21.3562 (21.3562)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 20:58:31 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [83/200][10/27]	eta 0:00:34 lr 0.000016	time 1.9561 (2.0117)	loss 5.4506 (7.1702)	miou 0.2493	grad_norm 149.0513 (37.4610)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 20:58:53 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [83/200][20/27]	eta 0:00:14 lr 0.000022	time 1.7743 (2.0762)	loss 7.9156 (7.3791)	miou 0.2368	grad_norm 23.0272 (31.1926)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 20:59:04 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 83 training takes 0:00:55
[32m[2025-07-13 20:59:04 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 20:59:15 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1959%
[32m[2025-07-13 20:59:15 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.3018%
[32m[2025-07-13 20:59:17 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [84/200][0/27]	eta 0:01:07 lr 0.000027	time 2.5145 (2.5145)	loss 8.7018 (8.7018)	miou 0.1901	grad_norm 42.0041 (42.0041)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 20:59:37 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [84/200][10/27]	eta 0:00:34 lr 0.000034	time 1.8211 (2.0008)	loss 5.9654 (7.0873)	miou 0.2164	grad_norm 38.8762 (38.4097)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 20:59:57 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [84/200][20/27]	eta 0:00:14 lr 0.000041	time 1.9272 (2.0312)	loss 7.9286 (7.3571)	miou 0.2260	grad_norm 33.5609 (55.5579)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 21:00:10 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 84 training takes 0:00:55
[32m[2025-07-13 21:00:10 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 21:00:20 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1949%
[32m[2025-07-13 21:00:20 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.3018%
[32m[2025-07-13 21:00:23 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [85/200][0/27]	eta 0:01:24 lr 0.000047	time 3.1408 (3.1408)	loss 4.8455 (4.8455)	miou 0.1954	grad_norm 86.8584 (86.8584)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 21:00:42 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [85/200][10/27]	eta 0:00:34 lr 0.000055	time 2.3990 (2.0321)	loss 8.4625 (6.3096)	miou 0.2241	grad_norm 14.3009 (38.7298)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 21:01:03 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [85/200][20/27]	eta 0:00:14 lr 0.000062	time 2.1029 (2.0413)	loss 5.2890 (6.7199)	miou 0.2099	grad_norm 43.6751 (35.9666)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 21:01:15 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 85 training takes 0:00:54
[32m[2025-07-13 21:01:15 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 21:01:25 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.2510%
[32m[2025-07-13 21:01:25 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.3018%
[32m[2025-07-13 21:01:28 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [86/200][0/27]	eta 0:01:04 lr 0.000068	time 2.3910 (2.3910)	loss 5.9607 (5.9607)	miou 0.2471	grad_norm 17.3160 (17.3160)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 21:01:47 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [86/200][10/27]	eta 0:00:33 lr 0.000075	time 1.9748 (1.9608)	loss 4.6920 (6.4156)	miou 0.2415	grad_norm 40.3517 (51.2455)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 21:02:07 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [86/200][20/27]	eta 0:00:14 lr 0.000081	time 1.9988 (2.0025)	loss 6.2254 (6.9602)	miou 0.2413	grad_norm 29.2180 (43.9372)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 21:02:19 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 86 training takes 0:00:53
[32m[2025-07-13 21:02:19 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 21:02:29 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.2423%
[32m[2025-07-13 21:02:29 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.3018%
[32m[2025-07-13 21:02:31 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [87/200][0/27]	eta 0:00:59 lr 0.000085	time 2.1922 (2.1922)	loss 8.2323 (8.2323)	miou 0.2402	grad_norm 27.2039 (27.2039)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 21:02:52 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [87/200][10/27]	eta 0:00:35 lr 0.000090	time 2.1136 (2.0788)	loss 7.2046 (7.0541)	miou 0.2606	grad_norm 21.6508 (28.5984)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 21:03:12 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [87/200][20/27]	eta 0:00:14 lr 0.000095	time 2.2575 (2.0504)	loss 6.9120 (7.1929)	miou 0.2416	grad_norm 19.4599 (27.0764)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 21:03:24 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 87 training takes 0:00:54
[32m[2025-07-13 21:03:24 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 21:03:34 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.2565%
[32m[2025-07-13 21:03:34 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.3018%
[32m[2025-07-13 21:03:36 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [88/200][0/27]	eta 0:00:50 lr 0.000097	time 1.8849 (1.8849)	loss 5.4063 (5.4063)	miou 0.2537	grad_norm 11.7504 (11.7504)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 21:03:57 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [88/200][10/27]	eta 0:00:34 lr 0.000099	time 1.9158 (2.0355)	loss 6.0893 (7.3873)	miou 0.2487	grad_norm 20.1193 (30.3625)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 21:04:16 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [88/200][20/27]	eta 0:00:13 lr 0.000100	time 2.0289 (1.9838)	loss 7.6972 (7.4123)	miou 0.2144	grad_norm 119.0156 (35.6597)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 21:04:28 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 88 training takes 0:00:53
[32m[2025-07-13 21:04:28 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 21:04:38 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1178%
[32m[2025-07-13 21:04:38 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.3018%
[32m[2025-07-13 21:04:41 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [89/200][0/27]	eta 0:01:06 lr 0.000100	time 2.4740 (2.4740)	loss 7.3330 (7.3330)	miou 0.1445	grad_norm 21.6936 (21.6936)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 21:05:01 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [89/200][10/27]	eta 0:00:34 lr 0.000099	time 2.0433 (2.0144)	loss 4.6380 (6.7779)	miou 0.2092	grad_norm 35.8937 (30.9748)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 21:05:22 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [89/200][20/27]	eta 0:00:14 lr 0.000096	time 1.9047 (2.0708)	loss 6.8873 (6.9007)	miou 0.2081	grad_norm 28.8224 (32.4264)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 21:05:34 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 89 training takes 0:00:55
[32m[2025-07-13 21:05:34 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 21:05:44 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.2222%
[32m[2025-07-13 21:05:44 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.3018%
[32m[2025-07-13 21:05:47 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [90/200][0/27]	eta 0:01:06 lr 0.000094	time 2.4480 (2.4480)	loss 7.8990 (7.8990)	miou 0.2182	grad_norm 23.1480 (23.1480)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 21:06:07 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [90/200][10/27]	eta 0:00:34 lr 0.000090	time 2.2477 (2.0167)	loss 5.5508 (6.8110)	miou 0.2366	grad_norm 29.7459 (27.2065)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 21:06:26 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [90/200][20/27]	eta 0:00:13 lr 0.000085	time 1.6945 (1.9817)	loss 7.5294 (6.5960)	miou 0.2374	grad_norm 20.0190 (25.2485)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 21:06:39 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 90 training takes 0:00:54
[32m[2025-07-13 21:06:39 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 21:06:49 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.2013%
[32m[2025-07-13 21:06:49 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.3018%
[32m[2025-07-13 21:06:52 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [91/200][0/27]	eta 0:01:11 lr 0.000081	time 2.6451 (2.6451)	loss 8.3800 (8.3800)	miou 0.2028	grad_norm 42.7218 (42.7218)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 21:07:12 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [91/200][10/27]	eta 0:00:35 lr 0.000074	time 2.0999 (2.0860)	loss 5.3155 (7.1562)	miou 0.2153	grad_norm 49.8816 (30.6796)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 21:07:31 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [91/200][20/27]	eta 0:00:13 lr 0.000067	time 1.9640 (1.9893)	loss 7.4579 (7.2015)	miou 0.2220	grad_norm 73.4285 (34.9755)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 21:07:43 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 91 training takes 0:00:54
[32m[2025-07-13 21:07:43 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 21:07:53 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1309%
[32m[2025-07-13 21:07:53 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.3018%
[32m[2025-07-13 21:07:56 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [92/200][0/27]	eta 0:01:03 lr 0.000062	time 2.3469 (2.3469)	loss 8.6732 (8.6732)	miou 0.1366	grad_norm 26.8874 (26.8874)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 21:08:17 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [92/200][10/27]	eta 0:00:36 lr 0.000054	time 2.1408 (2.1209)	loss 6.3890 (6.9199)	miou 0.1991	grad_norm 22.7675 (30.7141)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 21:08:36 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [92/200][20/27]	eta 0:00:14 lr 0.000046	time 2.3292 (2.0397)	loss 6.5289 (6.5777)	miou 0.2066	grad_norm 44.4112 (28.6575)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 21:08:49 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 92 training takes 0:00:55
[32m[2025-07-13 21:08:49 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 21:08:59 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1700%
[32m[2025-07-13 21:08:59 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.3018%
[32m[2025-07-13 21:09:02 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [93/200][0/27]	eta 0:01:09 lr 0.000041	time 2.5654 (2.5654)	loss 10.3757 (10.3757)	miou 0.1684	grad_norm 21.5177 (21.5177)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 21:09:21 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [93/200][10/27]	eta 0:00:33 lr 0.000033	time 2.5955 (1.9539)	loss 7.8550 (6.9653)	miou 0.2173	grad_norm 50.5413 (22.6575)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 21:09:41 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [93/200][20/27]	eta 0:00:13 lr 0.000026	time 2.0954 (1.9812)	loss 5.9252 (7.0086)	miou 0.2275	grad_norm 53.5217 (26.5472)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 21:09:53 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 93 training takes 0:00:54
[32m[2025-07-13 21:09:53 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 21:10:04 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1923%
[32m[2025-07-13 21:10:04 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.3018%
[32m[2025-07-13 21:10:06 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [94/200][0/27]	eta 0:01:11 lr 0.000021	time 2.6359 (2.6359)	loss 7.9079 (7.9079)	miou 0.1955	grad_norm 12.8807 (12.8807)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 21:10:25 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [94/200][10/27]	eta 0:00:33 lr 0.000015	time 1.9736 (1.9590)	loss 6.5497 (6.7603)	miou 0.2132	grad_norm 25.6212 (28.4745)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 21:10:46 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [94/200][20/27]	eta 0:00:14 lr 0.000010	time 1.9159 (2.0301)	loss 6.0047 (6.7004)	miou 0.2308	grad_norm 22.7452 (25.7074)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 21:10:58 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 94 training takes 0:00:53
[32m[2025-07-13 21:10:58 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 21:11:08 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1720%
[32m[2025-07-13 21:11:08 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.3018%
[32m[2025-07-13 21:11:10 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [95/200][0/27]	eta 0:00:54 lr 0.000007	time 2.0008 (2.0008)	loss 6.4924 (6.4924)	miou 0.1915	grad_norm 19.4795 (19.4795)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 21:11:31 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [95/200][10/27]	eta 0:00:35 lr 0.000004	time 2.0897 (2.0634)	loss 5.4210 (6.7044)	miou 0.2122	grad_norm 17.8152 (30.8291)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 21:11:50 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [95/200][20/27]	eta 0:00:14 lr 0.000001	time 1.8260 (2.0037)	loss 7.8049 (6.9635)	miou 0.2260	grad_norm 29.2376 (27.6434)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 21:12:02 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 95 training takes 0:00:53
[32m[2025-07-13 21:12:02 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 21:12:13 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.2027%
[32m[2025-07-13 21:12:13 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.3018%
[32m[2025-07-13 21:12:15 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [96/200][0/27]	eta 0:01:06 lr 0.000000	time 2.4657 (2.4657)	loss 6.5063 (6.5063)	miou 0.2054	grad_norm 18.8082 (18.8082)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 21:12:35 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [96/200][10/27]	eta 0:00:34 lr 0.000000	time 1.8307 (2.0367)	loss 6.0665 (6.1221)	miou 0.2405	grad_norm 21.9943 (32.9326)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 21:12:55 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [96/200][20/27]	eta 0:00:14 lr 0.000001	time 2.2190 (2.0252)	loss 5.7852 (6.2066)	miou 0.2535	grad_norm 18.4573 (28.7337)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 21:13:08 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 96 training takes 0:00:55
[32m[2025-07-13 21:13:08 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 21:13:19 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.1982%
[32m[2025-07-13 21:13:19 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.3018%
[32m[2025-07-13 21:13:22 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [97/200][0/27]	eta 0:01:11 lr 0.000002	time 2.6530 (2.6530)	loss 4.9282 (4.9282)	miou 0.2046	grad_norm 24.9168 (24.9168)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 21:13:41 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [97/200][10/27]	eta 0:00:34 lr 0.000005	time 2.2807 (2.0339)	loss 5.7304 (6.5283)	miou 0.2141	grad_norm 20.2521 (25.7815)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 21:14:00 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [97/200][20/27]	eta 0:00:13 lr 0.000010	time 2.2895 (1.9809)	loss 7.6502 (6.5308)	miou 0.2334	grad_norm 30.4465 (25.1277)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 21:14:12 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 97 training takes 0:00:53
[32m[2025-07-13 21:14:12 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 21:14:23 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.2060%
[32m[2025-07-13 21:14:23 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.3018%
[32m[2025-07-13 21:14:25 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [98/200][0/27]	eta 0:01:06 lr 0.000013	time 2.4577 (2.4577)	loss 5.6666 (5.6666)	miou 0.2187	grad_norm 20.7231 (20.7231)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 21:14:46 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [98/200][10/27]	eta 0:00:35 lr 0.000019	time 1.7467 (2.1022)	loss 6.5244 (7.3073)	miou 0.2295	grad_norm 31.2160 (26.4749)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 21:15:05 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [98/200][20/27]	eta 0:00:14 lr 0.000025	time 1.7691 (2.0037)	loss 7.8020 (7.4542)	miou 0.2271	grad_norm 63.7128 (28.9603)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
[32m[2025-07-13 21:15:17 3DDETR.yaml][33m(main.py 300)[39m: INFO EPOCH 98 training takes 0:00:54
[32m[2025-07-13 21:15:17 3DDETR.yaml][33m(main.py 321)[39m: INFO Starting validation...
[32m[2025-07-13 21:15:28 3DDETR.yaml][33m(main.py 165)[39m: INFO Mean IOU of the network on the 40 test images: 0.2180%
[32m[2025-07-13 21:15:28 3DDETR.yaml][33m(main.py 167)[39m: INFO Max miou: 0.3018%
[32m[2025-07-13 21:15:30 3DDETR.yaml][33m(main.py 280)[39m: INFO Train: [99/200][0/27]	eta 0:00:56 lr 0.000030	time 2.1055 (2.1055)	loss 5.8755 (5.8755)	miou 0.2175	grad_norm 18.2765 (18.2765)	loss_scale 131072.0000 (131072.0000)	mem 5971MB
