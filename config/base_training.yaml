# Base training YAML file for training the model

input_folder_path: "/home/<USER>/Coding/codingchallenge_sereact/data"

seed: 40
start_epoch: 0
max_epochs: 30
batch_size: 1
num_workers: 4
eval_every_epoch: 10
valid_only: false

model:
  encoder:
    dim: 256
    nheads: 4
    ffn_dim: 128
    dropout: 0.1
    activation: relu
    num_layers: 3
    type: vanilla
    preencoder_npoints: 2048
    use_color: false
  decoder:
    dim: 256
    nhead: 4
    ffn_dim: 256
    dropout: 0.1
    num_layers: 3
  position_embedding: fourier
  mlp_dropout: 0.3
  num_queries: 256
  num_angular_bins: 12
  pretrained_weights_path: "/home/<USER>/Coding/Pre_trained_Weights/3detr/scannet_ep1080.pth"
  export_model: false


optimizer:
  base_lr: 0.0001 # Was 0.0005
  warm_lr: 5e-6 # Increasing it to 5e-6 from 1e-6
  warm_lr_epochs: 9
  final_lr: 1e-6
  lr_scheduler: cosine
  weight_decay: 0.01 # Reduced the weight decay from 0.1 to 0.01
  filter_biases_wd: True
  clip_gradient: 0.1

loss:
  matcher_costs:
    giou: 5.0
    cost_box_corners: 1.0
    l1: 2.0
  weights:
    giou: 1.0
    box_corners: 1.0 # Was 2.0 made 1.0
    size: 1.0
    size_reg: 1.0
    # center: 5.0
    # angle_cls: 0.1
    # angle_reg: 0.5

debug:
  enable: false
  ds_number: 13

test_split: 0.2
checkpoint_dir: "/home/<USER>/Checkpoints/3D_Bbox/"
