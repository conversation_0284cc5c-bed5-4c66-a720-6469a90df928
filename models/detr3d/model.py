# Modified version of 3DETR that adds RGB feature fusion and 2D mask supervision

import torch
import torch.nn as nn
import torch.nn.functional as F
from torchvision.models import resnet18
from models.model_3ddetr import Model3DDETR  # Original base model from model_3detr.py

class RGBFusion3DETR(Model3DDETR):
    def __init__(
        self,
        *args,
        rgb_backbone_output_dim=64,
        intrinsics=None,
        image_size=(578, 646),
        use_rgb_fusion=True,
        use_mask_supervision=True,
        num_classes=10,
        **kwargs
    ):
        super().__init__(*args, **kwargs)
        self.use_rgb_fusion = use_rgb_fusion
        self.use_mask_supervision = use_mask_supervision
        self.image_size = image_size
        self.intrinsics = intrinsics if intrinsics is not None else torch.tensor([
            [575.0, 0.0, 323.0],
            [0.0, 575.0, 289.0],
            [0.0, 0.0, 1.0]
        ])

        if use_rgb_fusion:
            self.rgb_backbone = nn.Sequential(
                *list(resnet18(pretrained=True).children())[:6]
            )  # Output: (B, 64, H/8, W/8)
            self.rgb_proj = nn.Conv1d(rgb_backbone_output_dim + self.pre_encoder.mlp[-1].out_channels,
                                      self.pre_encoder.mlp[-1].out_channels, 1)

        if use_mask_supervision:
            self.segmentation_head = nn.Sequential(
                nn.Conv1d(self.pre_encoder.mlp[-1].out_channels, 128, 1),
                nn.ReLU(),
                nn.Conv1d(128, num_classes, 1)
            )

    def project_points(self, points_3d, intrinsics, image_size):
        fx, fy = intrinsics[0, 0], intrinsics[1, 1]
        cx, cy = intrinsics[0, 2], intrinsics[1, 2]

        x = points_3d[:, :, 0]
        y = points_3d[:, :, 1]
        z = points_3d[:, :, 2].clamp(min=1e-5)

        u = fx * (x / z) + cx
        v = fy * (y / z) + cy

        h, w = image_size
        u = u.clamp(0, w - 1)
        v = v.clamp(0, h - 1)

        return torch.stack([u, v], dim=-1)  # (B, N, 2)

    def sample_rgb_features(self, rgb_feat, uv_coords):
        B, C, H, W = rgb_feat.shape
        uv_norm = uv_coords.clone()
        uv_norm[..., 0] = (uv_coords[..., 0] / (W - 1)) * 2 - 1
        uv_norm[..., 1] = (uv_coords[..., 1] / (H - 1)) * 2 - 1
        grid = uv_norm.unsqueeze(2)
        sampled = F.grid_sample(rgb_feat, grid, mode='bilinear', align_corners=True)
        return sampled.squeeze(3)

    def fuse_rgb_with_points(self, pc_xyz, pc_features, rgb_image):
        rgb_feat = self.rgb_backbone(rgb_image)
        uv_coords = self.project_points(pc_xyz, self.intrinsics.to(pc_xyz.device), self.image_size)
        sampled_rgb = self.sample_rgb_features(rgb_feat, uv_coords)
        fused = torch.cat([pc_features, sampled_rgb], dim=1)
        return self.rgb_proj(fused)

    def forward(self, inputs_list, point_cloud_dims_min, point_cloud_dims_max, encoder_only=False):
        batch_predictions = []

        for input_dict, pcd_dim_min, pcd_dim_max in zip(inputs_list, point_cloud_dims_min, point_cloud_dims_max):
            rgb = input_dict["rgb"]
            pc_tensor = input_dict["pc"]

            xyz, feats = self._break_up_pc(pc_tensor.unsqueeze(0))

            if self.use_rgb_fusion:
                feats = self.fuse_rgb_with_points(xyz, feats, rgb.unsqueeze(0))

            xyz, features, indices = self.pre_encoder(xyz, feats)
            features = features.permute(2, 0, 1)

            encoder_xyz, encoder_features, _ = self.encoder(features, xyz=xyz)
            encoder_features = self.encoder_decoder_projection(
                encoder_features.permute(1, 2, 0)
            ).permute(2, 0, 1)

            if encoder_only:
                batch_predictions.append((encoder_xyz, encoder_features.transpose(0, 1)))
                continue

            point_cloud_dims = [pcd_dim_min, pcd_dim_max]

            query_xyz, query_embeddings = self.get_query_embedding(encoder_xyz, point_cloud_dims)

            encoder_pos = self.positional_embedding(encoder_xyz, input_range=point_cloud_dims).permute(2, 0, 1)
            query_embeddings = query_embeddings.permute(2, 0, 1)
            target = torch.zeros_like(query_embeddings)

            box_features = self.decoder(
                tgt=target,
                memory=encoder_features,
                query_pos=query_embeddings,
                pos=encoder_pos
            )[0]

            box_predictions = self.get_box_prediction(query_xyz, point_cloud_dims, box_features)

            if self.use_mask_supervision:
                segmentation_logits = self.segmentation_head(features.permute(1, 2, 0))
                box_predictions["segmentation_logits"] = segmentation_logits

            batch_predictions.append(box_predictions)

        return batch_predictions


