ninja_required_version = 1.3
cxx = c++
nvcc = /usr/bin/nvcc

cflags = -pthread -B /gel/usr/akath/.conda/envs/swin/compiler_compat -Wl,--sysroot=/ -Wsign-compare -DNDEBUG -g -fwrapv -O3 -Wall -Wstrict-prototypes -fPIC -I/home-local/akath.nobkp/codingchallenge_sereact/models/detr3d/_ext_src/include -I/gel/usr/akath/.conda/envs/swin/lib/python3.7/site-packages/torch/include -I/gel/usr/akath/.conda/envs/swin/lib/python3.7/site-packages/torch/include/torch/csrc/api/include -I/gel/usr/akath/.conda/envs/swin/lib/python3.7/site-packages/torch/include/TH -I/gel/usr/akath/.conda/envs/swin/lib/python3.7/site-packages/torch/include/THC -I/gel/usr/akath/.conda/envs/swin/include/python3.7m -c
post_cflags = -O2 -I_ext_src/include -DTORCH_API_INCLUDE_EXTENSION_H '-DPYBIND11_COMPILER_TYPE="_gcc"' '-DPYBIND11_STDLIB="_libstdcpp"' '-DPYBIND11_BUILD_ABI="_cxxabi1011"' -DTORCH_EXTENSION_NAME=_ext_src -D_GLIBCXX_USE_CXX11_ABI=0 -std=c++14
cuda_cflags = -I/home-local/akath.nobkp/codingchallenge_sereact/models/detr3d/_ext_src/include -I/gel/usr/akath/.conda/envs/swin/lib/python3.7/site-packages/torch/include -I/gel/usr/akath/.conda/envs/swin/lib/python3.7/site-packages/torch/include/torch/csrc/api/include -I/gel/usr/akath/.conda/envs/swin/lib/python3.7/site-packages/torch/include/TH -I/gel/usr/akath/.conda/envs/swin/lib/python3.7/site-packages/torch/include/THC -I/gel/usr/akath/.conda/envs/swin/include/python3.7m -c
cuda_post_cflags = -D__CUDA_NO_HALF_OPERATORS__ -D__CUDA_NO_HALF_CONVERSIONS__ -D__CUDA_NO_BFLOAT16_CONVERSIONS__ -D__CUDA_NO_HALF2_OPERATORS__ --expt-relaxed-constexpr --compiler-options ''"'"'-fPIC'"'"'' -O2 -I_ext_src/include -DTORCH_API_INCLUDE_EXTENSION_H '-DPYBIND11_COMPILER_TYPE="_gcc"' '-DPYBIND11_STDLIB="_libstdcpp"' '-DPYBIND11_BUILD_ABI="_cxxabi1011"' -DTORCH_EXTENSION_NAME=_ext_src -D_GLIBCXX_USE_CXX11_ABI=0 -gencode=arch=compute_61,code=compute_61 -gencode=arch=compute_61,code=sm_61 -std=c++14
ldflags = 

rule compile
  command = $cxx -MMD -MF $out.d $cflags -c $in -o $out $post_cflags
  depfile = $out.d
  deps = gcc

rule cuda_compile
  depfile = $out.d
  deps = gcc
  command = $nvcc --generate-dependencies-with-compile --dependency-output $out.d $cuda_cflags -c $in -o $out $cuda_post_cflags



build /home-local/akath.nobkp/codingchallenge_sereact/models/detr3d/build/temp.linux-x86_64-cpython-37/_ext_src/src/ball_query.o: compile /home-local/akath.nobkp/codingchallenge_sereact/models/detr3d/_ext_src/src/ball_query.cpp
build /home-local/akath.nobkp/codingchallenge_sereact/models/detr3d/build/temp.linux-x86_64-cpython-37/_ext_src/src/ball_query_gpu.o: cuda_compile /home-local/akath.nobkp/codingchallenge_sereact/models/detr3d/_ext_src/src/ball_query_gpu.cu
build /home-local/akath.nobkp/codingchallenge_sereact/models/detr3d/build/temp.linux-x86_64-cpython-37/_ext_src/src/bindings.o: compile /home-local/akath.nobkp/codingchallenge_sereact/models/detr3d/_ext_src/src/bindings.cpp
build /home-local/akath.nobkp/codingchallenge_sereact/models/detr3d/build/temp.linux-x86_64-cpython-37/_ext_src/src/group_points.o: compile /home-local/akath.nobkp/codingchallenge_sereact/models/detr3d/_ext_src/src/group_points.cpp
build /home-local/akath.nobkp/codingchallenge_sereact/models/detr3d/build/temp.linux-x86_64-cpython-37/_ext_src/src/group_points_gpu.o: cuda_compile /home-local/akath.nobkp/codingchallenge_sereact/models/detr3d/_ext_src/src/group_points_gpu.cu
build /home-local/akath.nobkp/codingchallenge_sereact/models/detr3d/build/temp.linux-x86_64-cpython-37/_ext_src/src/interpolate.o: compile /home-local/akath.nobkp/codingchallenge_sereact/models/detr3d/_ext_src/src/interpolate.cpp
build /home-local/akath.nobkp/codingchallenge_sereact/models/detr3d/build/temp.linux-x86_64-cpython-37/_ext_src/src/interpolate_gpu.o: cuda_compile /home-local/akath.nobkp/codingchallenge_sereact/models/detr3d/_ext_src/src/interpolate_gpu.cu
build /home-local/akath.nobkp/codingchallenge_sereact/models/detr3d/build/temp.linux-x86_64-cpython-37/_ext_src/src/sampling.o: compile /home-local/akath.nobkp/codingchallenge_sereact/models/detr3d/_ext_src/src/sampling.cpp
build /home-local/akath.nobkp/codingchallenge_sereact/models/detr3d/build/temp.linux-x86_64-cpython-37/_ext_src/src/sampling_gpu.o: cuda_compile /home-local/akath.nobkp/codingchallenge_sereact/models/detr3d/_ext_src/src/sampling_gpu.cu





