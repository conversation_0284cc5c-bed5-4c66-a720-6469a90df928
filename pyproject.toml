[tool.ruff]
line-length = 100

select = [
    "ANN", # flake8-annotations for type hints
    "C",  # mccabe rules
    "F",  # pyflakes rules
    "E",  # pycodestyle error rules
    "W",  # pycodestyle warning rules
    "B",  # flake8-bugbear rules
    "I",  # isort rules
    "G", # Google styled docstrings
]

ignore = [
    "C901",  # max-complexity-10
    "E501",  # line-too-long,
]

[tool.ruff.format]
indent-style = "space"
quote-style = "single"
